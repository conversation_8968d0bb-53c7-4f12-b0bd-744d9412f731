"""
Window Tiler - Main Entry Point

This module provides the main entry point and high-level commands for the
Window Tiler application. It offers simple, powerful commands for tiling and
managing windows based on type, process, or other characteristics.
"""

import argparse
import sys

from window_tiler.core import (
    Monitor, Window, Tiler,
    get_all_monitors, get_all_windows, get_windows_by_type,
    WindowType
)


def tile_by_type(window_type=None, process_name=None, window_class=None, 
                rows=2, columns=2, monitor_index=0, primary_only=True):
    """
    Tile windows matching specified criteria.
    
    Args:
        window_type: Optional WindowType enum value to filter by
        process_name: Optional process name to filter by
        window_class: Optional window class name to filter by
        rows: Number of rows in the grid
        columns: Number of columns in the grid
        monitor_index: Index of monitor to use (0 for primary)
        primary_only: Whether to use only the primary monitor
    """
    # Get all monitors
    monitors = get_all_monitors()
    
    # Get target monitor
    target_monitor = None
    if primary_only:
        # Find primary monitor
        for monitor in monitors.values():
            if monitor.is_primary:
                target_monitor = monitor
                break
    else:
        # Use monitor at specified index
        if monitor_index < len(monitors):
            target_monitor = list(monitors.values())[monitor_index]
    
    # Default to first monitor if target not found
    if not target_monitor and monitors:
        target_monitor = list(monitors.values())[0]
        
    if not target_monitor:
        print("No monitors found")
        return
        
    # Get windows by type
    windows = get_windows_by_type(window_type, process_name, window_class)
    if not windows:
        if process_name:
            print(f"No windows found matching process: {process_name}")
        elif window_class:
            print(f"No windows found matching class: {window_class}")
        else:
            print("No windows found matching criteria")
        return
        
    # Create tiler and tile windows
    tiler = Tiler(monitors)
    tiler.tile_grid(target_monitor, windows, rows, columns)
    
    print(f"Tiled {len(windows)} windows in a {rows}x{columns} grid")


def list_windows(show_types=False, show_classes=False):
    """
    List all visible windows with optional type and class information.
    
    Args:
        show_types: Whether to detect and show window types
        show_classes: Whether to show window class names
    """
    # Get all windows
    windows = get_all_windows(detect_types=show_types).values()
    
    print(f"Found {len(windows)} windows:")
    print("-" * 80)
    
    # Display windows
    for i, window in enumerate(windows):
        info = f"{i+1}. \"{window.title}\" (hwnd={window.hwnd})"
        
        if show_types and window.window_type:
            info += f" [Type: {window.window_type.name}]"
            
        if show_classes:
            info += f" [Class: {window.class_name}]"
            
        print(info)
        
    print("-" * 80)


def list_monitors():
    """List all monitors with their information."""
    # Get all monitors
    monitors = get_all_monitors()
    
    print(f"Found {len(monitors)} monitors:")
    print("-" * 80)
    
    # Display monitors
    for i, monitor in enumerate(monitors.values()):
        dims = monitor.get_dimensions()
        primary_text = " (Primary)" if monitor.is_primary else ""
        
        print(f"{i+1}. Monitor{primary_text} - {dims['width']}x{dims['height']} - {monitor.device}")
        
    print("-" * 80)


def tile_browsers(rows=2, columns=2):
    """Tile all browser windows in a grid."""
    print("Tiling all browser windows...")
    tile_by_type(WindowType.BROWSER, rows=rows, columns=columns)
    tile_by_type(WindowType.BROWSER_CHROME, rows=rows, columns=columns)
    tile_by_type(WindowType.BROWSER_EDGE, rows=rows, columns=columns)
    tile_by_type(WindowType.BROWSER_FIREFOX, rows=rows, columns=columns)


def tile_explorer(rows=2, columns=2):
    """Tile all Explorer windows in a grid."""
    print("Tiling all Explorer windows...")
    tile_by_type(WindowType.EXPLORER_NORMAL, rows=rows, columns=columns)
    tile_by_type(WindowType.EXPLORER_SPECIAL, rows=rows, columns=columns)


def tile_terminals(rows=2, columns=2):
    """Tile all terminal windows in a grid."""
    print("Tiling all terminal windows...")
    tile_by_type(WindowType.TERMINAL, rows=rows, columns=columns)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Window Tiler - Arrange windows by type')
    
    # Common parameters
    parser.add_argument('--rows', type=int, default=2, help='Number of rows in the grid')
    parser.add_argument('--columns', type=int, default=2, help='Number of columns in the grid')
    parser.add_argument('--monitor', type=int, default=0, 
                      help='Monitor index to use (0 for primary)')
    
    # Create subparsers for commands
    subparsers = parser.add_subparsers(dest='command', help='Command to execute')
    
    # list_windows command
    list_cmd = subparsers.add_parser('list', help='List all visible windows')
    list_cmd.add_argument('--types', action='store_true', help='Show window types')
    list_cmd.add_argument('--classes', action='store_true', help='Show window classes')
    
    # list_monitors command
    subparsers.add_parser('monitors', help='List all monitors')
    
    # tile_process command
    proc_cmd = subparsers.add_parser('process', help='Tile windows by process name')
    proc_cmd.add_argument('name', help='Process name to filter by')
    
    # tile_class command
    class_cmd = subparsers.add_parser('class', help='Tile windows by class name')
    class_cmd.add_argument('name', help='Window class name to filter by')
    
    # tile_browsers command
    subparsers.add_parser('browsers', help='Tile all browser windows')
    
    # tile_explorer command
    subparsers.add_parser('explorer', help='Tile all Explorer windows')
    
    # tile_terminals command
    subparsers.add_parser('terminals', help='Tile all terminal windows')
    
    return parser.parse_args()


def main():
    """Main entry point for the application."""
    # Parse command line arguments
    args = parse_args()
    
    # Execute command based on arguments
    if args.command == 'list':
        list_windows(show_types=args.types, show_classes=args.classes)
    elif args.command == 'monitors':
        list_monitors()
    elif args.command == 'process':
        tile_by_type(process_name=args.name, rows=args.rows, columns=args.columns, 
                   monitor_index=args.monitor)
    elif args.command == 'class':
        tile_by_type(window_class=args.name, rows=args.rows, columns=args.columns, 
                   monitor_index=args.monitor)
    elif args.command == 'browsers':
        tile_browsers(args.rows, args.columns)
    elif args.command == 'explorer':
        tile_explorer(args.rows, args.columns)
    elif args.command == 'terminals':
        tile_terminals(args.rows, args.columns)
    else:
        # Default behavior - show help
        list_windows(show_types=True)
        print("\nUse --help for more information on available commands")


if __name__ == '__main__':
    main()
