# Project Brief: Windows Window Tiler

## Overview
The Window Tiler is a utility designed to automatically arrange and organize windows on Windows operating systems. It allows users to dynamically tile windows based on their type (e.g., by process name), providing an efficient way to manage screen real estate across multiple monitors.

## Core Objectives
1. Detect and enumerate all available monitors on a system
2. Identify and categorize all visible windows on a system
3. Arrange windows in customizable grid layouts within specified monitor boundaries
4. Support filtering windows by process name/type for targeted tiling operations
5. Provide a simple and intuitive interface for managing window layouts
6. Enable easy window manipulation (resizing, moving, maximizing, centering)

## Key Features
- Multi-monitor support with primary monitor detection
- Advanced window property access (dimensions, position, visibility, focus status)
- Custom tiling with configurable rows, columns, and size ratios
- Window filtering based on process name, class, or other attributes
- Basic window operations (toggling visibility, bringing to front, etc.)

## Technical Requirements
- Python with Win32 API integration
- Minimal external dependencies
- Reliable window detection and manipulation
- Support for Windows 10/11 environments

## Project Status
The project currently exists as several iterations of implementation with core functionality working but needing consolidation. There are multiple versions of similar code spread across different directories, and the project requires cleanup to establish a single, coherent implementation.

## Consolidation Goals
1. Identify the most complete and robust implementation among existing files
2. Establish a clean architecture that separates concerns (monitors, windows, tiling logic)
3. Eliminate redundant code while preserving all useful functionality
4. Enhance the window type detection and filtering capabilities
5. Create clear documentation and usage examples
