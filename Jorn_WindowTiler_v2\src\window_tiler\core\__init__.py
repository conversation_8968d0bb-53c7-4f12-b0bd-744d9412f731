"""
Window Tiler Core Package

This package contains the core components of the Window Tiler application:
- Monitor: Detection and representation of physical displays
- Window: Detection, manipulation, and filtering of windows
- Tiler: Window arrangement algorithms and operations
- Types: Enumerations for window classification
"""

from .monitor import Monitor, get_all_monitors
from .window import Window, get_all_windows, get_windows_by_type
from .tiler import Tiler, tile_windows
from .types import WindowType, WindowState

__all__ = [
    # Monitor components
    'Monitor', 'get_all_monitors',
    
    # Window components
    'Window', 'get_all_windows', 'get_windows_by_type',
    
    # Tiler components
    'Tiler', 'tile_windows',
    
    # Type definitions
    'WindowType', 'WindowState'
]
