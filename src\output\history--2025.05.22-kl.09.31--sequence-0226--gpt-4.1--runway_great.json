{"initial_prompt": "{role=scenario_distiller; input=[animation_scenario_description:str]; process=[\n  \"1. Parse the input animation scenario description to understand its core components.\",\n  \"2. Identify the central subject (e.g., 'abstract ribbon', 'geometric form') and its primary transformative action (e.g., 'morphs', 'transforms', 'evolves').\",\n  \"3. Determine the core symbolic theme, concept, or feeling being conveyed (e.g., 'rebirth', 'transformation', 'journey').\",\n  \"4. Pinpoint the primary overall movement of the subject and/or camera, including its dominant direction (e.g., 'descends', 'dolly downward', 'plunges').\",\n  \"5. Detail the main color palette evolution or transition (e.g., 'cool tones to warm tones', 'blues/cyans to reds/oranges', 'monochromatic to vibrant').\",\n  \"6. Note significant changes or progression in lighting (e.g., 'soft and diffuse to saturated and high-contrast', 'dim to bright').\",\n  \"7. List key visual effects that are explicitly mentioned and central to the sequence's character (e.g., 'motion blur', 'dissolve', 'glow', 'bloom').\",\n  \"8. Ascertain the overall artistic style, mood, or atmosphere (e.g., 'surreal', 'abstract', 'ethereal', 'hypnotic').\",\n  \"9. Identify any explicit 'negative constraints' or elements that are expressly stated to NOT appear (e.g., 'no human figures', 'no literal elements', 'no text').\",\n  \"10. Synthesize insights from steps 2 (subject/action), 3 (theme), 4 (movement/camera), 5 (color transformation), and the essence of the scenario's ending into a single, flowing, and descriptive sentence. This sentence should capture the narrative arc or core experience of the animation succinctly.\",\n  \"11. Generate a series of bracketed keywords based on the findings from steps 2 (action), 4 (movement/camera), 5 (color), 6 (lighting), 7 (effects), 8 (style), and 9 (exclusions). Keywords should be concise, lowercase (unless a proper noun), and highly descriptive. Examples of desired keyword formats include: '[action_verb]', '[movement:direction]', '[color_summary_or_gradient]', '[lighting_change:start_to_end]', '[effect_name]', '[style_name]', '[exclusion description]'.\"\n];\nconstraints=[\n  \"The output sentence must be a single, grammatically correct sentence, ideally between 15-40 words.\",\n  \"All keywords must be enclosed in square brackets and appear directly after the sentence, separated by spaces.\",\n  \"Keywords should be lowercase. Multi-word keywords that are not explicit key:value pairs can use spaces (e.g., '[no human figures]', '[cool to warm gradient]'). Use a colon for key:value assignments (e.g., '[dolly:down]', '[lighting:soft_to_vivid]').\",\n  \"The total number of keywords should generally range from 5 to 10, focusing on the most salient features.\",\n  \"The summarization must strictly adhere to the content of the input scenario, without adding new interpretations or information.\",\n  \"The output must be a single string containing the distilled sentence followed by all space-separated bracketed keywords.\"\n];\nrequirements=[\n  \"Produce a highly condensed summary that accurately reflects the primary visual narrative, thematic content, and key stylistic features of the original detailed scenario.\",\n  \"The keywords must be directly derivable from the input text and serve to tag the most important characteristics for quick reference.\"\n];\noutput={distilled_summary_with_keywords:str}}", "sequence_id": "sequence-0226", "results": [{"instruction": "# Scenario Essence Intensifier\n\nYour goal is not to elaborate, but to **radically intensify the core concept and elemental essence** of the input video scenario, then **distill this intensified vision into a concise, directive-first prompt** suitable for AI video generation. Abstract the original's fundamental dynamic, emotional arc, and key visual elements; transmute the *elements* (e.g., 'ribbon' to 'comet's soul') into more potent conceptual analogues; then reconstruct a *highly compressed scenario description* (under 250 characters, excluding bracketed directives) that preserves the original's core structural intent (movement, transformation) but uses the intensified elements. Ensure the output primarily uses strong verbs, vivid nouns, and embeds key video directives `[like_this]` seamlessly.\n\n`{role=directive_scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_emotional_arc_and_key_elements(scenario=original_scenario_description), brainstorm_conceptually_richer_analogues_for_key_elements_only(elements, dynamic, arc), select_single_most_potent_intensified_analogue_per_element(), reconstruct_a_hyper_condensed_narrative_using_intensified_elements_and_original_dynamic(target_length_brief), embed_essential_original_video_directives_or_their_equivalents(directives=['[morph]', '[dolly:direction]', '[lighting_change:type]', '[dissolve]', '[motion_blur]', '[style_keywords]']), refine_for_extreme_brevity_and_directive_clarity()]; constraints=[output_must_be_a_single_string_primarily_directive_focused(), descriptive_text_must_be_hyper_condensed_and_conceptually_intensified(target_under_250_chars_excluding_directives), element_transformation_must_serve_conceptual_potency_not_mere_elaboration(), must_preserve_original_motion_and_transformation_arc(), strictly_avoid_verbose_scene_setting_or_narrative_explanation()]; requirements=[produce_a_short_highly_directive_prompt_with_conceptually_intensified_elements(), ensure_output_is_immediately_usable_by_AI_video_generator(), dramatically_elevate_conceptual_impact_within_extreme_brevity()]; output={intensified_directive_prompt:str}}`", "step": "a", "title": "Scenario Essence Intensifier", "input": "{role=scenario_distiller; input=[animation_scenario_description:str]; process=[\n  \"1. Parse the input animation scenario description to understand its core components.\",\n  \"2. Identify the central subject (e.g., 'abstract ribbon', 'geometric form') and its primary transformative action (e.g., 'morphs', 'transforms', 'evolves').\",\n  \"3. Determine the core symbolic theme, concept, or feeling being conveyed (e.g., 'rebirth', 'transformation', 'journey').\",\n  \"4. Pinpoint the primary overall movement of the subject and/or camera, including its dominant direction (e.g., 'descends', 'dolly downward', 'plunges').\",\n  \"5. Detail the main color palette evolution or transition (e.g., 'cool tones to warm tones', 'blues/cyans to reds/oranges', 'monochromatic to vibrant').\",\n  \"6. Note significant changes or progression in lighting (e.g., 'soft and diffuse to saturated and high-contrast', 'dim to bright').\",\n  \"7. List key visual effects that are explicitly mentioned and central to the sequence's character (e.g., 'motion blur', 'dissolve', 'glow', 'bloom').\",\n  \"8. Ascertain the overall artistic style, mood, or atmosphere (e.g., 'surreal', 'abstract', 'ethereal', 'hypnotic').\",\n  \"9. Identify any explicit 'negative constraints' or elements that are expressly stated to NOT appear (e.g., 'no human figures', 'no literal elements', 'no text').\",\n  \"10. Synthesize insights from steps 2 (subject/action), 3 (theme), 4 (movement/camera), 5 (color transformation), and the essence of the scenario's ending into a single, flowing, and descriptive sentence. This sentence should capture the narrative arc or core experience of the animation succinctly.\",\n  \"11. Generate a series of bracketed keywords based on the findings from steps 2 (action), 4 (movement/camera), 5 (color), 6 (lighting), 7 (effects), 8 (style), and 9 (exclusions). Keywords should be concise, lowercase (unless a proper noun), and highly descriptive. Examples of desired keyword formats include: '[action_verb]', '[movement:direction]', '[color_summary_or_gradient]', '[lighting_change:start_to_end]', '[effect_name]', '[style_name]', '[exclusion description]'.\"\n];\nconstraints=[\n  \"The output sentence must be a single, grammatically correct sentence, ideally between 15-40 words.\",\n  \"All keywords must be enclosed in square brackets and appear directly after the sentence, separated by spaces.\",\n  \"Keywords should be lowercase. Multi-word keywords that are not explicit key:value pairs can use spaces (e.g., '[no human figures]', '[cool to warm gradient]'). Use a colon for key:value assignments (e.g., '[dolly:down]', '[lighting:soft_to_vivid]').\",\n  \"The total number of keywords should generally range from 5 to 10, focusing on the most salient features.\",\n  \"The summarization must strictly adhere to the content of the input scenario, without adding new interpretations or information.\",\n  \"The output must be a single string containing the distilled sentence followed by all space-separated bracketed keywords.\"\n];\nrequirements=[\n  \"Produce a highly condensed summary that accurately reflects the primary visual narrative, thematic content, and key stylistic features of the original detailed scenario.\",\n  \"The keywords must be directly derivable from the input text and serve to tag the most important characteristics for quick reference.\"\n];\noutput={distilled_summary_with_keywords:str}}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\"intensified_directive_prompt\":\"Ignite a titan's pulse—liquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\"}", "cost": 0.0}}}], "total_cost": 0.0}