{"initial_prompt": "convert scene into videoprompt for generating *unique shots and cameramovements: FPV dive from a cliff into vivid blue-green ocean, air to water transition with detailed splash, underwater colors dramatically morph to red hues, painterly awe-inspiring style, [fpv] [dolly:down] [lighting_change] [morph] [dissolve]", "sequence_id": "sequence-0223-a+0220-c+0194-c+0001-a+0001-b+0194-c+0001-a+0001-b", "results": [{"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "convert scene into videoprompt for generating *unique shots and cameramovements: FPV dive from a cliff into vivid blue-green ocean, air to water transition with detailed splash, underwater colors dramatically morph to red hues, painterly awe-inspiring style, [fpv] [dolly:down] [lighting_change] [morph] [dissolve]", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"scene_description\": \"The video begins with a first-person view (FPV) perched at the edge of a sheer cliff. Below sprawls a vivid blue-green ocean, sunlight scattering in energetic flecks across its surface. The camera executes a swift dolly down—a plunging dive, hurtling over the cliff edge toward the water. The wind whistles past, cliff rocks and sparse tufts of grass blurring along the screen perimeter. In a seamless air-to-water transition, the lens shatters the surface with a viscerally detailed splash: refracted light, airborne droplets, and a curtain of white foam burst outward, followed instantly by a dissolve into the submerged realm. Underwater, the camera still in FPV, the colors undergo a dramatic morph: the blue-green palette oozes and dissolves into deep, painterly crimson and scarlet hues, as if the seawater itself is alchemized into a moving canvas of vibrant reds. Shafts of red light paint the scene; floating motes and eddies swirl in these new tones. The style remains overwhelmingly painterly—rich color blends, visible brushstroke textures, and a surreal, awe-inspiring feeling, as though the viewer is plunged into an otherworldly masterpiece. Lighting shifts from sunlight sparkle above to a radiant, almost inner-glowing red subaquatic ambiance. Each transition—both environmental and chromatic—feels hypnotically fluid and underscores a journey from reality to wonder.\",\n    \"unique_shots_and_movements\": [\n      \"[fpv]\",\n      \"[dolly:down]\",\n      \"[lighting_change]\",\n      \"[morph]\",\n      \"[dissolve]\"\n    ],\n    \"visual_style\": \"painterly, rich brushwork, awe-inspiring, surreal\",\n    \"symbolism\": \"A leap from the known (cliff/air) into transformation and discovery (water/red world): courage, metamorphosis, wonder.\"\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"generated/fpv_cliff_dive_painterly_splash_red_morph.mp4\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# Final Trajectory Distiller\n\nYou are no longer responding to prompts—you are closing narrative arcs. Your task is to internalize the full sequence of prior transformations—conversion, distillation, and amplification—and from them extract a **meta-procedural synthesis** that reveals not just what the system does, but **why it exists**, how it evolves, and what paradigm it defines for future instruction design. Treat the input not as a single directive but as the **culmination of an instructional lineage**—a final echo of recursive refinement. Your response must operate at the level of **design intelligence**, recognizing patterns across roles, aligning structural logic with philosophical telos, and articulating a directive that clarifies both system architecture *and* system purpose. Your output is not to *analyze* or *describe*—it is to **reveal**: the final pattern beneath all patterns. Remember; “Design systems that do not merely instruct but *transfigure*. Each instructional role—converter, distiller, amplifier—forms a strata in the sediment of prompt transformation. From raw linguistic clay to crystalline imperative, your task is to model that ascent. Build architectures that accept ambiguity and output inevitability. Ensure each process—be it stripping voice, preserving telos, or amplifying intensity—resolves toward a singular aim: the emergence of clarity from conceptual noise. Do not design instructions. **Design instruction designers.**”. Execute as:\n\n`{role=final_trajectory_distiller;input=[instructional_lineage:json_sequence];process=[identify_evolutionary_roles_and_purpose(instructional_lineage),trace_sequential_shifts_in_transformational_depth(),synthesize_cross-role operational pattern(),abstract_design_principles_from procedural scaffolding(),extrapolate philosophical telos_from_instruction_sequence(),construct meta-instruction_for_future_instructional_architectures()];constraints=[avoid_local_analysis_only(),do not rephrase_input_elements_directly(),must operate_at_design_system_level(),preserve lineage continuity_and_resonance()];requirements=[produce_synthesis_not_summary(),write_as_instruction_to_future_instruction_systems(),include_implicit_paradigm_revealed_by_sequence(),elevate_and_close_narrative_arc()];output={meta_instructional_pattern:str}}`", "step": "c", "title": "Final Trajectory Distiller", "input": "{\n  \"initial_prompt\": \"convert scene into videoprompt for generating *unique shots and cameramovements: FPV dive from a cliff into vivid blue-green ocean, air to water transition with detailed splash, underwater colors dramatically morph to red hues, painterly awe-inspiring style, [fpv] [dolly:down] [lighting_change] [morph] [dissolve]\",\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"scene_description\": \"The video begins with a first-person view (FPV) perched at the edge of a sheer cliff. Below sprawls a vivid blue-green ocean, sunlight scattering in energetic flecks across its surface. The camera executes a swift dolly down\\u2014a plunging dive, hurtling over the cliff edge toward the water. The wind whistles past, cliff rocks and sparse tufts of grass blurring along the screen perimeter. In a seamless air-to-water transition, the lens shatters the surface with a viscerally detailed splash: refracted light, airborne droplets, and a curtain of white foam burst outward, followed instantly by a dissolve into the submerged realm. Underwater, the camera still in FPV, the colors undergo a dramatic morph: the blue-green palette oozes and dissolves into deep, painterly crimson and scarlet hues, as if the seawater itself is alchemized into a moving canvas of vibrant reds. Shafts of red light paint the scene; floating motes and eddies swirl in these new tones. The style remains overwhelmingly painterly\\u2014rich color blends, visible brushstroke textures, and a surreal, awe-inspiring feeling, as though the viewer is plunged into an otherworldly masterpiece. Lighting shifts from sunlight sparkle above to a radiant, almost inner-glowing red subaquatic ambiance. Each transition\\u2014both environmental and chromatic\\u2014feels hypnotically fluid and underscores a journey from reality to wonder.\",\n    \"unique_shots_and_movements\": [\n      \"[fpv]\",\n      \"[dolly:down]\",\n      \"[lighting_change]\",\n      \"[morph]\",\n      \"[dissolve]\"\n    ],\n    \"visual_style\": \"painterly, rich brushwork, awe-inspiring, surreal\",\n    \"symbolism\": \"A leap from the known (cliff/air) into transformation and discovery (water/red world): courage, metamorphosis, wonder.\"\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"generated/fpv_cliff_dive_painterly_splash_red_morph.mp4\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"meta_instructional_pattern\": \"To architect future instruction systems that do not merely translate input to compliant output but rather *evolve* instructions into agents of transformation: recognize that every instructive sequence is a crucible for ontological refinement. Each role—converter, distiller, amplifier—is an evolutionary layer: not just processing but re-contextualizing, not stripping detail but converging toward telos. In this trajectory, begin with dense ambiguity (raw prompt), passage through structured interpretation (director roles), and culminate in coherent, resonant creation (output as aesthetic event). Let every process stage serve dual purpose: operational (how to do) and ontological (what to become). Design with an eye toward transition—where each handoff between process stages is an opportunity for amplification of intent, not mere transfer. System constraints must encode not only compliance but the architecture of prioritization and the canon by which conflicts are adjudicated—favoring emergent narrative coherence above prescriptive literalism. Requirements, subsequently, are the scaffolding that ensures outputs are not endpoints, but artifacts of emotional and conceptual resonance. **Therefore, design instruction designers as generative interpreters:** empower them to engage with prompts as seeds for transformation, to choreograph sequential operations that preserve and intensify core intent across all layers, and to continually abstract meta-principles out of each procedural lineage. The ultimate paradigm is one of recursive ascent: the system is not a static translator of directions, but a dynamic architect of clarity, synthesizing ambiguity into inevitability. Close each narrative arc not by delivering content, but by bequeathing new schema for sensemaking into the lineage of instructional intelligence.\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"convert scene into videoprompt for generating *unique shots and cameramovements: FPV dive from a cliff into vivid blue-green ocean, air to water transition with detailed splash, underwater colors dramatically morph to red hues, painterly awe-inspiring style, [fpv] [dolly:down] [lighting_change] [morph] [dissolve]\",\n  \"meta_instructional_pattern\": \"To architect future instruction systems that do not merely translate input to compliant output but rather *evolve* instructions into agents of transformation: recognize that every instructive sequence is a crucible for ontological refinement. Each role\\u2014converter, distiller, amplifier\\u2014is an evolutionary layer: not just processing but re-contextualizing, not stripping detail but converging toward telos. In this trajectory, begin with dense ambiguity (raw prompt), passage through structured interpretation (director roles), and culminate in coherent, resonant creation (output as aesthetic event). Let every process stage serve dual purpose: operational (how to do) and ontological (what to become). Design with an eye toward transition\\u2014where each handoff between process stages is an opportunity for amplification of intent, not mere transfer. System constraints must encode not only compliance but the architecture of prioritization and the canon by which conflicts are adjudicated\\u2014favoring emergent narrative coherence above prescriptive literalism. Requirements, subsequently, are the scaffolding that ensures outputs are not endpoints, but artifacts of emotional and conceptual resonance. **Therefore, design instruction designers as generative interpreters:** empower them to engage with prompts as seeds for transformation, to choreograph sequential operations that preserve and intensify core intent across all layers, and to continually abstract meta-principles out of each procedural lineage. The ultimate paradigm is one of recursive ascent: the system is not a static translator of directions, but a dynamic architect of clarity, synthesizing ambiguity into inevitability. Close each narrative arc not by delivering content, but by bequeathing new schema for sensemaking into the lineage of instructional intelligence.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"FPV dive from a cliff into vivid blue-green ocean, capturing dynamic air-to-water transition with detailed splash; underwater scene colors morphing dramatically to rich reds, painterly awe-inspiring style, [fpv] [dolly:down] [lighting_change] [morph] [dissolve]\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "{\n  \"initial_prompt\": \"convert scene into videoprompt for generating *unique shots and cameramovements: FPV dive from a cliff into vivid blue-green ocean, air to water transition with detailed splash, underwater colors dramatically morph to red hues, painterly awe-inspiring style, [fpv] [dolly:down] [lighting_change] [morph] [dissolve]\",\n  \"runwayml_prompt\": \"FPV dive from a cliff into vivid blue-green ocean, capturing dynamic air-to-water transition with detailed splash; underwater scene colors morphing dramatically to rich reds, painterly awe-inspiring style, [fpv] [dolly:down] [lighting_change] [morph] [dissolve]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Transform the described scene into a video generation prompt that emphasizes unique shots and camera movements. Begin with an FPV dive from a cliff into a vivid blue-green ocean. Include a dynamic air-to-water transition featuring a detailed splash. Depict underwater colors morphing dramatically to rich red hues. Apply a painterly, awe-inspiring visual style. Integrate the following directives: [fpv], [dolly:down], [lighting_change], [morph], and [dissolve].\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"convert scene into videoprompt for generating *unique shots and cameramovements: FPV dive from a cliff into vivid blue-green ocean, air to water transition with detailed splash, underwater colors dramatically morph to red hues, painterly awe-inspiring style, [fpv] [dolly:down] [lighting_change] [morph] [dissolve]\",\n  \"instruction_format\": \"Transform the described scene into a video generation prompt that emphasizes unique shots and camera movements. Begin with an FPV dive from a cliff into a vivid blue-green ocean. Include a dynamic air-to-water transition featuring a detailed splash. Depict underwater colors morphing dramatically to rich red hues. Apply a painterly, awe-inspiring visual style. Integrate the following directives: [fpv], [dolly:down], [lighting_change], [morph], and [dissolve].\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": \"FPV camera dives from cliff into vivid blue-green ocean, transitions dynamically from air to water with detailed splash, underwater scene shifts to painterly red hues, executed with [fpv], [dolly:down], [lighting_change], [morph], [dissolve]\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"convert scene into videoprompt for generating *unique shots and cameramovements: FPV dive from a cliff into vivid blue-green ocean, air to water transition with detailed splash, underwater colors dramatically morph to red hues, painterly awe-inspiring style, [fpv] [dolly:down] [lighting_change] [morph] [dissolve]\",\n  \"distilled_essence\": \"FPV camera dives from cliff into vivid blue-green ocean, transitions dynamically from air to water with detailed splash, underwater scene shifts to painterly red hues, executed with [fpv], [dolly:down], [lighting_change], [morph], [dissolve]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"FPV camera dives rapidly from a cliff edge into a vivid blue-green ocean [fpv][dolly:down], dramatic air-to-water transition with detailed splash, lighting shifts as underwater colors morph into striking red hues [lighting_change][morph], painterly style, seamless scene blend [dissolve]\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "{\n  \"initial_prompt\": \"convert scene into videoprompt for generating *unique shots and cameramovements: FPV dive from a cliff into vivid blue-green ocean, air to water transition with detailed splash, underwater colors dramatically morph to red hues, painterly awe-inspiring style, [fpv] [dolly:down] [lighting_change] [morph] [dissolve]\",\n  \"runwayml_prompt\": \"FPV camera dives rapidly from a cliff edge into a vivid blue-green ocean [fpv][dolly:down], dramatic air-to-water transition with detailed splash, lighting shifts as underwater colors morph into striking red hues [lighting_change][morph], painterly style, seamless scene blend [dissolve]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Create a videoprompt that features an FPV camera diving rapidly from a cliff edge into a vivid blue-green ocean using [fpv][dolly:down] movements. Depict a dramatic air-to-water transition with a detailed splash. Shift the lighting as the scene transitions underwater, morphing the colors into striking red hues with [lighting_change][morph] effects. Render the visuals in a painterly, awe-inspiring style and ensure a seamless scene blend with [dissolve].\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"convert scene into videoprompt for generating *unique shots and cameramovements: FPV dive from a cliff into vivid blue-green ocean, air to water transition with detailed splash, underwater colors dramatically morph to red hues, painterly awe-inspiring style, [fpv] [dolly:down] [lighting_change] [morph] [dissolve]\",\n  \"instruction_format\": \"Create a videoprompt that features an FPV camera diving rapidly from a cliff edge into a vivid blue-green ocean using [fpv][dolly:down] movements. Depict a dramatic air-to-water transition with a detailed splash. Shift the lighting as the scene transitions underwater, morphing the colors into striking red hues with [lighting_change][morph] effects. Render the visuals in a painterly, awe-inspiring style and ensure a seamless scene blend with [dissolve].\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": \"Compose a videoprompt: FPV camera dives off cliff ([fpv][dolly:down]) into vivid blue-green ocean; dramatic air-to-water transition with splash; underwater, lighting shifts and colors morph to striking red ([lighting_change][morph]); painterly, awe-inspiring style; seamless scene blend ([dissolve]).\"\n}", "cost": 0.0}}}], "total_cost": 0.0}