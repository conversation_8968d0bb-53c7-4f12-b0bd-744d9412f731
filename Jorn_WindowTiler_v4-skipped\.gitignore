
# =======================================================
# DIRNAMES
# =======================================================
# dirs: python
**/.cache/
**/.env/
**/.pytest_cache/
**/.venv/
**/__pycache__/
**/env/
**/venv/
# dirs: react
**/node_modules/
# dirs: logs and temp
**/build/
**/cache/
**/dist/
**/logs/
**/temp/
**/tmp/

# =======================================================
# EXTENSIONS
# =======================================================
# extensions: media
*.mp4
*.mkv
*.webm
*.mp3
*.wav
# extensions: unsorted
*.bin
*.blend1
*.dll
*.DS_Store
*.exe
*.ini.bak
*.ldb
*.log
*.pak
*.pickle
*.png
*.prv.ppk
*.prv.pub
*.pyc
*.pyo
*.swp
*.tmp

# =======================================================
# -- OVERRIDES --
# =======================================================

# filenames: unsorted
**/.what-is-this.md
**/app.log.yml
**/quit.blend
**/Run History-1.5a.csv
**/Search History-1.5a.csv
**/Session-1.5a.backup.json
**/Session-1.5a.json

# dirs
# **/.backups/
# **/.specstory/
# **/__meta__/

# types
*.sublime-workspace
*.sublime_session

# paths: files
**/*sync-conflict*.*
**/user-data/**/ui_messages.json
**/.specstory/history/.what-is-this.md