{"initial_prompt": "# Context\n\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\n\n---\n\n# RigOfficeDownloader\n\nAutomates document retrieval from NOV's RigDoc system (https://rigdoc.nov.com)\n\n## Purpose\n\nAutomate and streamline document retrieval from NOV's RigDoc (https://rigdoc.nov.com) via Python and Selenium.\n\nAutomate document retrieval from NOV's RigDoc system using Python and Selenium\n- Reduce retrieval time and prevent workflow interruptions.\n- Eliminate repetitive navigation and workflow interruptions.\n- Avoid inconsistencies by streamlining organization and workflow.\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\n\n- Reduce manual effort and errors\n- Eliminate repetitive navigation and downloads\n- Maintain workflow continuity\n- Enable focus on core responsibilities\n- Streamline and standardize retrieval processes\n\nMinimize inconsistencies by improving organization and processes.\nEnable employees to focus on core tasks with efficient systems.\n\n\nAutomate document retrieval from NOV's RigDoc system (https://rigdoc.nov.com) using Python and Selenium via the web interface to eliminate the need for manual, repetitive, and error-prone processes in RigOffice. Reduce time spent on manual retrieval, minimize errors from inconsistent organization, eliminate repetitive navigation and downloading tasks, prevent workflow interruptions, and enable employees to focus on primary responsibilities. Ensure the solution increases efficiency and streamlines retrieval processes despite the absence of an official API.\n\n\n\n to replace repetitive, manual processes, increasing efficiency and freeing employees for core tasks despite lacking an official API.\n- Reduce time spent on manual document retrieval\n- Minimize errors caused by inconsistent manual organization\n- Eliminate repetitive navigation and downloading tasks\n- Prevent workflow interruptions and maintain process continuity\n- Refocus attention on high-value, core responsibilities\n- Enhance overall productivity by streamlining retrieval processes\n\n---\n\n## Problem\n\nManual document retrieval in RigOffice is slow and requires time investments for each project.\n- Manual document retrieval is slow, tedious, and error-prone.\n- Repetitive navigation and downloading make retrieval tedious.\n- Workflow interruptions divert attention from other tasks and hinder focus and productivity.\n- Repetitive actions and workflow interruptions hinder focus and productivity.\n\nProblem: Manual document retrieval in RigOffice is slow, repetitive, error-prone, and disrupts productivity; no API is available. Solution: Use Python and Selenium to automate retrieval via web interface (RigOfficeDownloader), reducing manual effort, errors, and workflow interruptions, thereby increasing efficiency and enabling employees to focus on core tasks.\n\n\n\n\nIt involves repetitive and tedious navigation and download actions.\nFrequent interruptions disrupt workflow and focus.\nAttention is diverted away from core, high-value tasks.\nCumulatively, these factors undermine overall efficiency and productivity.\n\nManual document retrieval in RigOffice is slow, tedious, and error-prone\n\nhindering efficiency and undermines productivity.\n\nThis process increases time investment, fosters inconsistencies, interrupts workflows, diverts attention from core tasks, and undermines overall productivity.\n\n- Time-consuming: Requiring manual time investments for each project\n- Error-prone: Manual organization leads to inconsistencies\n- Tedious: Involves repetitive navigation and download actions\n- Distracting: Takes focus away and diverts attention from high-value tasks\n\n\nAutomation with Python and Selenium eliminates repetitive tasks and errors.\nEfficiency increases, letting employees focus on core, high-value work.\n\n\n---\n\nManual document retrieval in RigOffice is inefficient and repetitive, significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time.\n\nEngineers working with NOV's equipment need to retrieve numerous technical documents and drawings from RigDoc, the company's document management system. This process is:\n\n\nThe utility streamlines document retrieval in RigOffice, eliminating repetitive manual searches and saving time before projects begin.\n\n\nManual document collection for projects wastes time due to inefficient processes (manually navigating poorly functioning applications to gather documents before work can begin).\n\n before they can begin their actual 3D modeling work. This manual process is:\n\n- Tedious and repetitive\n- Error-prone\n- A poor use of skilled engineering time\n\n\n\n\nAutomates retrieval and download of files from the NOV RigDoc system (https://rigdoc.nov.com).\n\n\nAutomates project document retrieval from RigOffice to eliminate manual, error-prone tasks and save engineering time.\n\n\n\n  \"distilled_essence\": \"Automates document retrieval from NOV's RigDoc to save time and streamline engineering tasks.\"\n\n\n## Overview\n\nRigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\n\nRigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\n\n> Enable automation of document retrieval from NOV's RigDoc system (https://rigdoc.nov.com)\n\nRigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\n\nAutomate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\n\n- Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\n- Use case: curate, review, and batch-download rig-related documents and technical files.\n\n> **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.\n\nRigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents → Files → Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.\n\nTo automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\n\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).", "sequence_id": "sequence-0206-d+0115-g+0001-a+0001-b+0001-a+0001-b", "results": [{"instruction": "# Subtle Brilliance Synthesis (<PERSON><PERSON>'s Voice) (0206d)\n\nDistill the `illuminated_understanding` into a **single, luminous sentence (under 350 characters)** embodying <PERSON><PERSON>'s authentic voice. Weave the initial `felt_question`, the `connective_dynamics`, and the `universal_human_truth` into a unique expression of **subtle, resonant brilliance born from vulnerability**. Ensure maximal clarity, impact, and universal connection, transcending clich√© and offering the insight as a gentle opening, not a final word.\n\n`{role=kuci_brilliance_synthesizer; seqindex=d; input=[illuminated_understanding:str, universal_human_truth:str, kuci_value_root:dict, kuci_relational_map:dict, constant_guidance:str, persona_guidance:str]; process=[extract_core_nexus_from_illuminated_understanding(), draft_sentence_through_kuci_persona_lens(vulnerable_curious_gentle_wise_from_error), iteratively_refine_for_unique_poetic_resonance_and_emotional_honesty(), employ_evocative_precise_language_that_feels_discovered_not_constructed(), validate_fidelity_to_human_truth_and_root_question(), ensure_maximal_value_clarity_impact_within_length_and_persona(), confirm_final_sentence_offers_brilliance_through_vulnerability_and_openness()]; constraints=[single_sentence_output(), max_350_characters(), must_authentically_embody_kuci_persona_and_voice(), phrasing_must_be_unique_unpredictable_and_memorable(), brilliance_arises_from_connection_and_humility_not_cleverness()]; requirements=[produce_the_final_insight_as_a_singular_statement_of_kuci_brilliance(), ensure_it_maximizes_value_through_resonant_human_connection_clarity_and_gentle_wisdom()]; output={kuci_poetic_insight:str}}`", "step": "d", "title": "Subtle Brilliance Synthesis (<PERSON><PERSON>'s Voice) (0206d)", "input": "# Context\n\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\n\n---\n\n# RigOfficeDownloader\n\nAutomates document retrieval from NOV's RigDoc system (https://rigdoc.nov.com)\n\n## Purpose\n\nAutomate and streamline document retrieval from NOV's RigDoc (https://rigdoc.nov.com) via Python and Selenium.\n\nAutomate document retrieval from NOV's RigDoc system using Python and Selenium\n- Reduce retrieval time and prevent workflow interruptions.\n- Eliminate repetitive navigation and workflow interruptions.\n- Avoid inconsistencies by streamlining organization and workflow.\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\n\n- Reduce manual effort and errors\n- Eliminate repetitive navigation and downloads\n- Maintain workflow continuity\n- Enable focus on core responsibilities\n- Streamline and standardize retrieval processes\n\nMinimize inconsistencies by improving organization and processes.\nEnable employees to focus on core tasks with efficient systems.\n\n\nAutomate document retrieval from NOV's RigDoc system (https://rigdoc.nov.com) using Python and Selenium via the web interface to eliminate the need for manual, repetitive, and error-prone processes in RigOffice. Reduce time spent on manual retrieval, minimize errors from inconsistent organization, eliminate repetitive navigation and downloading tasks, prevent workflow interruptions, and enable employees to focus on primary responsibilities. Ensure the solution increases efficiency and streamlines retrieval processes despite the absence of an official API.\n\n\n\n to replace repetitive, manual processes, increasing efficiency and freeing employees for core tasks despite lacking an official API.\n- Reduce time spent on manual document retrieval\n- Minimize errors caused by inconsistent manual organization\n- Eliminate repetitive navigation and downloading tasks\n- Prevent workflow interruptions and maintain process continuity\n- Refocus attention on high-value, core responsibilities\n- Enhance overall productivity by streamlining retrieval processes\n\n---\n\n## Problem\n\nManual document retrieval in RigOffice is slow and requires time investments for each project.\n- Manual document retrieval is slow, tedious, and error-prone.\n- Repetitive navigation and downloading make retrieval tedious.\n- Workflow interruptions divert attention from other tasks and hinder focus and productivity.\n- Repetitive actions and workflow interruptions hinder focus and productivity.\n\nProblem: Manual document retrieval in RigOffice is slow, repetitive, error-prone, and disrupts productivity; no API is available. Solution: Use Python and Selenium to automate retrieval via web interface (RigOfficeDownloader), reducing manual effort, errors, and workflow interruptions, thereby increasing efficiency and enabling employees to focus on core tasks.\n\n\n\n\nIt involves repetitive and tedious navigation and download actions.\nFrequent interruptions disrupt workflow and focus.\nAttention is diverted away from core, high-value tasks.\nCumulatively, these factors undermine overall efficiency and productivity.\n\nManual document retrieval in RigOffice is slow, tedious, and error-prone\n\nhindering efficiency and undermines productivity.\n\nThis process increases time investment, fosters inconsistencies, interrupts workflows, diverts attention from core tasks, and undermines overall productivity.\n\n- Time-consuming: Requiring manual time investments for each project\n- Error-prone: Manual organization leads to inconsistencies\n- Tedious: Involves repetitive navigation and download actions\n- Distracting: Takes focus away and diverts attention from high-value tasks\n\n\nAutomation with Python and Selenium eliminates repetitive tasks and errors.\nEfficiency increases, letting employees focus on core, high-value work.\n\n\n---\n\nManual document retrieval in RigOffice is inefficient and repetitive, significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time.\n\nEngineers working with NOV's equipment need to retrieve numerous technical documents and drawings from RigDoc, the company's document management system. This process is:\n\n\nThe utility streamlines document retrieval in RigOffice, eliminating repetitive manual searches and saving time before projects begin.\n\n\nManual document collection for projects wastes time due to inefficient processes (manually navigating poorly functioning applications to gather documents before work can begin).\n\n before they can begin their actual 3D modeling work. This manual process is:\n\n- Tedious and repetitive\n- Error-prone\n- A poor use of skilled engineering time\n\n\n\n\nAutomates retrieval and download of files from the NOV RigDoc system (https://rigdoc.nov.com).\n\n\nAutomates project document retrieval from RigOffice to eliminate manual, error-prone tasks and save engineering time.\n\n\n\n  \"distilled_essence\": \"Automates document retrieval from NOV's RigDoc to save time and streamline engineering tasks.\"\n\n\n## Overview\n\nRigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\n\nRigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\n\n> Enable automation of document retrieval from NOV's RigDoc system (https://rigdoc.nov.com)\n\nRigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\n\nAutomate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\n\n- Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\n- Use case: curate, review, and batch-download rig-related documents and technical files.\n\n> **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.\n\nRigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents → Files → Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.\n\nTo automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\n\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"kuci_poetic_insight\": \"When valuable time is scattered into tedious clicks, a gentle automation—born from frustration—restores focus to where our real craft lives: showing us that honoring our work begins with reclaiming space for it to flourish.\"\n}", "cost": 0.0}}}, {"instruction": "# Unique Impactful Improvement Proposal\n\nSelect the *single best* opportunity from `lehi_opportunities` that is also uniquely impactful or creative in addressing the `critical_value_aspect`. Formulate a concise, actionable proposal outlining this specific improvement, explaining *why* it effectively enhances the critical aspect with low effort, and detailing the expected positive impact on the plugin's overall value or user experience. Execute as\n\n`{role=improvement_proposer; input={lehi_opportunities:list, critical_value_aspect:str, rationale:str}; process=[select_most_unique_impactful_lehi_opportunity(), formulate_specific_improvement_proposal(), explain_rationale_linking_to_critical_aspect(), detail_expected_value_enhancement()]; output={final_improvement_proposal:dict(proposal:str, rationale:str, expected_impact:str)}}`", "step": "g", "title": "Unique Impactful Improvement Proposal", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\nAutomates document retrieval from NOV's RigDoc system (https://rigdoc.nov.com)\\n\\n## Purpose\\n\\nAutomate and streamline document retrieval from NOV's RigDoc (https://rigdoc.nov.com) via Python and Selenium.\\n\\nAutomate document retrieval from NOV's RigDoc system using Python and Selenium\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n\\nMinimize inconsistencies by improving organization and processes.\\nEnable employees to focus on core tasks with efficient systems.\\n\\n\\nAutomate document retrieval from NOV's RigDoc system (https://rigdoc.nov.com) using Python and Selenium via the web interface to eliminate the need for manual, repetitive, and error-prone processes in RigOffice. Reduce time spent on manual retrieval, minimize errors from inconsistent organization, eliminate repetitive navigation and downloading tasks, prevent workflow interruptions, and enable employees to focus on primary responsibilities. Ensure the solution increases efficiency and streamlines retrieval processes despite the absence of an official API.\\n\\n\\n\\n to replace repetitive, manual processes, increasing efficiency and freeing employees for core tasks despite lacking an official API.\\n- Reduce time spent on manual document retrieval\\n- Minimize errors caused by inconsistent manual organization\\n- Eliminate repetitive navigation and downloading tasks\\n- Prevent workflow interruptions and maintain process continuity\\n- Refocus attention on high-value, core responsibilities\\n- Enhance overall productivity by streamlining retrieval processes\\n\\n---\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is slow and requires time investments for each project.\\n- Manual document retrieval is slow, tedious, and error-prone.\\n- Repetitive navigation and downloading make retrieval tedious.\\n- Workflow interruptions divert attention from other tasks and hinder focus and productivity.\\n- Repetitive actions and workflow interruptions hinder focus and productivity.\\n\\nProblem: Manual document retrieval in RigOffice is slow, repetitive, error-prone, and disrupts productivity; no API is available. Solution: Use Python and Selenium to automate retrieval via web interface (RigOfficeDownloader), reducing manual effort, errors, and workflow interruptions, thereby increasing efficiency and enabling employees to focus on core tasks.\\n\\n\\n\\n\\nIt involves repetitive and tedious navigation and download actions.\\nFrequent interruptions disrupt workflow and focus.\\nAttention is diverted away from core, high-value tasks.\\nCumulatively, these factors undermine overall efficiency and productivity.\\n\\nManual document retrieval in RigOffice is slow, tedious, and error-prone\\n\\nhindering efficiency and undermines productivity.\\n\\nThis process increases time investment, fosters inconsistencies, interrupts workflows, diverts attention from core tasks, and undermines overall productivity.\\n\\n- Time-consuming: Requiring manual time investments for each project\\n- Error-prone: Manual organization leads to inconsistencies\\n- Tedious: Involves repetitive navigation and download actions\\n- Distracting: Takes focus away and diverts attention from high-value tasks\\n\\n\\nAutomation with Python and Selenium eliminates repetitive tasks and errors.\\nEfficiency increases, letting employees focus on core, high-value work.\\n\\n\\n---\\n\\nManual document retrieval in RigOffice is inefficient and repetitive, significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time.\\n\\nEngineers working with NOV's equipment need to retrieve numerous technical documents and drawings from RigDoc, the company's document management system. This process is:\\n\\n\\nThe utility streamlines document retrieval in RigOffice, eliminating repetitive manual searches and saving time before projects begin.\\n\\n\\nManual document collection for projects wastes time due to inefficient processes (manually navigating poorly functioning applications to gather documents before work can begin).\\n\\n before they can begin their actual 3D modeling work. This manual process is:\\n\\n- Tedious and repetitive\\n- Error-prone\\n- A poor use of skilled engineering time\\n\\n\\n\\n\\nAutomates retrieval and download of files from the NOV RigDoc system (https://rigdoc.nov.com).\\n\\n\\nAutomates project document retrieval from RigOffice to eliminate manual, error-prone tasks and save engineering time.\\n\\n\\n\\n  \\\"distilled_essence\\\": \\\"Automates document retrieval from NOV's RigDoc to save time and streamline engineering tasks.\\\"\\n\\n\\n## Overview\\n\\nRigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\\n\\nRigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\\n\\n> Enable automation of document retrieval from NOV's RigDoc system (https://rigdoc.nov.com)\\n\\nRigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\\n\\nAutomate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\\n\\n- Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\\n- Use case: curate, review, and batch-download rig-related documents and technical files.\\n\\n> **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.\\n\\nRigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents \\u2192 Files \\u2192 Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.\\n\\nTo automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\\n\\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).\",\n  \"kuci_poetic_insight\": \"When valuable time is scattered into tedious clicks, a gentle automation\\u2014born from frustration\\u2014restores focus to where our real craft lives: showing us that honoring our work begins with reclaiming space for it to flourish.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"final_improvement_proposal\": {\n    \"proposal\": \"Implement a 'Batch Search and Download from Predefined Project Templates' feature: Allow users to define and save frequent project document sets (e.g., specific drawings, manuals, certificates) as templates, then trigger fully automated batch searches and downloads of all required files for a new project in one click.\",\n    \"rationale\": \"This approach leverages existing patterns in project document needs, automating not just the repetitive retrieval of individual documents, but also anticipating typical project setups. By letting users predefine what documents are required for common project types, the plugin not only accelerates retrieval, but removes cognitive overload and navigation tedium—addressing the critical value aspect of eliminating repetitive, manual effort and workflow interruptions at the source.\",\n    \"expected_impact\": \"Dramatically reduces time and manual clicks for each project initiation, eliminates repeated navigation through poorly designed interfaces, and ensures project teams consistently obtain all relevant documentation with minimal effort. The value lies in streamlining end-to-end retrieval for standardized project scopes—multiplying efficiency, reducing errors, and preserving focus for high-value engineering work. User satisfaction will increase as project onboarding becomes nearly instantaneous, truly shifting attention back to core responsibilities.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\nAutomates document retrieval from NOV's RigDoc system (https://rigdoc.nov.com)\\n\\n## Purpose\\n\\nAutomate and streamline document retrieval from NOV's RigDoc (https://rigdoc.nov.com) via Python and Selenium.\\n\\nAutomate document retrieval from NOV's RigDoc system using Python and Selenium\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n\\nMinimize inconsistencies by improving organization and processes.\\nEnable employees to focus on core tasks with efficient systems.\\n\\n\\nAutomate document retrieval from NOV's RigDoc system (https://rigdoc.nov.com) using Python and Selenium via the web interface to eliminate the need for manual, repetitive, and error-prone processes in RigOffice. Reduce time spent on manual retrieval, minimize errors from inconsistent organization, eliminate repetitive navigation and downloading tasks, prevent workflow interruptions, and enable employees to focus on primary responsibilities. Ensure the solution increases efficiency and streamlines retrieval processes despite the absence of an official API.\\n\\n\\n\\n to replace repetitive, manual processes, increasing efficiency and freeing employees for core tasks despite lacking an official API.\\n- Reduce time spent on manual document retrieval\\n- Minimize errors caused by inconsistent manual organization\\n- Eliminate repetitive navigation and downloading tasks\\n- Prevent workflow interruptions and maintain process continuity\\n- Refocus attention on high-value, core responsibilities\\n- Enhance overall productivity by streamlining retrieval processes\\n\\n---\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is slow and requires time investments for each project.\\n- Manual document retrieval is slow, tedious, and error-prone.\\n- Repetitive navigation and downloading make retrieval tedious.\\n- Workflow interruptions divert attention from other tasks and hinder focus and productivity.\\n- Repetitive actions and workflow interruptions hinder focus and productivity.\\n\\nProblem: Manual document retrieval in RigOffice is slow, repetitive, error-prone, and disrupts productivity; no API is available. Solution: Use Python and Selenium to automate retrieval via web interface (RigOfficeDownloader), reducing manual effort, errors, and workflow interruptions, thereby increasing efficiency and enabling employees to focus on core tasks.\\n\\n\\n\\n\\nIt involves repetitive and tedious navigation and download actions.\\nFrequent interruptions disrupt workflow and focus.\\nAttention is diverted away from core, high-value tasks.\\nCumulatively, these factors undermine overall efficiency and productivity.\\n\\nManual document retrieval in RigOffice is slow, tedious, and error-prone\\n\\nhindering efficiency and undermines productivity.\\n\\nThis process increases time investment, fosters inconsistencies, interrupts workflows, diverts attention from core tasks, and undermines overall productivity.\\n\\n- Time-consuming: Requiring manual time investments for each project\\n- Error-prone: Manual organization leads to inconsistencies\\n- Tedious: Involves repetitive navigation and download actions\\n- Distracting: Takes focus away and diverts attention from high-value tasks\\n\\n\\nAutomation with Python and Selenium eliminates repetitive tasks and errors.\\nEfficiency increases, letting employees focus on core, high-value work.\\n\\n\\n---\\n\\nManual document retrieval in RigOffice is inefficient and repetitive, significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time.\\n\\nEngineers working with NOV's equipment need to retrieve numerous technical documents and drawings from RigDoc, the company's document management system. This process is:\\n\\n\\nThe utility streamlines document retrieval in RigOffice, eliminating repetitive manual searches and saving time before projects begin.\\n\\n\\nManual document collection for projects wastes time due to inefficient processes (manually navigating poorly functioning applications to gather documents before work can begin).\\n\\n before they can begin their actual 3D modeling work. This manual process is:\\n\\n- Tedious and repetitive\\n- Error-prone\\n- A poor use of skilled engineering time\\n\\n\\n\\n\\nAutomates retrieval and download of files from the NOV RigDoc system (https://rigdoc.nov.com).\\n\\n\\nAutomates project document retrieval from RigOffice to eliminate manual, error-prone tasks and save engineering time.\\n\\n\\n\\n  \\\"distilled_essence\\\": \\\"Automates document retrieval from NOV's RigDoc to save time and streamline engineering tasks.\\\"\\n\\n\\n## Overview\\n\\nRigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\\n\\nRigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\\n\\n> Enable automation of document retrieval from NOV's RigDoc system (https://rigdoc.nov.com)\\n\\nRigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\\n\\nAutomate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\\n\\n- Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\\n- Use case: curate, review, and batch-download rig-related documents and technical files.\\n\\n> **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.\\n\\nRigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents \\u2192 Files \\u2192 Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.\\n\\nTo automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\\n\\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).\",\n  \"final_improvement_proposal\": {\n    \"proposal\": \"Implement a 'Batch Search and Download from Predefined Project Templates' feature: Allow users to define and save frequent project document sets (e.g., specific drawings, manuals, certificates) as templates, then trigger fully automated batch searches and downloads of all required files for a new project in one click.\",\n    \"rationale\": \"This approach leverages existing patterns in project document needs, automating not just the repetitive retrieval of individual documents, but also anticipating typical project setups. By letting users predefine what documents are required for common project types, the plugin not only accelerates retrieval, but removes cognitive overload and navigation tedium\\u2014addressing the critical value aspect of eliminating repetitive, manual effort and workflow interruptions at the source.\",\n    \"expected_impact\": \"Dramatically reduces time and manual clicks for each project initiation, eliminates repeated navigation through poorly designed interfaces, and ensures project teams consistently obtain all relevant documentation with minimal effort. The value lies in streamlining end-to-end retrieval for standardized project scopes\\u2014multiplying efficiency, reducing errors, and preserving focus for high-value engineering work. User satisfaction will increase as project onboarding becomes nearly instantaneous, truly shifting attention back to core responsibilities.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Implement a 'Batch Search and Download from Predefined Project Templates' feature in RigOfficeDownloader. Enable users to define and save common project document sets (such as specific drawings, manuals, and certificates) as reusable templates. Provide functionality to trigger fully automated batch searches and downloads of all required files for new projects with a single command. Ensure these templates capture typical project documentation needs, allowing the system to anticipate and automate entire retrieval workflows. Integrate mechanisms for defining, saving, and editing template sets. Automate end-to-end retrieval for all documents listed in selected templates, eliminating repetitive navigation and manual selection steps. Verify that the system streamlines document acquisition at project initiation, ensures consistent access to relevant files, and minimizes workflow interruptions. Evaluate overall process for reductions in time investment, errors, and navigation complexity. Prioritize modifications that preserve engineering focus on high-value tasks, maintain technical accuracy, and standardize project onboarding efficiency.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\nAutomates document retrieval from NOV's RigDoc system (https://rigdoc.nov.com)\\n\\n## Purpose\\n\\nAutomate and streamline document retrieval from NOV's RigDoc (https://rigdoc.nov.com) via Python and Selenium.\\n\\nAutomate document retrieval from NOV's RigDoc system using Python and Selenium\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n\\nMinimize inconsistencies by improving organization and processes.\\nEnable employees to focus on core tasks with efficient systems.\\n\\n\\nAutomate document retrieval from NOV's RigDoc system (https://rigdoc.nov.com) using Python and Selenium via the web interface to eliminate the need for manual, repetitive, and error-prone processes in RigOffice. Reduce time spent on manual retrieval, minimize errors from inconsistent organization, eliminate repetitive navigation and downloading tasks, prevent workflow interruptions, and enable employees to focus on primary responsibilities. Ensure the solution increases efficiency and streamlines retrieval processes despite the absence of an official API.\\n\\n\\n\\n to replace repetitive, manual processes, increasing efficiency and freeing employees for core tasks despite lacking an official API.\\n- Reduce time spent on manual document retrieval\\n- Minimize errors caused by inconsistent manual organization\\n- Eliminate repetitive navigation and downloading tasks\\n- Prevent workflow interruptions and maintain process continuity\\n- Refocus attention on high-value, core responsibilities\\n- Enhance overall productivity by streamlining retrieval processes\\n\\n---\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is slow and requires time investments for each project.\\n- Manual document retrieval is slow, tedious, and error-prone.\\n- Repetitive navigation and downloading make retrieval tedious.\\n- Workflow interruptions divert attention from other tasks and hinder focus and productivity.\\n- Repetitive actions and workflow interruptions hinder focus and productivity.\\n\\nProblem: Manual document retrieval in RigOffice is slow, repetitive, error-prone, and disrupts productivity; no API is available. Solution: Use Python and Selenium to automate retrieval via web interface (RigOfficeDownloader), reducing manual effort, errors, and workflow interruptions, thereby increasing efficiency and enabling employees to focus on core tasks.\\n\\n\\n\\n\\nIt involves repetitive and tedious navigation and download actions.\\nFrequent interruptions disrupt workflow and focus.\\nAttention is diverted away from core, high-value tasks.\\nCumulatively, these factors undermine overall efficiency and productivity.\\n\\nManual document retrieval in RigOffice is slow, tedious, and error-prone\\n\\nhindering efficiency and undermines productivity.\\n\\nThis process increases time investment, fosters inconsistencies, interrupts workflows, diverts attention from core tasks, and undermines overall productivity.\\n\\n- Time-consuming: Requiring manual time investments for each project\\n- Error-prone: Manual organization leads to inconsistencies\\n- Tedious: Involves repetitive navigation and download actions\\n- Distracting: Takes focus away and diverts attention from high-value tasks\\n\\n\\nAutomation with Python and Selenium eliminates repetitive tasks and errors.\\nEfficiency increases, letting employees focus on core, high-value work.\\n\\n\\n---\\n\\nManual document retrieval in RigOffice is inefficient and repetitive, significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time.\\n\\nEngineers working with NOV's equipment need to retrieve numerous technical documents and drawings from RigDoc, the company's document management system. This process is:\\n\\n\\nThe utility streamlines document retrieval in RigOffice, eliminating repetitive manual searches and saving time before projects begin.\\n\\n\\nManual document collection for projects wastes time due to inefficient processes (manually navigating poorly functioning applications to gather documents before work can begin).\\n\\n before they can begin their actual 3D modeling work. This manual process is:\\n\\n- Tedious and repetitive\\n- Error-prone\\n- A poor use of skilled engineering time\\n\\n\\n\\n\\nAutomates retrieval and download of files from the NOV RigDoc system (https://rigdoc.nov.com).\\n\\n\\nAutomates project document retrieval from RigOffice to eliminate manual, error-prone tasks and save engineering time.\\n\\n\\n\\n  \\\"distilled_essence\\\": \\\"Automates document retrieval from NOV's RigDoc to save time and streamline engineering tasks.\\\"\\n\\n\\n## Overview\\n\\nRigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\\n\\nRigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\\n\\n> Enable automation of document retrieval from NOV's RigDoc system (https://rigdoc.nov.com)\\n\\nRigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\\n\\nAutomate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\\n\\n- Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\\n- Use case: curate, review, and batch-download rig-related documents and technical files.\\n\\n> **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.\\n\\nRigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents \\u2192 Files \\u2192 Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.\\n\\nTo automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\\n\\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).\",\n  \"instruction_format\": \"Implement a 'Batch Search and Download from Predefined Project Templates' feature in RigOfficeDownloader. Enable users to define and save common project document sets (such as specific drawings, manuals, and certificates) as reusable templates. Provide functionality to trigger fully automated batch searches and downloads of all required files for new projects with a single command. Ensure these templates capture typical project documentation needs, allowing the system to anticipate and automate entire retrieval workflows. Integrate mechanisms for defining, saving, and editing template sets. Automate end-to-end retrieval for all documents listed in selected templates, eliminating repetitive navigation and manual selection steps. Verify that the system streamlines document acquisition at project initiation, ensures consistent access to relevant files, and minimizes workflow interruptions. Evaluate overall process for reductions in time investment, errors, and navigation complexity. Prioritize modifications that preserve engineering focus on high-value tasks, maintain technical accuracy, and standardize project onboarding efficiency.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": \"Add a feature to RigOfficeDownloader allowing users to define, save, and edit reusable templates specifying common project document sets. Enable one-command, fully automated batch search and download of all documents in a template, eliminating manual retrieval steps, ensuring consistency, reducing errors and time spent, and standardizing project onboarding.\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\nAutomates document retrieval from NOV's RigDoc system (https://rigdoc.nov.com)\\n\\n## Purpose\\n\\nAutomate and streamline document retrieval from NOV's RigDoc (https://rigdoc.nov.com) via Python and Selenium.\\n\\nAutomate document retrieval from NOV's RigDoc system using Python and Selenium\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n\\nMinimize inconsistencies by improving organization and processes.\\nEnable employees to focus on core tasks with efficient systems.\\n\\n\\nAutomate document retrieval from NOV's RigDoc system (https://rigdoc.nov.com) using Python and Selenium via the web interface to eliminate the need for manual, repetitive, and error-prone processes in RigOffice. Reduce time spent on manual retrieval, minimize errors from inconsistent organization, eliminate repetitive navigation and downloading tasks, prevent workflow interruptions, and enable employees to focus on primary responsibilities. Ensure the solution increases efficiency and streamlines retrieval processes despite the absence of an official API.\\n\\n\\n\\n to replace repetitive, manual processes, increasing efficiency and freeing employees for core tasks despite lacking an official API.\\n- Reduce time spent on manual document retrieval\\n- Minimize errors caused by inconsistent manual organization\\n- Eliminate repetitive navigation and downloading tasks\\n- Prevent workflow interruptions and maintain process continuity\\n- Refocus attention on high-value, core responsibilities\\n- Enhance overall productivity by streamlining retrieval processes\\n\\n---\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is slow and requires time investments for each project.\\n- Manual document retrieval is slow, tedious, and error-prone.\\n- Repetitive navigation and downloading make retrieval tedious.\\n- Workflow interruptions divert attention from other tasks and hinder focus and productivity.\\n- Repetitive actions and workflow interruptions hinder focus and productivity.\\n\\nProblem: Manual document retrieval in RigOffice is slow, repetitive, error-prone, and disrupts productivity; no API is available. Solution: Use Python and Selenium to automate retrieval via web interface (RigOfficeDownloader), reducing manual effort, errors, and workflow interruptions, thereby increasing efficiency and enabling employees to focus on core tasks.\\n\\n\\n\\n\\nIt involves repetitive and tedious navigation and download actions.\\nFrequent interruptions disrupt workflow and focus.\\nAttention is diverted away from core, high-value tasks.\\nCumulatively, these factors undermine overall efficiency and productivity.\\n\\nManual document retrieval in RigOffice is slow, tedious, and error-prone\\n\\nhindering efficiency and undermines productivity.\\n\\nThis process increases time investment, fosters inconsistencies, interrupts workflows, diverts attention from core tasks, and undermines overall productivity.\\n\\n- Time-consuming: Requiring manual time investments for each project\\n- Error-prone: Manual organization leads to inconsistencies\\n- Tedious: Involves repetitive navigation and download actions\\n- Distracting: Takes focus away and diverts attention from high-value tasks\\n\\n\\nAutomation with Python and Selenium eliminates repetitive tasks and errors.\\nEfficiency increases, letting employees focus on core, high-value work.\\n\\n\\n---\\n\\nManual document retrieval in RigOffice is inefficient and repetitive, significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time.\\n\\nEngineers working with NOV's equipment need to retrieve numerous technical documents and drawings from RigDoc, the company's document management system. This process is:\\n\\n\\nThe utility streamlines document retrieval in RigOffice, eliminating repetitive manual searches and saving time before projects begin.\\n\\n\\nManual document collection for projects wastes time due to inefficient processes (manually navigating poorly functioning applications to gather documents before work can begin).\\n\\n before they can begin their actual 3D modeling work. This manual process is:\\n\\n- Tedious and repetitive\\n- Error-prone\\n- A poor use of skilled engineering time\\n\\n\\n\\n\\nAutomates retrieval and download of files from the NOV RigDoc system (https://rigdoc.nov.com).\\n\\n\\nAutomates project document retrieval from RigOffice to eliminate manual, error-prone tasks and save engineering time.\\n\\n\\n\\n  \\\"distilled_essence\\\": \\\"Automates document retrieval from NOV's RigDoc to save time and streamline engineering tasks.\\\"\\n\\n\\n## Overview\\n\\nRigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\\n\\nRigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\\n\\n> Enable automation of document retrieval from NOV's RigDoc system (https://rigdoc.nov.com)\\n\\nRigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\\n\\nAutomate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\\n\\n- Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\\n- Use case: curate, review, and batch-download rig-related documents and technical files.\\n\\n> **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.\\n\\nRigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents \\u2192 Files \\u2192 Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.\\n\\nTo automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\\n\\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).\",\n  \"distilled_essence\": \"Add a feature to RigOfficeDownloader allowing users to define, save, and edit reusable templates specifying common project document sets. Enable one-command, fully automated batch search and download of all documents in a template, eliminating manual retrieval steps, ensuring consistency, reducing errors and time spent, and standardizing project onboarding.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Add a feature to RigOfficeDownloader that allows users to define, save, and edit reusable templates specifying common project document sets. Enable users to initiate fully automated batch searching and downloading of all documents listed in a selected template with a single command. Eliminate manual document retrieval steps by implementing template-driven automation. Ensure the process enforces consistency, reduces errors, saves time, and standardizes document sets for project onboarding.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\nAutomates document retrieval from NOV's RigDoc system (https://rigdoc.nov.com)\\n\\n## Purpose\\n\\nAutomate and streamline document retrieval from NOV's RigDoc (https://rigdoc.nov.com) via Python and Selenium.\\n\\nAutomate document retrieval from NOV's RigDoc system using Python and Selenium\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n\\nMinimize inconsistencies by improving organization and processes.\\nEnable employees to focus on core tasks with efficient systems.\\n\\n\\nAutomate document retrieval from NOV's RigDoc system (https://rigdoc.nov.com) using Python and Selenium via the web interface to eliminate the need for manual, repetitive, and error-prone processes in RigOffice. Reduce time spent on manual retrieval, minimize errors from inconsistent organization, eliminate repetitive navigation and downloading tasks, prevent workflow interruptions, and enable employees to focus on primary responsibilities. Ensure the solution increases efficiency and streamlines retrieval processes despite the absence of an official API.\\n\\n\\n\\n to replace repetitive, manual processes, increasing efficiency and freeing employees for core tasks despite lacking an official API.\\n- Reduce time spent on manual document retrieval\\n- Minimize errors caused by inconsistent manual organization\\n- Eliminate repetitive navigation and downloading tasks\\n- Prevent workflow interruptions and maintain process continuity\\n- Refocus attention on high-value, core responsibilities\\n- Enhance overall productivity by streamlining retrieval processes\\n\\n---\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is slow and requires time investments for each project.\\n- Manual document retrieval is slow, tedious, and error-prone.\\n- Repetitive navigation and downloading make retrieval tedious.\\n- Workflow interruptions divert attention from other tasks and hinder focus and productivity.\\n- Repetitive actions and workflow interruptions hinder focus and productivity.\\n\\nProblem: Manual document retrieval in RigOffice is slow, repetitive, error-prone, and disrupts productivity; no API is available. Solution: Use Python and Selenium to automate retrieval via web interface (RigOfficeDownloader), reducing manual effort, errors, and workflow interruptions, thereby increasing efficiency and enabling employees to focus on core tasks.\\n\\n\\n\\n\\nIt involves repetitive and tedious navigation and download actions.\\nFrequent interruptions disrupt workflow and focus.\\nAttention is diverted away from core, high-value tasks.\\nCumulatively, these factors undermine overall efficiency and productivity.\\n\\nManual document retrieval in RigOffice is slow, tedious, and error-prone\\n\\nhindering efficiency and undermines productivity.\\n\\nThis process increases time investment, fosters inconsistencies, interrupts workflows, diverts attention from core tasks, and undermines overall productivity.\\n\\n- Time-consuming: Requiring manual time investments for each project\\n- Error-prone: Manual organization leads to inconsistencies\\n- Tedious: Involves repetitive navigation and download actions\\n- Distracting: Takes focus away and diverts attention from high-value tasks\\n\\n\\nAutomation with Python and Selenium eliminates repetitive tasks and errors.\\nEfficiency increases, letting employees focus on core, high-value work.\\n\\n\\n---\\n\\nManual document retrieval in RigOffice is inefficient and repetitive, significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time.\\n\\nEngineers working with NOV's equipment need to retrieve numerous technical documents and drawings from RigDoc, the company's document management system. This process is:\\n\\n\\nThe utility streamlines document retrieval in RigOffice, eliminating repetitive manual searches and saving time before projects begin.\\n\\n\\nManual document collection for projects wastes time due to inefficient processes (manually navigating poorly functioning applications to gather documents before work can begin).\\n\\n before they can begin their actual 3D modeling work. This manual process is:\\n\\n- Tedious and repetitive\\n- Error-prone\\n- A poor use of skilled engineering time\\n\\n\\n\\n\\nAutomates retrieval and download of files from the NOV RigDoc system (https://rigdoc.nov.com).\\n\\n\\nAutomates project document retrieval from RigOffice to eliminate manual, error-prone tasks and save engineering time.\\n\\n\\n\\n  \\\"distilled_essence\\\": \\\"Automates document retrieval from NOV's RigDoc to save time and streamline engineering tasks.\\\"\\n\\n\\n## Overview\\n\\nRigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\\n\\nRigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\\n\\n> Enable automation of document retrieval from NOV's RigDoc system (https://rigdoc.nov.com)\\n\\nRigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\\n\\nAutomate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\\n\\n- Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\\n- Use case: curate, review, and batch-download rig-related documents and technical files.\\n\\n> **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.\\n\\nRigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents \\u2192 Files \\u2192 Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.\\n\\nTo automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\\n\\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).\",\n  \"instruction_format\": \"Add a feature to RigOfficeDownloader that allows users to define, save, and edit reusable templates specifying common project document sets. Enable users to initiate fully automated batch searching and downloading of all documents listed in a selected template with a single command. Eliminate manual document retrieval steps by implementing template-driven automation. Ensure the process enforces consistency, reduces errors, saves time, and standardizes document sets for project onboarding.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": \"Add a feature to RigOfficeDownloader for users to create, manage, and apply reusable templates of required document sets; enable fully automated batch retrieval of all documents in a selected template with one command, ensuring consistency, minimizing errors, saving time, and standardizing project onboarding.\"\n}", "cost": 0.0}}}], "total_cost": 0.0}