# RigOfficeDownloader

> **Automate** document retrieval from NOV’s RigDoc system, **minimize** repetitive tasks, and **accelerate** workflow.

---

## Overview

**RigOfficeDownloader** is a single-script Python utility that **scrapes**, **filters**, and **downloads** technical documentation from NOV’s RigDoc. Rather than wasting hours searching for and reviewing documents one by one, engineers can leverage an **interactive workflow** that guides them through each phase of document collection.

**Key Highlights**
- **Three-Stage Workflow**: Documents → Files → Downloads
- **Interactive Menu**: Run the entire workflow or individual steps on demand
- **Markdown Edits**: Quickly select or deselect items by editing `.md` tables
- **Subfolder Support**: Organize downloads with nested folder paths
- **Filter Chains**: Automate selection based on glob-style patterns
- **Single-File Simplicity**: All core logic in one script (~500 lines)

---

## The Problem

Engineering projects require diverse technical documents from RigDoc. This **manual** retrieval is:
- **Tedious & Repetitive**: Browsing multiple pages, copying metadata, creating folders
- **Error-Prone**: Risk of missing crucial docs or misplacing files
- **Time Consuming**: Delays the start of high-value engineering tasks

---

## The Solution

RigOfficeDownloader streamlines the entire process through an **automated workflow**:
1. **Fetch** document metadata (stores in `<rig>-a-docs.json`)
2. **Export** docs to Markdown (`<rig>-a-docs.md`): mark `item_include=true`
3. **Import** updated doc data back into JSON
4. **Fetch** file metadata for included docs
5. **Export** files to Markdown (`<rig>-b-files.md`): mark `item_download=true`
6. **Import** updated file data
7. **Download** files with subfolder paths derived from the generated names

---

## Workflow Steps in Detail

1. **Change / Configure**
   - Input your **rig number** and **search URLs**.
   - Set up or modify **filters** (e.g., auto-include `*DRILL*FLOOR*` docs, exclude `*VOID*` docs).

2. **Fetch Documents**
   - Scrape document metadata from RigDoc URLs.
   - Data saved to JSON (`<rig>-a-docs.json`), all set to `item_include=false`.

3. **Export & Review Docs**
   - Creates `<rig>-a-docs.md`.
   - Manually set `item_include=true` for relevant docs.

4. **Import Updated Docs**
   - Reads user changes back into JSON.

5. **Fetch File Metadata**
   - For each included doc, scrapes associated files (saved in `<rig>-b-files.json`).
   - `item_download=false` by default.

6. **Export & Review Files**
   - Creates `<rig>-b-files.md`.
   - Set `item_download=true` for desired files.

7. **Import Updated Files**
   - Sync file selections back into JSON.

8. **Download Files**
   - Retrieves selected files, applying `item_generated_name` for naming.
   - `'/'` in `item_generated_name` spawns subfolders in `outputs/downloads/<rig>`.

---

## Directory Structure

