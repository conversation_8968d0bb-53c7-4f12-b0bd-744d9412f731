from monitor import Monitor, get_all_monitors
from window import Window, get_all_windows
from window_tiler import WindowTiler

def test_window_functions(window: Window, monitor: Monitor):
    # Test resizing and moving
    window.resize_and_move(100, 100, 200, 200)
    print(f"Window resized and moved: {window.get_rect()}")

    # Test maximizing within monitor
    window.maximize_within_monitor(monitor)
    print(f"Window maximized: {window.get_rect()}")

    # Test centering within monitor
    window.center_within_monitor(monitor)
    print(f"Window centered: {window.get_rect()}")

    # Test toggling visibility
    window.toggle_visibility()
    print(f"Window visibility toggled: {window.is_visible()}")
    window.toggle_visibility()

    # Test if the window is full screen
    print(f"Is window full screen: {window.is_full_screen()}")

def tile_test_windows(monitors, windows):
    primary_monitor = next((mon for mon in monitors.values() if mon.is_primary()), None)
    if primary_monitor:
        tile_windows(primary_monitor, list(windows.values()), 2, 2)


def main():
    # Get all monitors and windows
    monitors = get_all_monitors()
    windows = get_all_windows()

    # Create a WindowTiler instance
    window_tiler = WindowTiler(monitors, windows)

    # Perform tiling
    primary_monitor = next((mon for mon in monitors.values() if mon.is_primary()), None)
    if primary_monitor:
        window_tiler.tile_windows(primary_monitor, list(windows.values()), 2, 2, column_ratios=[0.5, 0.25, 0.25])


if __name__ == "__main__":
    main()
