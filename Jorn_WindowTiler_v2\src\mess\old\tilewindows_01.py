import win32gui
import win32con
import win32api

def tile_windows(rows, columns):
    # Get the handle for the desktop
    desktop = win32gui.GetDesktopWindow()

    # Get the size of the screen
    screen_width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
    screen_height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)

    # Get a list of all windows
    windows = []
    def enum_windows(hwnd, result):
        windows.append((hwnd, win32gui.GetWindowText(hwnd)))
    win32gui.EnumWindows(enum_windows, [])

    # Filter out windows that are not visible or do not have a title
    windows = [(hwnd, title) for hwnd, title in windows if win32gui.IsWindowVisible(hwnd) and title != ""]

    # Determine the size of each window
    window_width = screen_width // columns
    window_height = screen_height // rows

    # Position each window in a grid
    for i, (hwnd, title) in enumerate(windows):
        row = i // columns
        col = i % columns
        win32gui.MoveWindow(hwnd, col * window_width, row * window_height, window_width, window_height, True)

# Example usage:
tile_windows(4, 4)
