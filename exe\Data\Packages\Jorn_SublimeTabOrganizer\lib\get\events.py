import time
import sublime
import sublime_plugin

class TabActivityListener(sublime_plugin.EventListener):
    def on_new_async(self, view):
        self._update_last_accessed(view)

    def on_load_async(self, view):
        self._update_last_accessed(view)

    def on_activated_async(self, view):
        self._update_last_accessed(view)

    def on_modified_async(self, view):
        self._update_last_accessed(view)

    def _update_last_accessed(self, view):
        if view and view.settings():
            view.settings().set('jorn_last_accessed_time', time.time())
