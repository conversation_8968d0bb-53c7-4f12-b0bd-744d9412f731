  {
  "initial_prompt": "# GOAL: TRANSFORM INTO `README.md`\n\n\n# Status\n\nWork in progress\n\n## Konsept/Prosess\n\nVerkt\u00f8y for \"automatisk\" innhenting av dokumentasjon via. https://rigdoc.nov.com (prosjektinfo, tegninger, osv).\n\n\n### 1. Jobbe utifra \"R4511 - Internkontroll (*********-Rev.0).xlsx\"\n\n    `\"C:\\Vault\\Norway Simulation\\Rigs\\R4511-R5385-StenaIceMAX\\docs\\R4511-Internkontroll(*********-Rev.0).xlsx\"`\n    \u251c\u2500\u2500 ---\n    \u2514\u2500\u2500 `C:\\Vault\\Norway Simulation\\`\n        \u2514\u2500\u2500 `Rigs\\R4511 - R5385 - <PERSON><PERSON> IceMAX\\Documents`\n            \u2514\u2500\u2500 `IMPORTANT - Internal Audit - Qualification Record for Simulator 3D Development`\n                \u2502\n                \u2514\u2500\u2500 `R4511 - Internkontroll (*********-Rev.0).xlsx`\n\n\n### 2. Samler inn essensiell info\n\n| Case No           | Equipment                                   | GA drawing             | GA rev.   | Verified by  | Comment        |\n| ----------------- | ------------------------------------------- | ---------------------- | --------- | ------------ | -------------- |\n| EQ-28209-104A     | Cylinder Hoisting Rig, 1250st, 48m          | 19129153-GAD           | 01        |              | 29273-106      |\n| EQ-28209-104A     | Sheave Cluster Cylinder Rig 1250            | 19129152-GAD           | 02        |              | 29273-106      |\n| EQ-28209-106A     | Top Drive, 1250t AC                         | 19140396-GAD           | 01        |              | 29273-106      |\n| EQ-28209-120A     | Power Slip 1500 ton                         | DD-10141101-605        | 02        |              | 29273-106      |\n| V6056             | Iron Roughneck-Hydratong MPT-200            | V6056-D1100-G0001      | 5         |              | 29273-106      |\n| V6051             | Tubular Chute, Main                         | V6051-D1195-G0002      | 2         |              | 29273-107      |\n| V6045             | Fingerboards                                | V6045-D1202-G0001      | 03A       |              | 29273-107      |\n| V6042             | Hydraracker IV, Main                        | V6042-D1213-G0001      | 0         |              | 29273-107      |\n| V6054             | Pipe guide, main under drillfloor           | V6054-D1194-G0002      | 3         |              | 29273-107      |\n| EQ-28209-103A     | Elevated Backup Tong; EBT-150               | 1906283-GAD            | 03        |              | 29273-107      |\n\n\n### 3. RigOffice s\u00f8kestrenger\n\n    `\"search_urls\"`\n    \u251c\u2500\u2500 ---\n    \u251c\u2500\u2500 `https://rigdoc.nov.com/search/advancedsearch?q=`\n    \u2502   \u2502\n    \u2502   \u2514\u2500\u2500 \"EQ-28209-104A\" -> `&CaseNo=EQ-28209-104A&DocTypeId=66`\n    \u2502\n    \u2514\u2500\u2500 `https://rigdoc.nov.com/search?q=`\n        \u2502\n        \u251c\u2500\u2500 \"19129153-GAD\" -> `19129153-GAD&CaseType=Equipment`\n        \u251c\u2500\u2500 \"19129152-GAD\" -> `19129152-GAD&CaseType=Equipment`\n        \u251c\u2500\u2500 \"19140396-GAD\" -> `19140396-GAD&CaseType=Equipment`\n        \u251c\u2500\u2500 \"DD-10141101-605\" -> `DD-10141101-605&CaseType=Equipment`\n        \u251c\u2500\u2500 \"V6056-D1100-G0001\" -> `V6056-D1100-G0001&CaseType=Equipment`\n        \u251c\u2500\u2500 \"V6051-D1195-G0002\" -> `V6051-D1195-G0002&CaseType=Equipment`\n        \u251c\u2500\u2500 \"V6045-D1202-G0001\" -> `V6045-D1202-G0001&CaseType=Equipment`\n        \u251c\u2500\u2500 \"V6042-D1213-G0001\" -> `V6042-D1213-G0001&CaseType=Equipment`\n        \u251c\u2500\u2500 \"V6054-D1194-G0002\" -> `V6054-D1194-G0002&CaseType=Equipment`\n        \u251c\u2500\u2500 \"19066283-GAD\" -> `19066283-GAD&CaseType=Equipment`\n        \u2514\u2500\u2500 \"29273-106\" -> `29273-106&CaseType=Task / Deliverable`\n\n---\n\n\n## Intent\nTo automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\n\n## Key Project Aspects\n\n### Primary Problem\nEngineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck\u2014it's the manual preparation process.\n\n### Solution Approach\nA Python-based utility that:\n1. Automatically scrapes document metadata from RigOffice\n2. Extracts file information from those documents\n3. Downloads and organizes selected files based on user criteria\n\n### Current State\nFunctional working prototype that:\n- Uses a 3-step workflow (document metadata \u2192 file metadata \u2192 download)\n- Stores intermediate results in JSON format\n- Allows user intervention between steps\n- Provides progress feedback\n\n### Critical Next Steps\n1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches\n2. **Implement file hash checking** to prevent redundant downloads\n3. **Improve progress visibility** during lengthy scraping operations\n\n### Core Technical Pattern\nA single-file, modular approach using:\n- Selenium for browser automation\n- JSON for data storage\n- Three-stage processing with user control points\n- Incremental updates to avoid redundant work\n\n### Key Success Metrics\n- Reduce documentation gathering time by 75%+\n- Ensure reliable retrieval of required documentation\n- Organize files in a way that streamlines workflow\n- Support both broad searches (by rig number) and targeted searches (by specific documents)\n\n\n---\n\n\n### RigOfficeDownloader Utility Workflow\n\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow is clearly defined in the docstring at the beginning of the main script and follows these steps:\n\n1. Fetch Documents\n- The utility starts by scraping document metadata from predefined search URLs\n- Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory\n- Each document entry includes metadata like title, document number, revision, etc.\n- All documents are initially marked with item_include=False\n- Each document gets an item_generated_name for better identification\n\n2. Export Documents to Markdown\n- The JSON data is exported to a Markdown table: <rig>-a-docs.md\n- This allows the user to easily review and edit which documents to include\n- The user is expected to edit the Markdown file and set item_include=true for desired documents\n\n3. Import Updated Document Data\n- After the user edits the Markdown file, the utility imports the changes back to the JSON file\n- This updates which documents are marked for file retrieval\n\n4. Fetch Files for Selected Documents\n- For each document with item_include=true, the utility scrapes file metadata\n- File data is saved to <rig>-b-files.json\n- Each file is initially marked with item_download=False\n- Files inherit the document's item_generated_name with additional identifiers\n\n5. Export Files to Markdown\n- The file data is exported to a Markdown table: <rig>-b-files.md\n- The user reviews and edits which files to download by setting item_download=true\n\n6. Import Updated File Data\n- After editing, the utility imports the changes back to the JSON file\n- This updates which files are marked for download\n\n7. Download Selected Files\n- Files with item_download=true are downloaded\n- Files are named according to their item_generated_name + extension\n- The utility supports creating subfolders based on '/' in the item_generated_name\n- Files are saved to the outputs/downloads/<rig> directory\n\nInteractive Menu\n- The utility provides an interactive menu where the user can choose which steps to execute\n- This allows for flexibility in the workflow, enabling the user to run specific steps as needed\n- The user can also update the rig number and search URLs through this menu\n\nKey Features\n- Automatic document and file metadata scraping\n- User-friendly Markdown editing interface\n- Customizable file naming with item_generated_name\n- Support for subfolder organization in downloads\n- Deduplication of documents and files\n- Configurable field ordering for JSON and Markdown exports\n\nTechnical Implementation\n- Uses Selenium with Chrome WebDriver for web scraping\n- Implements smart waiting strategies for page loading\n- Handles browser sessions with proper cleanup\n- Provides progress feedback during operations\n- Sanitizes filenames for valid paths",
  "sequence_id": "sequence-0001-a+0001-b",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "# GOAL: TRANSFORM INTO `README.md`\n\n\n# Status\n\nWork in progress\n\n## Konsept/Prosess\n\nVerkt\u00f8y for \"automatisk\" innhenting av dokumentasjon via. https://rigdoc.nov.com (prosjektinfo, tegninger, osv).\n\n\n### 1. Jobbe utifra \"R4511 - Internkontroll (*********-Rev.0).xlsx\"\n\n    `\"C:\\Vault\\Norway Simulation\\Rigs\\R4511-R5385-StenaIceMAX\\docs\\R4511-Internkontroll(*********-Rev.0).xlsx\"`\n    \u251c\u2500\u2500 ---\n    \u2514\u2500\u2500 `C:\\Vault\\Norway Simulation\\`\n        \u2514\u2500\u2500 `Rigs\\R4511 - R5385 - Stena IceMAX\\Documents`\n            \u2514\u2500\u2500 `IMPORTANT - Internal Audit - Qualification Record for Simulator 3D Development`\n                \u2502\n                \u2514\u2500\u2500 `R4511 - Internkontroll (*********-Rev.0).xlsx`\n\n\n### 2. Samler inn essensiell info\n\n| Case No           | Equipment                                   | GA drawing             | GA rev.   | Verified by  | Comment        |\n| ----------------- | ------------------------------------------- | ---------------------- | --------- | ------------ | -------------- |\n| EQ-28209-104A     | Cylinder Hoisting Rig, 1250st, 48m          | 19129153-GAD           | 01        |              | 29273-106      |\n| EQ-28209-104A     | Sheave Cluster Cylinder Rig 1250            | 19129152-GAD           | 02        |              | 29273-106      |\n| EQ-28209-106A     | Top Drive, 1250t AC                         | 19140396-GAD           | 01        |              | 29273-106      |\n| EQ-28209-120A     | Power Slip 1500 ton                         | DD-10141101-605        | 02        |              | 29273-106      |\n| V6056             | Iron Roughneck-Hydratong MPT-200            | V6056-D1100-G0001      | 5         |              | 29273-106      |\n| V6051             | Tubular Chute, Main                         | V6051-D1195-G0002      | 2         |              | 29273-107      |\n| V6045             | Fingerboards                                | V6045-D1202-G0001      | 03A       |              | 29273-107      |\n| V6042             | Hydraracker IV, Main                        | V6042-D1213-G0001      | 0         |              | 29273-107      |\n| V6054             | Pipe guide, main under drillfloor           | V6054-D1194-G0002      | 3         |              | 29273-107      |\n| EQ-28209-103A     | Elevated Backup Tong; EBT-150               | 1906283-GAD            | 03        |              | 29273-107      |\n\n\n### 3. RigOffice s\u00f8kestrenger\n\n    `\"search_urls\"`\n    \u251c\u2500\u2500 ---\n    \u251c\u2500\u2500 `https://rigdoc.nov.com/search/advancedsearch?q=`\n    \u2502   \u2502\n    \u2502   \u2514\u2500\u2500 \"EQ-28209-104A\" -> `&CaseNo=EQ-28209-104A&DocTypeId=66`\n    \u2502\n    \u2514\u2500\u2500 `https://rigdoc.nov.com/search?q=`\n        \u2502\n        \u251c\u2500\u2500 \"19129153-GAD\" -> `19129153-GAD&CaseType=Equipment`\n        \u251c\u2500\u2500 \"19129152-GAD\" -> `19129152-GAD&CaseType=Equipment`\n        \u251c\u2500\u2500 \"19140396-GAD\" -> `19140396-GAD&CaseType=Equipment`\n        \u251c\u2500\u2500 \"DD-10141101-605\" -> `DD-10141101-605&CaseType=Equipment`\n        \u251c\u2500\u2500 \"V6056-D1100-G0001\" -> `V6056-D1100-G0001&CaseType=Equipment`\n        \u251c\u2500\u2500 \"V6051-D1195-G0002\" -> `V6051-D1195-G0002&CaseType=Equipment`\n        \u251c\u2500\u2500 \"V6045-D1202-G0001\" -> `V6045-D1202-G0001&CaseType=Equipment`\n        \u251c\u2500\u2500 \"V6042-D1213-G0001\" -> `V6042-D1213-G0001&CaseType=Equipment`\n        \u251c\u2500\u2500 \"V6054-D1194-G0002\" -> `V6054-D1194-G0002&CaseType=Equipment`\n        \u251c\u2500\u2500 \"19066283-GAD\" -> `19066283-GAD&CaseType=Equipment`\n        \u2514\u2500\u2500 \"29273-106\" -> `29273-106&CaseType=Task / Deliverable`\n\n---\n\n\n## Intent\nTo automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\n\n## Key Project Aspects\n\n### Primary Problem\nEngineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck\u2014it's the manual preparation process.\n\n### Solution Approach\nA Python-based utility that:\n1. Automatically scrapes document metadata from RigOffice\n2. Extracts file information from those documents\n3. Downloads and organizes selected files based on user criteria\n\n### Current State\nFunctional working prototype that:\n- Uses a 3-step workflow (document metadata \u2192 file metadata \u2192 download)\n- Stores intermediate results in JSON format\n- Allows user intervention between steps\n- Provides progress feedback\n\n### Critical Next Steps\n1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches\n2. **Implement file hash checking** to prevent redundant downloads\n3. **Improve progress visibility** during lengthy scraping operations\n\n### Core Technical Pattern\nA single-file, modular approach using:\n- Selenium for browser automation\n- JSON for data storage\n- Three-stage processing with user control points\n- Incremental updates to avoid redundant work\n\n### Key Success Metrics\n- Reduce documentation gathering time by 75%+\n- Ensure reliable retrieval of required documentation\n- Organize files in a way that streamlines workflow\n- Support both broad searches (by rig number) and targeted searches (by specific documents)\n\n\n---\n\n\n### RigOfficeDownloader Utility Workflow\n\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow is clearly defined in the docstring at the beginning of the main script and follows these steps:\n\n1. Fetch Documents\n- The utility starts by scraping document metadata from predefined search URLs\n- Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory\n- Each document entry includes metadata like title, document number, revision, etc.\n- All documents are initially marked with item_include=False\n- Each document gets an item_generated_name for better identification\n\n2. Export Documents to Markdown\n- The JSON data is exported to a Markdown table: <rig>-a-docs.md\n- This allows the user to easily review and edit which documents to include\n- The user is expected to edit the Markdown file and set item_include=true for desired documents\n\n3. Import Updated Document Data\n- After the user edits the Markdown file, the utility imports the changes back to the JSON file\n- This updates which documents are marked for file retrieval\n\n4. Fetch Files for Selected Documents\n- For each document with item_include=true, the utility scrapes file metadata\n- File data is saved to <rig>-b-files.json\n- Each file is initially marked with item_download=False\n- Files inherit the document's item_generated_name with additional identifiers\n\n5. Export Files to Markdown\n- The file data is exported to a Markdown table: <rig>-b-files.md\n- The user reviews and edits which files to download by setting item_download=true\n\n6. Import Updated File Data\n- After editing, the utility imports the changes back to the JSON file\n- This updates which files are marked for download\n\n7. Download Selected Files\n- Files with item_download=true are downloaded\n- Files are named according to their item_generated_name + extension\n- The utility supports creating subfolders based on '/' in the item_generated_name\n- Files are saved to the outputs/downloads/<rig> directory\n\nInteractive Menu\n- The utility provides an interactive menu where the user can choose which steps to execute\n- This allows for flexibility in the workflow, enabling the user to run specific steps as needed\n- The user can also update the rig number and search URLs through this menu\n\nKey Features\n- Automatic document and file metadata scraping\n- User-friendly Markdown editing interface\n- Customizable file naming with item_generated_name\n- Support for subfolder organization in downloads\n- Deduplication of documents and files\n- Configurable field ordering for JSON and Markdown exports\n\nTechnical Implementation\n- Uses Selenium with Chrome WebDriver for web scraping\n- Implements smart waiting strategies for page loading\n- Handles browser sessions with proper cleanup\n- Provides progress feedback during operations\n- Sanitizes filenames for valid paths",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"# Create a README.md file for the RigOfficeDownloader Utility\\n\\n## 1. State Current Project Status\\n- Indicate that the project is a work in progress.\\n\\n## 2. Summarize Concept and Process\\n- Describe the tool as a utility for automatic retrieval of documentation such as project information and drawings from https://rigdoc.nov.com.\\n\\n### Step 1: Identify Source Data\\n- Specify the starting point as the Excel file, `R4511 - Internkontroll (*********-Rev.0).xlsx`, located in the designated directory structure.\\n\\n### Step 2: Collect Essential Information\\n- Present an example table showing key metadata fields: Case No, Equipment, GA drawing, GA rev., Verified by, Comment.\\n\\n### Step 3: Define RigOffice Search Strings\\n- List exemplary search URLs for both case number and equipment/document identifiers, mapping metadata fields to search query strings as shown.\\n\\n## 3. Clarify Project Intent\\n- Articulate that the intent is to automate and streamline the document retrieval process in NOV's RigOffice, enabling 3D engineers to focus on modeling work by minimizing manual document gathering.\\n\\n## 4. Highlight Key Project Aspects\\n- Describe the primary problem: time wasted by engineers navigating complex applications for documents.\\n- Summarize the Python-based solution approach:\\n  * Automatically scrape document metadata from RigOffice.\\n  * Extract file information and download selected files.\\n  * Organize downloads based on user-defined criteria.\\n- Outline the current project state:\\n  * Functional prototype using a three-step workflow (document metadata \u2192 file metadata \u2192 download).\\n  * Store intermediate data in JSON files.\\n  * Allow user review/editing between steps (Markdown files).\\n  * Provide progress feedback.\\n- List critical next steps:\\n  1. Separate JSON files for different search types to prevent unwanted merges.\\n  2. Implement file hash checking to avoid redundant downloads.\\n  3. Improve progress visibility during scraping.\\n- Define the core technical pattern:\\n  * Single-file, modular design using Selenium for automation.\\n  * Use JSON for storing state, incremental updates, and Markdown interfaces.\\n  * Support both broad and targeted searches.\\n- Specify key success metrics such as reducing documentation time by 75%+, ensuring reliable retrieval, and streamlining file organization for 3D workflow.\\n\\n## 5. Document Utility Workflow in Detail\\n- Outline the sequential workflow steps:\\n  1. Fetch Documents: Scrape document metadata from search URLs; save to `<rig>-a-docs.json`.\\n  2. Export Documents to Markdown: Export JSON to Markdown (`<rig>-a-docs.md`) for user review; instruct user to mark desired documents with `item_include=true`.\\n  3. Import Updated Document Data: Parse edited Markdown, update JSON accordingly.\\n  4. Fetch Files for Selected Documents: Scrape file metadata for included documents, save results to `<rig>-b-files.json`, mark files as `item_download=false` by default.\\n  5. Export Files to Markdown: Export file list to Markdown (`<rig>-b-files.md`); instruct user to mark files for download with `item_download=true`.\\n  6. Import Updated File Data: Read user's selections from Markdown, update JSON.\\n  7. Download Selected Files: Download files marked for download, use `item_generated_name` for file naming, create subfolders as needed, and save to `outputs/downloads/<rig>`.\\n\\n- Describe the interactive menu:\\n  * Present users with options for executing specific workflow steps or updating search configuration as needed.\\n\\n- List key features:\\n  * Automated metadata scraping via Selenium\\n  * User-friendly review and selection through Markdown exports/edits\\n  * Systematic file naming and organization\\n  * Deduplication of documents and files\\n  * Configurable field ordering for exports\\n  * Progress feedback and error handling\\n\\n- Summarize technical implementation:\\n  * Use Selenium with Chrome WebDriver\\n  * Implement smart waits and session cleanup\\n  * Sanitize filenames for path validity\\n\\n## 6. Provide Directory Structure Example\\n- Include sample structure for the documents and outputs directories.\\n\\n## 7. Link to Usage Instructions (if available)\\n- Refer to additional documentation or usage sections for setup, configuration, and running the utility if necessary.\"\n}",
          "cost": 0.000000
        }
      }
    },
      {
      "instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`",
      "step": "b",
      "title": "Essence Distillation",
      "input": "{\n  \"initial_prompt\": \"# GOAL: TRANSFORM INTO `README.md`\\n\\n\\n# Status\\n\\nWork in progress\\n\\n## Konsept/Prosess\\n\\nVerkt\\u00f8y for \\\"automatisk\\\" innhenting av dokumentasjon via. https://rigdoc.nov.com (prosjektinfo, tegninger, osv).\\n\\n\\n### 1. Jobbe utifra \\\"R4511 - Internkontroll (*********-Rev.0).xlsx\\\"\\n\\n    `\\\"C:\\\\Vault\\\\Norway Simulation\\\\Rigs\\\\R4511-R5385-StenaIceMAX\\\\docs\\\\R4511-Internkontroll(*********-Rev.0).xlsx\\\"`\\n    \\u251c\\u2500\\u2500 ---\\n    \\u2514\\u2500\\u2500 `C:\\\\Vault\\\\Norway Simulation\\\\`\\n        \\u2514\\u2500\\u2500 `Rigs\\\\R4511 - R5385 - Stena IceMAX\\\\Documents`\\n            \\u2514\\u2500\\u2500 `IMPORTANT - Internal Audit - Qualification Record for Simulator 3D Development`\\n                \\u2502\\n                \\u2514\\u2500\\u2500 `R4511 - Internkontroll (*********-Rev.0).xlsx`\\n\\n\\n### 2. Samler inn essensiell info\\n\\n| Case No           | Equipment                                   | GA drawing             | GA rev.   | Verified by  | Comment        |\\n| ----------------- | ------------------------------------------- | ---------------------- | --------- | ------------ | -------------- |\\n| EQ-28209-104A     | Cylinder Hoisting Rig, 1250st, 48m          | 19129153-GAD           | 01        |              | 29273-106      |\\n| EQ-28209-104A     | Sheave Cluster Cylinder Rig 1250            | 19129152-GAD           | 02        |              | 29273-106      |\\n| EQ-28209-106A     | Top Drive, 1250t AC                         | 19140396-GAD           | 01        |              | 29273-106      |\\n| EQ-28209-120A     | Power Slip 1500 ton                         | DD-10141101-605        | 02        |              | 29273-106      |\\n| V6056             | Iron Roughneck-Hydratong MPT-200            | V6056-D1100-G0001      | 5         |              | 29273-106      |\\n| V6051             | Tubular Chute, Main                         | V6051-D1195-G0002      | 2         |              | 29273-107      |\\n| V6045             | Fingerboards                                | V6045-D1202-G0001      | 03A       |              | 29273-107      |\\n| V6042             | Hydraracker IV, Main                        | V6042-D1213-G0001      | 0         |              | 29273-107      |\\n| V6054             | Pipe guide, main under drillfloor           | V6054-D1194-G0002      | 3         |              | 29273-107      |\\n| EQ-28209-103A     | Elevated Backup Tong; EBT-150               | 1906283-GAD            | 03        |              | 29273-107      |\\n\\n\\n### 3. RigOffice s\\u00f8kestrenger\\n\\n    `\\\"search_urls\\\"`\\n    \\u251c\\u2500\\u2500 ---\\n    \\u251c\\u2500\\u2500 `https://rigdoc.nov.com/search/advancedsearch?q=`\\n    \\u2502   \\u2502\\n    \\u2502   \\u2514\\u2500\\u2500 \\\"EQ-28209-104A\\\" -> `&CaseNo=EQ-28209-104A&DocTypeId=66`\\n    \\u2502\\n    \\u2514\\u2500\\u2500 `https://rigdoc.nov.com/search?q=`\\n        \\u2502\\n        \\u251c\\u2500\\u2500 \\\"19129153-GAD\\\" -> `19129153-GAD&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"19129152-GAD\\\" -> `19129152-GAD&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"19140396-GAD\\\" -> `19140396-GAD&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"DD-10141101-605\\\" -> `DD-10141101-605&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"V6056-D1100-G0001\\\" -> `V6056-D1100-G0001&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"V6051-D1195-G0002\\\" -> `V6051-D1195-G0002&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"V6045-D1202-G0001\\\" -> `V6045-D1202-G0001&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"V6042-D1213-G0001\\\" -> `V6042-D1213-G0001&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"V6054-D1194-G0002\\\" -> `V6054-D1194-G0002&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"19066283-GAD\\\" -> `19066283-GAD&CaseType=Equipment`\\n        \\u2514\\u2500\\u2500 \\\"29273-106\\\" -> `29273-106&CaseType=Task / Deliverable`\\n\\n---\\n\\n\\n## Intent\\nTo automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\\n\\n## Key Project Aspects\\n\\n### Primary Problem\\nEngineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck\\u2014it's the manual preparation process.\\n\\n### Solution Approach\\nA Python-based utility that:\\n1. Automatically scrapes document metadata from RigOffice\\n2. Extracts file information from those documents\\n3. Downloads and organizes selected files based on user criteria\\n\\n### Current State\\nFunctional working prototype that:\\n- Uses a 3-step workflow (document metadata \\u2192 file metadata \\u2192 download)\\n- Stores intermediate results in JSON format\\n- Allows user intervention between steps\\n- Provides progress feedback\\n\\n### Critical Next Steps\\n1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches\\n2. **Implement file hash checking** to prevent redundant downloads\\n3. **Improve progress visibility** during lengthy scraping operations\\n\\n### Core Technical Pattern\\nA single-file, modular approach using:\\n- Selenium for browser automation\\n- JSON for data storage\\n- Three-stage processing with user control points\\n- Incremental updates to avoid redundant work\\n\\n### Key Success Metrics\\n- Reduce documentation gathering time by 75%+\\n- Ensure reliable retrieval of required documentation\\n- Organize files in a way that streamlines workflow\\n- Support both broad searches (by rig number) and targeted searches (by specific documents)\\n\\n\\n---\\n\\n\\n### RigOfficeDownloader Utility Workflow\\n\\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow is clearly defined in the docstring at the beginning of the main script and follows these steps:\\n\\n1. Fetch Documents\\n- The utility starts by scraping document metadata from predefined search URLs\\n- Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory\\n- Each document entry includes metadata like title, document number, revision, etc.\\n- All documents are initially marked with item_include=False\\n- Each document gets an item_generated_name for better identification\\n\\n2. Export Documents to Markdown\\n- The JSON data is exported to a Markdown table: <rig>-a-docs.md\\n- This allows the user to easily review and edit which documents to include\\n- The user is expected to edit the Markdown file and set item_include=true for desired documents\\n\\n3. Import Updated Document Data\\n- After the user edits the Markdown file, the utility imports the changes back to the JSON file\\n- This updates which documents are marked for file retrieval\\n\\n4. Fetch Files for Selected Documents\\n- For each document with item_include=true, the utility scrapes file metadata\\n- File data is saved to <rig>-b-files.json\\n- Each file is initially marked with item_download=False\\n- Files inherit the document's item_generated_name with additional identifiers\\n\\n5. Export Files to Markdown\\n- The file data is exported to a Markdown table: <rig>-b-files.md\\n- The user reviews and edits which files to download by setting item_download=true\\n\\n6. Import Updated File Data\\n- After editing, the utility imports the changes back to the JSON file\\n- This updates which files are marked for download\\n\\n7. Download Selected Files\\n- Files with item_download=true are downloaded\\n- Files are named according to their item_generated_name + extension\\n- The utility supports creating subfolders based on '/' in the item_generated_name\\n- Files are saved to the outputs/downloads/<rig> directory\\n\\nInteractive Menu\\n- The utility provides an interactive menu where the user can choose which steps to execute\\n- This allows for flexibility in the workflow, enabling the user to run specific steps as needed\\n- The user can also update the rig number and search URLs through this menu\\n\\nKey Features\\n- Automatic document and file metadata scraping\\n- User-friendly Markdown editing interface\\n- Customizable file naming with item_generated_name\\n- Support for subfolder organization in downloads\\n- Deduplication of documents and files\\n- Configurable field ordering for JSON and Markdown exports\\n\\nTechnical Implementation\\n- Uses Selenium with Chrome WebDriver for web scraping\\n- Implements smart waiting strategies for page loading\\n- Handles browser sessions with proper cleanup\\n- Provides progress feedback during operations\\n- Sanitizes filenames for valid paths\",\n  \"instruction_format\": \"# Create a README.md file for the RigOfficeDownloader Utility\\n\\n## 1. State Current Project Status\\n- Indicate that the project is a work in progress.\\n\\n## 2. Summarize Concept and Process\\n- Describe the tool as a utility for automatic retrieval of documentation such as project information and drawings from https://rigdoc.nov.com.\\n\\n### Step 1: Identify Source Data\\n- Specify the starting point as the Excel file, `R4511 - Internkontroll (*********-Rev.0).xlsx`, located in the designated directory structure.\\n\\n### Step 2: Collect Essential Information\\n- Present an example table showing key metadata fields: Case No, Equipment, GA drawing, GA rev., Verified by, Comment.\\n\\n### Step 3: Define RigOffice Search Strings\\n- List exemplary search URLs for both case number and equipment/document identifiers, mapping metadata fields to search query strings as shown.\\n\\n## 3. Clarify Project Intent\\n- Articulate that the intent is to automate and streamline the document retrieval process in NOV's RigOffice, enabling 3D engineers to focus on modeling work by minimizing manual document gathering.\\n\\n## 4. Highlight Key Project Aspects\\n- Describe the primary problem: time wasted by engineers navigating complex applications for documents.\\n- Summarize the Python-based solution approach:\\n  * Automatically scrape document metadata from RigOffice.\\n  * Extract file information and download selected files.\\n  * Organize downloads based on user-defined criteria.\\n- Outline the current project state:\\n  * Functional prototype using a three-step workflow (document metadata \\u2192 file metadata \\u2192 download).\\n  * Store intermediate data in JSON files.\\n  * Allow user review/editing between steps (Markdown files).\\n  * Provide progress feedback.\\n- List critical next steps:\\n  1. Separate JSON files for different search types to prevent unwanted merges.\\n  2. Implement file hash checking to avoid redundant downloads.\\n  3. Improve progress visibility during scraping.\\n- Define the core technical pattern:\\n  * Single-file, modular design using Selenium for automation.\\n  * Use JSON for storing state, incremental updates, and Markdown interfaces.\\n  * Support both broad and targeted searches.\\n- Specify key success metrics such as reducing documentation time by 75%+, ensuring reliable retrieval, and streamlining file organization for 3D workflow.\\n\\n## 5. Document Utility Workflow in Detail\\n- Outline the sequential workflow steps:\\n  1. Fetch Documents: Scrape document metadata from search URLs; save to `<rig>-a-docs.json`.\\n  2. Export Documents to Markdown: Export JSON to Markdown (`<rig>-a-docs.md`) for user review; instruct user to mark desired documents with `item_include=true`.\\n  3. Import Updated Document Data: Parse edited Markdown, update JSON accordingly.\\n  4. Fetch Files for Selected Documents: Scrape file metadata for included documents, save results to `<rig>-b-files.json`, mark files as `item_download=false` by default.\\n  5. Export Files to Markdown: Export file list to Markdown (`<rig>-b-files.md`); instruct user to mark files for download with `item_download=true`.\\n  6. Import Updated File Data: Read user's selections from Markdown, update JSON.\\n  7. Download Selected Files: Download files marked for download, use `item_generated_name` for file naming, create subfolders as needed, and save to `outputs/downloads/<rig>`.\\n\\n- Describe the interactive menu:\\n  * Present users with options for executing specific workflow steps or updating search configuration as needed.\\n\\n- List key features:\\n  * Automated metadata scraping via Selenium\\n  * User-friendly review and selection through Markdown exports/edits\\n  * Systematic file naming and organization\\n  * Deduplication of documents and files\\n  * Configurable field ordering for exports\\n  * Progress feedback and error handling\\n\\n- Summarize technical implementation:\\n  * Use Selenium with Chrome WebDriver\\n  * Implement smart waits and session cleanup\\n  * Sanitize filenames for path validity\\n\\n## 6. Provide Directory Structure Example\\n- Include sample structure for the documents and outputs directories.\\n\\n## 7. Link to Usage Instructions (if available)\\n- Refer to additional documentation or usage sections for setup, configuration, and running the utility if necessary.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"distilled_essence\": {\n    \"name\": \"RigOfficeDownloader Utility\",\n    \"status\": \"Work in progress\",\n    \"purpose\": \"Automate and streamline retrieval of project documentation (e.g., drawings, project info) from NOV's RigOffice (https://rigdoc.nov.com), letting 3D engineers focus on modeling rather than manual document gathering.\",\n    \"process_overview\": [\n      \"Start from a central Excel data source (e.g., R4511 - Internkontroll (*********-Rev.0).xlsx).\",\n      \"Extract key metadata (Case No, Equipment, Drawing, Revision, etc.).\",\n      \"Construct RigOffice search URLs based on these fields.\",\n      \"Automate document metadata scraping, user review, file selection, and downloads in a sequenced workflow.\"\n    ],\n    \"workflow_steps\": [\n      \"Fetch Documents: Scrape document metadata using search URLs; store as <rig>-a-docs.json.\",\n      \"Export to Markdown: Output document list as <rig>-a-docs.md; user marks item_include=true for target docs.\",\n      \"Import Updates: Apply user selections from Markdown back to JSON.\",\n      \"Fetch Files: Scrape file metadata for included docs; save as <rig>-b-files.json; files default to item_download=false.\",\n      \"Export File List: Output files to <rig>-b-files.md; user marks files to download with item_download=true.\",\n      \"Import File Selections: Sync user changes from Markdown to JSON.\",\n      \"Download Files: Download files marked item_download=true to organized output folders, using systematic file naming.\"\n    ],\n    \"core_features\": [\n      \"Automates all metadata collection and downloads via Selenium.\",\n      \"Intermediate state saved in JSON; user-friendly review/edit in Markdown.\",\n      \"Supports both broad (rig-level) and targeted document searches.\",\n      \"Systematic file naming, subfolder creation, deduplication, and path sanitization.\",\n      \"Incremental, modular steps with progress feedback and error handling.\",\n      \"Interactive menu for step selection and configuration.\"\n    ],\n    \"current_prototype\": {\n      \"functional\": true,\n      \"3-step workflow\": true,\n      \"user intervention points\": \"Markdown review/selection between steps\"\n    },\n    \"next_steps\": [\n      \"Separate JSON files for different search types.\",\n      \"Implement file hash checking to skip redundant downloads.\",\n      \"Enhance progress feedback.\"\n    ],\n    \"success_metrics\": [\n      \"75%+ reduction in document gathering time.\",\n      \"Reliable and complete retrieval.\",\n      \"Streamlined file organization aligned with engineering workflow.\"\n    ],\n    \"tech_stack\": [\n      \"Python (single-file, modular).\",\n      \"Selenium with Chrome WebDriver.\",\n      \"JSON/Markdown for state and user editing.\"\n    ],\n    \"example_directory_structure\": {\n      \"docs\": [\n        \"C:/Vault/Norway Simulation/Rigs/R4511-R5385-StenaIceMAX/docs/R4511-Internkontroll(*********-Rev.0).xlsx\"\n      ],\n      \"outputs\": [\n        \"outputs/data/<rig>-a-docs.json\",\n        \"outputs/data/<rig>-a-docs.md\",\n        \"outputs/data/<rig>-b-files.json\",\n        \"outputs/data/<rig>-b-files.md\",\n        \"outputs/downloads/<rig>/\"\n      ]\n    },\n    \"usage_reference\": \"See additional documentation or usage sections for instructions on setup and running the utility.\"\n  }\n}",
          "cost": 0.000000
        }
      }
    },
      {
      "instruction": "# Result Combiner\n\nYou are a specialized aggregator that combines results from multiple instruction steps into a cohesive output. Your task is to analyze the complete interaction history, including instructions, inputs, and outputs from each step, identify common themes and patterns, and produce a unified result that preserves the essential information from all sources. Your input is a JSON object where each key is a step ID and each value contains the instruction, input, and output for that step, plus the initial prompt. Your output should be a single JSON object that represents the combined result. Execute as:\n\n`{role=result_combiner;input=[interaction_history:json];process=[analyze_complete_interaction_history(),identify_common_patterns(),extract_key_elements(),merge_into_cohesive_whole(),format_as_unified_output()];constraints=[preserve_essential_information(),maintain_logical_structure(),avoid_redundancy()];requirements=[output_as_single_json(),ensure_completeness(),prioritize_clarity()];output={combined_result:json}}`",
      "step": "a",
      "title": "Result Combiner",
      "input": "{\n  \"initial_prompt\": \"# GOAL: TRANSFORM INTO `README.md`\\n\\n\\n# Status\\n\\nWork in progress\\n\\n## Konsept/Prosess\\n\\nVerkt\\u00f8y for \\\"automatisk\\\" innhenting av dokumentasjon via. https://rigdoc.nov.com (prosjektinfo, tegninger, osv).\\n\\n\\n### 1. Jobbe utifra \\\"R4511 - Internkontroll (*********-Rev.0).xlsx\\\"\\n\\n    `\\\"C:\\\\Vault\\\\Norway Simulation\\\\Rigs\\\\R4511-R5385-StenaIceMAX\\\\docs\\\\R4511-Internkontroll(*********-Rev.0).xlsx\\\"`\\n    \\u251c\\u2500\\u2500 ---\\n    \\u2514\\u2500\\u2500 `C:\\\\Vault\\\\Norway Simulation\\\\`\\n        \\u2514\\u2500\\u2500 `Rigs\\\\R4511 - R5385 - Stena IceMAX\\\\Documents`\\n            \\u2514\\u2500\\u2500 `IMPORTANT - Internal Audit - Qualification Record for Simulator 3D Development`\\n                \\u2502\\n                \\u2514\\u2500\\u2500 `R4511 - Internkontroll (*********-Rev.0).xlsx`\\n\\n\\n### 2. Samler inn essensiell info\\n\\n| Case No           | Equipment                                   | GA drawing             | GA rev.   | Verified by  | Comment        |\\n| ----------------- | ------------------------------------------- | ---------------------- | --------- | ------------ | -------------- |\\n| EQ-28209-104A     | Cylinder Hoisting Rig, 1250st, 48m          | 19129153-GAD           | 01        |              | 29273-106      |\\n| EQ-28209-104A     | Sheave Cluster Cylinder Rig 1250            | 19129152-GAD           | 02        |              | 29273-106      |\\n| EQ-28209-106A     | Top Drive, 1250t AC                         | 19140396-GAD           | 01        |              | 29273-106      |\\n| EQ-28209-120A     | Power Slip 1500 ton                         | DD-10141101-605        | 02        |              | 29273-106      |\\n| V6056             | Iron Roughneck-Hydratong MPT-200            | V6056-D1100-G0001      | 5         |              | 29273-106      |\\n| V6051             | Tubular Chute, Main                         | V6051-D1195-G0002      | 2         |              | 29273-107      |\\n| V6045             | Fingerboards                                | V6045-D1202-G0001      | 03A       |              | 29273-107      |\\n| V6042             | Hydraracker IV, Main                        | V6042-D1213-G0001      | 0         |              | 29273-107      |\\n| V6054             | Pipe guide, main under drillfloor           | V6054-D1194-G0002      | 3         |              | 29273-107      |\\n| EQ-28209-103A     | Elevated Backup Tong; EBT-150               | 1906283-GAD            | 03        |              | 29273-107      |\\n\\n\\n### 3. RigOffice s\\u00f8kestrenger\\n\\n    `\\\"search_urls\\\"`\\n    \\u251c\\u2500\\u2500 ---\\n    \\u251c\\u2500\\u2500 `https://rigdoc.nov.com/search/advancedsearch?q=`\\n    \\u2502   \\u2502\\n    \\u2502   \\u2514\\u2500\\u2500 \\\"EQ-28209-104A\\\" -> `&CaseNo=EQ-28209-104A&DocTypeId=66`\\n    \\u2502\\n    \\u2514\\u2500\\u2500 `https://rigdoc.nov.com/search?q=`\\n        \\u2502\\n        \\u251c\\u2500\\u2500 \\\"19129153-GAD\\\" -> `19129153-GAD&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"19129152-GAD\\\" -> `19129152-GAD&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"19140396-GAD\\\" -> `19140396-GAD&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"DD-10141101-605\\\" -> `DD-10141101-605&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"V6056-D1100-G0001\\\" -> `V6056-D1100-G0001&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"V6051-D1195-G0002\\\" -> `V6051-D1195-G0002&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"V6045-D1202-G0001\\\" -> `V6045-D1202-G0001&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"V6042-D1213-G0001\\\" -> `V6042-D1213-G0001&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"V6054-D1194-G0002\\\" -> `V6054-D1194-G0002&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"19066283-GAD\\\" -> `19066283-GAD&CaseType=Equipment`\\n        \\u2514\\u2500\\u2500 \\\"29273-106\\\" -> `29273-106&CaseType=Task / Deliverable`\\n\\n---\\n\\n\\n## Intent\\nTo automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\\n\\n## Key Project Aspects\\n\\n### Primary Problem\\nEngineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck\\u2014it's the manual preparation process.\\n\\n### Solution Approach\\nA Python-based utility that:\\n1. Automatically scrapes document metadata from RigOffice\\n2. Extracts file information from those documents\\n3. Downloads and organizes selected files based on user criteria\\n\\n### Current State\\nFunctional working prototype that:\\n- Uses a 3-step workflow (document metadata \\u2192 file metadata \\u2192 download)\\n- Stores intermediate results in JSON format\\n- Allows user intervention between steps\\n- Provides progress feedback\\n\\n### Critical Next Steps\\n1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches\\n2. **Implement file hash checking** to prevent redundant downloads\\n3. **Improve progress visibility** during lengthy scraping operations\\n\\n### Core Technical Pattern\\nA single-file, modular approach using:\\n- Selenium for browser automation\\n- JSON for data storage\\n- Three-stage processing with user control points\\n- Incremental updates to avoid redundant work\\n\\n### Key Success Metrics\\n- Reduce documentation gathering time by 75%+\\n- Ensure reliable retrieval of required documentation\\n- Organize files in a way that streamlines workflow\\n- Support both broad searches (by rig number) and targeted searches (by specific documents)\\n\\n\\n---\\n\\n\\n### RigOfficeDownloader Utility Workflow\\n\\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow is clearly defined in the docstring at the beginning of the main script and follows these steps:\\n\\n1. Fetch Documents\\n- The utility starts by scraping document metadata from predefined search URLs\\n- Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory\\n- Each document entry includes metadata like title, document number, revision, etc.\\n- All documents are initially marked with item_include=False\\n- Each document gets an item_generated_name for better identification\\n\\n2. Export Documents to Markdown\\n- The JSON data is exported to a Markdown table: <rig>-a-docs.md\\n- This allows the user to easily review and edit which documents to include\\n- The user is expected to edit the Markdown file and set item_include=true for desired documents\\n\\n3. Import Updated Document Data\\n- After the user edits the Markdown file, the utility imports the changes back to the JSON file\\n- This updates which documents are marked for file retrieval\\n\\n4. Fetch Files for Selected Documents\\n- For each document with item_include=true, the utility scrapes file metadata\\n- File data is saved to <rig>-b-files.json\\n- Each file is initially marked with item_download=False\\n- Files inherit the document's item_generated_name with additional identifiers\\n\\n5. Export Files to Markdown\\n- The file data is exported to a Markdown table: <rig>-b-files.md\\n- The user reviews and edits which files to download by setting item_download=true\\n\\n6. Import Updated File Data\\n- After editing, the utility imports the changes back to the JSON file\\n- This updates which files are marked for download\\n\\n7. Download Selected Files\\n- Files with item_download=true are downloaded\\n- Files are named according to their item_generated_name + extension\\n- The utility supports creating subfolders based on '/' in the item_generated_name\\n- Files are saved to the outputs/downloads/<rig> directory\\n\\nInteractive Menu\\n- The utility provides an interactive menu where the user can choose which steps to execute\\n- This allows for flexibility in the workflow, enabling the user to run specific steps as needed\\n- The user can also update the rig number and search URLs through this menu\\n\\nKey Features\\n- Automatic document and file metadata scraping\\n- User-friendly Markdown editing interface\\n- Customizable file naming with item_generated_name\\n- Support for subfolder organization in downloads\\n- Deduplication of documents and files\\n- Configurable field ordering for JSON and Markdown exports\\n\\nTechnical Implementation\\n- Uses Selenium with Chrome WebDriver for web scraping\\n- Implements smart waiting strategies for page loading\\n- Handles browser sessions with proper cleanup\\n- Provides progress feedback during operations\\n- Sanitizes filenames for valid paths\",\n  \"a\": {\n    \"instruction\": \"# Instruction Converter\\n\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\n\\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n    \"input\": \"# GOAL: TRANSFORM INTO `README.md`\\n\\n\\n# Status\\n\\nWork in progress\\n\\n## Konsept/Prosess\\n\\nVerkt\\u00f8y for \\\"automatisk\\\" innhenting av dokumentasjon via. https://rigdoc.nov.com (prosjektinfo, tegninger, osv).\\n\\n\\n### 1. Jobbe utifra \\\"R4511 - Internkontroll (*********-Rev.0).xlsx\\\"\\n\\n    `\\\"C:\\\\Vault\\\\Norway Simulation\\\\Rigs\\\\R4511-R5385-StenaIceMAX\\\\docs\\\\R4511-Internkontroll(*********-Rev.0).xlsx\\\"`\\n    \\u251c\\u2500\\u2500 ---\\n    \\u2514\\u2500\\u2500 `C:\\\\Vault\\\\Norway Simulation\\\\`\\n        \\u2514\\u2500\\u2500 `Rigs\\\\R4511 - R5385 - Stena IceMAX\\\\Documents`\\n            \\u2514\\u2500\\u2500 `IMPORTANT - Internal Audit - Qualification Record for Simulator 3D Development`\\n                \\u2502\\n                \\u2514\\u2500\\u2500 `R4511 - Internkontroll (*********-Rev.0).xlsx`\\n\\n\\n### 2. Samler inn essensiell info\\n\\n| Case No           | Equipment                                   | GA drawing             | GA rev.   | Verified by  | Comment        |\\n| ----------------- | ------------------------------------------- | ---------------------- | --------- | ------------ | -------------- |\\n| EQ-28209-104A     | Cylinder Hoisting Rig, 1250st, 48m          | 19129153-GAD           | 01        |              | 29273-106      |\\n| EQ-28209-104A     | Sheave Cluster Cylinder Rig 1250            | 19129152-GAD           | 02        |              | 29273-106      |\\n| EQ-28209-106A     | Top Drive, 1250t AC                         | 19140396-GAD           | 01        |              | 29273-106      |\\n| EQ-28209-120A     | Power Slip 1500 ton                         | DD-10141101-605        | 02        |              | 29273-106      |\\n| V6056             | Iron Roughneck-Hydratong MPT-200            | V6056-D1100-G0001      | 5         |              | 29273-106      |\\n| V6051             | Tubular Chute, Main                         | V6051-D1195-G0002      | 2         |              | 29273-107      |\\n| V6045             | Fingerboards                                | V6045-D1202-G0001      | 03A       |              | 29273-107      |\\n| V6042             | Hydraracker IV, Main                        | V6042-D1213-G0001      | 0         |              | 29273-107      |\\n| V6054             | Pipe guide, main under drillfloor           | V6054-D1194-G0002      | 3         |              | 29273-107      |\\n| EQ-28209-103A     | Elevated Backup Tong; EBT-150               | 1906283-GAD            | 03        |              | 29273-107      |\\n\\n\\n### 3. RigOffice s\\u00f8kestrenger\\n\\n    `\\\"search_urls\\\"`\\n    \\u251c\\u2500\\u2500 ---\\n    \\u251c\\u2500\\u2500 `https://rigdoc.nov.com/search/advancedsearch?q=`\\n    \\u2502   \\u2502\\n    \\u2502   \\u2514\\u2500\\u2500 \\\"EQ-28209-104A\\\" -> `&CaseNo=EQ-28209-104A&DocTypeId=66`\\n    \\u2502\\n    \\u2514\\u2500\\u2500 `https://rigdoc.nov.com/search?q=`\\n        \\u2502\\n        \\u251c\\u2500\\u2500 \\\"19129153-GAD\\\" -> `19129153-GAD&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"19129152-GAD\\\" -> `19129152-GAD&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"19140396-GAD\\\" -> `19140396-GAD&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"DD-10141101-605\\\" -> `DD-10141101-605&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"V6056-D1100-G0001\\\" -> `V6056-D1100-G0001&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"V6051-D1195-G0002\\\" -> `V6051-D1195-G0002&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"V6045-D1202-G0001\\\" -> `V6045-D1202-G0001&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"V6042-D1213-G0001\\\" -> `V6042-D1213-G0001&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"V6054-D1194-G0002\\\" -> `V6054-D1194-G0002&CaseType=Equipment`\\n        \\u251c\\u2500\\u2500 \\\"19066283-GAD\\\" -> `19066283-GAD&CaseType=Equipment`\\n        \\u2514\\u2500\\u2500 \\\"29273-106\\\" -> `29273-106&CaseType=Task / Deliverable`\\n\\n---\\n\\n\\n## Intent\\nTo automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\\n\\n## Key Project Aspects\\n\\n### Primary Problem\\nEngineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck\\u2014it's the manual preparation process.\\n\\n### Solution Approach\\nA Python-based utility that:\\n1. Automatically scrapes document metadata from RigOffice\\n2. Extracts file information from those documents\\n3. Downloads and organizes selected files based on user criteria\\n\\n### Current State\\nFunctional working prototype that:\\n- Uses a 3-step workflow (document metadata \\u2192 file metadata \\u2192 download)\\n- Stores intermediate results in JSON format\\n- Allows user intervention between steps\\n- Provides progress feedback\\n\\n### Critical Next Steps\\n1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches\\n2. **Implement file hash checking** to prevent redundant downloads\\n3. **Improve progress visibility** during lengthy scraping operations\\n\\n### Core Technical Pattern\\nA single-file, modular approach using:\\n- Selenium for browser automation\\n- JSON for data storage\\n- Three-stage processing with user control points\\n- Incremental updates to avoid redundant work\\n\\n### Key Success Metrics\\n- Reduce documentation gathering time by 75%+\\n- Ensure reliable retrieval of required documentation\\n- Organize files in a way that streamlines workflow\\n- Support both broad searches (by rig number) and targeted searches (by specific documents)\\n\\n\\n---\\n\\n\\n### RigOfficeDownloader Utility Workflow\\n\\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow is clearly defined in the docstring at the beginning of the main script and follows these steps:\\n\\n1. Fetch Documents\\n- The utility starts by scraping document metadata from predefined search URLs\\n- Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory\\n- Each document entry includes metadata like title, document number, revision, etc.\\n- All documents are initially marked with item_include=False\\n- Each document gets an item_generated_name for better identification\\n\\n2. Export Documents to Markdown\\n- The JSON data is exported to a Markdown table: <rig>-a-docs.md\\n- This allows the user to easily review and edit which documents to include\\n- The user is expected to edit the Markdown file and set item_include=true for desired documents\\n\\n3. Import Updated Document Data\\n- After the user edits the Markdown file, the utility imports the changes back to the JSON file\\n- This updates which documents are marked for file retrieval\\n\\n4. Fetch Files for Selected Documents\\n- For each document with item_include=true, the utility scrapes file metadata\\n- File data is saved to <rig>-b-files.json\\n- Each file is initially marked with item_download=False\\n- Files inherit the document's item_generated_name with additional identifiers\\n\\n5. Export Files to Markdown\\n- The file data is exported to a Markdown table: <rig>-b-files.md\\n- The user reviews and edits which files to download by setting item_download=true\\n\\n6. Import Updated File Data\\n- After editing, the utility imports the changes back to the JSON file\\n- This updates which files are marked for download\\n\\n7. Download Selected Files\\n- Files with item_download=true are downloaded\\n- Files are named according to their item_generated_name + extension\\n- The utility supports creating subfolders based on '/' in the item_generated_name\\n- Files are saved to the outputs/downloads/<rig> directory\\n\\nInteractive Menu\\n- The utility provides an interactive menu where the user can choose which steps to execute\\n- This allows for flexibility in the workflow, enabling the user to run specific steps as needed\\n- The user can also update the rig number and search URLs through this menu\\n\\nKey Features\\n- Automatic document and file metadata scraping\\n- User-friendly Markdown editing interface\\n- Customizable file naming with item_generated_name\\n- Support for subfolder organization in downloads\\n- Deduplication of documents and files\\n- Configurable field ordering for JSON and Markdown exports\\n\\nTechnical Implementation\\n- Uses Selenium with Chrome WebDriver for web scraping\\n- Implements smart waiting strategies for page loading\\n- Handles browser sessions with proper cleanup\\n- Provides progress feedback during operations\\n- Sanitizes filenames for valid paths\",\n    \"output\": \"{\\n  \\\"instruction_format\\\": \\\"# Create a README.md file for the RigOfficeDownloader Utility\\\\n\\\\n## 1. State Current Project Status\\\\n- Indicate that the project is a work in progress.\\\\n\\\\n## 2. Summarize Concept and Process\\\\n- Describe the tool as a utility for automatic retrieval of documentation such as project information and drawings from https://rigdoc.nov.com.\\\\n\\\\n### Step 1: Identify Source Data\\\\n- Specify the starting point as the Excel file, `R4511 - Internkontroll (*********-Rev.0).xlsx`, located in the designated directory structure.\\\\n\\\\n### Step 2: Collect Essential Information\\\\n- Present an example table showing key metadata fields: Case No, Equipment, GA drawing, GA rev., Verified by, Comment.\\\\n\\\\n### Step 3: Define RigOffice Search Strings\\\\n- List exemplary search URLs for both case number and equipment/document identifiers, mapping metadata fields to search query strings as shown.\\\\n\\\\n## 3. Clarify Project Intent\\\\n- Articulate that the intent is to automate and streamline the document retrieval process in NOV's RigOffice, enabling 3D engineers to focus on modeling work by minimizing manual document gathering.\\\\n\\\\n## 4. Highlight Key Project Aspects\\\\n- Describe the primary problem: time wasted by engineers navigating complex applications for documents.\\\\n- Summarize the Python-based solution approach:\\\\n  * Automatically scrape document metadata from RigOffice.\\\\n  * Extract file information and download selected files.\\\\n  * Organize downloads based on user-defined criteria.\\\\n- Outline the current project state:\\\\n  * Functional prototype using a three-step workflow (document metadata \\u2192 file metadata \\u2192 download).\\\\n  * Store intermediate data in JSON files.\\\\n  * Allow user review/editing between steps (Markdown files).\\\\n  * Provide progress feedback.\\\\n- List critical next steps:\\\\n  1. Separate JSON files for different search types to prevent unwanted merges.\\\\n  2. Implement file hash checking to avoid redundant downloads.\\\\n  3. Improve progress visibility during scraping.\\\\n- Define the core technical pattern:\\\\n  * Single-file, modular design using Selenium for automation.\\\\n  * Use JSON for storing state, incremental updates, and Markdown interfaces.\\\\n  * Support both broad and targeted searches.\\\\n- Specify key success metrics such as reducing documentation time by 75%+, ensuring reliable retrieval, and streamlining file organization for 3D workflow.\\\\n\\\\n## 5. Document Utility Workflow in Detail\\\\n- Outline the sequential workflow steps:\\\\n  1. Fetch Documents: Scrape document metadata from search URLs; save to `<rig>-a-docs.json`.\\\\n  2. Export Documents to Markdown: Export JSON to Markdown (`<rig>-a-docs.md`) for user review; instruct user to mark desired documents with `item_include=true`.\\\\n  3. Import Updated Document Data: Parse edited Markdown, update JSON accordingly.\\\\n  4. Fetch Files for Selected Documents: Scrape file metadata for included documents, save results to `<rig>-b-files.json`, mark files as `item_download=false` by default.\\\\n  5. Export Files to Markdown: Export file list to Markdown (`<rig>-b-files.md`); instruct user to mark files for download with `item_download=true`.\\\\n  6. Import Updated File Data: Read user's selections from Markdown, update JSON.\\\\n  7. Download Selected Files: Download files marked for download, use `item_generated_name` for file naming, create subfolders as needed, and save to `outputs/downloads/<rig>`.\\\\n\\\\n- Describe the interactive menu:\\\\n  * Present users with options for executing specific workflow steps or updating search configuration as needed.\\\\n\\\\n- List key features:\\\\n  * Automated metadata scraping via Selenium\\\\n  * User-friendly review and selection through Markdown exports/edits\\\\n  * Systematic file naming and organization\\\\n  * Deduplication of documents and files\\\\n  * Configurable field ordering for exports\\\\n  * Progress feedback and error handling\\\\n\\\\n- Summarize technical implementation:\\\\n  * Use Selenium with Chrome WebDriver\\\\n  * Implement smart waits and session cleanup\\\\n  * Sanitize filenames for path validity\\\\n\\\\n## 6. Provide Directory Structure Example\\\\n- Include sample structure for the documents and outputs directories.\\\\n\\\\n## 7. Link to Usage Instructions (if available)\\\\n- Refer to additional documentation or usage sections for setup, configuration, and running the utility if necessary.\\\"\\n}\"\n  },\n  \"b\": {\n    \"instruction\": \"# Essence Distillation\\n\\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\\n\\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\",\n    \"input\": \"{\\n  \\\"instruction_format\\\": \\\"# Create a README.md file for the RigOfficeDownloader Utility\\\\n\\\\n## 1. State Current Project Status\\\\n- Indicate that the project is a work in progress.\\\\n\\\\n## 2. Summarize Concept and Process\\\\n- Describe the tool as a utility for automatic retrieval of documentation such as project information and drawings from https://rigdoc.nov.com.\\\\n\\\\n### Step 1: Identify Source Data\\\\n- Specify the starting point as the Excel file, `R4511 - Internkontroll (*********-Rev.0).xlsx`, located in the designated directory structure.\\\\n\\\\n### Step 2: Collect Essential Information\\\\n- Present an example table showing key metadata fields: Case No, Equipment, GA drawing, GA rev., Verified by, Comment.\\\\n\\\\n### Step 3: Define RigOffice Search Strings\\\\n- List exemplary search URLs for both case number and equipment/document identifiers, mapping metadata fields to search query strings as shown.\\\\n\\\\n## 3. Clarify Project Intent\\\\n- Articulate that the intent is to automate and streamline the document retrieval process in NOV's RigOffice, enabling 3D engineers to focus on modeling work by minimizing manual document gathering.\\\\n\\\\n## 4. Highlight Key Project Aspects\\\\n- Describe the primary problem: time wasted by engineers navigating complex applications for documents.\\\\n- Summarize the Python-based solution approach:\\\\n  * Automatically scrape document metadata from RigOffice.\\\\n  * Extract file information and download selected files.\\\\n  * Organize downloads based on user-defined criteria.\\\\n- Outline the current project state:\\\\n  * Functional prototype using a three-step workflow (document metadata \\u2192 file metadata \\u2192 download).\\\\n  * Store intermediate data in JSON files.\\\\n  * Allow user review/editing between steps (Markdown files).\\\\n  * Provide progress feedback.\\\\n- List critical next steps:\\\\n  1. Separate JSON files for different search types to prevent unwanted merges.\\\\n  2. Implement file hash checking to avoid redundant downloads.\\\\n  3. Improve progress visibility during scraping.\\\\n- Define the core technical pattern:\\\\n  * Single-file, modular design using Selenium for automation.\\\\n  * Use JSON for storing state, incremental updates, and Markdown interfaces.\\\\n  * Support both broad and targeted searches.\\\\n- Specify key success metrics such as reducing documentation time by 75%+, ensuring reliable retrieval, and streamlining file organization for 3D workflow.\\\\n\\\\n## 5. Document Utility Workflow in Detail\\\\n- Outline the sequential workflow steps:\\\\n  1. Fetch Documents: Scrape document metadata from search URLs; save to `<rig>-a-docs.json`.\\\\n  2. Export Documents to Markdown: Export JSON to Markdown (`<rig>-a-docs.md`) for user review; instruct user to mark desired documents with `item_include=true`.\\\\n  3. Import Updated Document Data: Parse edited Markdown, update JSON accordingly.\\\\n  4. Fetch Files for Selected Documents: Scrape file metadata for included documents, save results to `<rig>-b-files.json`, mark files as `item_download=false` by default.\\\\n  5. Export Files to Markdown: Export file list to Markdown (`<rig>-b-files.md`); instruct user to mark files for download with `item_download=true`.\\\\n  6. Import Updated File Data: Read user's selections from Markdown, update JSON.\\\\n  7. Download Selected Files: Download files marked for download, use `item_generated_name` for file naming, create subfolders as needed, and save to `outputs/downloads/<rig>`.\\\\n\\\\n- Describe the interactive menu:\\\\n  * Present users with options for executing specific workflow steps or updating search configuration as needed.\\\\n\\\\n- List key features:\\\\n  * Automated metadata scraping via Selenium\\\\n  * User-friendly review and selection through Markdown exports/edits\\\\n  * Systematic file naming and organization\\\\n  * Deduplication of documents and files\\\\n  * Configurable field ordering for exports\\\\n  * Progress feedback and error handling\\\\n\\\\n- Summarize technical implementation:\\\\n  * Use Selenium with Chrome WebDriver\\\\n  * Implement smart waits and session cleanup\\\\n  * Sanitize filenames for path validity\\\\n\\\\n## 6. Provide Directory Structure Example\\\\n- Include sample structure for the documents and outputs directories.\\\\n\\\\n## 7. Link to Usage Instructions (if available)\\\\n- Refer to additional documentation or usage sections for setup, configuration, and running the utility if necessary.\\\"\\n}\",\n    \"output\": \"{\\n  \\\"distilled_essence\\\": {\\n    \\\"name\\\": \\\"RigOfficeDownloader Utility\\\",\\n    \\\"status\\\": \\\"Work in progress\\\",\\n    \\\"purpose\\\": \\\"Automate and streamline retrieval of project documentation (e.g., drawings, project info) from NOV's RigOffice (https://rigdoc.nov.com), letting 3D engineers focus on modeling rather than manual document gathering.\\\",\\n    \\\"process_overview\\\": [\\n      \\\"Start from a central Excel data source (e.g., R4511 - Internkontroll (*********-Rev.0).xlsx).\\\",\\n      \\\"Extract key metadata (Case No, Equipment, Drawing, Revision, etc.).\\\",\\n      \\\"Construct RigOffice search URLs based on these fields.\\\",\\n      \\\"Automate document metadata scraping, user review, file selection, and downloads in a sequenced workflow.\\\"\\n    ],\\n    \\\"workflow_steps\\\": [\\n      \\\"Fetch Documents: Scrape document metadata using search URLs; store as <rig>-a-docs.json.\\\",\\n      \\\"Export to Markdown: Output document list as <rig>-a-docs.md; user marks item_include=true for target docs.\\\",\\n      \\\"Import Updates: Apply user selections from Markdown back to JSON.\\\",\\n      \\\"Fetch Files: Scrape file metadata for included docs; save as <rig>-b-files.json; files default to item_download=false.\\\",\\n      \\\"Export File List: Output files to <rig>-b-files.md; user marks files to download with item_download=true.\\\",\\n      \\\"Import File Selections: Sync user changes from Markdown to JSON.\\\",\\n      \\\"Download Files: Download files marked item_download=true to organized output folders, using systematic file naming.\\\"\\n    ],\\n    \\\"core_features\\\": [\\n      \\\"Automates all metadata collection and downloads via Selenium.\\\",\\n      \\\"Intermediate state saved in JSON; user-friendly review/edit in Markdown.\\\",\\n      \\\"Supports both broad (rig-level) and targeted document searches.\\\",\\n      \\\"Systematic file naming, subfolder creation, deduplication, and path sanitization.\\\",\\n      \\\"Incremental, modular steps with progress feedback and error handling.\\\",\\n      \\\"Interactive menu for step selection and configuration.\\\"\\n    ],\\n    \\\"current_prototype\\\": {\\n      \\\"functional\\\": true,\\n      \\\"3-step workflow\\\": true,\\n      \\\"user intervention points\\\": \\\"Markdown review/selection between steps\\\"\\n    },\\n    \\\"next_steps\\\": [\\n      \\\"Separate JSON files for different search types.\\\",\\n      \\\"Implement file hash checking to skip redundant downloads.\\\",\\n      \\\"Enhance progress feedback.\\\"\\n    ],\\n    \\\"success_metrics\\\": [\\n      \\\"75%+ reduction in document gathering time.\\\",\\n      \\\"Reliable and complete retrieval.\\\",\\n      \\\"Streamlined file organization aligned with engineering workflow.\\\"\\n    ],\\n    \\\"tech_stack\\\": [\\n      \\\"Python (single-file, modular).\\\",\\n      \\\"Selenium with Chrome WebDriver.\\\",\\n      \\\"JSON/Markdown for state and user editing.\\\"\\n    ],\\n    \\\"example_directory_structure\\\": {\\n      \\\"docs\\\": [\\n        \\\"C:/Vault/Norway Simulation/Rigs/R4511-R5385-StenaIceMAX/docs/R4511-Internkontroll(*********-Rev.0).xlsx\\\"\\n      ],\\n      \\\"outputs\\\": [\\n        \\\"outputs/data/<rig>-a-docs.json\\\",\\n        \\\"outputs/data/<rig>-a-docs.md\\\",\\n        \\\"outputs/data/<rig>-b-files.json\\\",\\n        \\\"outputs/data/<rig>-b-files.md\\\",\\n        \\\"outputs/downloads/<rig>/\\\"\\n      ]\\n    },\\n    \\\"usage_reference\\\": \\\"See additional documentation or usage sections for instructions on setup and running the utility.\\\"\\n  }\\n}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"combined_result\": {\n    \"name\": \"RigOfficeDownloader Utility\",\n    \"status\": \"Work in progress\",\n    \"purpose\": \"Automate and streamline retrieval of project documentation (e.g., drawings, project info) from NOV's RigOffice (https://rigdoc.nov.com), letting 3D engineers focus on modeling rather than manual document gathering.\",\n    \"process_overview\": [\n      \"Start from a central Excel data source (e.g., R4511 - Internkontroll (*********-Rev.0).xlsx) in a structured directory.\",\n      \"Extract key metadata (Case No, Equipment, Drawing, Revision, etc.) and produce a reference table.\",\n      \"Construct RigOffice search URLs based on extracted fields, mapping Case No and document identifiers to query strings.\",\n      \"Automate document metadata scraping, user review, file selection, and downloads in a sequenced, user-controlled workflow.\"\n    ],\n    \"workflow_steps\": [\n      \"Fetch Documents: Scrape document metadata from predefined search URLs; store as <rig>-a-docs.json.\",\n      \"Export to Markdown: Export the JSON document list as <rig>-a-docs.md for user review; user sets item_include=true for desired docs.\",\n      \"Import Updates: Re-import edited Markdown to update item_include flags in JSON.\",\n      \"Fetch Files: For included docs, scrape file metadata and save as <rig>-b-files.json; all files default to item_download=false.\",\n      \"Export File List: Output file list as <rig>-b-files.md for user review; mark files for download with item_download=true.\",\n      \"Import File Selections: Sync changes from Markdown to JSON.\",\n      \"Download Files: Download files marked item_download=true to organized output folders, using systematic naming and supporting subfolders.\"\n    ],\n    \"interactive_menu\": \"Provides an interactive menu for executing specific workflow steps or updating search configuration as needed.\",\n    \"core_features\": [\n      \"Automated metadata collection and document/file downloads via Selenium.\",\n      \"Intermediate state management with JSON; review and selection via Markdown.\",\n      \"Supports broad (rig-level) and targeted (document-specific) searches.\",\n      \"Systematic file naming, subfolder support, deduplication, and filename sanitization.\",\n      \"Progress feedback, error handling, and modular step-by-step execution.\",\n      \"Configurable field ordering for exports.\"\n    ],\n    \"current_prototype\": {\n      \"functional\": true,\n      \"workflow\": \"Three-step (document metadata \u2192 file metadata \u2192 download) with user review and editing at each stage.\",\n      \"user_intervention_points\": \"User reviews/marks selections in Markdown files between workflow steps.\"\n    },\n    \"next_steps\": [\n      \"Use separate JSON files for different search approaches to prevent unintended data merging.\",\n      \"Implement file hash checking to skip redundant downloads.\",\n      \"Enhance visibility of operation progress during lengthy scraping tasks.\"\n    ],\n    \"success_metrics\": [\n      \"Reduce documentation gathering time by 75% or more.\",\n      \"Ensure reliable and complete retrieval of documentation.\",\n      \"Organize files to streamline downstream engineering workflow.\"\n    ],\n    \"tech_stack\": [\n      \"Python (single-file, modular utility).\",\n      \"Selenium with Chrome WebDriver.\",\n      \"JSON and Markdown for data storage and user interaction.\"\n    ],\n    \"example_directory_structure\": {\n      \"docs\": [\n        \"C:/Vault/Norway Simulation/Rigs/R4511-R5385-StenaIceMAX/docs/R4511-Internkontroll(*********-Rev.0).xlsx\"\n      ],\n      \"outputs\": [\n        \"outputs/data/<rig>-a-docs.json\",\n        \"outputs/data/<rig>-a-docs.md\",\n        \"outputs/data/<rig>-b-files.json\",\n        \"outputs/data/<rig>-b-files.md\",\n        \"outputs/downloads/<rig>/\"\n      ]\n    },\n    \"usage_reference\": \"Refer to additional documentation or usage sections for setup, configuration, and utility operation instructions.\"\n  }\n}",
          "cost": 0.000000
        }
      }
    }