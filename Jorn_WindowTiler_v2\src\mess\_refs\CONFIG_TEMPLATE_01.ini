[EXPLORER-WINDOW-ID:0000]
# hwnd = 787866
# z_index = 1
title = 
path = D:/1
show_state = SW_SHOW
always_on_top = False
position = (1920, 25)
size = (874, 520)

[EXPLORER-WINDOW-ID:0001]
hwnd = 117866
z_index = 2
title = 
path = D:/5
show_state = SW_SHOW
always_on_top = False
position = (1920, 25)
size = (874, 520)

[EXPLORER-WINDOW-ID:0002]
hwnd = 917528
z_index = 3
title = 
path = D:/2
show_state = SW_SHOW
always_on_top = False
position = (520, 225)
size = (500, 600)

# ----------------------------------------------
# NOTAT: ENDRE/LEGGE TIL FUNKSJONALITET
# ----------------------------------------------
DENNE BLIR FJERNET, SÅ ERSTATTER JEG DET HELLER VED Å LEGGE INN MULIGHET FOR Å EKSKLUDERE VINDUER,
ALTERNATIV 1: LEGG INN [INCLUDE/EXCLUDE] FOR HVERT VINDU, SÅ KAN MAN JUSTERE SELV
ALTERNATIV 2: HA EN EGEN "GRUPPE" FOR VINDUER SOM AKTIVT SKAL IGNORERES (IKKE LAGRES/LOADES)
# LAYOUT SAVE OPTIONS
# Retrieve path from "special" shell windows such as "Control Panel", "Recycle Bin", etc:
#  '1': No, shell windows without a normal path will be excluded
#  '2': Yes, try to retrieve/open open-command for "special" windows
saveLayout_includeSpecialFolders = 1


; ----------------------------------------------
; ----- SETTINGS: CONFIG-TEMPLATE (START) ------
; ----------------------------------------------
# GENERAL


; ----------------------------------------------

# LAYOUT SAVE OPTIONS
# Action to take when saving a layout and an existing layout file is found:
#  '0': No action taken (layout will not be saved to file)
#  '1': Replace existing file (without creating a backup)
#  '2': Backup existing file before replacing it
saveLayout_actionIfLayoutExists = 2


# LAYOUT LOAD OPTIONS
# Action to take on existing windows that match with windows in the layout to load:
#  '1': Use matching window
#  '2': Create new window
loadLayout_actionIfWindowExists = 1


# LAYOUT LOAD OPTIONS
# Action to take on existing windows which does not match any windows in the layout to load:
#  '1': Ignore window
#  '2': Close window
loadLayout_actionIfUnmatched = 1
; ----------------------------------------------
; ------- SETTINGS: CONFIG-TEMPLATE (END) ------
; ----------------------------------------------



; ----------------------------------------------
; --- EACH-LAYOUT-ITEM: WINDOW DATA (START) ----
; ----------------------------------------------
# EACH-ITEM: EXPLORER-WINDOW
# The text displayed in the title bar of the window
window_title = "This PC"
# The class of the window, determines the type of window being created
window_class = "CabinetWClass"
# The command used to create the window (via subprocess.Popen())
window_command = "file:///"
# Specifies the state of the window controls (1=NORMAL, 2=MINIMIZED, 3=MAXIMIZED)
win_controls_state = 0
# Specifies the visibility state of the window (0=HIDDEN, 1=VISIBLE)
win_display_state = 0
# Specifies the always-on-top state of the window (0:FALSE, 1:TRUE)
win_topmost_state = 0
# Specifies the X/Y position of the window (horizontal/vertical coordinates from top-left corner of the screen)
win_position = (0, 0)
# Specifies the X/Y size of the window (width/height coordinates)
win_size = (0, 0)
; ----------------------------------------------
; --- EACH-LAYOUT-ITEM: WINDOW DATA (END) ----
; ----------------------------------------------



IS_MATCH_IF:[TITLE_IDENTICAL AND COMMAND_IDENTICAL]

UNMATCHED_WINDOWS_ACTION:[CLOSE/IGNORE]



# Selected items:
current_instance_items_com_obj = current_window_instance.Document.SelectedItems()
current_instance_items_str_list = [current_instance_items_com_obj.Item(index) for index in range(current_instance_items_com_obj.Count)]
# View mode
current_instance_view_mode = current_window_instance.Document.CurrentViewMode
; 0: Extra large icons
; 1: Large icons
; 2: Medium icons
; 3: Small icons
; 4: List
; 5: Details
; 6: Thumbnails
; 7: Tiles
; 8: Content
window_view_mode = ?
# ---------------------------
# [EACH-ITEM]
# [LAYOUT-ITEMS]
# The text displayed in the title bar of the window
window_title = "This PC"
# The class of the window, determines the type of window being created
window_class = "CabinetWClass"
# The command used to create the window (via subprocess.Popen())
window_command = "file:///"
# Sets the state of the window controls (1=NORMAL, 2=MINIMIZED, 3=MAXIMIZED)
set_controls_state = 0
# Sets the visibility state of the window (0=HIDDEN, 1=VISIBLE)
set_display_state = 0
# Sets the always-on-top state of the window (0:FALSE, 1:TRUE)
set_topmost_state = 0
# Sets the X/Y position of the window (horizontal/vertical coordinates from top-left corner of the screen)
set_position = (0, 0)
# Sets the X/Y size of the window (width/height coordinates)
set_size = (0, 0)

; ---------------------------
; The text that is displayed in the title bar of a window
winTitle = This PC
; The command that executes the creation of the window through subprocess.Popen(winCreateCmd)
winCreateCmd = file:///
; The window class for the window
winClassType = CabinetWClass


; The text that is displayed in the title bar of the window
; Specifies the title of the window
winTitle = This PC
; The command used to create the window using subprocess.Popen()
; Executes the command to create the window
winCreateCmd = file:///
; The class of the window
; Determines the type of the window being created
winClassType = CabinetWClass
; Sets the state of the window controls (0:SKIP / 1:NORMAL / 2:MINIMIZED / 3:MAXIMIZED)
setShowState = 0
; Sets the visibility state of the window (0:SKIP / 1:HIDDEN / 2:VISIBLE)
setHidden = 0
; Sets the always-on-top state of the window (0:False | 1:True)
setTopmost= 0
; Sets the X/Y position of the window (horizontal/vertical coordinates from top-left corner of the screen)
setPosXY = (520, 225)
; Sets the X/Y size of the window (width/height coordinates)
setSizeXY = (500, 600)
; ---------------------------

; The text displayed in the title bar of the window
winTitleText = This PC
; The class of the window, determines the type of window being created
winClassName = CabinetWClass
; The command used to create the window (through 'subprocess.Popen()')
winCreateCmd = file:///
; Sets the state of the window controls (1=NORMAL, 2=MINIMIZED, 3=MAXIMIZED)
setControlsState = 0
; Sets the visibility state of the window (0=HIDDEN, 1=VISIBLE)
setDisplayState = 0
; Sets the always-on-top state of the window (0:FALSE, 1:TRUE)
setTopmostState = 0
; Sets the X/Y position of the window (horizontal/vertical coordinates from top-left corner of the screen)
setPosXY = (0, 0)
; Sets the X/Y size of the window (width/height coordinates)
setSizeXY = (0, 0)
; ----------------
# The text displayed in the title bar of the window
window_title = "This PC"
# The class of the window, determines the type of window being created
window_class = "CabinetWClass"
# The command used to create the window (through 'subprocess.Popen()')
window_create_command = "file:///"

# Sets the state of the window controls (1=NORMAL, 2=MINIMIZED, 3=MAXIMIZED)
window_controls_state = 0
# Sets the visibility state of the window (0=HIDDEN, 1=VISIBLE)
window_display_state = 0
# Sets the always-on-top state of the window (0:FALSE, 1:TRUE)
window_topmost_state = 0
# Sets the X/Y position of the window (horizontal/vertical coordinates from top-left corner of the screen)
window_position = (0, 0)
# Sets the X/Y size of the window (width/height coordinates)
window_size = (0, 0)

; ---------------------------


# Title of the window to be displayed in the title bar
window_title = "This PC"
# Class of the window, determines the type of window being created
window_class = "CabinetWClass"
# Command used to create the window using subprocess.Popen()
window_create_command = "file:///"

# State of the window controls (1=NORMAL, 2=MINIMIZED, 3=MAXIMIZED)
controls_state = 0
# Visibility state of the window (0=HIDDEN, 1=VISIBLE)
display_state = 0
# Always-on-top state of the window (0=FALSE, 1=TRUE)
topmost_state = 0
# X/Y position of the window (horizontal/vertical coordinates from top-left corner of the screen)
position = (0, 0)
# X/Y size of the window (width/height coordinates)
size = (0, 0)

; -----------------
; The text displayed in the title bar of the window
winTitle = This PC
; The class of the window, determines the type of window being created
winClass = CabinetWClass
; The command used to create the window (through 'subprocess.Popen()')
winCreateCommand = file:///

; Sets the state of the window controls (1=NORMAL, 2=MINIMIZED, 3=MAXIMIZED)
setControlsState = 0
; Sets the visibility state of the window (0=HIDDEN, 1=VISIBLE)
setDisplayState = 0
; Sets the always-on-top state of the window (0:FALSE, 1:TRUE)
setTopmostState = 0
; Sets the X/Y position of the window (horizontal/vertical coordinates from top-left corner of the screen)
setPosition = (0, 0)
; Sets the X/Y size of the window (width/height coordinates)
setSize = (0, 0)
; ----------------
; Title of the window
winTitleText = This PC

; Class of the window, determines the type of window being created
winClassName = CabinetWClass

; Command used to create the window (via subprocess.Popen())
winCreateCmd = file:///

; Window control state
; 1 = NORMAL
; 2 = MINIMIZED
; 3 = MAXIMIZED
setControlsState = 0

; Window visibility state
; 0 = HIDDEN
; 1 = VISIBLE
setDisplayState = 0

; Always-on-top state of the window
; 0 = FALSE
; 1 = TRUE
setTopmostState = 0

; X/Y position of the window (horizontal/vertical coordinates from top-left corner of the screen)
setPosXY = (0, 0)

; X/Y size of the window (width/height coordinates)
setSizeXY = (0, 0)

; ---------------------------

; hwnd = 917528
; z_index = 3
winTitle = SaveWindowLayout ; Title of the window
winCreateCmd = file:///D:/Data/Prosjekter/Scripts/WIP/SaveWindowLayout ; Command to execute the creation of the window (through 'subprocess.Popen(winCreateCommand)')
winShowState = 3 ; 1:MinimizedState | 2:MaximizedState | 3:UnchangedState
winVisibility = 1 ; 0:Hidden | 1:Visible
winTopmost= 0 ; 0:Normal | 1:Always-on-top
winPosXY = (520, 225) ; Relative X/Y window-position (horizontal/vertical coordinates from top-left of the screen)
winSizeXY = (500, 600) ; Relative X/Y window-size (width/height coordinates)


# Vurdere: Kan jeg lagre windowstate som èn variabel istedenfor egne keys? (GetWinState/SaveWinState/LoadWinState: Hidden/Normal/Minimized/Maximized/Visible | NOTOPMOST/TOPMOST)
#          winStateVisibility=[Hidden, Visible] (evt winStateVisibility)
#          winStateDisplay=[Normal, Minimized, Maximized] (evt winTitleButtonAction)
#          winStateOptions=[NOTOPMOST, TOPMOST]:
#          - [NOTOPMOST]:Flyttes bakerst i window-stack og vil alltid være nederst i stack
#          - [TOPMOST]:  Flyttes fremst i window-stack of vil alltid prioriteres ovenfor andre vinduer (med mindre et annet med topmost opprettes forran den)
