# RigOfficeDownloader

Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.

```
Documents -> Files -> Downloads
```

## Features
- Three-stage workflow with user control points
- Interactive menu for flexible execution
- Configurable filter chains for automatic selection
- Smart file organization with subfolder support
- Markdown interfaces for document/file review

## Quick Start
```
1. Run py_venv_init.bat
2. Run RigOfficeDownloader-v4.bat
3. Follow the interactive menu
```

## Benefits
- Reduces documentation gathering time by 75%+
- Maintains consistent file organization
- Provides user control at key decision points
- Handles errors gracefully

## Requirements
- Windows OS
- Python 3.6+
- Chrome browser
