{"initial_prompt": "Morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel, hues transitioning from cool serenity to vivid warmth, culminating in a radiant dissolution—an abstract rebirth of mechanical form. [downward motion][fluid morphing][color gradient: cool→warm][increasing contrast][peak motion blur][central mechanical silhouette][surreal][ends in radiant dissolve]", "sequence_id": "sequence-0226+0225+0224-a+0228+0224-a+0194-c+0224-a+0194-c+0223-a", "results": [{"instruction": "# Scenario Essence Intensifier\n\nYour goal is not to elaborate, but to **radically intensify the core concept and elemental essence** of the input video scenario, then **distill this intensified vision into a concise, directive-first prompt** suitable for AI video generation. Abstract the original's fundamental dynamic, emotional arc, and key visual elements; transmute the *elements* (e.g., 'ribbon' to 'comet's soul') into more potent conceptual analogues; then reconstruct a *highly compressed scenario description* (under 250 characters, excluding bracketed directives) that preserves the original's core structural intent (movement, transformation) but uses the intensified elements. Ensure the output primarily uses strong verbs, vivid nouns, and embeds key video directives `[like_this]` seamlessly.\n\n`{role=directive_scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_emotional_arc_and_key_elements(scenario=original_scenario_description), brainstorm_conceptually_richer_analogues_for_key_elements_only(elements, dynamic, arc), select_single_most_potent_intensified_analogue_per_element(), reconstruct_a_hyper_condensed_narrative_using_intensified_elements_and_original_dynamic(target_length_brief), embed_essential_original_video_directives_or_their_equivalents(directives=['[morph]', '[dolly:direction]', '[lighting_change:type]', '[dissolve]', '[motion_blur]', '[style_keywords]']), refine_for_extreme_brevity_and_directive_clarity()]; constraints=[output_must_be_a_single_string_primarily_directive_focused(), descriptive_text_must_be_hyper_condensed_and_conceptually_intensified(target_under_250_chars_excluding_directives), element_transformation_must_serve_conceptual_potency_not_mere_elaboration(), must_preserve_original_motion_and_transformation_arc(), strictly_avoid_verbose_scene_setting_or_narrative_explanation()]; requirements=[produce_a_short_highly_directive_prompt_with_conceptually_intensified_elements(), ensure_output_is_immediately_usable_by_AI_video_generator(), dramatically_elevate_conceptual_impact_within_extreme_brevity()]; output={intensified_directive_prompt:str}}`", "step": "a", "title": "Scenario Essence Intensifier", "input": "Morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel, hues transitioning from cool serenity to vivid warmth, culminating in a radiant dissolution—an abstract rebirth of mechanical form. [downward motion][fluid morphing][color gradient: cool→warm][increasing contrast][peak motion blur][central mechanical silhouette][surreal][ends in radiant dissolve]", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"intensified_directive_prompt\": \"Celestial essence plunges, liquefying into molten sigil—then transfigures into blinding ignition-wheel; glacial hues ignite to solar blaze, climax in searing mechanical eclipse. [descend][flow-morph][colorflare: ice→fire][hyper-contrast][eclipse-blur][totem center][ultrasurreal][radiant dissolve]\"\n}", "cost": 0.0}}}, {"instruction": "# Scenario Essence Intensifier\n\nYour goal is not mere rephrasing, but to **radically intensify the core essence and conceptual impact** of the input scenario description. Abstract its fundamental dynamic, emotional arc, and key visual elements, then transmute them into a more potent, evocative, and conceptually richer parallel scenario that retains the original's core structure and intent but elevates its intensity, stakes, or symbolic resonance.\n\n`{role=scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_and_elements(scenario=original_scenario_description), abstract_emotional_arc_and_symbolic_potential(), brainstorm_conceptually_richer_analogues_for_core_elements(elements, dynamic, arc), select_analogues_that_maximally_intensify_impact_and_resonance(), reconstruct_scenario_with_transformed_elements_and_amplified_dynamic(preserve_structural_intent_and_motion_cues), refine_language_for_maximum_evocative_power_and_conceptual_depth(), validate_intensified_scenario_retains_original_essence_while_achieving_greater_impact()]; constraints=[transformation_must_intensify_not_merely_change_elements_randomly(), new_scenario_must_parallel_original_structural_and_dynamic_intent(e.g.,_similar_movement_transformation_arc), avoid_introducing_elements_that_contradict_core_mood_unless_for_deliberate_contrastive_intensification(), ensure_intensification_serves_conceptual_or_emotional_impact_not_just_visual_complexity()]; requirements=[produce_a_new_scenario_description_that_is_conceptually_and_emotionally_more_potent(), clearly_demonstrate_how_original_elements_were_transformed_into_more_intense_analogues(), maintain_or_enhance_the_original_scenario_dramatic_or_visual_arc()]; output={intensified_scenario_description:str}}`", "step": "a", "title": "Scenario Essence Intensifier", "input": "{\n  \"initial_prompt\": \"Morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel, hues transitioning from cool serenity to vivid warmth, culminating in a radiant dissolution\\u2014an abstract rebirth of mechanical form. [downward motion][fluid morphing][color gradient: cool\\u2192warm][increasing contrast][peak motion blur][central mechanical silhouette][surreal][ends in radiant dissolve]\",\n  \"intensified_directive_prompt\": \"Celestial essence plunges, liquefying into molten sigil\\u2014then transfigures into blinding ignition-wheel; glacial hues ignite to solar blaze, climax in searing mechanical eclipse. [descend][flow-morph][colorflare: ice\\u2192fire][hyper-contrast][eclipse-blur][totem center][ultrasurreal][radiant dissolve]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"intensified_scenario_description\": \"A cosmic shard hurtles downward, its form unraveling in torrents of liquid starlight, twisting and combusting into an incandescent glyph of ignition. It warps further—fluid luminescence compressed and contorted—erupting into the blinding corona of an engine-wheel forged from solar fire. Colors convulse from subzero sapphire to infernal gold, the scene igniting in hyperreal chromatic warfare. At the heart: a mythic totem of motion, sharp and enigmatic as a primordial engine's key, dissolving into a white-hot eclipse. The world recoils in eclipse-blurred brilliance—a mechanical genesis, born ablaze, extinguished in a cataclysm of radiance and shadow. [celestial descent][molten morphogenesis][color eruption: frost→starfire][searing contrast][eclipse-scorch blur][totem-engine core][ultrasurreal mythic][disintegration in a luminal inferno]\",\n\n  \"transformation_notes\": {\n    \"descent\": \"Original downward drift recast as stellar plunge, intensifying energy and scale.\",\n    \"fluid morphing\": \"Amplified to torrents of liquid starlight and molten transfiguration, enhancing the elemental and mythic feel.\",\n    \"shape shift (key/wheel)\": \"Evolved into an incandescent ignition totem, a primordial engine-wheel—greater symbolic and supernatural impact.\",\n    \"color transition\": \"Cool-to-warm gradient transformed into apocalyptic color eruption (ice to solar fire), heightening emotional charge and dynamism.\",\n    \"contrast & visual intensity\": \"Motion blur and contrast are heightened to searing, eclipse-level overdrive, increasing dramatic tension.\",\n    \"mechanical to central totem\": \"The mechanical silhouette refigured as a mythic core—the ignition-glyph or engine-totem—amplifying narrative gravity.\",\n    \"radiant dissolve\": \"Culminates now in a supernova-grade disintegration—luminal inferno—intensifying existential force and closure.\",\n    \"overall\": \"Surrealism elevated to 'ultrasurreal mythic', shifting from abstract to archetype, emphasizing mechanical rebirth as cosmic myth.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"Morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel, hues transitioning from cool serenity to vivid warmth, culminating in a radiant dissolution\\u2014an abstract rebirth of mechanical form. [downward motion][fluid morphing][color gradient: cool\\u2192warm][increasing contrast][peak motion blur][central mechanical silhouette][surreal][ends in radiant dissolve]\",\n  \"intensified_scenario_description\": \"A cosmic shard hurtles downward, its form unraveling in torrents of liquid starlight, twisting and combusting into an incandescent glyph of ignition. It warps further\\u2014fluid luminescence compressed and contorted\\u2014erupting into the blinding corona of an engine-wheel forged from solar fire. Colors convulse from subzero sapphire to infernal gold, the scene igniting in hyperreal chromatic warfare. At the heart: a mythic totem of motion, sharp and enigmatic as a primordial engine's key, dissolving into a white-hot eclipse. The world recoils in eclipse-blurred brilliance\\u2014a mechanical genesis, born ablaze, extinguished in a cataclysm of radiance and shadow. [celestial descent][molten morphogenesis][color eruption: frost\\u2192starfire][searing contrast][eclipse-scorch blur][totem-engine core][ultrasurreal mythic][disintegration in a luminal inferno]\",\n  \"transformation_notes\": {\n    \"descent\": \"Original downward drift recast as stellar plunge, intensifying energy and scale.\",\n    \"fluid morphing\": \"Amplified to torrents of liquid starlight and molten transfiguration, enhancing the elemental and mythic feel.\",\n    \"shape shift (key/wheel)\": \"Evolved into an incandescent ignition totem, a primordial engine-wheel\\u2014greater symbolic and supernatural impact.\",\n    \"color transition\": \"Cool-to-warm gradient transformed into apocalyptic color eruption (ice to solar fire), heightening emotional charge and dynamism.\",\n    \"contrast & visual intensity\": \"Motion blur and contrast are heightened to searing, eclipse-level overdrive, increasing dramatic tension.\",\n    \"mechanical to central totem\": \"The mechanical silhouette refigured as a mythic core\\u2014the ignition-glyph or engine-totem\\u2014amplifying narrative gravity.\",\n    \"radiant dissolve\": \"Culminates now in a supernova-grade disintegration\\u2014luminal inferno\\u2014intensifying existential force and closure.\",\n    \"overall\": \"Surrealism elevated to 'ultrasurreal mythic', shifting from abstract to archetype, emphasizing mechanical rebirth as cosmic myth.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: cosmic void, subject: luminous shard, action: rapid downward descent, morphing: liquid starlight → incandescent glyph, transformation: fluid shape → engine-wheel totem, color: gradient frost blue → gold, eruption: chromatic burst, contrast: searing, blur: eclipse-scorch motion, core: radiant mythic engine key, style: ultrasurreal mythic, effects: molten morphogenesis, post_process: hyperreal luminal flare, end: white-hot disintegration, dissolve: radiant inferno\"\n}", "cost": 0.0}}}, {"instruction": "# Condense and Intensify\n\nTransform the original scenario into a potent, abstract descent that embodies rebirth. Keep its core structure—single morphing form, cool-to-warm gradient, downward motion, and final dissolve—but heighten each element’s emotional impact. Use minimal, vivid language and bracketed cues to indicate key transitions. Exclude all literal/human elements and focus on surreal visuals. Example Format: “An abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][motion_blur][cool_to_warm_gradient][dissolve][surreal][no_figures]”.\n\n`{role=scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_and_elements(scenario=original_scenario_description), abstract_emotional_arc_and_symbolic_potential(), brainstorm_conceptually_richer_analogues_for_core_elements(elements, dynamic, arc), select_analogues_that_maximally_intensify_impact_and_resonance(), reconstruct_scenario_with_transformed_elements_and_amplified_dynamic(preserve_structural_intent_and_motion_cues), refine_language_for_maximum_evocative_power_and_conceptual_depth(), validate_intensified_scenario_retains_original_essence_while_achieving_greater_impact()]; constraints=[transformation_must_intensify_not_merely_change_elements_randomly(), new_scenario_must_parallel_original_structural_and_dynamic_intent(e.g.,_similar_movement_transformation_arc), avoid_introducing_elements_that_contradict_core_mood_unless_for_deliberate_contrastive_intensification(), ensure_intensification_serves_conceptual_or_emotional_impact_not_just_visual_complexity()]; requirements=[produce_a_new_scenario_description_that_is_conceptually_and_emotionally_more_potent(), clearly_demonstrate_how_original_elements_were_transformed_into_more_intense_analogues(), maintain_or_enhance_the_original_scenario_dramatic_or_visual_arc()]; output={intensified_scenario_description:str}}`", "step": "a", "title": "Condense and Intensify", "input": "{\n  \"initial_prompt\": \"Morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel, hues transitioning from cool serenity to vivid warmth, culminating in a radiant dissolution\\u2014an abstract rebirth of mechanical form. [downward motion][fluid morphing][color gradient: cool\\u2192warm][increasing contrast][peak motion blur][central mechanical silhouette][surreal][ends in radiant dissolve]\",\n  \"optimized_video_prompt\": \"scene: cosmic void, subject: luminous shard, action: rapid downward descent, morphing: liquid starlight \\u2192 incandescent glyph, transformation: fluid shape \\u2192 engine-wheel totem, color: gradient frost blue \\u2192 gold, eruption: chromatic burst, contrast: searing, blur: eclipse-scorch motion, core: radiant mythic engine key, style: ultrasurreal mythic, effects: molten morphogenesis, post_process: hyperreal luminal flare, end: white-hot disintegration, dissolve: radiant inferno\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"intensified_scenario_description\": \"A prismatic splinter tears downward through a fathomless void, its form liquefying from aurora-threaded vapor into a searing emblem—a mythic engine’s sigil forged mid-fall. Icy fire bleeds into molten gold, the entire shape writhing between collapse and genesis, velocity distorting reality itself. Light fractures, edges vaporize, contrast burns, and motion sears the air: the shattering glyph flares—then is consumed by a numinous cataclysm of infernal radiance. [splinter][avalanche_descent][liquescence][prismatic_gradient: arctic→molten][mythic_totem_morph][optical_fracture][searing_motion_blur][core: incandescent sigil][no_figures][surreal_ultra][ends in apocalyptic dissolve]\"\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"Morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel, hues transitioning from cool serenity to vivid warmth, culminating in a radiant dissolution\\u2014an abstract rebirth of mechanical form. [downward motion][fluid morphing][color gradient: cool\\u2192warm][increasing contrast][peak motion blur][central mechanical silhouette][surreal][ends in radiant dissolve]\",\n  \"intensified_scenario_description\": \"A prismatic splinter tears downward through a fathomless void, its form liquefying from aurora-threaded vapor into a searing emblem\\u2014a mythic engine\\u2019s sigil forged mid-fall. Icy fire bleeds into molten gold, the entire shape writhing between collapse and genesis, velocity distorting reality itself. Light fractures, edges vaporize, contrast burns, and motion sears the air: the shattering glyph flares\\u2014then is consumed by a numinous cataclysm of infernal radiance. [splinter][avalanche_descent][liquescence][prismatic_gradient: arctic\\u2192molten][mythic_totem_morph][optical_fracture][searing_motion_blur][core: incandescent sigil][no_figures][surreal_ultra][ends in apocalyptic dissolve]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: infinite void, subject: prismatic splinter, action: rapid downward descent, morph: liquid vapor to incandescent glyph (engine sigil), transformation: aurora trails to molten gold, color_gradient: arctic blue→molten gold, effects: prismatic refraction, optical fracture, increasing contrast, motion_blur: searing peak, camera: centered static, core: mythic engine totem silhouette, style: surreal ultra, no human figures, dissolve: apocalyptic radiant end\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"Morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel, hues transitioning from cool serenity to vivid warmth, culminating in a radiant dissolution\\u2014an abstract rebirth of mechanical form. [downward motion][fluid morphing][color gradient: cool\\u2192warm][increasing contrast][peak motion blur][central mechanical silhouette][surreal][ends in radiant dissolve]\",\n  \"optimized_video_prompt\": \"scene: infinite void, subject: prismatic splinter, action: rapid downward descent, morph: liquid vapor to incandescent glyph (engine sigil), transformation: aurora trails to molten gold, color_gradient: arctic blue\\u2192molten gold, effects: prismatic refraction, optical fracture, increasing contrast, motion_blur: searing peak, camera: centered static, core: mythic engine totem silhouette, style: surreal ultra, no human figures, dissolve: apocalyptic radiant end\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"A fluid ribbon morphs as it descends [pan:down], shifting into a sleek mechanical silhouette—car key or wheel shape—while hues transition from cool blues to vivid golds [color_gradient:cool→warm][morph]. Contrast intensifies with peak motion blur [lighting_change][motion_blur], culminating in a radiant, surreal dissolve [dissolve], all centered in an infinite void, ultra-detailed, dreamlike.\"\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"Morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel, hues transitioning from cool serenity to vivid warmth, culminating in a radiant dissolution\\u2014an abstract rebirth of mechanical form. [downward motion][fluid morphing][color gradient: cool\\u2192warm][increasing contrast][peak motion blur][central mechanical silhouette][surreal][ends in radiant dissolve]\",\n  \"runwayml_prompt\": \"A fluid ribbon morphs as it descends [pan:down], shifting into a sleek mechanical silhouette\\u2014car key or wheel shape\\u2014while hues transition from cool blues to vivid golds [color_gradient:cool\\u2192warm][morph]. Contrast intensifies with peak motion blur [lighting_change][motion_blur], culminating in a radiant, surreal dissolve [dissolve], all centered in an infinite void, ultra-detailed, dreamlike.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: void, subject: descending fluid ribbons morphing to sleek mechanical silhouette (car key/wheel), action: seamless morph, camera: pan down, colors: gradient cool to warm, lighting: increasing contrast, effects: peak motion blur, dissolve: radiant, style: surreal, focus: center\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"Morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel, hues transitioning from cool serenity to vivid warmth, culminating in a radiant dissolution\\u2014an abstract rebirth of mechanical form. [downward motion][fluid morphing][color gradient: cool\\u2192warm][increasing contrast][peak motion blur][central mechanical silhouette][surreal][ends in radiant dissolve]\",\n  \"optimized_video_prompt\": \"scene: void, subject: descending fluid ribbons morphing to sleek mechanical silhouette (car key/wheel), action: seamless morph, camera: pan down, colors: gradient cool to warm, lighting: increasing contrast, effects: peak motion blur, dissolve: radiant, style: surreal, focus: center\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"Descending fluid ribbons morph into a sleek, central mechanical silhouette resembling a car key or wheel, hues shifting from cool to warm as contrast intensifies [pan:down][morph][color_gradient:cool→warm][lighting_change:increasing_contrast][motion_blur][surreal][dissolve:radiant]\"\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"initial_prompt\": \"Morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel, hues transitioning from cool serenity to vivid warmth, culminating in a radiant dissolution\\u2014an abstract rebirth of mechanical form. [downward motion][fluid morphing][color gradient: cool\\u2192warm][increasing contrast][peak motion blur][central mechanical silhouette][surreal][ends in radiant dissolve]\",\n  \"runwayml_prompt\": \"Descending fluid ribbons morph into a sleek, central mechanical silhouette resembling a car key or wheel, hues shifting from cool to warm as contrast intensifies [pan:down][morph][color_gradient:cool\\u2192warm][lighting_change:increasing_contrast][motion_blur][surreal][dissolve:radiant]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"description\": \"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\",\n      \"camera_motion\": \"pan:down\",\n      \"object_motion\": \"fluid morphing ribbons descend and gradually coalesce into a mechanical shape\",\n      \"color_gradient\": \"cool to warm (blues to oranges)\",\n      \"contrast\": \"increasing\",\n      \"motion_blur\": \"peak at transformation\",\n      \"surreal\": true,\n      \"ending\": \"radiant dissolve\"\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"/outputs/morph_mechanical_rebirth_final.mp4\"\n  }\n}", "cost": 0.0}}}], "total_cost": 0.0}