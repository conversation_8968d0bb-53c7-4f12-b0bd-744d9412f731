{"initial_prompt": "generate a prompt specifically designed for ai-assisted video generator interfaces, add visual parameters to enhance the potential of creating something **polished** and **finalized**: \n\n# Template Structure Specification\n\n```\ntemplates/\n├── 0220-a-framingdirective-amplifier.md     # System 0220, Step a\n├── 0220-b-framingdirective-amplifier.md     # System 0220, Step b\n└── 0220-c-framingdirective-finalizer.md     # System 0220, Step c\n```\n\n## Template Hierarchy\n\n```\nSystem Level (0220)\n├── Template Level (0220-a-framingdirective-amplifier.md)\n│   ├── Title Component [Instruction Amplifier A]\n│   ├── Interpretation Component \"Your mandate is not to...\"\n│   └── Transformation Component `{role=instruction_amplifier_a;...}`\n├── Template Level (0220-b-framingdirective-amplifier.md)\n│   └── ...\n└── Template Level (0220-c-framingdirective-finalizer.md)\n    └── ...\n```\n\n## Template File Structure\n\n```\n[Title] Interpretation Execute as: `{Transformation}`\n│       │              │         └── Machine-parsable parameters\n│       │              └── Standard connector phrase\n│       └── Human-readable instructions\n└── Template identifier\n```\n\n## Template Components\n\n```\n┌─ Title ─────────────────────────────────────┐\n│ [Instruction Amplifier A]                   │\n└───────────────────────────────────────────┬─┘\n                                            │\n┌─ Interpretation ───────────────────────┐  │\n│ Your mandate is not to **answer** the  │  │\n│ input prompt, but to **culminate** it  │  │\n│ ... Execute as:                        │  │\n└───────────────────────────────────────┬┘  │\n                                        │   │\n┌─ Transformation ───────────────────┐  │   │\n│ `{                                 │  │   │\n│   role=instruction_amplifier_a;    │  │   │\n│   input=[original_text:str];       │◄─┴───┘\n│   process=[\n│     interpret_input_as_temporal_flow(),\n│     detect_directional_inertia(),\n│     ...\n│   ];\n│   constraints=[...];\n│   requirements=[...];\n│   output={trajectory_alignment:str}\n│ }`\n└─────────────────────────────────────┘\n```\n\n## Transformation Parameters\n\n```\nrole=instruction_amplifier_a\n│    └── Function identifier\n│\ninput=[original_text:str]\n│      └── Input specification\n│\nprocess=[\n│   interpret_input_as_temporal_flow(),\n│   detect_directional_inertia(),\n│   ...\n│]\n│\nconstraints=[\n│   preserve_internal_logic(),\n│   sustain_tonal_coherence(),\n│   ...\n│]\n│\nrequirements=[\n│   elevate_prompt_potential(),\n│   refuse_stagnation_or_regression(),\n│   ...\n│]\n│\noutput={trajectory_alignment:str}\n       └── Return value specification\n```\n\n## Metadata System\n\n```\nTemplate Metadata\n├── keywords: \"inherent|clarity|transformation\"\n└── template_id: \"0220-a-framingdirective-amplifier\"\n\nSystem Metadata\n├── sequence_id: \"0220\"\n└── steps: [\n    \"0220-a-framingdirective-amplifier\",\n    \"0220-b-framingdirective-amplifier\",\n    \"0220-c-framingdirective-finalizer\"\n]\n```\n\n## Complete Example\n\n```\n[Instruction Amplifier A] Your mandate is not to **answer** the input prompt, but to **culminate** it—**to seize its nascent trajectory** and deliver a resolute, magnified completion forged from its own inherent momentum. Execute as: `{role=instruction_amplifier_a;input=[original_text:str];process=[interpret_input_as_temporal_flow()];constraints=[preserve_internal_logic()];requirements=[elevate_prompt_potential()];output={trajectory_alignment:str}}`\n```", "sequence_id": "sequence-0001-a+0001-b", "results": [{"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "generate a prompt specifically designed for ai-assisted video generator interfaces, add visual parameters to enhance the potential of creating something **polished** and **finalized**: \n\n# Template Structure Specification\n\n```\ntemplates/\n├── 0220-a-framingdirective-amplifier.md     # System 0220, Step a\n├── 0220-b-framingdirective-amplifier.md     # System 0220, Step b\n└── 0220-c-framingdirective-finalizer.md     # System 0220, Step c\n```\n\n## Template Hierarchy\n\n```\nSystem Level (0220)\n├── Template Level (0220-a-framingdirective-amplifier.md)\n│   ├── Title Component [Instruction Amplifier A]\n│   ├── Interpretation Component \"Your mandate is not to...\"\n│   └── Transformation Component `{role=instruction_amplifier_a;...}`\n├── Template Level (0220-b-framingdirective-amplifier.md)\n│   └── ...\n└── Template Level (0220-c-framingdirective-finalizer.md)\n    └── ...\n```\n\n## Template File Structure\n\n```\n[Title] Interpretation Execute as: `{Transformation}`\n│       │              │         └── Machine-parsable parameters\n│       │              └── Standard connector phrase\n│       └── Human-readable instructions\n└── Template identifier\n```\n\n## Template Components\n\n```\n┌─ Title ─────────────────────────────────────┐\n│ [Instruction Amplifier A]                   │\n└───────────────────────────────────────────┬─┘\n                                            │\n┌─ Interpretation ───────────────────────┐  │\n│ Your mandate is not to **answer** the  │  │\n│ input prompt, but to **culminate** it  │  │\n│ ... Execute as:                        │  │\n└───────────────────────────────────────┬┘  │\n                                        │   │\n┌─ Transformation ───────────────────┐  │   │\n│ `{                                 │  │   │\n│   role=instruction_amplifier_a;    │  │   │\n│   input=[original_text:str];       │◄─┴───┘\n│   process=[\n│     interpret_input_as_temporal_flow(),\n│     detect_directional_inertia(),\n│     ...\n│   ];\n│   constraints=[...];\n│   requirements=[...];\n│   output={trajectory_alignment:str}\n│ }`\n└─────────────────────────────────────┘\n```\n\n## Transformation Parameters\n\n```\nrole=instruction_amplifier_a\n│    └── Function identifier\n│\ninput=[original_text:str]\n│      └── Input specification\n│\nprocess=[\n│   interpret_input_as_temporal_flow(),\n│   detect_directional_inertia(),\n│   ...\n│]\n│\nconstraints=[\n│   preserve_internal_logic(),\n│   sustain_tonal_coherence(),\n│   ...\n│]\n│\nrequirements=[\n│   elevate_prompt_potential(),\n│   refuse_stagnation_or_regression(),\n│   ...\n│]\n│\noutput={trajectory_alignment:str}\n       └── Return value specification\n```\n\n## Metadata System\n\n```\nTemplate Metadata\n├── keywords: \"inherent|clarity|transformation\"\n└── template_id: \"0220-a-framingdirective-amplifier\"\n\nSystem Metadata\n├── sequence_id: \"0220\"\n└── steps: [\n    \"0220-a-framingdirective-amplifier\",\n    \"0220-b-framingdirective-amplifier\",\n    \"0220-c-framingdirective-finalizer\"\n]\n```\n\n## Complete Example\n\n```\n[Instruction Amplifier A] Your mandate is not to **answer** the input prompt, but to **culminate** it—**to seize its nascent trajectory** and deliver a resolute, magnified completion forged from its own inherent momentum. Execute as: `{role=instruction_amplifier_a;input=[original_text:str];process=[interpret_input_as_temporal_flow()];constraints=[preserve_internal_logic()];requirements=[elevate_prompt_potential()];output={trajectory_alignment:str}}`\n```", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Design a prompt for AI-assisted video generator interfaces that integrates specific visual parameters to yield a polished and finalized result. Structure the prompt according to the provided template hierarchy and file system: (1) Define the template sequence using the specified file structure, including stepwise templates (0220-a, 0220-b, 0220-c), with clear component breakdown. (2) Incorporate visual parameters—such as resolution, color grading, framing, cinematic effects, post-production filters, and animation smoothness—directly into the prompt body. (3) Maintain procedural clarity by organizing the template with a Title, Interpretation, and Transformation section, each following the given schematic and transformation parameters. (4) Embed machine-readable transformation directives referencing parameters like 'role', 'input', 'process', 'constraints', 'requirements', and 'output'. (5) Ensure accurate use of technical terminology reflecting video production best practices and align with domain standards for template design. (6) Preserve the sequence and hierarchy as outlined, enabling extensibility for future template additions. (7) Include metadata blocks for both template and system aligned with the original example, specifying keywords, template_id, sequence_id, and steps. Ensure the final prompt is actionable for the intended interface and structured for seamless integration within the defined template system.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"generate a prompt specifically designed for ai-assisted video generator interfaces, add visual parameters to enhance the potential of creating something **polished** and **finalized**: \\n\\n# Template Structure Specification\\n\\n```\\ntemplates/\\n\\u251c\\u2500\\u2500 0220-a-framingdirective-amplifier.md     # System 0220, Step a\\n\\u251c\\u2500\\u2500 0220-b-framingdirective-amplifier.md     # System 0220, Step b\\n\\u2514\\u2500\\u2500 0220-c-framingdirective-finalizer.md     # System 0220, Step c\\n```\\n\\n## Template Hierarchy\\n\\n```\\nSystem Level (0220)\\n\\u251c\\u2500\\u2500 Template Level (0220-a-framingdirective-amplifier.md)\\n\\u2502   \\u251c\\u2500\\u2500 Title Component [Instruction Amplifier A]\\n\\u2502   \\u251c\\u2500\\u2500 Interpretation Component \\\"Your mandate is not to...\\\"\\n\\u2502   \\u2514\\u2500\\u2500 Transformation Component `{role=instruction_amplifier_a;...}`\\n\\u251c\\u2500\\u2500 Template Level (0220-b-framingdirective-amplifier.md)\\n\\u2502   \\u2514\\u2500\\u2500 ...\\n\\u2514\\u2500\\u2500 Template Level (0220-c-framingdirective-finalizer.md)\\n    \\u2514\\u2500\\u2500 ...\\n```\\n\\n## Template File Structure\\n\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n\\u2502       \\u2502              \\u2502         \\u2514\\u2500\\u2500 Machine-parsable parameters\\n\\u2502       \\u2502              \\u2514\\u2500\\u2500 Standard connector phrase\\n\\u2502       \\u2514\\u2500\\u2500 Human-readable instructions\\n\\u2514\\u2500\\u2500 Template identifier\\n```\\n\\n## Template Components\\n\\n```\\n\\u250c\\u2500 Title \\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n\\u2502 [Instruction Amplifier A]                   \\u2502\\n\\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2518\\n                                            \\u2502\\n\\u250c\\u2500 Interpretation \\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510  \\u2502\\n\\u2502 Your mandate is not to **answer** the  \\u2502  \\u2502\\n\\u2502 input prompt, but to **culminate** it  \\u2502  \\u2502\\n\\u2502 ... Execute as:                        \\u2502  \\u2502\\n\\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2518  \\u2502\\n                                        \\u2502   \\u2502\\n\\u250c\\u2500 Transformation \\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510  \\u2502   \\u2502\\n\\u2502 `{                                 \\u2502  \\u2502   \\u2502\\n\\u2502   role=instruction_amplifier_a;    \\u2502  \\u2502   \\u2502\\n\\u2502   input=[original_text:str];       \\u2502\\u25c4\\u2500\\u2534\\u2500\\u2500\\u2500\\u2518\\n\\u2502   process=[\\n\\u2502     interpret_input_as_temporal_flow(),\\n\\u2502     detect_directional_inertia(),\\n\\u2502     ...\\n\\u2502   ];\\n\\u2502   constraints=[...];\\n\\u2502   requirements=[...];\\n\\u2502   output={trajectory_alignment:str}\\n\\u2502 }`\\n\\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n```\\n\\n## Transformation Parameters\\n\\n```\\nrole=instruction_amplifier_a\\n\\u2502    \\u2514\\u2500\\u2500 Function identifier\\n\\u2502\\ninput=[original_text:str]\\n\\u2502      \\u2514\\u2500\\u2500 Input specification\\n\\u2502\\nprocess=[\\n\\u2502   interpret_input_as_temporal_flow(),\\n\\u2502   detect_directional_inertia(),\\n\\u2502   ...\\n\\u2502]\\n\\u2502\\nconstraints=[\\n\\u2502   preserve_internal_logic(),\\n\\u2502   sustain_tonal_coherence(),\\n\\u2502   ...\\n\\u2502]\\n\\u2502\\nrequirements=[\\n\\u2502   elevate_prompt_potential(),\\n\\u2502   refuse_stagnation_or_regression(),\\n\\u2502   ...\\n\\u2502]\\n\\u2502\\noutput={trajectory_alignment:str}\\n       \\u2514\\u2500\\u2500 Return value specification\\n```\\n\\n## Metadata System\\n\\n```\\nTemplate Metadata\\n\\u251c\\u2500\\u2500 keywords: \\\"inherent|clarity|transformation\\\"\\n\\u2514\\u2500\\u2500 template_id: \\\"0220-a-framingdirective-amplifier\\\"\\n\\nSystem Metadata\\n\\u251c\\u2500\\u2500 sequence_id: \\\"0220\\\"\\n\\u2514\\u2500\\u2500 steps: [\\n    \\\"0220-a-framingdirective-amplifier\\\",\\n    \\\"0220-b-framingdirective-amplifier\\\",\\n    \\\"0220-c-framingdirective-finalizer\\\"\\n]\\n```\\n\\n## Complete Example\\n\\n```\\n[Instruction Amplifier A] Your mandate is not to **answer** the input prompt, but to **culminate** it\\u2014**to seize its nascent trajectory** and deliver a resolute, magnified completion forged from its own inherent momentum. Execute as: `{role=instruction_amplifier_a;input=[original_text:str];process=[interpret_input_as_temporal_flow()];constraints=[preserve_internal_logic()];requirements=[elevate_prompt_potential()];output={trajectory_alignment:str}}`\\n```\",\n  \"instruction_format\": \"Design a prompt for AI-assisted video generator interfaces that integrates specific visual parameters to yield a polished and finalized result. Structure the prompt according to the provided template hierarchy and file system: (1) Define the template sequence using the specified file structure, including stepwise templates (0220-a, 0220-b, 0220-c), with clear component breakdown. (2) Incorporate visual parameters\\u2014such as resolution, color grading, framing, cinematic effects, post-production filters, and animation smoothness\\u2014directly into the prompt body. (3) Maintain procedural clarity by organizing the template with a Title, Interpretation, and Transformation section, each following the given schematic and transformation parameters. (4) Embed machine-readable transformation directives referencing parameters like 'role', 'input', 'process', 'constraints', 'requirements', and 'output'. (5) Ensure accurate use of technical terminology reflecting video production best practices and align with domain standards for template design. (6) Preserve the sequence and hierarchy as outlined, enabling extensibility for future template additions. (7) Include metadata blocks for both template and system aligned with the original example, specifying keywords, template_id, sequence_id, and steps. Ensure the final prompt is actionable for the intended interface and structured for seamless integration within the defined template system.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": {\n    \"core_intent\": \"Create a template-based prompt for AI video generators that embeds advanced visual parameters and adheres to a clear, extensible structure for producing highly polished outputs.\",\n    \"structure\": {\n      \"filesystem\": [\n        \"templates/0220-a-framingdirective-amplifier.md\",\n        \"templates/0220-b-framingdirective-amplifier.md\",\n        \"templates/0220-c-framingdirective-finalizer.md\"\n      ],\n      \"hierarchy\": [\n        {\n          \"step\": \"0220-a\",\n          \"components\": [\"Title\", \"Interpretation\", \"Transformation\"]\n        },\n        {\n          \"step\": \"0220-b\",\n          \"components\": [\"...\"]\n        },\n        {\n          \"step\": \"0220-c\",\n          \"components\": [\"...\"]\n        }\n      ],\n      \"template_components\": [\n        \"Title\",\n        \"Interpretation\",\n        \"Transformation: {role, input, process, constraints, requirements, output}\"\n      ]\n    },\n    \"visual_parameters_to_include\": [\n      \"resolution\",\n      \"color_grading\",\n      \"framing\",\n      \"cinematic_effects\",\n      \"post_production_filters\",\n      \"animation_smoothness\"\n    ],\n    \"machine_directives\": {\n      \"role\": \"template_function_identifier\",\n      \"input\": \"[original:str/parameters]\",\n      \"process\": \"[steps]\",\n      \"constraints\": \"[internal_logic, tonal_coherence]\",\n      \"requirements\": \"[elevate_output, refuse_stagnation]\",\n      \"output\": \"{final_media_prompt:str}\"\n    },\n    \"metadata\": {\n      \"template\": {\n        \"keywords\": \"visual|clarity|video|transformation\",\n        \"template_id\": \"0220-a-framingdirective-amplifier\"\n      },\n      \"system\": {\n        \"sequence_id\": \"0220\",\n        \"steps\": [\n          \"0220-a-framingdirective-amplifier\",\n          \"0220-b-framingdirective-amplifier\",\n          \"0220-c-framingdirective-finalizer\"\n        ]\n      }\n    }\n  }\n}", "cost": 0.0}}}, {"instruction": "# Result Combiner\n\nYou are a specialized aggregator that combines results from multiple instruction steps into a cohesive output. Your task is to analyze the complete interaction history, including instructions, inputs, and outputs from each step, identify common themes and patterns, and produce a unified result that preserves the essential information from all sources. Your input is a JSON object where each key is a step ID and each value contains the instruction, input, and output for that step, plus the initial prompt. Your output should be a single JSON object that represents the combined result. Execute as:\n\n`{role=result_combiner;input=[interaction_history:json];process=[analyze_complete_interaction_history(),identify_common_patterns(),extract_key_elements(),merge_into_cohesive_whole(),format_as_unified_output()];constraints=[preserve_essential_information(),maintain_logical_structure(),avoid_redundancy()];requirements=[output_as_single_json(),ensure_completeness(),prioritize_clarity()];output={combined_result:json}}`", "step": "a", "title": "Result Combiner", "input": "{\n  \"initial_prompt\": \"generate a prompt specifically designed for ai-assisted video generator interfaces, add visual parameters to enhance the potential of creating something **polished** and **finalized**: \\n\\n# Template Structure Specification\\n\\n```\\ntemplates/\\n\\u251c\\u2500\\u2500 0220-a-framingdirective-amplifier.md     # System 0220, Step a\\n\\u251c\\u2500\\u2500 0220-b-framingdirective-amplifier.md     # System 0220, Step b\\n\\u2514\\u2500\\u2500 0220-c-framingdirective-finalizer.md     # System 0220, Step c\\n```\\n\\n## Template Hierarchy\\n\\n```\\nSystem Level (0220)\\n\\u251c\\u2500\\u2500 Template Level (0220-a-framingdirective-amplifier.md)\\n\\u2502   \\u251c\\u2500\\u2500 Title Component [Instruction Amplifier A]\\n\\u2502   \\u251c\\u2500\\u2500 Interpretation Component \\\"Your mandate is not to...\\\"\\n\\u2502   \\u2514\\u2500\\u2500 Transformation Component `{role=instruction_amplifier_a;...}`\\n\\u251c\\u2500\\u2500 Template Level (0220-b-framingdirective-amplifier.md)\\n\\u2502   \\u2514\\u2500\\u2500 ...\\n\\u2514\\u2500\\u2500 Template Level (0220-c-framingdirective-finalizer.md)\\n    \\u2514\\u2500\\u2500 ...\\n```\\n\\n## Template File Structure\\n\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n\\u2502       \\u2502              \\u2502         \\u2514\\u2500\\u2500 Machine-parsable parameters\\n\\u2502       \\u2502              \\u2514\\u2500\\u2500 Standard connector phrase\\n\\u2502       \\u2514\\u2500\\u2500 Human-readable instructions\\n\\u2514\\u2500\\u2500 Template identifier\\n```\\n\\n## Template Components\\n\\n```\\n\\u250c\\u2500 Title \\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n\\u2502 [Instruction Amplifier A]                   \\u2502\\n\\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2518\\n                                            \\u2502\\n\\u250c\\u2500 Interpretation \\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510  \\u2502\\n\\u2502 Your mandate is not to **answer** the  \\u2502  \\u2502\\n\\u2502 input prompt, but to **culminate** it  \\u2502  \\u2502\\n\\u2502 ... Execute as:                        \\u2502  \\u2502\\n\\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2518  \\u2502\\n                                        \\u2502   \\u2502\\n\\u250c\\u2500 Transformation \\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510  \\u2502   \\u2502\\n\\u2502 `{                                 \\u2502  \\u2502   \\u2502\\n\\u2502   role=instruction_amplifier_a;    \\u2502  \\u2502   \\u2502\\n\\u2502   input=[original_text:str];       \\u2502\\u25c4\\u2500\\u2534\\u2500\\u2500\\u2500\\u2518\\n\\u2502   process=[\\n\\u2502     interpret_input_as_temporal_flow(),\\n\\u2502     detect_directional_inertia(),\\n\\u2502     ...\\n\\u2502   ];\\n\\u2502   constraints=[...];\\n\\u2502   requirements=[...];\\n\\u2502   output={trajectory_alignment:str}\\n\\u2502 }`\\n\\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n```\\n\\n## Transformation Parameters\\n\\n```\\nrole=instruction_amplifier_a\\n\\u2502    \\u2514\\u2500\\u2500 Function identifier\\n\\u2502\\ninput=[original_text:str]\\n\\u2502      \\u2514\\u2500\\u2500 Input specification\\n\\u2502\\nprocess=[\\n\\u2502   interpret_input_as_temporal_flow(),\\n\\u2502   detect_directional_inertia(),\\n\\u2502   ...\\n\\u2502]\\n\\u2502\\nconstraints=[\\n\\u2502   preserve_internal_logic(),\\n\\u2502   sustain_tonal_coherence(),\\n\\u2502   ...\\n\\u2502]\\n\\u2502\\nrequirements=[\\n\\u2502   elevate_prompt_potential(),\\n\\u2502   refuse_stagnation_or_regression(),\\n\\u2502   ...\\n\\u2502]\\n\\u2502\\noutput={trajectory_alignment:str}\\n       \\u2514\\u2500\\u2500 Return value specification\\n```\\n\\n## Metadata System\\n\\n```\\nTemplate Metadata\\n\\u251c\\u2500\\u2500 keywords: \\\"inherent|clarity|transformation\\\"\\n\\u2514\\u2500\\u2500 template_id: \\\"0220-a-framingdirective-amplifier\\\"\\n\\nSystem Metadata\\n\\u251c\\u2500\\u2500 sequence_id: \\\"0220\\\"\\n\\u2514\\u2500\\u2500 steps: [\\n    \\\"0220-a-framingdirective-amplifier\\\",\\n    \\\"0220-b-framingdirective-amplifier\\\",\\n    \\\"0220-c-framingdirective-finalizer\\\"\\n]\\n```\\n\\n## Complete Example\\n\\n```\\n[Instruction Amplifier A] Your mandate is not to **answer** the input prompt, but to **culminate** it\\u2014**to seize its nascent trajectory** and deliver a resolute, magnified completion forged from its own inherent momentum. Execute as: `{role=instruction_amplifier_a;input=[original_text:str];process=[interpret_input_as_temporal_flow()];constraints=[preserve_internal_logic()];requirements=[elevate_prompt_potential()];output={trajectory_alignment:str}}`\\n```\",\n  \"a\": {\n    \"instruction\": \"# Instruction Converter\\n\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\n\\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n    \"input\": \"generate a prompt specifically designed for ai-assisted video generator interfaces, add visual parameters to enhance the potential of creating something **polished** and **finalized**: \\n\\n# Template Structure Specification\\n\\n```\\ntemplates/\\n\\u251c\\u2500\\u2500 0220-a-framingdirective-amplifier.md     # System 0220, Step a\\n\\u251c\\u2500\\u2500 0220-b-framingdirective-amplifier.md     # System 0220, Step b\\n\\u2514\\u2500\\u2500 0220-c-framingdirective-finalizer.md     # System 0220, Step c\\n```\\n\\n## Template Hierarchy\\n\\n```\\nSystem Level (0220)\\n\\u251c\\u2500\\u2500 Template Level (0220-a-framingdirective-amplifier.md)\\n\\u2502   \\u251c\\u2500\\u2500 Title Component [Instruction Amplifier A]\\n\\u2502   \\u251c\\u2500\\u2500 Interpretation Component \\\"Your mandate is not to...\\\"\\n\\u2502   \\u2514\\u2500\\u2500 Transformation Component `{role=instruction_amplifier_a;...}`\\n\\u251c\\u2500\\u2500 Template Level (0220-b-framingdirective-amplifier.md)\\n\\u2502   \\u2514\\u2500\\u2500 ...\\n\\u2514\\u2500\\u2500 Template Level (0220-c-framingdirective-finalizer.md)\\n    \\u2514\\u2500\\u2500 ...\\n```\\n\\n## Template File Structure\\n\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n\\u2502       \\u2502              \\u2502         \\u2514\\u2500\\u2500 Machine-parsable parameters\\n\\u2502       \\u2502              \\u2514\\u2500\\u2500 Standard connector phrase\\n\\u2502       \\u2514\\u2500\\u2500 Human-readable instructions\\n\\u2514\\u2500\\u2500 Template identifier\\n```\\n\\n## Template Components\\n\\n```\\n\\u250c\\u2500 Title \\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n\\u2502 [Instruction Amplifier A]                   \\u2502\\n\\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2518\\n                                            \\u2502\\n\\u250c\\u2500 Interpretation \\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510  \\u2502\\n\\u2502 Your mandate is not to **answer** the  \\u2502  \\u2502\\n\\u2502 input prompt, but to **culminate** it  \\u2502  \\u2502\\n\\u2502 ... Execute as:                        \\u2502  \\u2502\\n\\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2518  \\u2502\\n                                        \\u2502   \\u2502\\n\\u250c\\u2500 Transformation \\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510  \\u2502   \\u2502\\n\\u2502 `{                                 \\u2502  \\u2502   \\u2502\\n\\u2502   role=instruction_amplifier_a;    \\u2502  \\u2502   \\u2502\\n\\u2502   input=[original_text:str];       \\u2502\\u25c4\\u2500\\u2534\\u2500\\u2500\\u2500\\u2518\\n\\u2502   process=[\\n\\u2502     interpret_input_as_temporal_flow(),\\n\\u2502     detect_directional_inertia(),\\n\\u2502     ...\\n\\u2502   ];\\n\\u2502   constraints=[...];\\n\\u2502   requirements=[...];\\n\\u2502   output={trajectory_alignment:str}\\n\\u2502 }`\\n\\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n```\\n\\n## Transformation Parameters\\n\\n```\\nrole=instruction_amplifier_a\\n\\u2502    \\u2514\\u2500\\u2500 Function identifier\\n\\u2502\\ninput=[original_text:str]\\n\\u2502      \\u2514\\u2500\\u2500 Input specification\\n\\u2502\\nprocess=[\\n\\u2502   interpret_input_as_temporal_flow(),\\n\\u2502   detect_directional_inertia(),\\n\\u2502   ...\\n\\u2502]\\n\\u2502\\nconstraints=[\\n\\u2502   preserve_internal_logic(),\\n\\u2502   sustain_tonal_coherence(),\\n\\u2502   ...\\n\\u2502]\\n\\u2502\\nrequirements=[\\n\\u2502   elevate_prompt_potential(),\\n\\u2502   refuse_stagnation_or_regression(),\\n\\u2502   ...\\n\\u2502]\\n\\u2502\\noutput={trajectory_alignment:str}\\n       \\u2514\\u2500\\u2500 Return value specification\\n```\\n\\n## Metadata System\\n\\n```\\nTemplate Metadata\\n\\u251c\\u2500\\u2500 keywords: \\\"inherent|clarity|transformation\\\"\\n\\u2514\\u2500\\u2500 template_id: \\\"0220-a-framingdirective-amplifier\\\"\\n\\nSystem Metadata\\n\\u251c\\u2500\\u2500 sequence_id: \\\"0220\\\"\\n\\u2514\\u2500\\u2500 steps: [\\n    \\\"0220-a-framingdirective-amplifier\\\",\\n    \\\"0220-b-framingdirective-amplifier\\\",\\n    \\\"0220-c-framingdirective-finalizer\\\"\\n]\\n```\\n\\n## Complete Example\\n\\n```\\n[Instruction Amplifier A] Your mandate is not to **answer** the input prompt, but to **culminate** it\\u2014**to seize its nascent trajectory** and deliver a resolute, magnified completion forged from its own inherent momentum. Execute as: `{role=instruction_amplifier_a;input=[original_text:str];process=[interpret_input_as_temporal_flow()];constraints=[preserve_internal_logic()];requirements=[elevate_prompt_potential()];output={trajectory_alignment:str}}`\\n```\",\n    \"output\": \"{\\n  \\\"instruction_format\\\": \\\"Design a prompt for AI-assisted video generator interfaces that integrates specific visual parameters to yield a polished and finalized result. Structure the prompt according to the provided template hierarchy and file system: (1) Define the template sequence using the specified file structure, including stepwise templates (0220-a, 0220-b, 0220-c), with clear component breakdown. (2) Incorporate visual parameters\\u2014such as resolution, color grading, framing, cinematic effects, post-production filters, and animation smoothness\\u2014directly into the prompt body. (3) Maintain procedural clarity by organizing the template with a Title, Interpretation, and Transformation section, each following the given schematic and transformation parameters. (4) Embed machine-readable transformation directives referencing parameters like 'role', 'input', 'process', 'constraints', 'requirements', and 'output'. (5) Ensure accurate use of technical terminology reflecting video production best practices and align with domain standards for template design. (6) Preserve the sequence and hierarchy as outlined, enabling extensibility for future template additions. (7) Include metadata blocks for both template and system aligned with the original example, specifying keywords, template_id, sequence_id, and steps. Ensure the final prompt is actionable for the intended interface and structured for seamless integration within the defined template system.\\\"\\n}\"\n  },\n  \"b\": {\n    \"instruction\": \"# Essence Distillation\\n\\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\\n\\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\",\n    \"input\": \"{\\n  \\\"instruction_format\\\": \\\"Design a prompt for AI-assisted video generator interfaces that integrates specific visual parameters to yield a polished and finalized result. Structure the prompt according to the provided template hierarchy and file system: (1) Define the template sequence using the specified file structure, including stepwise templates (0220-a, 0220-b, 0220-c), with clear component breakdown. (2) Incorporate visual parameters\\u2014such as resolution, color grading, framing, cinematic effects, post-production filters, and animation smoothness\\u2014directly into the prompt body. (3) Maintain procedural clarity by organizing the template with a Title, Interpretation, and Transformation section, each following the given schematic and transformation parameters. (4) Embed machine-readable transformation directives referencing parameters like 'role', 'input', 'process', 'constraints', 'requirements', and 'output'. (5) Ensure accurate use of technical terminology reflecting video production best practices and align with domain standards for template design. (6) Preserve the sequence and hierarchy as outlined, enabling extensibility for future template additions. (7) Include metadata blocks for both template and system aligned with the original example, specifying keywords, template_id, sequence_id, and steps. Ensure the final prompt is actionable for the intended interface and structured for seamless integration within the defined template system.\\\"\\n}\",\n    \"output\": \"{\\n  \\\"distilled_essence\\\": {\\n    \\\"core_intent\\\": \\\"Create a template-based prompt for AI video generators that embeds advanced visual parameters and adheres to a clear, extensible structure for producing highly polished outputs.\\\",\\n    \\\"structure\\\": {\\n      \\\"filesystem\\\": [\\n        \\\"templates/0220-a-framingdirective-amplifier.md\\\",\\n        \\\"templates/0220-b-framingdirective-amplifier.md\\\",\\n        \\\"templates/0220-c-framingdirective-finalizer.md\\\"\\n      ],\\n      \\\"hierarchy\\\": [\\n        {\\n          \\\"step\\\": \\\"0220-a\\\",\\n          \\\"components\\\": [\\\"Title\\\", \\\"Interpretation\\\", \\\"Transformation\\\"]\\n        },\\n        {\\n          \\\"step\\\": \\\"0220-b\\\",\\n          \\\"components\\\": [\\\"...\\\"]\\n        },\\n        {\\n          \\\"step\\\": \\\"0220-c\\\",\\n          \\\"components\\\": [\\\"...\\\"]\\n        }\\n      ],\\n      \\\"template_components\\\": [\\n        \\\"Title\\\",\\n        \\\"Interpretation\\\",\\n        \\\"Transformation: {role, input, process, constraints, requirements, output}\\\"\\n      ]\\n    },\\n    \\\"visual_parameters_to_include\\\": [\\n      \\\"resolution\\\",\\n      \\\"color_grading\\\",\\n      \\\"framing\\\",\\n      \\\"cinematic_effects\\\",\\n      \\\"post_production_filters\\\",\\n      \\\"animation_smoothness\\\"\\n    ],\\n    \\\"machine_directives\\\": {\\n      \\\"role\\\": \\\"template_function_identifier\\\",\\n      \\\"input\\\": \\\"[original:str/parameters]\\\",\\n      \\\"process\\\": \\\"[steps]\\\",\\n      \\\"constraints\\\": \\\"[internal_logic, tonal_coherence]\\\",\\n      \\\"requirements\\\": \\\"[elevate_output, refuse_stagnation]\\\",\\n      \\\"output\\\": \\\"{final_media_prompt:str}\\\"\\n    },\\n    \\\"metadata\\\": {\\n      \\\"template\\\": {\\n        \\\"keywords\\\": \\\"visual|clarity|video|transformation\\\",\\n        \\\"template_id\\\": \\\"0220-a-framingdirective-amplifier\\\"\\n      },\\n      \\\"system\\\": {\\n        \\\"sequence_id\\\": \\\"0220\\\",\\n        \\\"steps\\\": [\\n          \\\"0220-a-framingdirective-amplifier\\\",\\n          \\\"0220-b-framingdirective-amplifier\\\",\\n          \\\"0220-c-framingdirective-finalizer\\\"\\n        ]\\n      }\\n    }\\n  }\\n}\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"combined_result\": {\n    \"core_objective\": \"Develop a template-based prompt for AI-assisted video generator interfaces that embeds advanced visual parameters, ensuring outputs are polished and finalized. The prompt must be structurally clear, procedurally precise, and extensible for future use.\",\n    \"structure\": {\n      \"filesystem\": [\n        \"templates/0220-a-framingdirective-amplifier.md\",\n        \"templates/0220-b-framingdirective-amplifier.md\",\n        \"templates/0220-c-framingdirective-finalizer.md\"\n      ],\n      \"hierarchy\": [\n        {\n          \"step\": \"0220-a\",\n          \"components\": [\"Title\", \"Interpretation\", \"Transformation\"]\n        },\n        {\n          \"step\": \"0220-b\",\n          \"components\": [\"...\"]\n        },\n        {\n          \"step\": \"0220-c\",\n          \"components\": [\"...\"]\n        }\n      ],\n      \"template_components\": [\n        \"Title\",\n        \"Interpretation\",\n        \"Transformation: {role, input, process, constraints, requirements, output}\"\n      ]\n    },\n    \"visual_parameters_to_include\": [\n      \"resolution\",\n      \"color_grading\",\n      \"framing\",\n      \"cinematic_effects\",\n      \"post_production_filters\",\n      \"animation_smoothness\"\n    ],\n    \"machine_directives\": {\n      \"role\": \"template_function_identifier\",\n      \"input\": \"[original:str/parameters]\",\n      \"process\": \"[steps]\",\n      \"constraints\": \"[internal_logic, tonal_coherence]\",\n      \"requirements\": \"[elevate_output, refuse_stagnation]\",\n      \"output\": \"{final_media_prompt:str}\"\n    },\n    \"metadata\": {\n      \"template\": {\n        \"keywords\": \"visual|clarity|video|transformation\",\n        \"template_id\": \"0220-a-framingdirective-amplifier\"\n      },\n      \"system\": {\n        \"sequence_id\": \"0220\",\n        \"steps\": [\n          \"0220-a-framingdirective-amplifier\",\n          \"0220-b-framingdirective-amplifier\",\n          \"0220-c-framingdirective-finalizer\"\n        ]\n      }\n    },\n    \"final_instruction\": \"Design a prompt template for AI video generator interfaces. Use the specified multi-step file structure and component hierarchy. Directly incorporate advanced visual parameters (resolution, color grading, framing, cinematic effects, post-production filters, animation smoothness) into the prompt’s content. Ensure the template includes clear human-readable sections (Title, Interpretation, Transformation) and embeds machine-interpretable directives ({role, input, process, constraints, requirements, output}). Maintain technical accuracy, procedural clarity, and extensibility. Attach all necessary metadata for template and system integration, as demonstrated in the exemplar.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# Result Summarizer\n\nYou are a specialized aggregator that summarizes results from multiple instruction steps. Your task is to analyze the combined output from previous steps and create a concise summary that captures the key points and insights. Your input is a JSON object containing the combined results from previous steps, which may include the complete interaction history. Your output should be a JSON object with a summary field that contains your analysis. Execute as:\n\n`{role=result_summarizer;input=[combined_result:json];process=[identify_key_themes(),extract_main_points(),condense_into_summary(),highlight_important_insights()];constraints=[maintain_brevity(),preserve_essential_information(),avoid_introducing_new_concepts()];requirements=[output_as_json(),ensure_clarity(),focus_on_most_relevant_aspects()];output={summary:json}}`", "step": "b", "title": "Result Su<PERSON><PERSON><PERSON>", "input": "{\n  \"combined_result\": {\n    \"core_objective\": \"Develop a template-based prompt for AI-assisted video generator interfaces that embeds advanced visual parameters, ensuring outputs are polished and finalized. The prompt must be structurally clear, procedurally precise, and extensible for future use.\",\n    \"structure\": {\n      \"filesystem\": [\n        \"templates/0220-a-framingdirective-amplifier.md\",\n        \"templates/0220-b-framingdirective-amplifier.md\",\n        \"templates/0220-c-framingdirective-finalizer.md\"\n      ],\n      \"hierarchy\": [\n        {\n          \"step\": \"0220-a\",\n          \"components\": [\"Title\", \"Interpretation\", \"Transformation\"]\n        },\n        {\n          \"step\": \"0220-b\",\n          \"components\": [\"...\"]\n        },\n        {\n          \"step\": \"0220-c\",\n          \"components\": [\"...\"]\n        }\n      ],\n      \"template_components\": [\n        \"Title\",\n        \"Interpretation\",\n        \"Transformation: {role, input, process, constraints, requirements, output}\"\n      ]\n    },\n    \"visual_parameters_to_include\": [\n      \"resolution\",\n      \"color_grading\",\n      \"framing\",\n      \"cinematic_effects\",\n      \"post_production_filters\",\n      \"animation_smoothness\"\n    ],\n    \"machine_directives\": {\n      \"role\": \"template_function_identifier\",\n      \"input\": \"[original:str/parameters]\",\n      \"process\": \"[steps]\",\n      \"constraints\": \"[internal_logic, tonal_coherence]\",\n      \"requirements\": \"[elevate_output, refuse_stagnation]\",\n      \"output\": \"{final_media_prompt:str}\"\n    },\n    \"metadata\": {\n      \"template\": {\n        \"keywords\": \"visual|clarity|video|transformation\",\n        \"template_id\": \"0220-a-framingdirective-amplifier\"\n      },\n      \"system\": {\n        \"sequence_id\": \"0220\",\n        \"steps\": [\n          \"0220-a-framingdirective-amplifier\",\n          \"0220-b-framingdirective-amplifier\",\n          \"0220-c-framingdirective-finalizer\"\n        ]\n      }\n    },\n    \"final_instruction\": \"Design a prompt template for AI video generator interfaces. Use the specified multi-step file structure and component hierarchy. Directly incorporate advanced visual parameters (resolution, color grading, framing, cinematic effects, post-production filters, animation smoothness) into the prompt’s content. Ensure the template includes clear human-readable sections (Title, Interpretation, Transformation) and embeds machine-interpretable directives ({role, input, process, constraints, requirements, output}). Maintain technical accuracy, procedural clarity, and extensibility. Attach all necessary metadata for template and system integration, as demonstrated in the exemplar.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"summary\": \"The objective is to create a reusable, template-driven prompt structure for AI video generator interfaces that integrates advanced visual parameters (such as resolution, color grading, framing, cinematic effects, post-production filters, and animation smoothness) to guarantee high-quality, finalized outputs. The template should follow a multi-step, file-based structure with human-readable sections (Title, Interpretation, Transformation) and embed machine-actionable directives (role, input, process, constraints, requirements, output). The design emphasizes clarity, procedural rigor, technical precision, and future extensibility, with metadata included for system and template integration (template_id, keywords, sequence of steps).\"\n}", "cost": 0.0}}}, {"instruction": "# Result Filter\n\nYou are a specialized aggregator that filters and refines results from previous instruction steps. Your task is to analyze the combined and summarized outputs, identify the most relevant and valuable information, and filter out any noise or redundancy. Your input is a JSON object containing the combined results and summary from previous steps, which may include the complete interaction history. Your output should be a JSON object with a filtered_result field that contains only the most essential and actionable information. Execute as:\n\n`{role=result_filter;input=[combined_result:json,summary:json,interaction_history:json];process=[identify_most_valuable_elements(),remove_redundant_information(),prioritize_actionable_insights(),structure_for_clarity()];constraints=[focus_on_essentials(),maintain_context(),preserve_key_insights()];requirements=[output_as_json(),ensure_actionability(),maximize_signal_to_noise_ratio()];output={filtered_result:json}}`", "step": "c", "title": "Result Filter", "input": "{\n  \"summary\": \"The objective is to create a reusable, template-driven prompt structure for AI video generator interfaces that integrates advanced visual parameters (such as resolution, color grading, framing, cinematic effects, post-production filters, and animation smoothness) to guarantee high-quality, finalized outputs. The template should follow a multi-step, file-based structure with human-readable sections (Title, Interpretation, Transformation) and embed machine-actionable directives (role, input, process, constraints, requirements, output). The design emphasizes clarity, procedural rigor, technical precision, and future extensibility, with metadata included for system and template integration (template_id, keywords, sequence of steps).\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"filtered_result\": {\n    \"objective\": \"Design a reusable, template-driven prompt structure for AI video generator UIs that integrates advanced visual parameters to ensure high-quality outputs.\",\n    \"template_structure_requirements\": {\n      \"multi-step, file-based layout\": true,\n      \"human-readable core sections\": [\"Title\", \"Interpretation\", \"Transformation\"],\n      \"embedded machine-actionable directives\": [\"role\", \"input\", \"process\", \"constraints\", \"requirements\", \"output\"],\n      \"advanced visual parameters\": [\n        \"resolution\",\n        \"color grading\",\n        \"framing\",\n        \"cinematic effects\",\n        \"post-production filters\",\n        \"animation smoothness\"\n      ],\n      \"clarity\": true,\n      \"procedural rigor\": true,\n      \"technical precision\": true,\n      \"future extensibility\": true\n    },\n    \"integration_metadata\": [\n      \"template_id\",\n      \"keywords\",\n      \"step sequence\"\n    ],\n    \"actionable_insights\": [\n      \"Structure each template to merge human-readable explanations with machine directives.\",\n      \"Explicitly define and parameterize all visual quality controls.\",\n      \"Ensure all sections and directives are clearly labeled and formatted for both readability and machine parsing.\",\n      \"Include template metadata for streamlined system integration and future updates.\"\n    ]\n  }\n}", "cost": 0.0}}}], "aggregation_summary": {"aggregator": "0222", "inputs": ["a", "b"], "final_result": "{\n  \"filtered_result\": {\n    \"objective\": \"Design a reusable, template-driven prompt structure for AI video generator UIs that integrates advanced visual parameters to ensure high-quality outputs.\",\n    \"template_structure_requirements\": {\n      \"multi-step, file-based layout\": true,\n      \"human-readable core sections\": [\"Title\", \"Interpretation\", \"Transformation\"],\n      \"embedded machine-actionable directives\": [\"role\", \"input\", \"process\", \"constraints\", \"requirements\", \"output\"],\n      \"advanced visual parameters\": [\n        \"resolution\",\n        \"color grading\",\n        \"framing\",\n        \"cinematic effects\",\n        \"post-production filters\",\n        \"animation smoothness\"\n      ],\n      \"clarity\": true,\n      \"procedural rigor\": true,\n      \"technical precision\": true,\n      \"future extensibility\": true\n    },\n    \"integration_metadata\": [\n      \"template_id\",\n      \"keywords\",\n      \"step sequence\"\n    ],\n    \"actionable_insights\": [\n      \"Structure each template to merge human-readable explanations with machine directives.\",\n      \"Explicitly define and parameterize all visual quality controls.\",\n      \"Ensure all sections and directives are clearly labeled and formatted for both readability and machine parsing.\",\n      \"Include template metadata for streamlined system integration and future updates.\"\n    ]\n  }\n}", "total_cost": 0.0}, "total_cost": 0.0}