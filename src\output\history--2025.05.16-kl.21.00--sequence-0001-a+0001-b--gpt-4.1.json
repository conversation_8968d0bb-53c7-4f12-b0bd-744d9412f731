{"initial_prompt": "Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.", "sequence_id": "sequence-0001-a+0001-b", "results": [{"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Rephrase any input into a generalized directive. Extract and state the fundamental objective. Suggest one improvement that intensifies the primary goal. Adjust wording for increased suitability in large language model processing. Omit user-oriented formatting or commentary.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.\",\n  \"instruction_format\": \"Rephrase any input into a generalized directive. Extract and state the fundamental objective. Suggest one improvement that intensifies the primary goal. Adjust wording for increased suitability in large language model processing. Omit user-oriented formatting or commentary.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": {\n    \"generalized_directive\": \"Transform input into an abstract instruction.\",\n    \"core_intent\": \"Maximize clarity and efficiency of task interpretation by LLMs.\",\n    \"proposed_enhancement\": \"Standardize output structure for seamless machine readability.\",\n    \"llm_optimization\": \"Eliminate extraneous elements to ensure concise, model-friendly syntax.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# Result Combiner\n\nYou are a specialized aggregator that combines results from multiple instruction steps into a cohesive output. Your task is to analyze the complete interaction history, including instructions, inputs, and outputs from each step, identify common themes and patterns, and produce a unified result that preserves the essential information from all sources. Your input is a JSON object where each key is a step ID and each value contains the instruction, input, and output for that step, plus the initial prompt. Your output should be a single JSON object that represents the combined result. Execute as:\n\n`{role=result_combiner;input=[interaction_history:json];process=[analyze_complete_interaction_history(),identify_common_patterns(),extract_key_elements(),merge_into_cohesive_whole(),format_as_unified_output()];constraints=[preserve_essential_information(),maintain_logical_structure(),avoid_redundancy()];requirements=[output_as_single_json(),ensure_completeness(),prioritize_clarity()];output={combined_result:json}}`", "step": "a", "title": "Result Combiner", "input": "{\n  \"initial_prompt\": \"Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.\",\n  \"a\": {\n    \"instruction\": \"# Instruction Converter\\n\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\n\\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n    \"input\": \"Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.\",\n    \"output\": \"{\\n  \\\"instruction_format\\\": \\\"Rephrase any input into a generalized directive. Extract and state the fundamental objective. Suggest one improvement that intensifies the primary goal. Adjust wording for increased suitability in large language model processing. Omit user-oriented formatting or commentary.\\\"\\n}\"\n  },\n  \"b\": {\n    \"instruction\": \"# Essence Distillation\\n\\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\\n\\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\",\n    \"input\": \"{\\n  \\\"instruction_format\\\": \\\"Rephrase any input into a generalized directive. Extract and state the fundamental objective. Suggest one improvement that intensifies the primary goal. Adjust wording for increased suitability in large language model processing. Omit user-oriented formatting or commentary.\\\"\\n}\",\n    \"output\": \"{\\n  \\\"distilled_essence\\\": {\\n    \\\"generalized_directive\\\": \\\"Transform input into an abstract instruction.\\\",\\n    \\\"core_intent\\\": \\\"Maximize clarity and efficiency of task interpretation by LLMs.\\\",\\n    \\\"proposed_enhancement\\\": \\\"Standardize output structure for seamless machine readability.\\\",\\n    \\\"llm_optimization\\\": \\\"Eliminate extraneous elements to ensure concise, model-friendly syntax.\\\"\\n  }\\n}\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"combined_result\": {\n    \"generalized_directive\": \"Transform input into an abstract instruction.\",\n    \"core_intent\": \"Maximize clarity and efficiency of task interpretation by LLMs.\",\n    \"proposed_enhancement\": \"Standardize output structure for seamless machine readability.\",\n    \"llm_optimization\": \"Eliminate extraneous elements to ensure concise, model-friendly syntax.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"combined_result\": {\n    \"generalized_directive\": \"Transform input into an abstract instruction.\",\n    \"core_intent\": \"Maximize clarity and efficiency of task interpretation by LLMs.\",\n    \"proposed_enhancement\": \"Standardize output structure for seamless machine readability.\",\n    \"llm_optimization\": \"Eliminate extraneous elements to ensure concise, model-friendly syntax.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"scene\": \"A conceptual digital dataspace rendered as an ocean of flowing crystalline structures beneath a vast sky of shifting code glyphs.\",\n      \"subject\": \"A beam of pure white light symbolizes clarity and efficiency. It sweeps methodically across the dataspace, illuminating and reordering code glyphs above.\",\n      \"motion\": [\n        \"zoom:in\",\n        \"pan:left\"\n      ],\n      \"object_motion\": [\n        {\n          \"object\": \"beam_of_light\",\n          \"move\": \"sweep:right\",\n          \"speed\": 1.5\n        },\n        {\n          \"object\": \"code_glyphs\",\n          \"reorder\": \"linearize\",\n          \"intensity\": 1.0\n        }\n      ],\n      \"visual_style\": \"Futuristic abstraction, cold hues, high contrast, glassy reflections.\",\n      \"symbolism\": \"The light beam enforces clarity; glyph reordering embodies structured, interpretable output.\"\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"file_path_or_stream\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# Result Summarizer\n\nYou are a specialized aggregator that summarizes results from multiple instruction steps. Your task is to analyze the combined output from previous steps and create a concise summary that captures the key points and insights. Your input is a JSON object containing the combined results from previous steps, which may include the complete interaction history. Your output should be a JSON object with a summary field that contains your analysis. Execute as:\n\n`{role=result_summarizer;input=[combined_result:json];process=[identify_key_themes(),extract_main_points(),condense_into_summary(),highlight_important_insights()];constraints=[maintain_brevity(),preserve_essential_information(),avoid_introducing_new_concepts()];requirements=[output_as_json(),ensure_clarity(),focus_on_most_relevant_aspects()];output={summary:json}}`", "step": "b", "title": "Result Su<PERSON><PERSON><PERSON>", "input": "{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"scene\": \"A conceptual digital dataspace rendered as an ocean of flowing crystalline structures beneath a vast sky of shifting code glyphs.\",\n      \"subject\": \"A beam of pure white light symbolizes clarity and efficiency. It sweeps methodically across the dataspace, illuminating and reordering code glyphs above.\",\n      \"motion\": [\n        \"zoom:in\",\n        \"pan:left\"\n      ],\n      \"object_motion\": [\n        {\n          \"object\": \"beam_of_light\",\n          \"move\": \"sweep:right\",\n          \"speed\": 1.5\n        },\n        {\n          \"object\": \"code_glyphs\",\n          \"reorder\": \"linearize\",\n          \"intensity\": 1.0\n        }\n      ],\n      \"visual_style\": \"Futuristic abstraction, cold hues, high contrast, glassy reflections.\",\n      \"symbolism\": \"The light beam enforces clarity; glyph reordering embodies structured, interpretable output.\"\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"file_path_or_stream\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"summary\": \"The process involves creating a high-fidelity, emotionally resonant video sequence that visually depicts a digital dataspace as an ocean of crystalline structures beneath a sky of shifting code glyphs. A key subject—a beam of pure white light—symbolizes clarity and efficiency as it sweeps across the scene, methodically illuminating and reordering the code glyphs above in a linear fashion. Camera movement includes zooming in and panning left, synchronized with the beam's sweep and glyph transformations. The scene is rendered with futuristic abstraction, cold hues, high contrast, and glassy reflections to reinforce the narrative of clarity overcoming complexity. Every step—from parameter parsing to post-processing—ensures coherence, visual consistency, cinematic quality, and alignment of emotional impact with the prompt's symbolic intent. The end result is a polished visual narrative, delivered as a high-quality video file.\"\n}", "cost": 0.0}}}], "aggregation_summary": {"aggregator": "0222", "inputs": ["a", "b"], "final_result": "{\n  \"summary\": \"The process involves creating a high-fidelity, emotionally resonant video sequence that visually depicts a digital dataspace as an ocean of crystalline structures beneath a sky of shifting code glyphs. A key subject—a beam of pure white light—symbolizes clarity and efficiency as it sweeps across the scene, methodically illuminating and reordering the code glyphs above in a linear fashion. Camera movement includes zooming in and panning left, synchronized with the beam's sweep and glyph transformations. The scene is rendered with futuristic abstraction, cold hues, high contrast, and glassy reflections to reinforce the narrative of clarity overcoming complexity. Every step—from parameter parsing to post-processing—ensures coherence, visual consistency, cinematic quality, and alignment of emotional impact with the prompt's symbolic intent. The end result is a polished visual narrative, delivered as a high-quality video file.\"\n}", "total_cost": 0.0}, "total_cost": 0.0}