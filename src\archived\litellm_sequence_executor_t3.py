#!/usr/bin/env python3

# =============================================================================
# SECTION 1: Core Imports & Environment Setup
# =============================================================================
import asyncio
import json
import os
import sys
import argparse
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable

# External Dependencies
from pydantic import BaseModel, Field  # For data validation and structuring
import litellm                     # Abstraction layer for LLM API calls

# Internal Dependencies: Functions for template/sequence management
from templates.lvl1.templates_lvl1_md_catalog_generator import (
    load_catalog,
    get_sequence,
    get_all_sequences,
    get_system_instruction,
    regenerate_catalog
)


# =============================================================================
# SECTION 2: Centralized Configuration Management (`Config`)
# =============================================================================
class Config:
    """
    Manages LLM provider/model selection, parameters, and LiteLLM settings.
    Designed for clarity and ease of modification via centralized definitions.
    """
    # --- Environment Setup Utility ---
    @staticmethod
    def _ensure_utf8_encoding():
        """Configure stdout/stderr for UTF-8 to prevent display errors."""
        for stream in (sys.stdout, sys.stderr):
            if hasattr(stream, "reconfigure"):
                try:
                    stream.reconfigure(encoding="utf-8", errors="replace")
                except Exception:
                    pass # Ignore if reconfiguring fails (non-critical)

    # --- Provider Identifiers (Constants for reliability) ---
    PROVIDER_ANTHROPIC = "anthropic"
    PROVIDER_DEEPSEEK  = "deepseek"
    PROVIDER_GOOGLE    = "google"
    PROVIDER_OPENAI    = "openai"

    # --- Default Provider Selection ---
    # The last assignment determines the active application-wide default provider.
    DEFAULT_PROVIDER = PROVIDER_DEEPSEEK
    DEFAULT_PROVIDER = PROVIDER_ANTHROPIC
    DEFAULT_PROVIDER = PROVIDER_GOOGLE  # Active default
    DEFAULT_PROVIDER = PROVIDER_OPENAI

    # --- Provider-Specific Model Configuration & Default Selection ---
    # Defines models per provider and sets the default model for each.
    # The last "model_name"* entry within a provider's dict becomes its default.
    PROVIDER_MODELS = {
        PROVIDER_ANTHROPIC: {
            "model_name": "claude-3-opus-20240229",
            "model_name": "claude-3-sonnet-20240229",
            "model_name": "claude-3-haiku-20240307",
            "model_name": "openrouter/anthropic/claude-3.7-sonnet:beta", # Default for Anthropic
            # "temperature": 0.2, # Example provider-specific default override
        },
        PROVIDER_DEEPSEEK: {
            "model_name": "deepseek/deepseek-reasoner",
            "model_name": "deepseek/deepseek-coder",
            "model_name": "deepseek/deepseek-chat", # Default for DeepSeek
        },
        PROVIDER_GOOGLE: {
            "model_name": "gemini/gemini-1.5-flash-latest",
            "model_name": "gemini/gemini-1.5-pro", # Default for Google
        },
        PROVIDER_OPENAI: {
            "model_name": "gpt-4o",
            "model_name": "gpt-4o-mini",
            "model_name": "gpt-3.5-turbo",
            "model_name": "o3-mini", # Default for OpenAI
        },
    }

    # --- Name to LiteLLM Model ID Mapping ---
    # Translates short names used in CLI/config to specific LiteLLM identifiers.
    MODEL_REGISTRY = {
        # OpenAI
        "gpt-4o": "gpt-4o", "gpt-4o-mini": "gpt-4o-mini", "gpt-4-turbo": "gpt-4-turbo",
        "gpt-4": "gpt-4", "gpt-3.5-turbo": "gpt-3.5-turbo", "o3-mini": "o3-mini",
        # Anthropic
        "claude-3-opus": "anthropic/claude-3-opus-20240229",
        "claude-3-sonnet": "anthropic/claude-3-sonnet-20240229",
        "claude-3-haiku": "anthropic/claude-3-haiku-20240307",
        "claude-3.7-sonnet": "openrouter/anthropic/claude-3.7-sonnet:beta",
        # Google
        "gemini-pro": "gemini/gemini-1.5-pro", "gemini-flash": "gemini/gemini-1.5-flash-latest",
        # Deepseek
        "deepseek-reasoner": "deepseek/deepseek-reasoner", "deepseek-coder": "deepseek/deepseek-coder",
        "deepseek-chat": "deepseek/deepseek-chat",
    }

    # --- Model Resolution Logic ---
    @classmethod
    def get_default_model(cls, provider: Optional[str] = None) -> str:
        """
        Retrieves the default model name for a provider (or the application default).
        Uses the 'last assignment wins' logic in PROVIDER_MODELS.
        """
        provider = provider or cls.DEFAULT_PROVIDER
        provider_config = cls.PROVIDER_MODELS.get(provider, {})

        if "model_name" in provider_config:
            return provider_config["model_name"]

        # Fallback if a provider block somehow lacks 'model_name'
        fallbacks = {
            cls.PROVIDER_OPENAI: "gpt-3.5-turbo",
            cls.PROVIDER_ANTHROPIC: "claude-3-haiku-20240307",
            cls.PROVIDER_GOOGLE: "gemini/gemini-1.5-flash-latest",
            cls.PROVIDER_DEEPSEEK: "deepseek/deepseek-chat",
        }
        return fallbacks.get(provider, "gpt-3.5-turbo") # Ultimate fallback

    @classmethod
    def get_model_params(cls, model_name: Optional[str] = None, provider: Optional[str] = None) -> Dict[str, Any]:
        """
        Constructs LiteLLM parameters, resolving model name to ID and adding provider defaults.
        """
        provider = provider or cls.DEFAULT_PROVIDER
        model_name = model_name or cls.get_default_model(provider)

        provider_defaults = cls.PROVIDER_MODELS.get(provider, {})
        params = {k: v for k, v in provider_defaults.items() if k != "model_name"} # Get only params

        # Resolve user name to LiteLLM ID; use name if not in registry
        actual_model_id = cls.MODEL_REGISTRY.get(model_name, model_name)

        return {"model": actual_model_id, **params}

    # --- Informational Methods ---
    @classmethod
    def get_available_models(cls) -> Dict[str, List[Dict[str, Any]]]:
        """Generates a structured list of registered models, grouped by provider."""
        available_models = {}
        for provider, config_block in cls.PROVIDER_MODELS.items():
            current_default = cls.get_default_model(provider)
            provider_list = []
            for user_name, model_id in cls.MODEL_REGISTRY.items():
                # Associate model with provider based on prefix or presence in config block
                belongs_to_provider = (
                    (provider == cls.PROVIDER_OPENAI and '/' not in model_id and not model_id.startswith("openrouter/")) or
                    (provider != cls.PROVIDER_OPENAI and model_id.startswith(f"{provider}/")) or
                    (model_id in config_block.values()) or
                    (user_name == config_block.get("model_name"))
                )
                if belongs_to_provider:
                    is_default = (user_name == current_default or model_id == current_default)
                    provider_list.append({
                        "name": user_name, "model_id": model_id, "is_default": is_default
                    })
            provider_list.sort(key=lambda x: x['name']) # Sort for consistent display
            available_models[provider] = provider_list
        return available_models

    # --- LiteLLM Initialization ---
    @classmethod
    def configure_litellm(cls):
        """Configures global LiteLLM settings and terminal encoding."""
        litellm.drop_params = True     # Prevent errors from unsupported parameters
        litellm.num_retries = 3        # Retry failed API calls
        litellm.request_timeout = 120  # Set API request timeout
        litellm.set_verbose = False    # Reduce LiteLLM's own console output
        litellm.callbacks = []         # Disable default callbacks unless explicitly configured
        litellm.calculate_cost = True

        cls._ensure_utf8_encoding()    # Set terminal encoding

        # Log summary for user confirmation
        print(f"\n[Config] LiteLLM Initialized:")
        print(f"  - Default Provider: {cls.DEFAULT_PROVIDER}")
        default_model_name = cls.get_default_model()
        resolved_params = cls.get_model_params()
        print(f"  - Default Model   : {default_model_name} (ID: {resolved_params.get('model')})")
        print(f"  - Retries: {litellm.num_retries}, Timeout: {litellm.request_timeout}s")


# =============================================================================
# SECTION 3: Data Structures for Execution Results (`Pydantic Models`)
# =============================================================================
# These models define the structure for storing and validating execution results.
# Field descriptions aid understanding but don't generate runtime comments.

class ModelResponse(BaseModel):
    """Output and metadata from a single model call."""
    model: str = Field(..., description="Identifier of the model used.")
    content: str = Field(..., description="Full text content generated by the model.")
    cost: float = Field(default=0.0, description="Estimated API call cost in USD.")

class InstructionResult(BaseModel):
    """Aggregated results for one instruction step across all models."""
    instruction: str = Field(..., description="System instruction for this step.")
    step: str = Field(..., description="Sequence step identifier (e.g., 'a', '01').")
    title: str = Field(..., description="Descriptive title for the step.")
    responses: Dict[str, ModelResponse] = Field(..., description="Model names mapped to their responses.")

class ExecutionResults(BaseModel):
    """Complete results for executing a sequence against a user prompt."""
    user_prompt: str = Field(..., description="The initial user prompt.")
    sequence_id: str = Field(..., description="Identifier of the sequence executed.")
    results: List[InstructionResult] = Field(..., description="List of results, one per step.")
    total_cost: float = Field(default=0.0, description="Total estimated cost in USD.")


# =============================================================================
# SECTION 4: Cost Tracking Utility
# =============================================================================
class CostTracker:
    """Accumulates estimated costs from LLM API calls."""
    def __init__(self):
        self._total_cost: float = 0.0

    def add(self, cost: Optional[float]):
        """Adds a valid cost (positive number) to the running total."""
        if isinstance(cost, (int, float)) and cost > 0:
            self._total_cost += cost

    def total(self) -> float:
        """Returns the current accumulated cost."""
        return self._total_cost

    def reset(self):
        """Resets the cost to zero."""
        self._total_cost = 0.0


# =============================================================================
# SECTION 5: Legacy Text Sequence Support (Loading)
# =============================================================================
def load_text_sequence(sequence_name: str) -> List[str]:
    """Loads instructions from a legacy '.txt' file separated by '---'."""
    script_dir = os.path.dirname(__file__) or "."
    templates_dir = os.path.join(script_dir, "templates")
    sequence_path = os.path.join(templates_dir, f"{sequence_name}.txt")

    if not os.path.exists(sequence_path):
        raise FileNotFoundError(f"Legacy text sequence file not found: {sequence_path}")

    try:
        with open(sequence_path, "r", encoding="utf-8") as f:
            content = f.read()
        # Split by separator, strip whitespace, filter empty results
        instructions = [part.strip() for part in content.split("---") if part.strip()]
        if not instructions:
             print(f"Warning: Text sequence file '{sequence_path}' is empty or only contains separators.", file=sys.stderr)
        return instructions
    except Exception as e:
        raise IOError(f"Error reading text sequence file '{sequence_path}': {e}") from e


# =============================================================================
# SECTION 6: Core Sequence Execution Engine
# =============================================================================
async def execute_sequence(
    sequence_steps: List[tuple], # (step_id, template_data)
    user_prompt: str,
    sequence_id: str,
    models: List[str],
    output_file: str,
    cost_tracker: CostTracker,
    system_instruction_extractor: Callable[[Any], str], # Function to get system message
    **litellm_kwargs: Any # Pass-through LiteLLM parameters
) -> None:
    """
    Executes instruction steps against models, streams results to JSON, tracks cost.
    """
    execution_results_list: List[InstructionResult] = [] # In-memory results storage
    total_cost_start = cost_tracker.total()

    print(f"\n[Executor] Starting sequence '{sequence_id}'")
    print(f"[Executor] Models: {', '.join(models)}")
    print(f"[Executor] Output file: {output_file}")

    outfile = None
    try:
        outfile = open(output_file, "w", encoding="utf-8")

        # Write JSON file header
        outfile.write('{\n')
        outfile.write(f'  "user_prompt": {json.dumps(user_prompt)},\n')
        outfile.write(f'  "sequence_id": {json.dumps(sequence_id)},\n')
        outfile.write('  "results": [\n') # Start main results array

        # --- Process Each Step ---
        for i, (step_id, template_data) in enumerate(sequence_steps):
            system_instruction = system_instruction_extractor(template_data)
            title = template_data.get("parts", {}).get("title", f"Step {step_id}") if isinstance(template_data, dict) else f"Step {step_id}"

            print(f"\n--- Step {step_id}: {title} ---")

            if i > 0: outfile.write(",\n") # Comma between step objects

            # Write step metadata
            outfile.write('    {\n')
            outfile.write(f'      "instruction": {json.dumps(system_instruction)},\n')
            outfile.write(f'      "step": {json.dumps(step_id)},\n')
            outfile.write(f'      "title": {json.dumps(title)},\n')
            outfile.write('      "responses": {\n') # Start responses dict for this step

            step_model_responses: Dict[str, ModelResponse] = {}

            # --- Run Each Model for Current Step ---
            for j, model_name in enumerate(models):
                print(f"  Model: {model_name}")
                if j > 0: outfile.write(',\n') # Comma between model responses

                # Write start of this model's response object
                outfile.write(f'        {json.dumps(model_name)}: {{\n')
                outfile.write(f'          "model": {json.dumps(model_name)},\n')
                outfile.write('          "content": "') # Start content string (needs escaping)
                outfile.flush()

                full_response_content = ""
                step_cost = 0.0

                try:
                    # Prepare parameters: combine defaults with CLI overrides
                    model_params = Config.get_model_params(model_name)
                    model_params.update(litellm_kwargs)

                    # Construct messages (consider making JSON enforcement optional)
                    messages = [
                        {"role": "system", "content": system_instruction + "\n\nRESPONSE MUST BE A VALID JSON OBJECT."}, # Crucial instruction if JSON is expected
                        {"role": "user", "content": user_prompt}
                    ]

                    # Call LiteLLM asynchronously with streaming
                    response_stream = await litellm.acompletion(
                        messages=messages, stream=True, **model_params
                    )

                    # Process the stream
                    print("    Response: ", end="")
                    async for chunk in response_stream:
                        text_piece = chunk.choices[0].delta.content or ""
                        print(text_piece, end="", flush=True)
                        full_response_content += text_piece

                        # Attempt to extract cost directly from chunk metadata if available
                        current_chunk_cost = getattr(chunk, 'cost', None)
                        if isinstance(current_chunk_cost, dict) and 'total_cost' in current_chunk_cost:
                            step_cost = max(step_cost, current_chunk_cost['total_cost']) # Take highest reported cost

                    print() # Newline after stream

                except Exception as e:
                    error_message = f"LLM API Error ({model_name}): {type(e).__name__} - {e}"
                    print(f"\n    ERROR: {error_message}", file=sys.stderr)
                    full_response_content = f"Error: {error_message}"
                    step_cost = 0.0 # Failed request incurs no cost

                # --- Finalize Model Response ---
                cost_tracker.add(step_cost) # Add cost regardless of success (0 if failed)

                # Use json.dumps for robust escaping, then strip outer quotes
                escaped_content = json.dumps(full_response_content)[1:-1]

                outfile.write(escaped_content)
                outfile.write('",\n')
                outfile.write(f'          "cost": {step_cost:.6f}\n') # Write cost
                outfile.write('        }') # Close model response object
                outfile.flush()

                # Store raw result in memory
                step_model_responses[model_name] = ModelResponse(
                    model=model_name, content=full_response_content, cost=step_cost
                )

            # --- Finalize Step ---
            outfile.write('\n      }\n') # Close "responses" dictionary
            outfile.write('    }') # Close step result object
            outfile.flush()

            # Add step result to in-memory list
            execution_results_list.append(InstructionResult(
                instruction=system_instruction, step=step_id, title=title, responses=step_model_responses
            ))

        # --- Finalize JSON File ---
        total_execution_cost = cost_tracker.total() - total_cost_start
        outfile.write('\n  ],\n') # Close "results" array
        outfile.write(f'  "total_cost": {total_execution_cost:.6f}\n') # Write total cost
        outfile.write('}\n') # Close root object

    except IOError as e:
        print(f"\n[Executor] FATAL ERROR writing output file '{output_file}': {e}", file=sys.stderr)
        raise
    except Exception as e:
        print(f"\n[Executor] UNEXPECTED ERROR during execution: {type(e).__name__} - {e}", file=sys.stderr)
        raise
    finally:
        if outfile and not outfile.closed:
            outfile.close() # Ensure file is closed

    # --- Print Summary ---
    print("\n=== EXECUTION SUMMARY ===")
    print(f"Sequence ID : {sequence_id}")
    print(f"Models Used : {', '.join(models)}")
    print(f"Total Cost  : ${total_execution_cost:.6f} (estimated)")
    print(f"Results File: {output_file}")
    print("\n[Executor] Sequence execution completed.")


# =============================================================================
# SECTION 7: Command-Line Interface (CLI) & Main Application Logic
# =============================================================================
def print_available_models():
    """Prints a formatted list of models configured in the Config class."""
    models_by_provider = Config.get_available_models()
    print("\n=== Available Models (Configured) ===")
    if not models_by_provider:
        print("No models configured.")
        return

    for provider, models in models_by_provider.items():
        print(f"\nProvider: {provider.upper()}")
        if not models:
            print("  (No models registered for this provider)")
            continue
        for model_info in models:
            default_marker = " (*Default*)" if model_info["is_default"] else ""
            print(f"  - Name: {model_info['name']}{default_marker}")
            if model_info['name'] != model_info['model_id']:
                print(f"    LiteLLM ID: {model_info['model_id']}") # Show ID only if different

async def main():
    """Parses args, sets up config, loads sequence, and runs execution."""
    parser = argparse.ArgumentParser(
        description="Execute multi-step LLM instruction sequences via LiteLLM.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # --- Arguments ---
    parser.add_argument("--sequence", type=str, default="0099", help="Sequence ID (catalog) or base name (text file).")
    parser.add_argument("--prompt", type=str, help="User prompt text. Uses default if omitted.")
    parser.add_argument("--output", type=str, help="Output JSON file path. Auto-generates if omitted.")
    model_group = parser.add_mutually_exclusive_group()
    model_group.add_argument("--models", type=str, help="Comma-separated model names (e.g., 'gpt-4o,claude-3-haiku').")
    model_group.add_argument("--provider", type=str, choices=list(Config.PROVIDER_MODELS.keys()), help="Use default model for this provider.")
    parser.add_argument("--use-text", action="store_true", help="Load sequence from legacy '<sequence>.txt' file.")
    parser.add_argument("--force-regenerate", action="store_true", help="Force catalog regeneration.")
    parser.add_argument("--list-sequences", action="store_true", help="List catalog sequences and exit.")
    parser.add_argument("--list-models", action="store_true", help="List configured models and exit.")
    parser.add_argument("--temperature", type=float, default=None, metavar='FLOAT', help="Override model temperature.")
    parser.add_argument("--max-tokens", type=int, default=None, metavar='INT', help="Override max tokens generated.")

    args = parser.parse_args()

    # --- Initial Setup ---
    Config.configure_litellm()
    cost_tracker = CostTracker()

    # --- Handle Informational Flags ---
    if args.list_models:
        print_available_models()
        sys.exit(0)

    catalog = None
    if not args.use_text or args.list_sequences or args.force_regenerate:
        print("[Main] Loading/Regenerating template catalog...")
        try:
            catalog = regenerate_catalog(force=args.force_regenerate) # Load or create catalog
            if not catalog: raise ValueError("Catalog is empty or failed to load.")
            print("[Main] Catalog ready.")
        except Exception as e:
            print(f"[Main] Error loading/regenerating catalog: {e}", file=sys.stderr)
            sys.exit(1)

    if args.list_sequences:
        if not catalog: sys.exit("[Main] Catalog required for listing, but failed to load.")
        print("\n=== Available Sequences (Catalog) ===")
        all_seq_ids = get_all_sequences(catalog)
        if not all_seq_ids: print("No sequences found.")
        else:
            for seq_id in sorted(all_seq_ids):
                sequence, title, num_steps = get_sequence(catalog, seq_id), "N/A", 0
                if sequence:
                    num_steps = len(sequence)
                    try: title = sequence[0][1].get("parts", {}).get("title", "N/A") # Safely get title
                    except (IndexError, AttributeError, KeyError): pass
                print(f"  - ID: {seq_id:<6} | Steps: {num_steps:<3} | Title: {title}")
        sys.exit(0)

    # --- Determine Execution Parameters ---

    # 1. Select Models
    if args.models:
        selected_models = [m.strip() for m in args.models.split(',') if m.strip()]
        print(f"[Main] Using specified models: {selected_models}")
    elif args.provider:
        selected_models = [Config.get_default_model(args.provider)]
        print(f"[Main] Using default model for provider '{args.provider}': {selected_models}")
    else:
        selected_models = [Config.get_default_model()] # Use global default
        print(f"[Main] Using default model for default provider '{Config.DEFAULT_PROVIDER}': {selected_models}")
    if not selected_models: sys.exit("[Main] Error: No valid models selected.")

    # 2. Define User Prompt
    default_prompt = "Analyze the trade-offs between microservices and monolithic architectures."
    user_prompt = args.prompt or default_prompt
    print(f"[Main] Using prompt (preview): '{user_prompt[:100]}...'")

    # 3. Sequence ID and Source
    sequence_id = args.sequence
    source_type = "text" if args.use_text else "catalog"
    display_sequence_id = f"{source_type}-{sequence_id}"

    # 4. Output Path
    if args.output:
        output_path = args.output
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_tag = "_".join(selected_models).replace('/', '-').replace(':','-')[:30] # Sanitize/shorten tag
        output_path = f"output_{timestamp}_{display_sequence_id}_{model_tag}.json"
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir); print(f"[Main] Created output directory: {output_dir}")
        except OSError as e: sys.exit(f"[Main] Error creating output directory '{output_dir}': {e}")

    # 5. Load Sequence Steps & Instruction Extractor
    sequence_steps: List[tuple]
    system_instruction_extractor: Callable[[Any], str]
    try:
        if args.use_text:
            print(f"[Main] Loading sequence '{sequence_id}' from text file...")
            instructions = load_text_sequence(sequence_id)
            sequence_steps = [(chr(97 + i), {"raw": instr}) for i, instr in enumerate(instructions)]
            system_instruction_extractor = lambda data: data.get("raw", "") # Simple extractor for text
            print(f"[Main] Loaded {len(sequence_steps)} steps from text file.")
        else: # Use catalog
            if not catalog: sys.exit("[Main] Error: Catalog required but not loaded.")
            print(f"[Main] Loading sequence '{sequence_id}' from catalog...")
            sequence_steps = get_sequence(catalog, sequence_id)
            if not sequence_steps: sys.exit(f"[Main] Error: Sequence ID '{sequence_id}' not found in catalog.")
            system_instruction_extractor = get_system_instruction # Standard catalog extractor
            print(f"[Main] Loaded {len(sequence_steps)} steps from catalog.")
        if not sequence_steps: sys.exit("[Main] Error: No sequence steps loaded.")
    except (FileNotFoundError, IOError, ValueError, KeyError) as e:
        sys.exit(f"[Main] Error loading sequence: {e}")

    # 6. Collect LiteLLM Overrides
    litellm_overrides = {}
    if args.temperature is not None: litellm_overrides["temperature"] = args.temperature; print(f"[Main] Overriding temperature: {args.temperature}")
    if args.max_tokens is not None: litellm_overrides["max_tokens"] = args.max_tokens; print(f"[Main] Overriding max_tokens: {args.max_tokens}")

    # --- Execute ---
    try:
        await execute_sequence(
            sequence_steps=sequence_steps, user_prompt=user_prompt,
            sequence_id=display_sequence_id, models=selected_models,
            output_file=output_path, cost_tracker=cost_tracker,
            system_instruction_extractor=system_instruction_extractor,
            **litellm_overrides
        )
    except Exception as e:
        # Errors should be logged within execute_sequence, but catch residual issues
        print(f"[Main] Execution failed: {type(e).__name__} - {e}", file=sys.stderr)
        sys.exit(1)

    print("\n[Main] Process finished successfully.")


# =============================================================================
# SECTION 8: Script Entry Point
# =============================================================================
if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n[Main] Execution interrupted by user.", file=sys.stderr)
        sys.exit(130) # Exit code for Ctrl+C
    except Exception as e:
        print(f"\n[Main] An unhandled error occurred: {type(e).__name__} - {e}", file=sys.stderr)
        sys.exit(1) # General error exit code
