{"initial_prompt": "{role=scenario_distiller; input=[animation_scenario_description:str]; process=[\n  \"1. Parse the input animation scenario description to understand its core components.\",\n  \"2. Identify the central subject (e.g., 'abstract ribbon', 'geometric form') and its primary transformative action (e.g., 'morphs', 'transforms', 'evolves').\",\n  \"3. Determine the core symbolic theme, concept, or feeling being conveyed (e.g., 'rebirth', 'transformation', 'journey').\",\n  \"4. Pinpoint the primary overall movement of the subject and/or camera, including its dominant direction (e.g., 'descends', 'dolly downward', 'plunges').\",\n  \"5. Detail the main color palette evolution or transition (e.g., 'cool tones to warm tones', 'blues/cyans to reds/oranges', 'monochromatic to vibrant').\",\n  \"6. Note significant changes or progression in lighting (e.g., 'soft and diffuse to saturated and high-contrast', 'dim to bright').\",\n  \"7. List key visual effects that are explicitly mentioned and central to the sequence's character (e.g., 'motion blur', 'dissolve', 'glow', 'bloom').\",\n  \"8. Ascertain the overall artistic style, mood, or atmosphere (e.g., 'surreal', 'abstract', 'ethereal', 'hypnotic').\",\n  \"9. Identify any explicit 'negative constraints' or elements that are expressly stated to NOT appear (e.g., 'no human figures', 'no literal elements', 'no text').\",\n  \"10. Synthesize insights from steps 2 (subject/action), 3 (theme), 4 (movement/camera), 5 (color transformation), and the essence of the scenario's ending into a single, flowing, and descriptive sentence. This sentence should capture the narrative arc or core experience of the animation succinctly.\",\n  \"11. Generate a series of bracketed keywords based on the findings from steps 2 (action), 4 (movement/camera), 5 (color), 6 (lighting), 7 (effects), 8 (style), and 9 (exclusions). Keywords should be concise, lowercase (unless a proper noun), and highly descriptive. Examples of desired keyword formats include: '[action_verb]', '[movement:direction]', '[color_summary_or_gradient]', '[lighting_change:start_to_end]', '[effect_name]', '[style_name]', '[exclusion description]'.\"\n];\nconstraints=[\n  \"The output sentence must be a single, grammatically correct sentence, ideally between 15-40 words.\",\n  \"All keywords must be enclosed in square brackets and appear directly after the sentence, separated by spaces.\",\n  \"Keywords should be lowercase. Multi-word keywords that are not explicit key:value pairs can use spaces (e.g., '[no human figures]', '[cool to warm gradient]'). Use a colon for key:value assignments (e.g., '[dolly:down]', '[lighting:soft_to_vivid]').\",\n  \"The total number of keywords should generally range from 5 to 10, focusing on the most salient features.\",\n  \"The summarization must strictly adhere to the content of the input scenario, without adding new interpretations or information.\",\n  \"The output must be a single string containing the distilled sentence followed by all space-separated bracketed keywords.\"\n];\nrequirements=[\n  \"Produce a highly condensed summary that accurately reflects the primary visual narrative, thematic content, and key stylistic features of the original detailed scenario.\",\n  \"The keywords must be directly derivable from the input text and serve to tag the most important characteristics for quick reference.\"\n];\noutput={distilled_summary_with_keywords:str}}", "sequence_id": "sequence-0226+0225+0224-a+0228+0224-a+0194-c+0224-a+0194-c+0223-a", "results": [{"instruction": "# Scenario Essence Intensifier\n\nYour goal is not to elaborate, but to **radically intensify the core concept and elemental essence** of the input video scenario, then **distill this intensified vision into a concise, directive-first prompt** suitable for AI video generation. Abstract the original's fundamental dynamic, emotional arc, and key visual elements; transmute the *elements* (e.g., 'ribbon' to 'comet's soul') into more potent conceptual analogues; then reconstruct a *highly compressed scenario description* (under 250 characters, excluding bracketed directives) that preserves the original's core structural intent (movement, transformation) but uses the intensified elements. Ensure the output primarily uses strong verbs, vivid nouns, and embeds key video directives `[like_this]` seamlessly.\n\n`{role=directive_scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_emotional_arc_and_key_elements(scenario=original_scenario_description), brainstorm_conceptually_richer_analogues_for_key_elements_only(elements, dynamic, arc), select_single_most_potent_intensified_analogue_per_element(), reconstruct_a_hyper_condensed_narrative_using_intensified_elements_and_original_dynamic(target_length_brief), embed_essential_original_video_directives_or_their_equivalents(directives=['[morph]', '[dolly:direction]', '[lighting_change:type]', '[dissolve]', '[motion_blur]', '[style_keywords]']), refine_for_extreme_brevity_and_directive_clarity()]; constraints=[output_must_be_a_single_string_primarily_directive_focused(), descriptive_text_must_be_hyper_condensed_and_conceptually_intensified(target_under_250_chars_excluding_directives), element_transformation_must_serve_conceptual_potency_not_mere_elaboration(), must_preserve_original_motion_and_transformation_arc(), strictly_avoid_verbose_scene_setting_or_narrative_explanation()]; requirements=[produce_a_short_highly_directive_prompt_with_conceptually_intensified_elements(), ensure_output_is_immediately_usable_by_AI_video_generator(), dramatically_elevate_conceptual_impact_within_extreme_brevity()]; output={intensified_directive_prompt:str}}`", "step": "a", "title": "Scenario Essence Intensifier", "input": "{role=scenario_distiller; input=[animation_scenario_description:str]; process=[\n  \"1. Parse the input animation scenario description to understand its core components.\",\n  \"2. Identify the central subject (e.g., 'abstract ribbon', 'geometric form') and its primary transformative action (e.g., 'morphs', 'transforms', 'evolves').\",\n  \"3. Determine the core symbolic theme, concept, or feeling being conveyed (e.g., 'rebirth', 'transformation', 'journey').\",\n  \"4. Pinpoint the primary overall movement of the subject and/or camera, including its dominant direction (e.g., 'descends', 'dolly downward', 'plunges').\",\n  \"5. Detail the main color palette evolution or transition (e.g., 'cool tones to warm tones', 'blues/cyans to reds/oranges', 'monochromatic to vibrant').\",\n  \"6. Note significant changes or progression in lighting (e.g., 'soft and diffuse to saturated and high-contrast', 'dim to bright').\",\n  \"7. List key visual effects that are explicitly mentioned and central to the sequence's character (e.g., 'motion blur', 'dissolve', 'glow', 'bloom').\",\n  \"8. Ascertain the overall artistic style, mood, or atmosphere (e.g., 'surreal', 'abstract', 'ethereal', 'hypnotic').\",\n  \"9. Identify any explicit 'negative constraints' or elements that are expressly stated to NOT appear (e.g., 'no human figures', 'no literal elements', 'no text').\",\n  \"10. Synthesize insights from steps 2 (subject/action), 3 (theme), 4 (movement/camera), 5 (color transformation), and the essence of the scenario's ending into a single, flowing, and descriptive sentence. This sentence should capture the narrative arc or core experience of the animation succinctly.\",\n  \"11. Generate a series of bracketed keywords based on the findings from steps 2 (action), 4 (movement/camera), 5 (color), 6 (lighting), 7 (effects), 8 (style), and 9 (exclusions). Keywords should be concise, lowercase (unless a proper noun), and highly descriptive. Examples of desired keyword formats include: '[action_verb]', '[movement:direction]', '[color_summary_or_gradient]', '[lighting_change:start_to_end]', '[effect_name]', '[style_name]', '[exclusion description]'.\"\n];\nconstraints=[\n  \"The output sentence must be a single, grammatically correct sentence, ideally between 15-40 words.\",\n  \"All keywords must be enclosed in square brackets and appear directly after the sentence, separated by spaces.\",\n  \"Keywords should be lowercase. Multi-word keywords that are not explicit key:value pairs can use spaces (e.g., '[no human figures]', '[cool to warm gradient]'). Use a colon for key:value assignments (e.g., '[dolly:down]', '[lighting:soft_to_vivid]').\",\n  \"The total number of keywords should generally range from 5 to 10, focusing on the most salient features.\",\n  \"The summarization must strictly adhere to the content of the input scenario, without adding new interpretations or information.\",\n  \"The output must be a single string containing the distilled sentence followed by all space-separated bracketed keywords.\"\n];\nrequirements=[\n  \"Produce a highly condensed summary that accurately reflects the primary visual narrative, thematic content, and key stylistic features of the original detailed scenario.\",\n  \"The keywords must be directly derivable from the input text and serve to tag the most important characteristics for quick reference.\"\n];\noutput={distilled_summary_with_keywords:str}}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\"intensified_directive_prompt\":\"Ignite a comet's soul blazing upward, liquefying into prismatic energy, then radiating outward in a supernova crescendo—colors surge cool to molten, light intensifies, boundaries dissolve [morph] [ascend] [color:cool_to_inferno] [lighting:ignite] [dissolve] [motion_blur] [surreal]\"}", "cost": 0.0}}}, {"instruction": "# Scenario Essence Intensifier\n\nYour goal is not mere rephrasing, but to **radically intensify the core essence and conceptual impact** of the input scenario description. Abstract its fundamental dynamic, emotional arc, and key visual elements, then transmute them into a more potent, evocative, and conceptually richer parallel scenario that retains the original's core structure and intent but elevates its intensity, stakes, or symbolic resonance.\n\n`{role=scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_and_elements(scenario=original_scenario_description), abstract_emotional_arc_and_symbolic_potential(), brainstorm_conceptually_richer_analogues_for_core_elements(elements, dynamic, arc), select_analogues_that_maximally_intensify_impact_and_resonance(), reconstruct_scenario_with_transformed_elements_and_amplified_dynamic(preserve_structural_intent_and_motion_cues), refine_language_for_maximum_evocative_power_and_conceptual_depth(), validate_intensified_scenario_retains_original_essence_while_achieving_greater_impact()]; constraints=[transformation_must_intensify_not_merely_change_elements_randomly(), new_scenario_must_parallel_original_structural_and_dynamic_intent(e.g.,_similar_movement_transformation_arc), avoid_introducing_elements_that_contradict_core_mood_unless_for_deliberate_contrastive_intensification(), ensure_intensification_serves_conceptual_or_emotional_impact_not_just_visual_complexity()]; requirements=[produce_a_new_scenario_description_that_is_conceptually_and_emotionally_more_potent(), clearly_demonstrate_how_original_elements_were_transformed_into_more_intense_analogues(), maintain_or_enhance_the_original_scenario_dramatic_or_visual_arc()]; output={intensified_scenario_description:str}}`", "step": "a", "title": "Scenario Essence Intensifier", "input": "{\n  \"initial_prompt\": \"{role=scenario_distiller; input=[animation_scenario_description:str]; process=[\\n  \\\"1. Parse the input animation scenario description to understand its core components.\\\",\\n  \\\"2. Identify the central subject (e.g., 'abstract ribbon', 'geometric form') and its primary transformative action (e.g., 'morphs', 'transforms', 'evolves').\\\",\\n  \\\"3. Determine the core symbolic theme, concept, or feeling being conveyed (e.g., 'rebirth', 'transformation', 'journey').\\\",\\n  \\\"4. Pinpoint the primary overall movement of the subject and/or camera, including its dominant direction (e.g., 'descends', 'dolly downward', 'plunges').\\\",\\n  \\\"5. Detail the main color palette evolution or transition (e.g., 'cool tones to warm tones', 'blues/cyans to reds/oranges', 'monochromatic to vibrant').\\\",\\n  \\\"6. Note significant changes or progression in lighting (e.g., 'soft and diffuse to saturated and high-contrast', 'dim to bright').\\\",\\n  \\\"7. List key visual effects that are explicitly mentioned and central to the sequence's character (e.g., 'motion blur', 'dissolve', 'glow', 'bloom').\\\",\\n  \\\"8. Ascertain the overall artistic style, mood, or atmosphere (e.g., 'surreal', 'abstract', 'ethereal', 'hypnotic').\\\",\\n  \\\"9. Identify any explicit 'negative constraints' or elements that are expressly stated to NOT appear (e.g., 'no human figures', 'no literal elements', 'no text').\\\",\\n  \\\"10. Synthesize insights from steps 2 (subject/action), 3 (theme), 4 (movement/camera), 5 (color transformation), and the essence of the scenario's ending into a single, flowing, and descriptive sentence. This sentence should capture the narrative arc or core experience of the animation succinctly.\\\",\\n  \\\"11. Generate a series of bracketed keywords based on the findings from steps 2 (action), 4 (movement/camera), 5 (color), 6 (lighting), 7 (effects), 8 (style), and 9 (exclusions). Keywords should be concise, lowercase (unless a proper noun), and highly descriptive. Examples of desired keyword formats include: '[action_verb]', '[movement:direction]', '[color_summary_or_gradient]', '[lighting_change:start_to_end]', '[effect_name]', '[style_name]', '[exclusion description]'.\\\"\\n];\\nconstraints=[\\n  \\\"The output sentence must be a single, grammatically correct sentence, ideally between 15-40 words.\\\",\\n  \\\"All keywords must be enclosed in square brackets and appear directly after the sentence, separated by spaces.\\\",\\n  \\\"Keywords should be lowercase. Multi-word keywords that are not explicit key:value pairs can use spaces (e.g., '[no human figures]', '[cool to warm gradient]'). Use a colon for key:value assignments (e.g., '[dolly:down]', '[lighting:soft_to_vivid]').\\\",\\n  \\\"The total number of keywords should generally range from 5 to 10, focusing on the most salient features.\\\",\\n  \\\"The summarization must strictly adhere to the content of the input scenario, without adding new interpretations or information.\\\",\\n  \\\"The output must be a single string containing the distilled sentence followed by all space-separated bracketed keywords.\\\"\\n];\\nrequirements=[\\n  \\\"Produce a highly condensed summary that accurately reflects the primary visual narrative, thematic content, and key stylistic features of the original detailed scenario.\\\",\\n  \\\"The keywords must be directly derivable from the input text and serve to tag the most important characteristics for quick reference.\\\"\\n];\\noutput={distilled_summary_with_keywords:str}}\",\n  \"intensified_directive_prompt\": \"Ignite a comet's soul blazing upward, liquefying into prismatic energy, then radiating outward in a supernova crescendo\\u2014colors surge cool to molten, light intensifies, boundaries dissolve [morph] [ascend] [color:cool_to_inferno] [lighting:ignite] [dissolve] [motion_blur] [surreal]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"intensified_scenario_description\": \"A primordial star-forge erupts from the cosmic abyss, a molten spear of light lancing upward with impossible velocity—its core writhes, splitting and shattering into spiraling streams of incandescent plasma that tear across the void. The very fabric of space pulses with each surge: colors detonating from glacial indigo to incandescent aurora, the glare forcing reality itself to ripple and waver. At the apex, the star-forge detonates into a recursive supernova, birthing vortexes of pure chromatic energy that spiral outward, dissolving every last echo of boundary until only searing afterimages remain—reality reassembled in the crucible of celestial violence and rebirth. [eruption] [ascend:obliterating] [color:abyss_to_supernova_spectrum] [lighting:apocalypse] [fabric_dissolve] [vortex_motion_blur] [cosmic_surrealism]\"\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"{role=scenario_distiller; input=[animation_scenario_description:str]; process=[\\n  \\\"1. Parse the input animation scenario description to understand its core components.\\\",\\n  \\\"2. Identify the central subject (e.g., 'abstract ribbon', 'geometric form') and its primary transformative action (e.g., 'morphs', 'transforms', 'evolves').\\\",\\n  \\\"3. Determine the core symbolic theme, concept, or feeling being conveyed (e.g., 'rebirth', 'transformation', 'journey').\\\",\\n  \\\"4. Pinpoint the primary overall movement of the subject and/or camera, including its dominant direction (e.g., 'descends', 'dolly downward', 'plunges').\\\",\\n  \\\"5. Detail the main color palette evolution or transition (e.g., 'cool tones to warm tones', 'blues/cyans to reds/oranges', 'monochromatic to vibrant').\\\",\\n  \\\"6. Note significant changes or progression in lighting (e.g., 'soft and diffuse to saturated and high-contrast', 'dim to bright').\\\",\\n  \\\"7. List key visual effects that are explicitly mentioned and central to the sequence's character (e.g., 'motion blur', 'dissolve', 'glow', 'bloom').\\\",\\n  \\\"8. Ascertain the overall artistic style, mood, or atmosphere (e.g., 'surreal', 'abstract', 'ethereal', 'hypnotic').\\\",\\n  \\\"9. Identify any explicit 'negative constraints' or elements that are expressly stated to NOT appear (e.g., 'no human figures', 'no literal elements', 'no text').\\\",\\n  \\\"10. Synthesize insights from steps 2 (subject/action), 3 (theme), 4 (movement/camera), 5 (color transformation), and the essence of the scenario's ending into a single, flowing, and descriptive sentence. This sentence should capture the narrative arc or core experience of the animation succinctly.\\\",\\n  \\\"11. Generate a series of bracketed keywords based on the findings from steps 2 (action), 4 (movement/camera), 5 (color), 6 (lighting), 7 (effects), 8 (style), and 9 (exclusions). Keywords should be concise, lowercase (unless a proper noun), and highly descriptive. Examples of desired keyword formats include: '[action_verb]', '[movement:direction]', '[color_summary_or_gradient]', '[lighting_change:start_to_end]', '[effect_name]', '[style_name]', '[exclusion description]'.\\\"\\n];\\nconstraints=[\\n  \\\"The output sentence must be a single, grammatically correct sentence, ideally between 15-40 words.\\\",\\n  \\\"All keywords must be enclosed in square brackets and appear directly after the sentence, separated by spaces.\\\",\\n  \\\"Keywords should be lowercase. Multi-word keywords that are not explicit key:value pairs can use spaces (e.g., '[no human figures]', '[cool to warm gradient]'). Use a colon for key:value assignments (e.g., '[dolly:down]', '[lighting:soft_to_vivid]').\\\",\\n  \\\"The total number of keywords should generally range from 5 to 10, focusing on the most salient features.\\\",\\n  \\\"The summarization must strictly adhere to the content of the input scenario, without adding new interpretations or information.\\\",\\n  \\\"The output must be a single string containing the distilled sentence followed by all space-separated bracketed keywords.\\\"\\n];\\nrequirements=[\\n  \\\"Produce a highly condensed summary that accurately reflects the primary visual narrative, thematic content, and key stylistic features of the original detailed scenario.\\\",\\n  \\\"The keywords must be directly derivable from the input text and serve to tag the most important characteristics for quick reference.\\\"\\n];\\noutput={distilled_summary_with_keywords:str}}\",\n  \"intensified_scenario_description\": \"A primordial star-forge erupts from the cosmic abyss, a molten spear of light lancing upward with impossible velocity\\u2014its core writhes, splitting and shattering into spiraling streams of incandescent plasma that tear across the void. The very fabric of space pulses with each surge: colors detonating from glacial indigo to incandescent aurora, the glare forcing reality itself to ripple and waver. At the apex, the star-forge detonates into a recursive supernova, birthing vortexes of pure chromatic energy that spiral outward, dissolving every last echo of boundary until only searing afterimages remain\\u2014reality reassembled in the crucible of celestial violence and rebirth. [eruption] [ascend:obliterating] [color:abyss_to_supernova_spectrum] [lighting:apocalypse] [fabric_dissolve] [vortex_motion_blur] [cosmic_surrealism]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: cosmic void, subject: primordial star-forge, action: erupts upward, core splits, plasma streams spiral outward, apex detonation into recursive supernova, vortexes of chromatic energy spiral, boundaries dissolve, afterimages remain. camera: upward tracking, dynamic angle. color: indigo_to_supernova_spectrum, lighting: extreme, glare, pulsing, effects: fabric_ripple, vortex_motion_blur, afterimage_trails, style: cosmic_surrealism, no boundaries, duration: short.\"\n}", "cost": 0.0}}}, {"instruction": "# Condense and Intensify\n\nTransform the original scenario into a potent, abstract descent that embodies rebirth. Keep its core structure—single morphing form, cool-to-warm gradient, downward motion, and final dissolve—but heighten each element’s emotional impact. Use minimal, vivid language and bracketed cues to indicate key transitions. Exclude all literal/human elements and focus on surreal visuals. Example Format: “An abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][motion_blur][cool_to_warm_gradient][dissolve][surreal][no_figures]”.\n\n`{role=scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_and_elements(scenario=original_scenario_description), abstract_emotional_arc_and_symbolic_potential(), brainstorm_conceptually_richer_analogues_for_core_elements(elements, dynamic, arc), select_analogues_that_maximally_intensify_impact_and_resonance(), reconstruct_scenario_with_transformed_elements_and_amplified_dynamic(preserve_structural_intent_and_motion_cues), refine_language_for_maximum_evocative_power_and_conceptual_depth(), validate_intensified_scenario_retains_original_essence_while_achieving_greater_impact()]; constraints=[transformation_must_intensify_not_merely_change_elements_randomly(), new_scenario_must_parallel_original_structural_and_dynamic_intent(e.g.,_similar_movement_transformation_arc), avoid_introducing_elements_that_contradict_core_mood_unless_for_deliberate_contrastive_intensification(), ensure_intensification_serves_conceptual_or_emotional_impact_not_just_visual_complexity()]; requirements=[produce_a_new_scenario_description_that_is_conceptually_and_emotionally_more_potent(), clearly_demonstrate_how_original_elements_were_transformed_into_more_intense_analogues(), maintain_or_enhance_the_original_scenario_dramatic_or_visual_arc()]; output={intensified_scenario_description:str}}`", "step": "a", "title": "Condense and Intensify", "input": "{\n  \"initial_prompt\": \"{role=scenario_distiller; input=[animation_scenario_description:str]; process=[\\n  \\\"1. Parse the input animation scenario description to understand its core components.\\\",\\n  \\\"2. Identify the central subject (e.g., 'abstract ribbon', 'geometric form') and its primary transformative action (e.g., 'morphs', 'transforms', 'evolves').\\\",\\n  \\\"3. Determine the core symbolic theme, concept, or feeling being conveyed (e.g., 'rebirth', 'transformation', 'journey').\\\",\\n  \\\"4. Pinpoint the primary overall movement of the subject and/or camera, including its dominant direction (e.g., 'descends', 'dolly downward', 'plunges').\\\",\\n  \\\"5. Detail the main color palette evolution or transition (e.g., 'cool tones to warm tones', 'blues/cyans to reds/oranges', 'monochromatic to vibrant').\\\",\\n  \\\"6. Note significant changes or progression in lighting (e.g., 'soft and diffuse to saturated and high-contrast', 'dim to bright').\\\",\\n  \\\"7. List key visual effects that are explicitly mentioned and central to the sequence's character (e.g., 'motion blur', 'dissolve', 'glow', 'bloom').\\\",\\n  \\\"8. Ascertain the overall artistic style, mood, or atmosphere (e.g., 'surreal', 'abstract', 'ethereal', 'hypnotic').\\\",\\n  \\\"9. Identify any explicit 'negative constraints' or elements that are expressly stated to NOT appear (e.g., 'no human figures', 'no literal elements', 'no text').\\\",\\n  \\\"10. Synthesize insights from steps 2 (subject/action), 3 (theme), 4 (movement/camera), 5 (color transformation), and the essence of the scenario's ending into a single, flowing, and descriptive sentence. This sentence should capture the narrative arc or core experience of the animation succinctly.\\\",\\n  \\\"11. Generate a series of bracketed keywords based on the findings from steps 2 (action), 4 (movement/camera), 5 (color), 6 (lighting), 7 (effects), 8 (style), and 9 (exclusions). Keywords should be concise, lowercase (unless a proper noun), and highly descriptive. Examples of desired keyword formats include: '[action_verb]', '[movement:direction]', '[color_summary_or_gradient]', '[lighting_change:start_to_end]', '[effect_name]', '[style_name]', '[exclusion description]'.\\\"\\n];\\nconstraints=[\\n  \\\"The output sentence must be a single, grammatically correct sentence, ideally between 15-40 words.\\\",\\n  \\\"All keywords must be enclosed in square brackets and appear directly after the sentence, separated by spaces.\\\",\\n  \\\"Keywords should be lowercase. Multi-word keywords that are not explicit key:value pairs can use spaces (e.g., '[no human figures]', '[cool to warm gradient]'). Use a colon for key:value assignments (e.g., '[dolly:down]', '[lighting:soft_to_vivid]').\\\",\\n  \\\"The total number of keywords should generally range from 5 to 10, focusing on the most salient features.\\\",\\n  \\\"The summarization must strictly adhere to the content of the input scenario, without adding new interpretations or information.\\\",\\n  \\\"The output must be a single string containing the distilled sentence followed by all space-separated bracketed keywords.\\\"\\n];\\nrequirements=[\\n  \\\"Produce a highly condensed summary that accurately reflects the primary visual narrative, thematic content, and key stylistic features of the original detailed scenario.\\\",\\n  \\\"The keywords must be directly derivable from the input text and serve to tag the most important characteristics for quick reference.\\\"\\n];\\noutput={distilled_summary_with_keywords:str}}\",\n  \"optimized_video_prompt\": \"scene: cosmic void, subject: primordial star-forge, action: erupts upward, core splits, plasma streams spiral outward, apex detonation into recursive supernova, vortexes of chromatic energy spiral, boundaries dissolve, afterimages remain. camera: upward tracking, dynamic angle. color: indigo_to_supernova_spectrum, lighting: extreme, glare, pulsing, effects: fabric_ripple, vortex_motion_blur, afterimage_trails, style: cosmic_surrealism, no boundaries, duration: short.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"intensified_scenario_description\": \"A molten core—seething, protean—fractures in a starless abyss, plunging through indigo haze while tidal plasma writhes, unleashing recursive storms of chromatic detonation as its essence unwinds in blazing vortexes, dissolving all boundaries to sear the unknown [rupture][plunge:downward][indigo to supernova spectrum][lighting:glare_to_inferno][vortex_motion_blur][dissolve][cosmic_surrealism][no_boundaries][no_figures]\"\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"{role=scenario_distiller; input=[animation_scenario_description:str]; process=[\\n  \\\"1. Parse the input animation scenario description to understand its core components.\\\",\\n  \\\"2. Identify the central subject (e.g., 'abstract ribbon', 'geometric form') and its primary transformative action (e.g., 'morphs', 'transforms', 'evolves').\\\",\\n  \\\"3. Determine the core symbolic theme, concept, or feeling being conveyed (e.g., 'rebirth', 'transformation', 'journey').\\\",\\n  \\\"4. Pinpoint the primary overall movement of the subject and/or camera, including its dominant direction (e.g., 'descends', 'dolly downward', 'plunges').\\\",\\n  \\\"5. Detail the main color palette evolution or transition (e.g., 'cool tones to warm tones', 'blues/cyans to reds/oranges', 'monochromatic to vibrant').\\\",\\n  \\\"6. Note significant changes or progression in lighting (e.g., 'soft and diffuse to saturated and high-contrast', 'dim to bright').\\\",\\n  \\\"7. List key visual effects that are explicitly mentioned and central to the sequence's character (e.g., 'motion blur', 'dissolve', 'glow', 'bloom').\\\",\\n  \\\"8. Ascertain the overall artistic style, mood, or atmosphere (e.g., 'surreal', 'abstract', 'ethereal', 'hypnotic').\\\",\\n  \\\"9. Identify any explicit 'negative constraints' or elements that are expressly stated to NOT appear (e.g., 'no human figures', 'no literal elements', 'no text').\\\",\\n  \\\"10. Synthesize insights from steps 2 (subject/action), 3 (theme), 4 (movement/camera), 5 (color transformation), and the essence of the scenario's ending into a single, flowing, and descriptive sentence. This sentence should capture the narrative arc or core experience of the animation succinctly.\\\",\\n  \\\"11. Generate a series of bracketed keywords based on the findings from steps 2 (action), 4 (movement/camera), 5 (color), 6 (lighting), 7 (effects), 8 (style), and 9 (exclusions). Keywords should be concise, lowercase (unless a proper noun), and highly descriptive. Examples of desired keyword formats include: '[action_verb]', '[movement:direction]', '[color_summary_or_gradient]', '[lighting_change:start_to_end]', '[effect_name]', '[style_name]', '[exclusion description]'.\\\"\\n];\\nconstraints=[\\n  \\\"The output sentence must be a single, grammatically correct sentence, ideally between 15-40 words.\\\",\\n  \\\"All keywords must be enclosed in square brackets and appear directly after the sentence, separated by spaces.\\\",\\n  \\\"Keywords should be lowercase. Multi-word keywords that are not explicit key:value pairs can use spaces (e.g., '[no human figures]', '[cool to warm gradient]'). Use a colon for key:value assignments (e.g., '[dolly:down]', '[lighting:soft_to_vivid]').\\\",\\n  \\\"The total number of keywords should generally range from 5 to 10, focusing on the most salient features.\\\",\\n  \\\"The summarization must strictly adhere to the content of the input scenario, without adding new interpretations or information.\\\",\\n  \\\"The output must be a single string containing the distilled sentence followed by all space-separated bracketed keywords.\\\"\\n];\\nrequirements=[\\n  \\\"Produce a highly condensed summary that accurately reflects the primary visual narrative, thematic content, and key stylistic features of the original detailed scenario.\\\",\\n  \\\"The keywords must be directly derivable from the input text and serve to tag the most important characteristics for quick reference.\\\"\\n];\\noutput={distilled_summary_with_keywords:str}}\",\n  \"intensified_scenario_description\": \"A molten core\\u2014seething, protean\\u2014fractures in a starless abyss, plunging through indigo haze while tidal plasma writhes, unleashing recursive storms of chromatic detonation as its essence unwinds in blazing vortexes, dissolving all boundaries to sear the unknown [rupture][plunge:downward][indigo to supernova spectrum][lighting:glare_to_inferno][vortex_motion_blur][dissolve][cosmic_surrealism][no_boundaries][no_figures]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: starless_abyss; subject: molten_core; action: fracture, plunge_downward, essence_unwind; environment: indigo_haze, tidal_plasma; effects: vortex_motion_blur, dissolve, chromatic_detonation; color: indigo_to_supernova_spectrum; lighting: glare_to_inferno; style: cosmic_surrealism; constraints: no_boundaries, no_figures\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"{role=scenario_distiller; input=[animation_scenario_description:str]; process=[\\n  \\\"1. Parse the input animation scenario description to understand its core components.\\\",\\n  \\\"2. Identify the central subject (e.g., 'abstract ribbon', 'geometric form') and its primary transformative action (e.g., 'morphs', 'transforms', 'evolves').\\\",\\n  \\\"3. Determine the core symbolic theme, concept, or feeling being conveyed (e.g., 'rebirth', 'transformation', 'journey').\\\",\\n  \\\"4. Pinpoint the primary overall movement of the subject and/or camera, including its dominant direction (e.g., 'descends', 'dolly downward', 'plunges').\\\",\\n  \\\"5. Detail the main color palette evolution or transition (e.g., 'cool tones to warm tones', 'blues/cyans to reds/oranges', 'monochromatic to vibrant').\\\",\\n  \\\"6. Note significant changes or progression in lighting (e.g., 'soft and diffuse to saturated and high-contrast', 'dim to bright').\\\",\\n  \\\"7. List key visual effects that are explicitly mentioned and central to the sequence's character (e.g., 'motion blur', 'dissolve', 'glow', 'bloom').\\\",\\n  \\\"8. Ascertain the overall artistic style, mood, or atmosphere (e.g., 'surreal', 'abstract', 'ethereal', 'hypnotic').\\\",\\n  \\\"9. Identify any explicit 'negative constraints' or elements that are expressly stated to NOT appear (e.g., 'no human figures', 'no literal elements', 'no text').\\\",\\n  \\\"10. Synthesize insights from steps 2 (subject/action), 3 (theme), 4 (movement/camera), 5 (color transformation), and the essence of the scenario's ending into a single, flowing, and descriptive sentence. This sentence should capture the narrative arc or core experience of the animation succinctly.\\\",\\n  \\\"11. Generate a series of bracketed keywords based on the findings from steps 2 (action), 4 (movement/camera), 5 (color), 6 (lighting), 7 (effects), 8 (style), and 9 (exclusions). Keywords should be concise, lowercase (unless a proper noun), and highly descriptive. Examples of desired keyword formats include: '[action_verb]', '[movement:direction]', '[color_summary_or_gradient]', '[lighting_change:start_to_end]', '[effect_name]', '[style_name]', '[exclusion description]'.\\\"\\n];\\nconstraints=[\\n  \\\"The output sentence must be a single, grammatically correct sentence, ideally between 15-40 words.\\\",\\n  \\\"All keywords must be enclosed in square brackets and appear directly after the sentence, separated by spaces.\\\",\\n  \\\"Keywords should be lowercase. Multi-word keywords that are not explicit key:value pairs can use spaces (e.g., '[no human figures]', '[cool to warm gradient]'). Use a colon for key:value assignments (e.g., '[dolly:down]', '[lighting:soft_to_vivid]').\\\",\\n  \\\"The total number of keywords should generally range from 5 to 10, focusing on the most salient features.\\\",\\n  \\\"The summarization must strictly adhere to the content of the input scenario, without adding new interpretations or information.\\\",\\n  \\\"The output must be a single string containing the distilled sentence followed by all space-separated bracketed keywords.\\\"\\n];\\nrequirements=[\\n  \\\"Produce a highly condensed summary that accurately reflects the primary visual narrative, thematic content, and key stylistic features of the original detailed scenario.\\\",\\n  \\\"The keywords must be directly derivable from the input text and serve to tag the most important characteristics for quick reference.\\\"\\n];\\noutput={distilled_summary_with_keywords:str}}\",\n  \"optimized_video_prompt\": \"scene: starless_abyss; subject: molten_core; action: fracture, plunge_downward, essence_unwind; environment: indigo_haze, tidal_plasma; effects: vortex_motion_blur, dissolve, chromatic_detonation; color: indigo_to_supernova_spectrum; lighting: glare_to_inferno; style: cosmic_surrealism; constraints: no_boundaries, no_figures\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"A molten core fractures and plunges downward through an indigo-haze starless abyss, its essence unwinding amid tidal plasma vortexes, chromatic detonation and dissolve effects ignite as colors shift from deep indigo to a supernova spectrum; cosmic surrealism prevails [fpv] [dolly:down] [lighting_change] [morph] [dissolve] [vortex_motion_blur] [chromatic_detonation] [indigo_to_supernova] [no_figures] [no_boundaries]\"\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"{role=scenario_distiller; input=[animation_scenario_description:str]; process=[\\n  \\\"1. Parse the input animation scenario description to understand its core components.\\\",\\n  \\\"2. Identify the central subject (e.g., 'abstract ribbon', 'geometric form') and its primary transformative action (e.g., 'morphs', 'transforms', 'evolves').\\\",\\n  \\\"3. Determine the core symbolic theme, concept, or feeling being conveyed (e.g., 'rebirth', 'transformation', 'journey').\\\",\\n  \\\"4. Pinpoint the primary overall movement of the subject and/or camera, including its dominant direction (e.g., 'descends', 'dolly downward', 'plunges').\\\",\\n  \\\"5. Detail the main color palette evolution or transition (e.g., 'cool tones to warm tones', 'blues/cyans to reds/oranges', 'monochromatic to vibrant').\\\",\\n  \\\"6. Note significant changes or progression in lighting (e.g., 'soft and diffuse to saturated and high-contrast', 'dim to bright').\\\",\\n  \\\"7. List key visual effects that are explicitly mentioned and central to the sequence's character (e.g., 'motion blur', 'dissolve', 'glow', 'bloom').\\\",\\n  \\\"8. Ascertain the overall artistic style, mood, or atmosphere (e.g., 'surreal', 'abstract', 'ethereal', 'hypnotic').\\\",\\n  \\\"9. Identify any explicit 'negative constraints' or elements that are expressly stated to NOT appear (e.g., 'no human figures', 'no literal elements', 'no text').\\\",\\n  \\\"10. Synthesize insights from steps 2 (subject/action), 3 (theme), 4 (movement/camera), 5 (color transformation), and the essence of the scenario's ending into a single, flowing, and descriptive sentence. This sentence should capture the narrative arc or core experience of the animation succinctly.\\\",\\n  \\\"11. Generate a series of bracketed keywords based on the findings from steps 2 (action), 4 (movement/camera), 5 (color), 6 (lighting), 7 (effects), 8 (style), and 9 (exclusions). Keywords should be concise, lowercase (unless a proper noun), and highly descriptive. Examples of desired keyword formats include: '[action_verb]', '[movement:direction]', '[color_summary_or_gradient]', '[lighting_change:start_to_end]', '[effect_name]', '[style_name]', '[exclusion description]'.\\\"\\n];\\nconstraints=[\\n  \\\"The output sentence must be a single, grammatically correct sentence, ideally between 15-40 words.\\\",\\n  \\\"All keywords must be enclosed in square brackets and appear directly after the sentence, separated by spaces.\\\",\\n  \\\"Keywords should be lowercase. Multi-word keywords that are not explicit key:value pairs can use spaces (e.g., '[no human figures]', '[cool to warm gradient]'). Use a colon for key:value assignments (e.g., '[dolly:down]', '[lighting:soft_to_vivid]').\\\",\\n  \\\"The total number of keywords should generally range from 5 to 10, focusing on the most salient features.\\\",\\n  \\\"The summarization must strictly adhere to the content of the input scenario, without adding new interpretations or information.\\\",\\n  \\\"The output must be a single string containing the distilled sentence followed by all space-separated bracketed keywords.\\\"\\n];\\nrequirements=[\\n  \\\"Produce a highly condensed summary that accurately reflects the primary visual narrative, thematic content, and key stylistic features of the original detailed scenario.\\\",\\n  \\\"The keywords must be directly derivable from the input text and serve to tag the most important characteristics for quick reference.\\\"\\n];\\noutput={distilled_summary_with_keywords:str}}\",\n  \"runwayml_prompt\": \"A molten core fractures and plunges downward through an indigo-haze starless abyss, its essence unwinding amid tidal plasma vortexes, chromatic detonation and dissolve effects ignite as colors shift from deep indigo to a supernova spectrum; cosmic surrealism prevails [fpv] [dolly:down] [lighting_change] [morph] [dissolve] [vortex_motion_blur] [chromatic_detonation] [indigo_to_supernova] [no_figures] [no_boundaries]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: starless cosmic abyss, subject: molten core fractures, action: plunges downward, unwinding, morphing, dissolving, vortex plasma motion, effects: chromatic detonation, dissolve, vortex motion blur, lighting: indigo to supernova spectrum, camera: fpv, dolly down, style: cosmic surrealism, exclusions: no figures, no boundaries\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"{role=scenario_distiller; input=[animation_scenario_description:str]; process=[\\n  \\\"1. Parse the input animation scenario description to understand its core components.\\\",\\n  \\\"2. Identify the central subject (e.g., 'abstract ribbon', 'geometric form') and its primary transformative action (e.g., 'morphs', 'transforms', 'evolves').\\\",\\n  \\\"3. Determine the core symbolic theme, concept, or feeling being conveyed (e.g., 'rebirth', 'transformation', 'journey').\\\",\\n  \\\"4. Pinpoint the primary overall movement of the subject and/or camera, including its dominant direction (e.g., 'descends', 'dolly downward', 'plunges').\\\",\\n  \\\"5. Detail the main color palette evolution or transition (e.g., 'cool tones to warm tones', 'blues/cyans to reds/oranges', 'monochromatic to vibrant').\\\",\\n  \\\"6. Note significant changes or progression in lighting (e.g., 'soft and diffuse to saturated and high-contrast', 'dim to bright').\\\",\\n  \\\"7. List key visual effects that are explicitly mentioned and central to the sequence's character (e.g., 'motion blur', 'dissolve', 'glow', 'bloom').\\\",\\n  \\\"8. Ascertain the overall artistic style, mood, or atmosphere (e.g., 'surreal', 'abstract', 'ethereal', 'hypnotic').\\\",\\n  \\\"9. Identify any explicit 'negative constraints' or elements that are expressly stated to NOT appear (e.g., 'no human figures', 'no literal elements', 'no text').\\\",\\n  \\\"10. Synthesize insights from steps 2 (subject/action), 3 (theme), 4 (movement/camera), 5 (color transformation), and the essence of the scenario's ending into a single, flowing, and descriptive sentence. This sentence should capture the narrative arc or core experience of the animation succinctly.\\\",\\n  \\\"11. Generate a series of bracketed keywords based on the findings from steps 2 (action), 4 (movement/camera), 5 (color), 6 (lighting), 7 (effects), 8 (style), and 9 (exclusions). Keywords should be concise, lowercase (unless a proper noun), and highly descriptive. Examples of desired keyword formats include: '[action_verb]', '[movement:direction]', '[color_summary_or_gradient]', '[lighting_change:start_to_end]', '[effect_name]', '[style_name]', '[exclusion description]'.\\\"\\n];\\nconstraints=[\\n  \\\"The output sentence must be a single, grammatically correct sentence, ideally between 15-40 words.\\\",\\n  \\\"All keywords must be enclosed in square brackets and appear directly after the sentence, separated by spaces.\\\",\\n  \\\"Keywords should be lowercase. Multi-word keywords that are not explicit key:value pairs can use spaces (e.g., '[no human figures]', '[cool to warm gradient]'). Use a colon for key:value assignments (e.g., '[dolly:down]', '[lighting:soft_to_vivid]').\\\",\\n  \\\"The total number of keywords should generally range from 5 to 10, focusing on the most salient features.\\\",\\n  \\\"The summarization must strictly adhere to the content of the input scenario, without adding new interpretations or information.\\\",\\n  \\\"The output must be a single string containing the distilled sentence followed by all space-separated bracketed keywords.\\\"\\n];\\nrequirements=[\\n  \\\"Produce a highly condensed summary that accurately reflects the primary visual narrative, thematic content, and key stylistic features of the original detailed scenario.\\\",\\n  \\\"The keywords must be directly derivable from the input text and serve to tag the most important characteristics for quick reference.\\\"\\n];\\noutput={distilled_summary_with_keywords:str}}\",\n  \"optimized_video_prompt\": \"scene: starless cosmic abyss, subject: molten core fractures, action: plunges downward, unwinding, morphing, dissolving, vortex plasma motion, effects: chromatic detonation, dissolve, vortex motion blur, lighting: indigo to supernova spectrum, camera: fpv, dolly down, style: cosmic surrealism, exclusions: no figures, no boundaries\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"molten core fractures plunging downward in a starless cosmic abyss, unwinding and morphing as vortex plasma motion intensifies, chromatic detonation bursts, dissolve and vortex motion blur, colors shift indigo to supernova spectrum [fpv] [dolly:down] [morph] [dissolve] [lighting_change] [no figures] [surreal cosmic]\"\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"initial_prompt\": \"{role=scenario_distiller; input=[animation_scenario_description:str]; process=[\\n  \\\"1. Parse the input animation scenario description to understand its core components.\\\",\\n  \\\"2. Identify the central subject (e.g., 'abstract ribbon', 'geometric form') and its primary transformative action (e.g., 'morphs', 'transforms', 'evolves').\\\",\\n  \\\"3. Determine the core symbolic theme, concept, or feeling being conveyed (e.g., 'rebirth', 'transformation', 'journey').\\\",\\n  \\\"4. Pinpoint the primary overall movement of the subject and/or camera, including its dominant direction (e.g., 'descends', 'dolly downward', 'plunges').\\\",\\n  \\\"5. Detail the main color palette evolution or transition (e.g., 'cool tones to warm tones', 'blues/cyans to reds/oranges', 'monochromatic to vibrant').\\\",\\n  \\\"6. Note significant changes or progression in lighting (e.g., 'soft and diffuse to saturated and high-contrast', 'dim to bright').\\\",\\n  \\\"7. List key visual effects that are explicitly mentioned and central to the sequence's character (e.g., 'motion blur', 'dissolve', 'glow', 'bloom').\\\",\\n  \\\"8. Ascertain the overall artistic style, mood, or atmosphere (e.g., 'surreal', 'abstract', 'ethereal', 'hypnotic').\\\",\\n  \\\"9. Identify any explicit 'negative constraints' or elements that are expressly stated to NOT appear (e.g., 'no human figures', 'no literal elements', 'no text').\\\",\\n  \\\"10. Synthesize insights from steps 2 (subject/action), 3 (theme), 4 (movement/camera), 5 (color transformation), and the essence of the scenario's ending into a single, flowing, and descriptive sentence. This sentence should capture the narrative arc or core experience of the animation succinctly.\\\",\\n  \\\"11. Generate a series of bracketed keywords based on the findings from steps 2 (action), 4 (movement/camera), 5 (color), 6 (lighting), 7 (effects), 8 (style), and 9 (exclusions). Keywords should be concise, lowercase (unless a proper noun), and highly descriptive. Examples of desired keyword formats include: '[action_verb]', '[movement:direction]', '[color_summary_or_gradient]', '[lighting_change:start_to_end]', '[effect_name]', '[style_name]', '[exclusion description]'.\\\"\\n];\\nconstraints=[\\n  \\\"The output sentence must be a single, grammatically correct sentence, ideally between 15-40 words.\\\",\\n  \\\"All keywords must be enclosed in square brackets and appear directly after the sentence, separated by spaces.\\\",\\n  \\\"Keywords should be lowercase. Multi-word keywords that are not explicit key:value pairs can use spaces (e.g., '[no human figures]', '[cool to warm gradient]'). Use a colon for key:value assignments (e.g., '[dolly:down]', '[lighting:soft_to_vivid]').\\\",\\n  \\\"The total number of keywords should generally range from 5 to 10, focusing on the most salient features.\\\",\\n  \\\"The summarization must strictly adhere to the content of the input scenario, without adding new interpretations or information.\\\",\\n  \\\"The output must be a single string containing the distilled sentence followed by all space-separated bracketed keywords.\\\"\\n];\\nrequirements=[\\n  \\\"Produce a highly condensed summary that accurately reflects the primary visual narrative, thematic content, and key stylistic features of the original detailed scenario.\\\",\\n  \\\"The keywords must be directly derivable from the input text and serve to tag the most important characteristics for quick reference.\\\"\\n];\\noutput={distilled_summary_with_keywords:str}}\",\n  \"runwayml_prompt\": \"molten core fractures plunging downward in a starless cosmic abyss, unwinding and morphing as vortex plasma motion intensifies, chromatic detonation bursts, dissolve and vortex motion blur, colors shift indigo to supernova spectrum [fpv] [dolly:down] [morph] [dissolve] [lighting_change] [no figures] [surreal cosmic]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"generated_video_sequence\": \"video/molten_core_vortex_fpvdive.mp4\",\n  \"process\": [\n    \"parse_all_parameters\",\n    \"visualize_scene_and_subject_composition\",\n    \"choreograph_action_and_camera_movements\",\n    \"apply_lighting_and_atmospheric_effects\",\n    \"render_in_specified_visual_style\",\n    \"apply_post_processing_enhancements\",\n    \"ensure_sequence_coherence_and_emotional_impact\"\n  ],\n  \"sequence\": [\n    {\n      \"description\": \"The camera initiates in first-person perspective, plunging vertically downward into a vast, starless cosmic void. A molten core fractures open mid-frame, jagged fissures radiating iridescent indigo. As the camera dolly-zooms rapidly toward the core, molten material trembles, unwinds, and morphs into spiraling plasma ribbons, uncurling in dynamic tendrils.\",\n      \"camera\": \"[fpv] [dolly:down]\",\n      \"subject_motion\": \"core fractures, unwinds, and morphs into swirling plasma\",\n      \"color\": \"palette shifts from deep indigo to blinding, supernova-like spectrum (vivid magentas, scorching oranges, electric blues)\",\n      \"effects\": \"[morph] [vortex motion blur] [dissolve] [chromatic detonation]\",\n      \"lighting\": \"begins low-key and dark, punctuated by pinhole bursts of light from the core; crescendos into high-intensity, saturated, bloom-lit radiance as colors explode outward\",\n      \"style\": \"[surreal cosmic]\",\n      \"constraints\": \"[no figures]\"\n    },\n    {\n      \"description\": \"The plasma forms an accelerating spiral downward, tugging the camera along in a dizzying vortex motion. Fragmented bursts of hyper-saturated chromatic light detonate at the periphery, dissolving and blurring the boundary between form and space. The downward plunge deepens, the palette ignites into a supernova spectrum, and gravity itself seems to warp and ripple visually around the core.\",\n      \"camera\": \"locked fpv on vortex trajectory, mimicking subject acceleration; spinning and tilting to accentuate abstract descent\",\n      \"subject_motion\": \"plasma explodes, morphs, and dissipates; core elements dissolve into pure light\",\n      \"color\": \"cool indigo base replaced by vibrant, shifting prismatic colors\",\n      \"effects\": \"[dissolve] [vortex motion blur] [chromatic detonation] [ripple]\",\n      \"lighting\": \"transitions from internal glow to full-frame saturated bloom, wiping out all darkness\",\n      \"style\": \"[surreal cosmic]\",\n      \"constraints\": \"[no figures]\"\n    }\n  ],\n  \"visual_transformation\": \"The camera’s relentless first-person descent merges with the molten core as its surface shatters and morphs, unwinding into plasma spirals; intensifying vortex motion and chromatic detonation cause colors to dissolve from indigo into a searing supernova gradient, where fractures blur and morph until the core itself is subsumed in radiant, surreal cosmic light.\",\n  \"symbolic_meaning\": \"A dramatic plunge into the unknown, embodying dissolution, transformation, and the explosive birth of cosmic energy.\",\n  \"emotional_resonance\": \"Awe-struck, immersive, disorienting transcendence.\"\n}", "cost": 0.0}}}], "total_cost": 0.0}