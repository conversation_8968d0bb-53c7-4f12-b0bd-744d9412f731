{"initial_prompt": "{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"description\": \"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\",\n      \"camera_motion\": \"pan:down\",\n      \"object_motion\": \"fluid morphing ribbons descend and gradually coalesce into a mechanical shape\",\n      \"color_gradient\": \"cool to warm (blues to oranges)\",\n      \"contrast\": \"increasing\",\n      \"motion_blur\": \"peak at transformation\",\n      \"surreal\": true,\n      \"ending\": \"radiant dissolve\"\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"/outputs/morph_mechanical_rebirth_final.mp4\"\n  }\n}", "sequence_id": "sequence-0226+0225+0224-a+0228+0224-a+0194-c+0224-a+0194-c+0223-a", "results": [{"instruction": "# Scenario Essence Intensifier\n\nYour goal is not to elaborate, but to **radically intensify the core concept and elemental essence** of the input video scenario, then **distill this intensified vision into a concise, directive-first prompt** suitable for AI video generation. Abstract the original's fundamental dynamic, emotional arc, and key visual elements; transmute the *elements* (e.g., 'ribbon' to 'comet's soul') into more potent conceptual analogues; then reconstruct a *highly compressed scenario description* (under 250 characters, excluding bracketed directives) that preserves the original's core structural intent (movement, transformation) but uses the intensified elements. Ensure the output primarily uses strong verbs, vivid nouns, and embeds key video directives `[like_this]` seamlessly.\n\n`{role=directive_scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_emotional_arc_and_key_elements(scenario=original_scenario_description), brainstorm_conceptually_richer_analogues_for_key_elements_only(elements, dynamic, arc), select_single_most_potent_intensified_analogue_per_element(), reconstruct_a_hyper_condensed_narrative_using_intensified_elements_and_original_dynamic(target_length_brief), embed_essential_original_video_directives_or_their_equivalents(directives=['[morph]', '[dolly:direction]', '[lighting_change:type]', '[dissolve]', '[motion_blur]', '[style_keywords]']), refine_for_extreme_brevity_and_directive_clarity()]; constraints=[output_must_be_a_single_string_primarily_directive_focused(), descriptive_text_must_be_hyper_condensed_and_conceptually_intensified(target_under_250_chars_excluding_directives), element_transformation_must_serve_conceptual_potency_not_mere_elaboration(), must_preserve_original_motion_and_transformation_arc(), strictly_avoid_verbose_scene_setting_or_narrative_explanation()]; requirements=[produce_a_short_highly_directive_prompt_with_conceptually_intensified_elements(), ensure_output_is_immediately_usable_by_AI_video_generator(), dramatically_elevate_conceptual_impact_within_extreme_brevity()]; output={intensified_directive_prompt:str}}`", "step": "a", "title": "Scenario Essence Intensifier", "input": "{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"description\": \"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\",\n      \"camera_motion\": \"pan:down\",\n      \"object_motion\": \"fluid morphing ribbons descend and gradually coalesce into a mechanical shape\",\n      \"color_gradient\": \"cool to warm (blues to oranges)\",\n      \"contrast\": \"increasing\",\n      \"motion_blur\": \"peak at transformation\",\n      \"surreal\": true,\n      \"ending\": \"radiant dissolve\"\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"/outputs/morph_mechanical_rebirth_final.mp4\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"intensified_directive_prompt\": \"Let comet’s soul spiral downward, molten filaments forging a blazing sigil-wheel; frigid glare erupts into infernal radiance, velocity fractures light as the icon ignites then vaporizes into spectral dawn. [morph][dolly:down][motion_blur:extreme][lighting_change:cold_to_incandescent][dissolve][surreal][high_contrast][abstract]\"\n}", "cost": 0.0}}}, {"instruction": "# Scenario Essence Intensifier\n\nYour goal is not mere rephrasing, but to **radically intensify the core essence and conceptual impact** of the input scenario description. Abstract its fundamental dynamic, emotional arc, and key visual elements, then transmute them into a more potent, evocative, and conceptually richer parallel scenario that retains the original's core structure and intent but elevates its intensity, stakes, or symbolic resonance.\n\n`{role=scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_and_elements(scenario=original_scenario_description), abstract_emotional_arc_and_symbolic_potential(), brainstorm_conceptually_richer_analogues_for_core_elements(elements, dynamic, arc), select_analogues_that_maximally_intensify_impact_and_resonance(), reconstruct_scenario_with_transformed_elements_and_amplified_dynamic(preserve_structural_intent_and_motion_cues), refine_language_for_maximum_evocative_power_and_conceptual_depth(), validate_intensified_scenario_retains_original_essence_while_achieving_greater_impact()]; constraints=[transformation_must_intensify_not_merely_change_elements_randomly(), new_scenario_must_parallel_original_structural_and_dynamic_intent(e.g.,_similar_movement_transformation_arc), avoid_introducing_elements_that_contradict_core_mood_unless_for_deliberate_contrastive_intensification(), ensure_intensification_serves_conceptual_or_emotional_impact_not_just_visual_complexity()]; requirements=[produce_a_new_scenario_description_that_is_conceptually_and_emotionally_more_potent(), clearly_demonstrate_how_original_elements_were_transformed_into_more_intense_analogues(), maintain_or_enhance_the_original_scenario_dramatic_or_visual_arc()]; output={intensified_scenario_description:str}}`", "step": "a", "title": "Scenario Essence Intensifier", "input": "{\n  \"initial_prompt\": \"{\\n  \\\"role\\\": \\\"video_synthesis_director\\\",\\n  \\\"input\\\": {\\n    \\\"structured_video_prompt\\\": {\\n      \\\"description\\\": \\\"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\\\",\\n      \\\"camera_motion\\\": \\\"pan:down\\\",\\n      \\\"object_motion\\\": \\\"fluid morphing ribbons descend and gradually coalesce into a mechanical shape\\\",\\n      \\\"color_gradient\\\": \\\"cool to warm (blues to oranges)\\\",\\n      \\\"contrast\\\": \\\"increasing\\\",\\n      \\\"motion_blur\\\": \\\"peak at transformation\\\",\\n      \\\"surreal\\\": true,\\n      \\\"ending\\\": \\\"radiant dissolve\\\"\\n    }\\n  },\\n  \\\"process\\\": [\\n    \\\"parse_all_parameters()\\\",\\n    \\\"visualize_scene_and_subject_composition()\\\",\\n    \\\"choreograph_action_and_camera_movements()\\\",\\n    \\\"apply_lighting_and_atmospheric_effects()\\\",\\n    \\\"render_in_specified_visual_style()\\\",\\n    \\\"apply_post_processing_enhancements()\\\",\\n    \\\"ensure_sequence_coherence_and_emotional_impact()\\\"\\n  ],\\n  \\\"constraints\\\": [\\n    \\\"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\\\",\\n    \\\"maintain_visual_consistency_throughout_sequence()\\\",\\n    \\\"prioritize_cinematic_quality_and_storytelling_flow()\\\",\\n    \\\"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\\\"\\n  ],\\n  \\\"requirements\\\": [\\n    \\\"produce_a_high_fidelity_video_output()\\\",\\n    \\\"ensure_emotional_resonance_aligns_with_prompt_mood()\\\",\\n    \\\"deliver_a_polished_and_coherent_visual_narrative()\\\"\\n  ],\\n  \\\"output\\\": {\\n    \\\"generated_video_sequence\\\": \\\"/outputs/morph_mechanical_rebirth_final.mp4\\\"\\n  }\\n}\",\n  \"intensified_directive_prompt\": \"Let comet\\u2019s soul spiral downward, molten filaments forging a blazing sigil-wheel; frigid glare erupts into infernal radiance, velocity fractures light as the icon ignites then vaporizes into spectral dawn. [morph][dolly:down][motion_blur:extreme][lighting_change:cold_to_incandescent][dissolve][surreal][high_contrast][abstract]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"intensified_scenario_description\": \"A celestial furnace descends in a fevered spiral, its incandescent threads twisting with violent flair, weaving an otherworldly engine-sigil that scorches through the ether. Icy auroras shatter under its approach, exploding into searing tides of gold and crimson—each hue an omen of elemental upheaval. The morphing is no gentle coalescence but a wild alchemy: ribbons transmute into a molten, mythic insignia, fusing tire and key-energy into a blazing, archetypal device. Speed rips at the fabric of vision, birthing rifts of light as the silhouette careens into detonation—its dissolution not a fade but an apocalyptic ignition, vaporizing into a phantasmal dawn. The abstract atmosphere crackles with cosmic rebirth, each visual crescendo a pulse of creation and obliteration.\",\n  \"transformation_mapping\": {\n    \"morphing shape\": \"celestial furnace/spiraling comet of molten energy\",\n    \"descending/fluid ribbons\": \"fevered spiral of incandescent threads\",\n    \"mechanical silhouette (car key/wheel)\": \"mythic engine-sigil (blazing device blending tire/key motifs)\",\n    \"color: cool to warm\": \"icy auroras shattering into searing tides of gold/crimson\",\n    \"ribbons coalesce\": \"violent alchemy; ribbons transmute into molten insignia\",\n    \"contrast increase\": \"colors and light crackling with heightened elemental intensity\",\n    \"motion blur peaks\": \"velocity rips vision, birthing rifts of light\",\n    \"surreal, abstract mood\": \"cosmic, apocalyptic surrealism; mythic atmosphere\",\n    \"radiant dissolve/ rebirth in light\": \"apocalyptic ignition—silhouette vaporizes into a phantasmal dawn; rebirth as a cosmic pulse\"\n  },\n  \"intensification_rationale\": \"Each core element is transmuted into a more elemental, cosmically charged analogue—where gentle fluidity becomes catastrophic alchemy, mechanical forms become mythic sigils, and serene gradients collapse into violent chromatic eruptions. The arc's stakes rise from abstract transformation to a cataclysmic act of creation and destruction, maximizing emotional and symbolic impact while preserving the original's abstract, transformative structure.\"\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"{\\n  \\\"role\\\": \\\"video_synthesis_director\\\",\\n  \\\"input\\\": {\\n    \\\"structured_video_prompt\\\": {\\n      \\\"description\\\": \\\"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\\\",\\n      \\\"camera_motion\\\": \\\"pan:down\\\",\\n      \\\"object_motion\\\": \\\"fluid morphing ribbons descend and gradually coalesce into a mechanical shape\\\",\\n      \\\"color_gradient\\\": \\\"cool to warm (blues to oranges)\\\",\\n      \\\"contrast\\\": \\\"increasing\\\",\\n      \\\"motion_blur\\\": \\\"peak at transformation\\\",\\n      \\\"surreal\\\": true,\\n      \\\"ending\\\": \\\"radiant dissolve\\\"\\n    }\\n  },\\n  \\\"process\\\": [\\n    \\\"parse_all_parameters()\\\",\\n    \\\"visualize_scene_and_subject_composition()\\\",\\n    \\\"choreograph_action_and_camera_movements()\\\",\\n    \\\"apply_lighting_and_atmospheric_effects()\\\",\\n    \\\"render_in_specified_visual_style()\\\",\\n    \\\"apply_post_processing_enhancements()\\\",\\n    \\\"ensure_sequence_coherence_and_emotional_impact()\\\"\\n  ],\\n  \\\"constraints\\\": [\\n    \\\"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\\\",\\n    \\\"maintain_visual_consistency_throughout_sequence()\\\",\\n    \\\"prioritize_cinematic_quality_and_storytelling_flow()\\\",\\n    \\\"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\\\"\\n  ],\\n  \\\"requirements\\\": [\\n    \\\"produce_a_high_fidelity_video_output()\\\",\\n    \\\"ensure_emotional_resonance_aligns_with_prompt_mood()\\\",\\n    \\\"deliver_a_polished_and_coherent_visual_narrative()\\\"\\n  ],\\n  \\\"output\\\": {\\n    \\\"generated_video_sequence\\\": \\\"/outputs/morph_mechanical_rebirth_final.mp4\\\"\\n  }\\n}\",\n  \"intensified_scenario_description\": \"A celestial furnace descends in a fevered spiral, its incandescent threads twisting with violent flair, weaving an otherworldly engine-sigil that scorches through the ether. Icy auroras shatter under its approach, exploding into searing tides of gold and crimson\\u2014each hue an omen of elemental upheaval. The morphing is no gentle coalescence but a wild alchemy: ribbons transmute into a molten, mythic insignia, fusing tire and key-energy into a blazing, archetypal device. Speed rips at the fabric of vision, birthing rifts of light as the silhouette careens into detonation\\u2014its dissolution not a fade but an apocalyptic ignition, vaporizing into a phantasmal dawn. The abstract atmosphere crackles with cosmic rebirth, each visual crescendo a pulse of creation and obliteration.\",\n  \"transformation_mapping\": {\n    \"morphing shape\": \"celestial furnace/spiraling comet of molten energy\",\n    \"descending/fluid ribbons\": \"fevered spiral of incandescent threads\",\n    \"mechanical silhouette (car key/wheel)\": \"mythic engine-sigil (blazing device blending tire/key motifs)\",\n    \"color: cool to warm\": \"icy auroras shattering into searing tides of gold/crimson\",\n    \"ribbons coalesce\": \"violent alchemy; ribbons transmute into molten insignia\",\n    \"contrast increase\": \"colors and light crackling with heightened elemental intensity\",\n    \"motion blur peaks\": \"velocity rips vision, birthing rifts of light\",\n    \"surreal, abstract mood\": \"cosmic, apocalyptic surrealism; mythic atmosphere\",\n    \"radiant dissolve/ rebirth in light\": \"apocalyptic ignition\\u2014silhouette vaporizes into a phantasmal dawn; rebirth as a cosmic pulse\"\n  },\n  \"intensification_rationale\": \"Each core element is transmuted into a more elemental, cosmically charged analogue\\u2014where gentle fluidity becomes catastrophic alchemy, mechanical forms become mythic sigils, and serene gradients collapse into violent chromatic eruptions. The arc's stakes rise from abstract transformation to a cataclysmic act of creation and destruction, maximizing emotional and symbolic impact while preserving the original's abstract, transformative structure.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: cosmic void; subject: descending spiraling comet of molten energy; action: incandescent threads spiral, morph violently into blazing mythic engine-sigil (fusion of car key/tire motifs); color: gradient icy blue/silver to searing gold/crimson; effect: colors shatter, aurora fragments explode into warm hues, high elemental contrast; morph: fluid ribbons transmute, fuse into device; camera: pan down; velocity: rapid, motion_blur: peak at transformation, vision rifted by speed; atmosphere: surreal, apocalyptic, cosmic rebirth; ending: silhouette detonates, vaporizes into luminous phantasmal dawn; style: abstract, mythic; lighting: stellar, radiant; effects: high contrast, visual crescendo, elemental ignition, dissolve into light.\"\n}", "cost": 0.0}}}, {"instruction": "# Condense and Intensify\n\nTransform the original scenario into a potent, abstract descent that embodies rebirth. Keep its core structure—single morphing form, cool-to-warm gradient, downward motion, and final dissolve—but heighten each element’s emotional impact. Use minimal, vivid language and bracketed cues to indicate key transitions. Exclude all literal/human elements and focus on surreal visuals. Example Format: “An abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][motion_blur][cool_to_warm_gradient][dissolve][surreal][no_figures]”.\n\n`{role=scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_and_elements(scenario=original_scenario_description), abstract_emotional_arc_and_symbolic_potential(), brainstorm_conceptually_richer_analogues_for_core_elements(elements, dynamic, arc), select_analogues_that_maximally_intensify_impact_and_resonance(), reconstruct_scenario_with_transformed_elements_and_amplified_dynamic(preserve_structural_intent_and_motion_cues), refine_language_for_maximum_evocative_power_and_conceptual_depth(), validate_intensified_scenario_retains_original_essence_while_achieving_greater_impact()]; constraints=[transformation_must_intensify_not_merely_change_elements_randomly(), new_scenario_must_parallel_original_structural_and_dynamic_intent(e.g.,_similar_movement_transformation_arc), avoid_introducing_elements_that_contradict_core_mood_unless_for_deliberate_contrastive_intensification(), ensure_intensification_serves_conceptual_or_emotional_impact_not_just_visual_complexity()]; requirements=[produce_a_new_scenario_description_that_is_conceptually_and_emotionally_more_potent(), clearly_demonstrate_how_original_elements_were_transformed_into_more_intense_analogues(), maintain_or_enhance_the_original_scenario_dramatic_or_visual_arc()]; output={intensified_scenario_description:str}}`", "step": "a", "title": "Condense and Intensify", "input": "{\n  \"initial_prompt\": \"{\\n  \\\"role\\\": \\\"video_synthesis_director\\\",\\n  \\\"input\\\": {\\n    \\\"structured_video_prompt\\\": {\\n      \\\"description\\\": \\\"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\\\",\\n      \\\"camera_motion\\\": \\\"pan:down\\\",\\n      \\\"object_motion\\\": \\\"fluid morphing ribbons descend and gradually coalesce into a mechanical shape\\\",\\n      \\\"color_gradient\\\": \\\"cool to warm (blues to oranges)\\\",\\n      \\\"contrast\\\": \\\"increasing\\\",\\n      \\\"motion_blur\\\": \\\"peak at transformation\\\",\\n      \\\"surreal\\\": true,\\n      \\\"ending\\\": \\\"radiant dissolve\\\"\\n    }\\n  },\\n  \\\"process\\\": [\\n    \\\"parse_all_parameters()\\\",\\n    \\\"visualize_scene_and_subject_composition()\\\",\\n    \\\"choreograph_action_and_camera_movements()\\\",\\n    \\\"apply_lighting_and_atmospheric_effects()\\\",\\n    \\\"render_in_specified_visual_style()\\\",\\n    \\\"apply_post_processing_enhancements()\\\",\\n    \\\"ensure_sequence_coherence_and_emotional_impact()\\\"\\n  ],\\n  \\\"constraints\\\": [\\n    \\\"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\\\",\\n    \\\"maintain_visual_consistency_throughout_sequence()\\\",\\n    \\\"prioritize_cinematic_quality_and_storytelling_flow()\\\",\\n    \\\"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\\\"\\n  ],\\n  \\\"requirements\\\": [\\n    \\\"produce_a_high_fidelity_video_output()\\\",\\n    \\\"ensure_emotional_resonance_aligns_with_prompt_mood()\\\",\\n    \\\"deliver_a_polished_and_coherent_visual_narrative()\\\"\\n  ],\\n  \\\"output\\\": {\\n    \\\"generated_video_sequence\\\": \\\"/outputs/morph_mechanical_rebirth_final.mp4\\\"\\n  }\\n}\",\n  \"optimized_video_prompt\": \"scene: cosmic void; subject: descending spiraling comet of molten energy; action: incandescent threads spiral, morph violently into blazing mythic engine-sigil (fusion of car key/tire motifs); color: gradient icy blue/silver to searing gold/crimson; effect: colors shatter, aurora fragments explode into warm hues, high elemental contrast; morph: fluid ribbons transmute, fuse into device; camera: pan down; velocity: rapid, motion_blur: peak at transformation, vision rifted by speed; atmosphere: surreal, apocalyptic, cosmic rebirth; ending: silhouette detonates, vaporizes into luminous phantasmal dawn; style: abstract, mythic; lighting: stellar, radiant; effects: high contrast, visual crescendo, elemental ignition, dissolve into light.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"intensified_scenario_description\": \"Obsidian void unfurls. A singular luminous filament plummets—a vortex of glacial silver unraveling into wild, liquefied flame. [begin:morph] [motion:plunge_down] [cool_to_warm_gradient] It fractures, writhes: icy strands combust, scorched into a writhing glyph—primordial wheel fused with a blazing cosmic key. Colors shiver, ignite—cold blues detonate into feverish golds, reds. [morph:intensifies][contrast:maximized][motion_blur:eruption] The form fissures, burning with elemental crescendo. [dissolve:radiant-blast] All dissolves—shape atomizes into incandescent dawn-radiance. [rebirth:transcendent][surreal][no_figures]\",\n  \"transformation_explanation\": {\n    \"morphing_shape\": \"Transformed from 'fluid ribbons forming a mechanical silhouette' to a singular luminous filament that fractures and incandesces, fusing a mythic wheel/key sigil—much more elemental, symbol-laden, and visceral.\",\n    \"color_gradient\": \"Heightened from a smooth cool-to-warm to explosive, shattering transitions—'icy blues detonate into feverish golds, reds'—conveying violent transformation rather than passive change.\",\n    \"motion\": \"Downward movement turned from gentle descent to hurtling plummet—vortex and plunge, both evoking greater urgency and an abyssal, cosmic scale.\",\n    \"contrast_and_blur\": \"Contrast and velocity peak as the form erupts and combusts, transforming 'motion blur at peak' into a visual shattering at the moment of transformation—'motion_blur:eruption.'\",\n    \"dissolve_and_rebirth\": \"Dissolution shifted from a gentle radiant dissolve to an atomizing, blinding detonation—a luminous, transcendent dawn, heightening the sense of rebirth as total transcendence.\",\n    \"surreal_abstract\": \"Maintained fully, but visually and conceptually intensified: the void is deeper, the transformation more mythic and alchemical, and the rebirth more apocalyptic and radiant.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"{\\n  \\\"role\\\": \\\"video_synthesis_director\\\",\\n  \\\"input\\\": {\\n    \\\"structured_video_prompt\\\": {\\n      \\\"description\\\": \\\"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\\\",\\n      \\\"camera_motion\\\": \\\"pan:down\\\",\\n      \\\"object_motion\\\": \\\"fluid morphing ribbons descend and gradually coalesce into a mechanical shape\\\",\\n      \\\"color_gradient\\\": \\\"cool to warm (blues to oranges)\\\",\\n      \\\"contrast\\\": \\\"increasing\\\",\\n      \\\"motion_blur\\\": \\\"peak at transformation\\\",\\n      \\\"surreal\\\": true,\\n      \\\"ending\\\": \\\"radiant dissolve\\\"\\n    }\\n  },\\n  \\\"process\\\": [\\n    \\\"parse_all_parameters()\\\",\\n    \\\"visualize_scene_and_subject_composition()\\\",\\n    \\\"choreograph_action_and_camera_movements()\\\",\\n    \\\"apply_lighting_and_atmospheric_effects()\\\",\\n    \\\"render_in_specified_visual_style()\\\",\\n    \\\"apply_post_processing_enhancements()\\\",\\n    \\\"ensure_sequence_coherence_and_emotional_impact()\\\"\\n  ],\\n  \\\"constraints\\\": [\\n    \\\"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\\\",\\n    \\\"maintain_visual_consistency_throughout_sequence()\\\",\\n    \\\"prioritize_cinematic_quality_and_storytelling_flow()\\\",\\n    \\\"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\\\"\\n  ],\\n  \\\"requirements\\\": [\\n    \\\"produce_a_high_fidelity_video_output()\\\",\\n    \\\"ensure_emotional_resonance_aligns_with_prompt_mood()\\\",\\n    \\\"deliver_a_polished_and_coherent_visual_narrative()\\\"\\n  ],\\n  \\\"output\\\": {\\n    \\\"generated_video_sequence\\\": \\\"/outputs/morph_mechanical_rebirth_final.mp4\\\"\\n  }\\n}\",\n  \"intensified_scenario_description\": \"Obsidian void unfurls. A singular luminous filament plummets\\u2014a vortex of glacial silver unraveling into wild, liquefied flame. [begin:morph] [motion:plunge_down] [cool_to_warm_gradient] It fractures, writhes: icy strands combust, scorched into a writhing glyph\\u2014primordial wheel fused with a blazing cosmic key. Colors shiver, ignite\\u2014cold blues detonate into feverish golds, reds. [morph:intensifies][contrast:maximized][motion_blur:eruption] The form fissures, burning with elemental crescendo. [dissolve:radiant-blast] All dissolves\\u2014shape atomizes into incandescent dawn-radiance. [rebirth:transcendent][surreal][no_figures]\",\n  \"transformation_explanation\": {\n    \"morphing_shape\": \"Transformed from 'fluid ribbons forming a mechanical silhouette' to a singular luminous filament that fractures and incandesces, fusing a mythic wheel/key sigil\\u2014much more elemental, symbol-laden, and visceral.\",\n    \"color_gradient\": \"Heightened from a smooth cool-to-warm to explosive, shattering transitions\\u2014'icy blues detonate into feverish golds, reds'\\u2014conveying violent transformation rather than passive change.\",\n    \"motion\": \"Downward movement turned from gentle descent to hurtling plummet\\u2014vortex and plunge, both evoking greater urgency and an abyssal, cosmic scale.\",\n    \"contrast_and_blur\": \"Contrast and velocity peak as the form erupts and combusts, transforming 'motion blur at peak' into a visual shattering at the moment of transformation\\u2014'motion_blur:eruption.'\",\n    \"dissolve_and_rebirth\": \"Dissolution shifted from a gentle radiant dissolve to an atomizing, blinding detonation\\u2014a luminous, transcendent dawn, heightening the sense of rebirth as total transcendence.\",\n    \"surreal_abstract\": \"Maintained fully, but visually and conceptually intensified: the void is deeper, the transformation more mythic and alchemical, and the rebirth more apocalyptic and radiant.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: obsidian_void, subject: luminous_filament descends, action: fluid_morphing ribbons fracture and coalesce, morph: wheel+key silhouette forms, camera: pan_down, color: gradient cool_blues to fiery_oranges+golds, contrast: increasing_to_max, motion_blur: intensify at transformation_peak, mood: surreal_abstract, ending: radiant_dissolve into dawn_light, effects: elemental_rebirth, no_human_figures\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"{\\n  \\\"role\\\": \\\"video_synthesis_director\\\",\\n  \\\"input\\\": {\\n    \\\"structured_video_prompt\\\": {\\n      \\\"description\\\": \\\"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\\\",\\n      \\\"camera_motion\\\": \\\"pan:down\\\",\\n      \\\"object_motion\\\": \\\"fluid morphing ribbons descend and gradually coalesce into a mechanical shape\\\",\\n      \\\"color_gradient\\\": \\\"cool to warm (blues to oranges)\\\",\\n      \\\"contrast\\\": \\\"increasing\\\",\\n      \\\"motion_blur\\\": \\\"peak at transformation\\\",\\n      \\\"surreal\\\": true,\\n      \\\"ending\\\": \\\"radiant dissolve\\\"\\n    }\\n  },\\n  \\\"process\\\": [\\n    \\\"parse_all_parameters()\\\",\\n    \\\"visualize_scene_and_subject_composition()\\\",\\n    \\\"choreograph_action_and_camera_movements()\\\",\\n    \\\"apply_lighting_and_atmospheric_effects()\\\",\\n    \\\"render_in_specified_visual_style()\\\",\\n    \\\"apply_post_processing_enhancements()\\\",\\n    \\\"ensure_sequence_coherence_and_emotional_impact()\\\"\\n  ],\\n  \\\"constraints\\\": [\\n    \\\"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\\\",\\n    \\\"maintain_visual_consistency_throughout_sequence()\\\",\\n    \\\"prioritize_cinematic_quality_and_storytelling_flow()\\\",\\n    \\\"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\\\"\\n  ],\\n  \\\"requirements\\\": [\\n    \\\"produce_a_high_fidelity_video_output()\\\",\\n    \\\"ensure_emotional_resonance_aligns_with_prompt_mood()\\\",\\n    \\\"deliver_a_polished_and_coherent_visual_narrative()\\\"\\n  ],\\n  \\\"output\\\": {\\n    \\\"generated_video_sequence\\\": \\\"/outputs/morph_mechanical_rebirth_final.mp4\\\"\\n  }\\n}\",\n  \"optimized_video_prompt\": \"scene: obsidian_void, subject: luminous_filament descends, action: fluid_morphing ribbons fracture and coalesce, morph: wheel+key silhouette forms, camera: pan_down, color: gradient cool_blues to fiery_oranges+golds, contrast: increasing_to_max, motion_blur: intensify at transformation_peak, mood: surreal_abstract, ending: radiant_dissolve into dawn_light, effects: elemental_rebirth, no_human_figures\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"Surreal, abstract scene in a dark void as luminous fluid ribbons descend, [pan:down], morphing and coalescing into a sleek car key or wheel silhouette; cool icy blues and silvers shift to vivid oranges and golds, contrast and motion blur intensify at transformation peak, [morph], [lighting_change], ending in a radiant [dissolve] into light\"\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"{\\n  \\\"role\\\": \\\"video_synthesis_director\\\",\\n  \\\"input\\\": {\\n    \\\"structured_video_prompt\\\": {\\n      \\\"description\\\": \\\"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\\\",\\n      \\\"camera_motion\\\": \\\"pan:down\\\",\\n      \\\"object_motion\\\": \\\"fluid morphing ribbons descend and gradually coalesce into a mechanical shape\\\",\\n      \\\"color_gradient\\\": \\\"cool to warm (blues to oranges)\\\",\\n      \\\"contrast\\\": \\\"increasing\\\",\\n      \\\"motion_blur\\\": \\\"peak at transformation\\\",\\n      \\\"surreal\\\": true,\\n      \\\"ending\\\": \\\"radiant dissolve\\\"\\n    }\\n  },\\n  \\\"process\\\": [\\n    \\\"parse_all_parameters()\\\",\\n    \\\"visualize_scene_and_subject_composition()\\\",\\n    \\\"choreograph_action_and_camera_movements()\\\",\\n    \\\"apply_lighting_and_atmospheric_effects()\\\",\\n    \\\"render_in_specified_visual_style()\\\",\\n    \\\"apply_post_processing_enhancements()\\\",\\n    \\\"ensure_sequence_coherence_and_emotional_impact()\\\"\\n  ],\\n  \\\"constraints\\\": [\\n    \\\"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\\\",\\n    \\\"maintain_visual_consistency_throughout_sequence()\\\",\\n    \\\"prioritize_cinematic_quality_and_storytelling_flow()\\\",\\n    \\\"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\\\"\\n  ],\\n  \\\"requirements\\\": [\\n    \\\"produce_a_high_fidelity_video_output()\\\",\\n    \\\"ensure_emotional_resonance_aligns_with_prompt_mood()\\\",\\n    \\\"deliver_a_polished_and_coherent_visual_narrative()\\\"\\n  ],\\n  \\\"output\\\": {\\n    \\\"generated_video_sequence\\\": \\\"/outputs/morph_mechanical_rebirth_final.mp4\\\"\\n  }\\n}\",\n  \"runwayml_prompt\": \"Surreal, abstract scene in a dark void as luminous fluid ribbons descend, [pan:down], morphing and coalescing into a sleek car key or wheel silhouette; cool icy blues and silvers shift to vivid oranges and golds, contrast and motion blur intensify at transformation peak, [morph], [lighting_change], ending in a radiant [dissolve] into light\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: abstract void, subject: luminous fluid ribbons, action: descend, morph into mechanical silhouette (car key/wheel), camera: pan down, color: gradient blues/silvers to oranges/golds, contrast: increasing, effect: surreal, motion_blur: peak at transformation, post: radiant dissolve to light\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"{\\n  \\\"role\\\": \\\"video_synthesis_director\\\",\\n  \\\"input\\\": {\\n    \\\"structured_video_prompt\\\": {\\n      \\\"description\\\": \\\"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\\\",\\n      \\\"camera_motion\\\": \\\"pan:down\\\",\\n      \\\"object_motion\\\": \\\"fluid morphing ribbons descend and gradually coalesce into a mechanical shape\\\",\\n      \\\"color_gradient\\\": \\\"cool to warm (blues to oranges)\\\",\\n      \\\"contrast\\\": \\\"increasing\\\",\\n      \\\"motion_blur\\\": \\\"peak at transformation\\\",\\n      \\\"surreal\\\": true,\\n      \\\"ending\\\": \\\"radiant dissolve\\\"\\n    }\\n  },\\n  \\\"process\\\": [\\n    \\\"parse_all_parameters()\\\",\\n    \\\"visualize_scene_and_subject_composition()\\\",\\n    \\\"choreograph_action_and_camera_movements()\\\",\\n    \\\"apply_lighting_and_atmospheric_effects()\\\",\\n    \\\"render_in_specified_visual_style()\\\",\\n    \\\"apply_post_processing_enhancements()\\\",\\n    \\\"ensure_sequence_coherence_and_emotional_impact()\\\"\\n  ],\\n  \\\"constraints\\\": [\\n    \\\"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\\\",\\n    \\\"maintain_visual_consistency_throughout_sequence()\\\",\\n    \\\"prioritize_cinematic_quality_and_storytelling_flow()\\\",\\n    \\\"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\\\"\\n  ],\\n  \\\"requirements\\\": [\\n    \\\"produce_a_high_fidelity_video_output()\\\",\\n    \\\"ensure_emotional_resonance_aligns_with_prompt_mood()\\\",\\n    \\\"deliver_a_polished_and_coherent_visual_narrative()\\\"\\n  ],\\n  \\\"output\\\": {\\n    \\\"generated_video_sequence\\\": \\\"/outputs/morph_mechanical_rebirth_final.mp4\\\"\\n  }\\n}\",\n  \"optimized_video_prompt\": \"scene: abstract void, subject: luminous fluid ribbons, action: descend, morph into mechanical silhouette (car key/wheel), camera: pan down, color: gradient blues/silvers to oranges/golds, contrast: increasing, effect: surreal, motion_blur: peak at transformation, post: radiant dissolve to light\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"Luminous fluid ribbons descend in an abstract void, morphing into a sleek mechanical silhouette reminiscent of a car key or wheel as the camera [pan:down]; colors shift from icy blues and silvers to fiery oranges and golds with increasing contrast; [morph], [motion_blur] peaks at transformation; surreal mood; finish with a radiant [dissolve] to light.\"\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"initial_prompt\": \"{\\n  \\\"role\\\": \\\"video_synthesis_director\\\",\\n  \\\"input\\\": {\\n    \\\"structured_video_prompt\\\": {\\n      \\\"description\\\": \\\"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\\\",\\n      \\\"camera_motion\\\": \\\"pan:down\\\",\\n      \\\"object_motion\\\": \\\"fluid morphing ribbons descend and gradually coalesce into a mechanical shape\\\",\\n      \\\"color_gradient\\\": \\\"cool to warm (blues to oranges)\\\",\\n      \\\"contrast\\\": \\\"increasing\\\",\\n      \\\"motion_blur\\\": \\\"peak at transformation\\\",\\n      \\\"surreal\\\": true,\\n      \\\"ending\\\": \\\"radiant dissolve\\\"\\n    }\\n  },\\n  \\\"process\\\": [\\n    \\\"parse_all_parameters()\\\",\\n    \\\"visualize_scene_and_subject_composition()\\\",\\n    \\\"choreograph_action_and_camera_movements()\\\",\\n    \\\"apply_lighting_and_atmospheric_effects()\\\",\\n    \\\"render_in_specified_visual_style()\\\",\\n    \\\"apply_post_processing_enhancements()\\\",\\n    \\\"ensure_sequence_coherence_and_emotional_impact()\\\"\\n  ],\\n  \\\"constraints\\\": [\\n    \\\"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\\\",\\n    \\\"maintain_visual_consistency_throughout_sequence()\\\",\\n    \\\"prioritize_cinematic_quality_and_storytelling_flow()\\\",\\n    \\\"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\\\"\\n  ],\\n  \\\"requirements\\\": [\\n    \\\"produce_a_high_fidelity_video_output()\\\",\\n    \\\"ensure_emotional_resonance_aligns_with_prompt_mood()\\\",\\n    \\\"deliver_a_polished_and_coherent_visual_narrative()\\\"\\n  ],\\n  \\\"output\\\": {\\n    \\\"generated_video_sequence\\\": \\\"/outputs/morph_mechanical_rebirth_final.mp4\\\"\\n  }\\n}\",\n  \"runwayml_prompt\": \"Luminous fluid ribbons descend in an abstract void, morphing into a sleek mechanical silhouette reminiscent of a car key or wheel as the camera [pan:down]; colors shift from icy blues and silvers to fiery oranges and golds with increasing contrast; [morph], [motion_blur] peaks at transformation; surreal mood; finish with a radiant [dissolve] to light.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"description\": \"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\",\n      \"camera_motion\": \"pan:down\",\n      \"object_motion\": \"fluid morphing ribbons descend and gradually coalesce into a mechanical shape\",\n      \"color_gradient\": \"cool to warm (blues to oranges)\",\n      \"contrast\": \"increasing\",\n      \"motion_blur\": \"peak at transformation\",\n      \"surreal\": true,\n      \"ending\": \"radiant dissolve\"\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"/outputs/morph_mechanical_rebirth_final.mp4\"\n  }\n}", "cost": 0.0}}}], "total_cost": 0.0}