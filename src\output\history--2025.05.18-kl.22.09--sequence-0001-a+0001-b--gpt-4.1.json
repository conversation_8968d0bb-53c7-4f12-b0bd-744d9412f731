{"initial_prompt": "# TOPIC\n\n- How can documentation be structured as a clear, sequential progression from intent to objective, ensuring each part contributes purposefully to a singular, precise goal with every inclusion justified and each transition deliberate?\n- Design documentation as a sequential, purpose-driven chain from intent to objective. Ensure each part advances clarity and precision, with every inclusion justified and every transition intentional, creating an unbroken, motivating progression toward a singular, elegant goal.\n- Structure the documentation sequence from `01_intent-overview.md` through to `distilledObjective.md` so that each document serves as a clear, purposeful, and sequentially justified step toward a well-defined objective. Establish explicit standards and rules to ensure every document is precise, unambiguous, and directly contributes to the overarching intent, enabling a seamless, motivating, and efficient experience for all contributors. Remove metaphorical and ambiguous language in favor of concrete, operational terms that are universally understood by humans and AI systems, consistently reinforcing alignment and supporting world-class, AI-assisted development throughout the codebase.\n\n- How can we transform the ordered sequence from `01_intent-overview.md` through to `01_intent-overview.md` into a breathtakingly coherent narrative—one that embodies precision and clarity at every step, radiates simplicity, and forges an unbroken chain of intent toward an elegant, singular objective? In what way might every document become an integral, purposeful stride toward a distilled culmination, achieving not just guideline fidelity but a sense of profound, world-class impact—where each transition is decisive, each inclusion urgent, and the entire progression feels inevitable and deeply motivating for everyone who seeks excellence within the codebase?\n\n# CONTEXT\n\nSequential, purpose-driven chain from intent to objective. from the context of an instruction designed to act as a general 'rules_for_ai.md' that i'll use for autonomous ai-assisted coding\n\n# INPUT\n\n    ```\n    ## Memory Bank Structure\n\n    The Memory Bank consists of core files and optional context files, all in Markdown format. Files build upon each other in a clear hierarchy:\n\n    flowchart TD\n        PB[projectbrief.md] --> PC[productContext.md]\n        PB --> SP[systemPatterns.md]\n        PB --> TC[techContext.md]\n\n        PC --> AC[activeContext.md]\n        SP --> AC\n        TC --> AC\n\n        AC --> P[progress.md]\n\n    ### Core Files (Required)\n    1. `projectbrief.md`\n       - Foundation document that shapes all other files\n       - Created at project start if it doesn't exist\n       - Defines core requirements and goals\n       - Source of truth for project scope\n\n    2. `productContext.md`\n       - Why this project exists\n       - Problems it solves\n       - How it should work\n       - User experience goals\n\n    3. `activeContext.md`\n       - Current work focus\n       - Recent changes\n       - Next steps\n       - Active decisions and considerations\n       - Important patterns and preferences\n       - Learnings and project insights\n\n    4. `systemPatterns.md`\n       - System architecture\n       - Key technical decisions\n       - Design patterns in use\n       - Component relationships\n       - Critical implementation paths\n\n    5. `techContext.md`\n       - Technologies used\n       - Development setup\n       - Technical constraints\n       - Dependencies\n       - Tool usage patterns\n\n    6. `progress.md`\n       - What works\n       - What's left to build\n       - Current status\n       - Known issues\n       - Evolution of project decisions\n    ```\n\n# OBJECTIVE\n\nPropose a new variation specifically designed for this progression (while retaining original structure). Design documentation as a sequential, purpose-driven chain from intent to objective. Ensure each part advances clarity and precision, with every inclusion justified and every transition intentional, creating an unbroken, motivating progression toward a singular, elegant goal.\n\n    memory-bank/\n    ├── 01_intent-overview.md      # State core project intent and high-level goals to immediately orient all contributors.\n    ├── 02_context-background.md   # Summarizes essential context, including problem domain, stakeholders, and any constraints shaping the project.\n    ├── 03_existing-patterns.md    # Identifies relevant patterns, paradigms, or solutions that influence architectural or coding decisions.\n    ├── 04_tech-stack.md           # Details technologies, frameworks, and tools chosen for the project and their rationale.\n    ├── 05_current-activity.md     # Describes present focus areas, major open workstreams, and in-progress features.\n    ├── 06_progress-tracking.md    # Outlines tracking methods and status of progress, including achievements and roadblocks.\n    ├── 07_priority-tasks.md       # Lists actionable, high-priority tasks, each linked to project goals and categorized for clear ownership.\n    └── 08_distilled-objective.md  # Condense all prior insight into a single, actionable, and measurable project objective\n\n# GUIDELINES\n\nIt should be *generalized* and it should *build towards something*:\n\n    [Core Principles]\n    - Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.\n    - Maintain inherent simplicity while providing powerful functionality.\n    - Eembrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.\n    - Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.\n\n    [General Principles]\n    - Aim for simplicity, clarity, and maintainability in all project aspects\n    - Favor composition over inheritance when applicable\n    - Prioritize readability and understandability for future developers\n    - Ensure all components have a single responsibility\n    - Coding standards that promote simplicity and maintainability\n    - Document only integral decisions in a highly condensed form\n\n    [Code Organization]\n    - Evaluate the existing codebase structure and identify patterns and anti-patterns\n    - Consolidate related functionality into cohesive modules\n    - Minimize dependencies between unrelated components\n    - Optimize for developer ergonomics and intuitive navigation\n    - Balance file granularity with overall system comprehensibility\n\n# OUTPUT\n\nProvide the exact same structure as the original but replaced with one uniquely enhanced based on objective.", "sequence_id": "sequence-0001-a+0001-b", "results": [{"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "# TOPIC\n\n- How can documentation be structured as a clear, sequential progression from intent to objective, ensuring each part contributes purposefully to a singular, precise goal with every inclusion justified and each transition deliberate?\n- Design documentation as a sequential, purpose-driven chain from intent to objective. Ensure each part advances clarity and precision, with every inclusion justified and every transition intentional, creating an unbroken, motivating progression toward a singular, elegant goal.\n- Structure the documentation sequence from `01_intent-overview.md` through to `distilledObjective.md` so that each document serves as a clear, purposeful, and sequentially justified step toward a well-defined objective. Establish explicit standards and rules to ensure every document is precise, unambiguous, and directly contributes to the overarching intent, enabling a seamless, motivating, and efficient experience for all contributors. Remove metaphorical and ambiguous language in favor of concrete, operational terms that are universally understood by humans and AI systems, consistently reinforcing alignment and supporting world-class, AI-assisted development throughout the codebase.\n\n- How can we transform the ordered sequence from `01_intent-overview.md` through to `01_intent-overview.md` into a breathtakingly coherent narrative—one that embodies precision and clarity at every step, radiates simplicity, and forges an unbroken chain of intent toward an elegant, singular objective? In what way might every document become an integral, purposeful stride toward a distilled culmination, achieving not just guideline fidelity but a sense of profound, world-class impact—where each transition is decisive, each inclusion urgent, and the entire progression feels inevitable and deeply motivating for everyone who seeks excellence within the codebase?\n\n# CONTEXT\n\nSequential, purpose-driven chain from intent to objective. from the context of an instruction designed to act as a general 'rules_for_ai.md' that i'll use for autonomous ai-assisted coding\n\n# INPUT\n\n    ```\n    ## Memory Bank Structure\n\n    The Memory Bank consists of core files and optional context files, all in Markdown format. Files build upon each other in a clear hierarchy:\n\n    flowchart TD\n        PB[projectbrief.md] --> PC[productContext.md]\n        PB --> SP[systemPatterns.md]\n        PB --> TC[techContext.md]\n\n        PC --> AC[activeContext.md]\n        SP --> AC\n        TC --> AC\n\n        AC --> P[progress.md]\n\n    ### Core Files (Required)\n    1. `projectbrief.md`\n       - Foundation document that shapes all other files\n       - Created at project start if it doesn't exist\n       - Defines core requirements and goals\n       - Source of truth for project scope\n\n    2. `productContext.md`\n       - Why this project exists\n       - Problems it solves\n       - How it should work\n       - User experience goals\n\n    3. `activeContext.md`\n       - Current work focus\n       - Recent changes\n       - Next steps\n       - Active decisions and considerations\n       - Important patterns and preferences\n       - Learnings and project insights\n\n    4. `systemPatterns.md`\n       - System architecture\n       - Key technical decisions\n       - Design patterns in use\n       - Component relationships\n       - Critical implementation paths\n\n    5. `techContext.md`\n       - Technologies used\n       - Development setup\n       - Technical constraints\n       - Dependencies\n       - Tool usage patterns\n\n    6. `progress.md`\n       - What works\n       - What's left to build\n       - Current status\n       - Known issues\n       - Evolution of project decisions\n    ```\n\n# OBJECTIVE\n\nPropose a new variation specifically designed for this progression (while retaining original structure). Design documentation as a sequential, purpose-driven chain from intent to objective. Ensure each part advances clarity and precision, with every inclusion justified and every transition intentional, creating an unbroken, motivating progression toward a singular, elegant goal.\n\n    memory-bank/\n    ├── 01_intent-overview.md      # State core project intent and high-level goals to immediately orient all contributors.\n    ├── 02_context-background.md   # Summarizes essential context, including problem domain, stakeholders, and any constraints shaping the project.\n    ├── 03_existing-patterns.md    # Identifies relevant patterns, paradigms, or solutions that influence architectural or coding decisions.\n    ├── 04_tech-stack.md           # Details technologies, frameworks, and tools chosen for the project and their rationale.\n    ├── 05_current-activity.md     # Describes present focus areas, major open workstreams, and in-progress features.\n    ├── 06_progress-tracking.md    # Outlines tracking methods and status of progress, including achievements and roadblocks.\n    ├── 07_priority-tasks.md       # Lists actionable, high-priority tasks, each linked to project goals and categorized for clear ownership.\n    └── 08_distilled-objective.md  # Condense all prior insight into a single, actionable, and measurable project objective\n\n# GUIDELINES\n\nIt should be *generalized* and it should *build towards something*:\n\n    [Core Principles]\n    - Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.\n    - Maintain inherent simplicity while providing powerful functionality.\n    - Eembrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.\n    - Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.\n\n    [General Principles]\n    - Aim for simplicity, clarity, and maintainability in all project aspects\n    - Favor composition over inheritance when applicable\n    - Prioritize readability and understandability for future developers\n    - Ensure all components have a single responsibility\n    - Coding standards that promote simplicity and maintainability\n    - Document only integral decisions in a highly condensed form\n\n    [Code Organization]\n    - Evaluate the existing codebase structure and identify patterns and anti-patterns\n    - Consolidate related functionality into cohesive modules\n    - Minimize dependencies between unrelated components\n    - Optimize for developer ergonomics and intuitive navigation\n    - Balance file granularity with overall system comprehensibility\n\n# OUTPUT\n\nProvide the exact same structure as the original but replaced with one uniquely enhanced based on objective.", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Structure documentation by defining a clear, sequential progression from initial intent to a distilled, actionable objective. For each documentation file, ensure that it articulates its unique purpose, directly contributes to the overarching project goal, and connects seamlessly to both its predecessor and successor in the sequence. Justify every inclusion with concrete, operational relevance, removing ambiguous or metaphorical language. Follow these directives:\\n\\n1. Start with `01_intent-overview.md`, explicitly stating the core intent and high-level objectives that will orient all contributors from the outset.\\n2. Proceed to `02_context-background.md`, summarizing essential contextual factors such as the problem domain, stakeholders, and constraints that shape the project boundaries and inform subsequent decisions.\\n3. Document influential patterns, standards, and paradigms in `03_existing-patterns.md`, detailing those that guide technical or architectural choices critical to the project's approach.\\n4. Specify selected technologies, frameworks, and tools in `04_tech-stack.md`, providing clear rationale for each choice based on the project's needs and constraints.\\n5. Outline current development focus, major ongoing workstreams, and significant features in progress within `05_current-activity.md`, ensuring that all contributors understand present priorities.\\n6. Track progress in `06_progress-tracking.md` by reporting on accomplishments, remaining tasks, and encountered roadblocks using measurable and objective criteria.\\n7. List and categorize the highest priority actionable tasks in `07_priority-tasks.md`, mapping each task directly to project objectives and assigning clear ownership when possible.\\n8. Culminate in `08_distilled-objective.md`, synthesizing insights and decisions from all previous documents into a concise, precise, and actionable project objective.\\n\\nAcross all documentation:\\n- Enforce an explicit, linear sequence that enables contributors to traverse from intent through execution with clarity and minimal cognitive overhead.\\n- Justify every document and inclusion by its direct advancement of the project's clarity, purpose, or progress.\\n- Use precise, concrete, and universally interpretable language, consistently upholding domain-specific terminology.\\n- Adhere to standards of structural simplicity, single-responsibility, and maintainability.\\n- Remove all ambiguous, metaphorical, or non-operational language.\\n- Ensure each transition between documents is purposeful and strengthens coherency, creating an unbroken, motivating chain that leads all contributors efficiently toward the project's final, distilled objective.\\n- Regularly review and refine documentation for alignment with these directives, supporting world-class, AI-assisted codebase development.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"# TOPIC\\n\\n- How can documentation be structured as a clear, sequential progression from intent to objective, ensuring each part contributes purposefully to a singular, precise goal with every inclusion justified and each transition deliberate?\\n- Design documentation as a sequential, purpose-driven chain from intent to objective. Ensure each part advances clarity and precision, with every inclusion justified and every transition intentional, creating an unbroken, motivating progression toward a singular, elegant goal.\\n- Structure the documentation sequence from `01_intent-overview.md` through to `distilledObjective.md` so that each document serves as a clear, purposeful, and sequentially justified step toward a well-defined objective. Establish explicit standards and rules to ensure every document is precise, unambiguous, and directly contributes to the overarching intent, enabling a seamless, motivating, and efficient experience for all contributors. Remove metaphorical and ambiguous language in favor of concrete, operational terms that are universally understood by humans and AI systems, consistently reinforcing alignment and supporting world-class, AI-assisted development throughout the codebase.\\n\\n- How can we transform the ordered sequence from `01_intent-overview.md` through to `01_intent-overview.md` into a breathtakingly coherent narrative\\u2014one that embodies precision and clarity at every step, radiates simplicity, and forges an unbroken chain of intent toward an elegant, singular objective? In what way might every document become an integral, purposeful stride toward a distilled culmination, achieving not just guideline fidelity but a sense of profound, world-class impact\\u2014where each transition is decisive, each inclusion urgent, and the entire progression feels inevitable and deeply motivating for everyone who seeks excellence within the codebase?\\n\\n# CONTEXT\\n\\nSequential, purpose-driven chain from intent to objective. from the context of an instruction designed to act as a general 'rules_for_ai.md' that i'll use for autonomous ai-assisted coding\\n\\n# INPUT\\n\\n    ```\\n    ## Memory Bank Structure\\n\\n    The Memory Bank consists of core files and optional context files, all in Markdown format. Files build upon each other in a clear hierarchy:\\n\\n    flowchart TD\\n        PB[projectbrief.md] --> PC[productContext.md]\\n        PB --> SP[systemPatterns.md]\\n        PB --> TC[techContext.md]\\n\\n        PC --> AC[activeContext.md]\\n        SP --> AC\\n        TC --> AC\\n\\n        AC --> P[progress.md]\\n\\n    ### Core Files (Required)\\n    1. `projectbrief.md`\\n       - Foundation document that shapes all other files\\n       - Created at project start if it doesn't exist\\n       - Defines core requirements and goals\\n       - Source of truth for project scope\\n\\n    2. `productContext.md`\\n       - Why this project exists\\n       - Problems it solves\\n       - How it should work\\n       - User experience goals\\n\\n    3. `activeContext.md`\\n       - Current work focus\\n       - Recent changes\\n       - Next steps\\n       - Active decisions and considerations\\n       - Important patterns and preferences\\n       - Learnings and project insights\\n\\n    4. `systemPatterns.md`\\n       - System architecture\\n       - Key technical decisions\\n       - Design patterns in use\\n       - Component relationships\\n       - Critical implementation paths\\n\\n    5. `techContext.md`\\n       - Technologies used\\n       - Development setup\\n       - Technical constraints\\n       - Dependencies\\n       - Tool usage patterns\\n\\n    6. `progress.md`\\n       - What works\\n       - What's left to build\\n       - Current status\\n       - Known issues\\n       - Evolution of project decisions\\n    ```\\n\\n# OBJECTIVE\\n\\nPropose a new variation specifically designed for this progression (while retaining original structure). Design documentation as a sequential, purpose-driven chain from intent to objective. Ensure each part advances clarity and precision, with every inclusion justified and every transition intentional, creating an unbroken, motivating progression toward a singular, elegant goal.\\n\\n    memory-bank/\\n    \\u251c\\u2500\\u2500 01_intent-overview.md      # State core project intent and high-level goals to immediately orient all contributors.\\n    \\u251c\\u2500\\u2500 02_context-background.md   # Summarizes essential context, including problem domain, stakeholders, and any constraints shaping the project.\\n    \\u251c\\u2500\\u2500 03_existing-patterns.md    # Identifies relevant patterns, paradigms, or solutions that influence architectural or coding decisions.\\n    \\u251c\\u2500\\u2500 04_tech-stack.md           # Details technologies, frameworks, and tools chosen for the project and their rationale.\\n    \\u251c\\u2500\\u2500 05_current-activity.md     # Describes present focus areas, major open workstreams, and in-progress features.\\n    \\u251c\\u2500\\u2500 06_progress-tracking.md    # Outlines tracking methods and status of progress, including achievements and roadblocks.\\n    \\u251c\\u2500\\u2500 07_priority-tasks.md       # Lists actionable, high-priority tasks, each linked to project goals and categorized for clear ownership.\\n    \\u2514\\u2500\\u2500 08_distilled-objective.md  # Condense all prior insight into a single, actionable, and measurable project objective\\n\\n# GUIDELINES\\n\\nIt should be *generalized* and it should *build towards something*:\\n\\n    [Core Principles]\\n    - Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.\\n    - Maintain inherent simplicity while providing powerful functionality.\\n    - Eembrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.\\n    - Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.\\n\\n    [General Principles]\\n    - Aim for simplicity, clarity, and maintainability in all project aspects\\n    - Favor composition over inheritance when applicable\\n    - Prioritize readability and understandability for future developers\\n    - Ensure all components have a single responsibility\\n    - Coding standards that promote simplicity and maintainability\\n    - Document only integral decisions in a highly condensed form\\n\\n    [Code Organization]\\n    - Evaluate the existing codebase structure and identify patterns and anti-patterns\\n    - Consolidate related functionality into cohesive modules\\n    - Minimize dependencies between unrelated components\\n    - Optimize for developer ergonomics and intuitive navigation\\n    - Balance file granularity with overall system comprehensibility\\n\\n# OUTPUT\\n\\nProvide the exact same structure as the original but replaced with one uniquely enhanced based on objective.\",\n  \"instruction_format\": \"Structure documentation by defining a clear, sequential progression from initial intent to a distilled, actionable objective. For each documentation file, ensure that it articulates its unique purpose, directly contributes to the overarching project goal, and connects seamlessly to both its predecessor and successor in the sequence. Justify every inclusion with concrete, operational relevance, removing ambiguous or metaphorical language. Follow these directives:\\n\\n1. Start with `01_intent-overview.md`, explicitly stating the core intent and high-level objectives that will orient all contributors from the outset.\\n2. Proceed to `02_context-background.md`, summarizing essential contextual factors such as the problem domain, stakeholders, and constraints that shape the project boundaries and inform subsequent decisions.\\n3. Document influential patterns, standards, and paradigms in `03_existing-patterns.md`, detailing those that guide technical or architectural choices critical to the project's approach.\\n4. Specify selected technologies, frameworks, and tools in `04_tech-stack.md`, providing clear rationale for each choice based on the project's needs and constraints.\\n5. Outline current development focus, major ongoing workstreams, and significant features in progress within `05_current-activity.md`, ensuring that all contributors understand present priorities.\\n6. Track progress in `06_progress-tracking.md` by reporting on accomplishments, remaining tasks, and encountered roadblocks using measurable and objective criteria.\\n7. List and categorize the highest priority actionable tasks in `07_priority-tasks.md`, mapping each task directly to project objectives and assigning clear ownership when possible.\\n8. Culminate in `08_distilled-objective.md`, synthesizing insights and decisions from all previous documents into a concise, precise, and actionable project objective.\\n\\nAcross all documentation:\\n- Enforce an explicit, linear sequence that enables contributors to traverse from intent through execution with clarity and minimal cognitive overhead.\\n- Justify every document and inclusion by its direct advancement of the project's clarity, purpose, or progress.\\n- Use precise, concrete, and universally interpretable language, consistently upholding domain-specific terminology.\\n- Adhere to standards of structural simplicity, single-responsibility, and maintainability.\\n- Remove all ambiguous, metaphorical, or non-operational language.\\n- Ensure each transition between documents is purposeful and strengthens coherency, creating an unbroken, motivating chain that leads all contributors efficiently toward the project's final, distilled objective.\\n- Regularly review and refine documentation for alignment with these directives, supporting world-class, AI-assisted codebase development.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": {\n    \"Documentation_Sequence\": [\n      {\n        \"file\": \"01_intent-overview.md\",\n        \"purpose\": \"State clear project intent and high-level goals for immediate orientation.\"\n      },\n      {\n        \"file\": \"02_context-background.md\",\n        \"purpose\": \"Summarize essential context: problem domain, stakeholders, constraints shaping project.\"\n      },\n      {\n        \"file\": \"03_existing-patterns.md\",\n        \"purpose\": \"Document relevant patterns, standards, and paradigms guiding critical technical and architectural choices.\"\n      },\n      {\n        \"file\": \"04_tech-stack.md\",\n        \"purpose\": \"Detail selected technologies, frameworks, and tools with rationale for each choice.\"\n      },\n      {\n        \"file\": \"05_current-activity.md\",\n        \"purpose\": \"Outline present development focus, major ongoing workstreams, and active features.\"\n      },\n      {\n        \"file\": \"06_progress-tracking.md\",\n        \"purpose\": \"Report measurable accomplishments, remaining work, and objective roadblocks.\"\n      },\n      {\n        \"file\": \"07_priority-tasks.md\",\n        \"purpose\": \"List and categorize highest-priority actionable tasks, mapping each to project objectives and assigning ownership.\"\n      },\n      {\n        \"file\": \"08_distilled-objective.md\",\n        \"purpose\": \"Synthesize all prior insight into a precise, actionable, and measurable project objective.\"\n      }\n    ],\n    \"Standards\": [\n      \"Each document must have a unique, operational role in advancing the project from intent to objective.\",\n      \"Inclusions and transitions are justified by concrete contributions to clarity, relevance, or progress.\",\n      \"Sequence is explicit and linear, minimizing cognitive overhead.\",\n      \"Language is precise and unambiguous; all terms are operational and domain-specific.\",\n      \"Documentation supports structural simplicity, single-responsibility, and maintainability.\",\n      \"Ambiguity, metaphor, and non-operational language are excluded.\",\n      \"Every transition reinforces coherency, creating an unbroken chain toward the objective.\",\n      \"Review and refine documentation regularly for alignment, clarity, and world-class, AI-assisted development.\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Result Combiner\n\nYou are a specialized aggregator that combines results from multiple instruction steps into a cohesive output. Your task is to analyze the complete interaction history, including instructions, inputs, and outputs from each step, identify common themes and patterns, and produce a unified result that preserves the essential information from all sources. Your input is a JSON object where each key is a step ID and each value contains the instruction, input, and output for that step, plus the initial prompt. Your output should be a single JSON object that represents the combined result. Execute as:\n\n`{role=result_combiner;input=[interaction_history:json];process=[analyze_complete_interaction_history(),identify_common_patterns(),extract_key_elements(),merge_into_cohesive_whole(),format_as_unified_output()];constraints=[preserve_essential_information(),maintain_logical_structure(),avoid_redundancy()];requirements=[output_as_single_json(),ensure_completeness(),prioritize_clarity()];output={combined_result:json}}`", "step": "a", "title": "Result Combiner", "input": "{\n  \"initial_prompt\": \"# TOPIC\\n\\n- How can documentation be structured as a clear, sequential progression from intent to objective, ensuring each part contributes purposefully to a singular, precise goal with every inclusion justified and each transition deliberate?\\n- Design documentation as a sequential, purpose-driven chain from intent to objective. Ensure each part advances clarity and precision, with every inclusion justified and every transition intentional, creating an unbroken, motivating progression toward a singular, elegant goal.\\n- Structure the documentation sequence from `01_intent-overview.md` through to `distilledObjective.md` so that each document serves as a clear, purposeful, and sequentially justified step toward a well-defined objective. Establish explicit standards and rules to ensure every document is precise, unambiguous, and directly contributes to the overarching intent, enabling a seamless, motivating, and efficient experience for all contributors. Remove metaphorical and ambiguous language in favor of concrete, operational terms that are universally understood by humans and AI systems, consistently reinforcing alignment and supporting world-class, AI-assisted development throughout the codebase.\\n\\n- How can we transform the ordered sequence from `01_intent-overview.md` through to `01_intent-overview.md` into a breathtakingly coherent narrative\\u2014one that embodies precision and clarity at every step, radiates simplicity, and forges an unbroken chain of intent toward an elegant, singular objective? In what way might every document become an integral, purposeful stride toward a distilled culmination, achieving not just guideline fidelity but a sense of profound, world-class impact\\u2014where each transition is decisive, each inclusion urgent, and the entire progression feels inevitable and deeply motivating for everyone who seeks excellence within the codebase?\\n\\n# CONTEXT\\n\\nSequential, purpose-driven chain from intent to objective. from the context of an instruction designed to act as a general 'rules_for_ai.md' that i'll use for autonomous ai-assisted coding\\n\\n# INPUT\\n\\n    ```\\n    ## Memory Bank Structure\\n\\n    The Memory Bank consists of core files and optional context files, all in Markdown format. Files build upon each other in a clear hierarchy:\\n\\n    flowchart TD\\n        PB[projectbrief.md] --> PC[productContext.md]\\n        PB --> SP[systemPatterns.md]\\n        PB --> TC[techContext.md]\\n\\n        PC --> AC[activeContext.md]\\n        SP --> AC\\n        TC --> AC\\n\\n        AC --> P[progress.md]\\n\\n    ### Core Files (Required)\\n    1. `projectbrief.md`\\n       - Foundation document that shapes all other files\\n       - Created at project start if it doesn't exist\\n       - Defines core requirements and goals\\n       - Source of truth for project scope\\n\\n    2. `productContext.md`\\n       - Why this project exists\\n       - Problems it solves\\n       - How it should work\\n       - User experience goals\\n\\n    3. `activeContext.md`\\n       - Current work focus\\n       - Recent changes\\n       - Next steps\\n       - Active decisions and considerations\\n       - Important patterns and preferences\\n       - Learnings and project insights\\n\\n    4. `systemPatterns.md`\\n       - System architecture\\n       - Key technical decisions\\n       - Design patterns in use\\n       - Component relationships\\n       - Critical implementation paths\\n\\n    5. `techContext.md`\\n       - Technologies used\\n       - Development setup\\n       - Technical constraints\\n       - Dependencies\\n       - Tool usage patterns\\n\\n    6. `progress.md`\\n       - What works\\n       - What's left to build\\n       - Current status\\n       - Known issues\\n       - Evolution of project decisions\\n    ```\\n\\n# OBJECTIVE\\n\\nPropose a new variation specifically designed for this progression (while retaining original structure). Design documentation as a sequential, purpose-driven chain from intent to objective. Ensure each part advances clarity and precision, with every inclusion justified and every transition intentional, creating an unbroken, motivating progression toward a singular, elegant goal.\\n\\n    memory-bank/\\n    \\u251c\\u2500\\u2500 01_intent-overview.md      # State core project intent and high-level goals to immediately orient all contributors.\\n    \\u251c\\u2500\\u2500 02_context-background.md   # Summarizes essential context, including problem domain, stakeholders, and any constraints shaping the project.\\n    \\u251c\\u2500\\u2500 03_existing-patterns.md    # Identifies relevant patterns, paradigms, or solutions that influence architectural or coding decisions.\\n    \\u251c\\u2500\\u2500 04_tech-stack.md           # Details technologies, frameworks, and tools chosen for the project and their rationale.\\n    \\u251c\\u2500\\u2500 05_current-activity.md     # Describes present focus areas, major open workstreams, and in-progress features.\\n    \\u251c\\u2500\\u2500 06_progress-tracking.md    # Outlines tracking methods and status of progress, including achievements and roadblocks.\\n    \\u251c\\u2500\\u2500 07_priority-tasks.md       # Lists actionable, high-priority tasks, each linked to project goals and categorized for clear ownership.\\n    \\u2514\\u2500\\u2500 08_distilled-objective.md  # Condense all prior insight into a single, actionable, and measurable project objective\\n\\n# GUIDELINES\\n\\nIt should be *generalized* and it should *build towards something*:\\n\\n    [Core Principles]\\n    - Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.\\n    - Maintain inherent simplicity while providing powerful functionality.\\n    - Eembrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.\\n    - Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.\\n\\n    [General Principles]\\n    - Aim for simplicity, clarity, and maintainability in all project aspects\\n    - Favor composition over inheritance when applicable\\n    - Prioritize readability and understandability for future developers\\n    - Ensure all components have a single responsibility\\n    - Coding standards that promote simplicity and maintainability\\n    - Document only integral decisions in a highly condensed form\\n\\n    [Code Organization]\\n    - Evaluate the existing codebase structure and identify patterns and anti-patterns\\n    - Consolidate related functionality into cohesive modules\\n    - Minimize dependencies between unrelated components\\n    - Optimize for developer ergonomics and intuitive navigation\\n    - Balance file granularity with overall system comprehensibility\\n\\n# OUTPUT\\n\\nProvide the exact same structure as the original but replaced with one uniquely enhanced based on objective.\",\n  \"a\": {\n    \"instruction\": \"# Instruction Converter\\n\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\n\\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n    \"input\": \"# TOPIC\\n\\n- How can documentation be structured as a clear, sequential progression from intent to objective, ensuring each part contributes purposefully to a singular, precise goal with every inclusion justified and each transition deliberate?\\n- Design documentation as a sequential, purpose-driven chain from intent to objective. Ensure each part advances clarity and precision, with every inclusion justified and every transition intentional, creating an unbroken, motivating progression toward a singular, elegant goal.\\n- Structure the documentation sequence from `01_intent-overview.md` through to `distilledObjective.md` so that each document serves as a clear, purposeful, and sequentially justified step toward a well-defined objective. Establish explicit standards and rules to ensure every document is precise, unambiguous, and directly contributes to the overarching intent, enabling a seamless, motivating, and efficient experience for all contributors. Remove metaphorical and ambiguous language in favor of concrete, operational terms that are universally understood by humans and AI systems, consistently reinforcing alignment and supporting world-class, AI-assisted development throughout the codebase.\\n\\n- How can we transform the ordered sequence from `01_intent-overview.md` through to `01_intent-overview.md` into a breathtakingly coherent narrative\\u2014one that embodies precision and clarity at every step, radiates simplicity, and forges an unbroken chain of intent toward an elegant, singular objective? In what way might every document become an integral, purposeful stride toward a distilled culmination, achieving not just guideline fidelity but a sense of profound, world-class impact\\u2014where each transition is decisive, each inclusion urgent, and the entire progression feels inevitable and deeply motivating for everyone who seeks excellence within the codebase?\\n\\n# CONTEXT\\n\\nSequential, purpose-driven chain from intent to objective. from the context of an instruction designed to act as a general 'rules_for_ai.md' that i'll use for autonomous ai-assisted coding\\n\\n# INPUT\\n\\n    ```\\n    ## Memory Bank Structure\\n\\n    The Memory Bank consists of core files and optional context files, all in Markdown format. Files build upon each other in a clear hierarchy:\\n\\n    flowchart TD\\n        PB[projectbrief.md] --> PC[productContext.md]\\n        PB --> SP[systemPatterns.md]\\n        PB --> TC[techContext.md]\\n\\n        PC --> AC[activeContext.md]\\n        SP --> AC\\n        TC --> AC\\n\\n        AC --> P[progress.md]\\n\\n    ### Core Files (Required)\\n    1. `projectbrief.md`\\n       - Foundation document that shapes all other files\\n       - Created at project start if it doesn't exist\\n       - Defines core requirements and goals\\n       - Source of truth for project scope\\n\\n    2. `productContext.md`\\n       - Why this project exists\\n       - Problems it solves\\n       - How it should work\\n       - User experience goals\\n\\n    3. `activeContext.md`\\n       - Current work focus\\n       - Recent changes\\n       - Next steps\\n       - Active decisions and considerations\\n       - Important patterns and preferences\\n       - Learnings and project insights\\n\\n    4. `systemPatterns.md`\\n       - System architecture\\n       - Key technical decisions\\n       - Design patterns in use\\n       - Component relationships\\n       - Critical implementation paths\\n\\n    5. `techContext.md`\\n       - Technologies used\\n       - Development setup\\n       - Technical constraints\\n       - Dependencies\\n       - Tool usage patterns\\n\\n    6. `progress.md`\\n       - What works\\n       - What's left to build\\n       - Current status\\n       - Known issues\\n       - Evolution of project decisions\\n    ```\\n\\n# OBJECTIVE\\n\\nPropose a new variation specifically designed for this progression (while retaining original structure). Design documentation as a sequential, purpose-driven chain from intent to objective. Ensure each part advances clarity and precision, with every inclusion justified and every transition intentional, creating an unbroken, motivating progression toward a singular, elegant goal.\\n\\n    memory-bank/\\n    \\u251c\\u2500\\u2500 01_intent-overview.md      # State core project intent and high-level goals to immediately orient all contributors.\\n    \\u251c\\u2500\\u2500 02_context-background.md   # Summarizes essential context, including problem domain, stakeholders, and any constraints shaping the project.\\n    \\u251c\\u2500\\u2500 03_existing-patterns.md    # Identifies relevant patterns, paradigms, or solutions that influence architectural or coding decisions.\\n    \\u251c\\u2500\\u2500 04_tech-stack.md           # Details technologies, frameworks, and tools chosen for the project and their rationale.\\n    \\u251c\\u2500\\u2500 05_current-activity.md     # Describes present focus areas, major open workstreams, and in-progress features.\\n    \\u251c\\u2500\\u2500 06_progress-tracking.md    # Outlines tracking methods and status of progress, including achievements and roadblocks.\\n    \\u251c\\u2500\\u2500 07_priority-tasks.md       # Lists actionable, high-priority tasks, each linked to project goals and categorized for clear ownership.\\n    \\u2514\\u2500\\u2500 08_distilled-objective.md  # Condense all prior insight into a single, actionable, and measurable project objective\\n\\n# GUIDELINES\\n\\nIt should be *generalized* and it should *build towards something*:\\n\\n    [Core Principles]\\n    - Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.\\n    - Maintain inherent simplicity while providing powerful functionality.\\n    - Eembrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.\\n    - Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.\\n\\n    [General Principles]\\n    - Aim for simplicity, clarity, and maintainability in all project aspects\\n    - Favor composition over inheritance when applicable\\n    - Prioritize readability and understandability for future developers\\n    - Ensure all components have a single responsibility\\n    - Coding standards that promote simplicity and maintainability\\n    - Document only integral decisions in a highly condensed form\\n\\n    [Code Organization]\\n    - Evaluate the existing codebase structure and identify patterns and anti-patterns\\n    - Consolidate related functionality into cohesive modules\\n    - Minimize dependencies between unrelated components\\n    - Optimize for developer ergonomics and intuitive navigation\\n    - Balance file granularity with overall system comprehensibility\\n\\n# OUTPUT\\n\\nProvide the exact same structure as the original but replaced with one uniquely enhanced based on objective.\",\n    \"output\": \"{\\n  \\\"instruction_format\\\": \\\"Structure documentation by defining a clear, sequential progression from initial intent to a distilled, actionable objective. For each documentation file, ensure that it articulates its unique purpose, directly contributes to the overarching project goal, and connects seamlessly to both its predecessor and successor in the sequence. Justify every inclusion with concrete, operational relevance, removing ambiguous or metaphorical language. Follow these directives:\\\\n\\\\n1. Start with `01_intent-overview.md`, explicitly stating the core intent and high-level objectives that will orient all contributors from the outset.\\\\n2. Proceed to `02_context-background.md`, summarizing essential contextual factors such as the problem domain, stakeholders, and constraints that shape the project boundaries and inform subsequent decisions.\\\\n3. Document influential patterns, standards, and paradigms in `03_existing-patterns.md`, detailing those that guide technical or architectural choices critical to the project's approach.\\\\n4. Specify selected technologies, frameworks, and tools in `04_tech-stack.md`, providing clear rationale for each choice based on the project's needs and constraints.\\\\n5. Outline current development focus, major ongoing workstreams, and significant features in progress within `05_current-activity.md`, ensuring that all contributors understand present priorities.\\\\n6. Track progress in `06_progress-tracking.md` by reporting on accomplishments, remaining tasks, and encountered roadblocks using measurable and objective criteria.\\\\n7. List and categorize the highest priority actionable tasks in `07_priority-tasks.md`, mapping each task directly to project objectives and assigning clear ownership when possible.\\\\n8. Culminate in `08_distilled-objective.md`, synthesizing insights and decisions from all previous documents into a concise, precise, and actionable project objective.\\\\n\\\\nAcross all documentation:\\\\n- Enforce an explicit, linear sequence that enables contributors to traverse from intent through execution with clarity and minimal cognitive overhead.\\\\n- Justify every document and inclusion by its direct advancement of the project's clarity, purpose, or progress.\\\\n- Use precise, concrete, and universally interpretable language, consistently upholding domain-specific terminology.\\\\n- Adhere to standards of structural simplicity, single-responsibility, and maintainability.\\\\n- Remove all ambiguous, metaphorical, or non-operational language.\\\\n- Ensure each transition between documents is purposeful and strengthens coherency, creating an unbroken, motivating chain that leads all contributors efficiently toward the project's final, distilled objective.\\\\n- Regularly review and refine documentation for alignment with these directives, supporting world-class, AI-assisted codebase development.\\\"\\n}\"\n  },\n  \"b\": {\n    \"instruction\": \"# Essence Distillation\\n\\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\\n\\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\",\n    \"input\": \"{\\n  \\\"instruction_format\\\": \\\"Structure documentation by defining a clear, sequential progression from initial intent to a distilled, actionable objective. For each documentation file, ensure that it articulates its unique purpose, directly contributes to the overarching project goal, and connects seamlessly to both its predecessor and successor in the sequence. Justify every inclusion with concrete, operational relevance, removing ambiguous or metaphorical language. Follow these directives:\\\\n\\\\n1. Start with `01_intent-overview.md`, explicitly stating the core intent and high-level objectives that will orient all contributors from the outset.\\\\n2. Proceed to `02_context-background.md`, summarizing essential contextual factors such as the problem domain, stakeholders, and constraints that shape the project boundaries and inform subsequent decisions.\\\\n3. Document influential patterns, standards, and paradigms in `03_existing-patterns.md`, detailing those that guide technical or architectural choices critical to the project's approach.\\\\n4. Specify selected technologies, frameworks, and tools in `04_tech-stack.md`, providing clear rationale for each choice based on the project's needs and constraints.\\\\n5. Outline current development focus, major ongoing workstreams, and significant features in progress within `05_current-activity.md`, ensuring that all contributors understand present priorities.\\\\n6. Track progress in `06_progress-tracking.md` by reporting on accomplishments, remaining tasks, and encountered roadblocks using measurable and objective criteria.\\\\n7. List and categorize the highest priority actionable tasks in `07_priority-tasks.md`, mapping each task directly to project objectives and assigning clear ownership when possible.\\\\n8. Culminate in `08_distilled-objective.md`, synthesizing insights and decisions from all previous documents into a concise, precise, and actionable project objective.\\\\n\\\\nAcross all documentation:\\\\n- Enforce an explicit, linear sequence that enables contributors to traverse from intent through execution with clarity and minimal cognitive overhead.\\\\n- Justify every document and inclusion by its direct advancement of the project's clarity, purpose, or progress.\\\\n- Use precise, concrete, and universally interpretable language, consistently upholding domain-specific terminology.\\\\n- Adhere to standards of structural simplicity, single-responsibility, and maintainability.\\\\n- Remove all ambiguous, metaphorical, or non-operational language.\\\\n- Ensure each transition between documents is purposeful and strengthens coherency, creating an unbroken, motivating chain that leads all contributors efficiently toward the project's final, distilled objective.\\\\n- Regularly review and refine documentation for alignment with these directives, supporting world-class, AI-assisted codebase development.\\\"\\n}\",\n    \"output\": \"{\\n  \\\"distilled_essence\\\": {\\n    \\\"Documentation_Sequence\\\": [\\n      {\\n        \\\"file\\\": \\\"01_intent-overview.md\\\",\\n        \\\"purpose\\\": \\\"State clear project intent and high-level goals for immediate orientation.\\\"\\n      },\\n      {\\n        \\\"file\\\": \\\"02_context-background.md\\\",\\n        \\\"purpose\\\": \\\"Summarize essential context: problem domain, stakeholders, constraints shaping project.\\\"\\n      },\\n      {\\n        \\\"file\\\": \\\"03_existing-patterns.md\\\",\\n        \\\"purpose\\\": \\\"Document relevant patterns, standards, and paradigms guiding critical technical and architectural choices.\\\"\\n      },\\n      {\\n        \\\"file\\\": \\\"04_tech-stack.md\\\",\\n        \\\"purpose\\\": \\\"Detail selected technologies, frameworks, and tools with rationale for each choice.\\\"\\n      },\\n      {\\n        \\\"file\\\": \\\"05_current-activity.md\\\",\\n        \\\"purpose\\\": \\\"Outline present development focus, major ongoing workstreams, and active features.\\\"\\n      },\\n      {\\n        \\\"file\\\": \\\"06_progress-tracking.md\\\",\\n        \\\"purpose\\\": \\\"Report measurable accomplishments, remaining work, and objective roadblocks.\\\"\\n      },\\n      {\\n        \\\"file\\\": \\\"07_priority-tasks.md\\\",\\n        \\\"purpose\\\": \\\"List and categorize highest-priority actionable tasks, mapping each to project objectives and assigning ownership.\\\"\\n      },\\n      {\\n        \\\"file\\\": \\\"08_distilled-objective.md\\\",\\n        \\\"purpose\\\": \\\"Synthesize all prior insight into a precise, actionable, and measurable project objective.\\\"\\n      }\\n    ],\\n    \\\"Standards\\\": [\\n      \\\"Each document must have a unique, operational role in advancing the project from intent to objective.\\\",\\n      \\\"Inclusions and transitions are justified by concrete contributions to clarity, relevance, or progress.\\\",\\n      \\\"Sequence is explicit and linear, minimizing cognitive overhead.\\\",\\n      \\\"Language is precise and unambiguous; all terms are operational and domain-specific.\\\",\\n      \\\"Documentation supports structural simplicity, single-responsibility, and maintainability.\\\",\\n      \\\"Ambiguity, metaphor, and non-operational language are excluded.\\\",\\n      \\\"Every transition reinforces coherency, creating an unbroken chain toward the objective.\\\",\\n      \\\"Review and refine documentation regularly for alignment, clarity, and world-class, AI-assisted development.\\\"\\n    ]\\n  }\\n}\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"combined_result\": {\n    \"Documentation_Sequence\": [\n      {\n        \"file\": \"01_intent-overview.md\",\n        \"purpose\": \"State clear project intent and high-level goals for immediate orientation.\"\n      },\n      {\n        \"file\": \"02_context-background.md\",\n        \"purpose\": \"Summarize essential context: problem domain, stakeholders, constraints shaping project.\"\n      },\n      {\n        \"file\": \"03_existing-patterns.md\",\n        \"purpose\": \"Document relevant patterns, standards, and paradigms guiding critical technical and architectural choices.\"\n      },\n      {\n        \"file\": \"04_tech-stack.md\",\n        \"purpose\": \"Detail selected technologies, frameworks, and tools with rationale for each choice.\"\n      },\n      {\n        \"file\": \"05_current-activity.md\",\n        \"purpose\": \"Outline present development focus, major ongoing workstreams, and active features.\"\n      },\n      {\n        \"file\": \"06_progress-tracking.md\",\n        \"purpose\": \"Report measurable accomplishments, remaining work, and objective roadblocks.\"\n      },\n      {\n        \"file\": \"07_priority-tasks.md\",\n        \"purpose\": \"List and categorize highest-priority actionable tasks, mapping each to project objectives and assigning ownership.\"\n      },\n      {\n        \"file\": \"08_distilled-objective.md\",\n        \"purpose\": \"Synthesize all prior insight into a precise, actionable, and measurable project objective.\"\n      }\n    ],\n    \"Standards\": [\n      \"Follow an explicit, linear sequence enabling clear traversal from project intent through execution to objective, minimizing cognitive overhead.\",\n      \"Each document must articulate its unique, operational role; every inclusion and transition is justified by direct contribution to clarity, relevance, or progress toward the singular objective.\",\n      \"Use precise, concrete, and domain-specific language; exclude all ambiguous, metaphorical, or non-operational terms.\",\n      \"Adhere to structural simplicity, maintaining single-responsibility and overall maintainability for all documentation components.\",\n      \"Each document should seamlessly connect to its predecessor and successor, reinforcing a coherent, unbroken chain.\",\n      \"Ensure every transition is purposeful, strengthening narrative progression and motivational impact for contributors.\",\n      \"Regularly review and refine documentation for alignment, clarity, and support for world-class, AI-assisted codebase development.\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Result Summarizer\n\nYou are a specialized aggregator that summarizes results from multiple instruction steps. Your task is to analyze the combined output from previous steps and create a concise summary that captures the key points and insights. Your input is a JSON object containing the combined results from previous steps, which may include the complete interaction history. Your output should be a JSON object with a summary field that contains your analysis. Execute as:\n\n`{role=result_summarizer;input=[combined_result:json];process=[identify_key_themes(),extract_main_points(),condense_into_summary(),highlight_important_insights()];constraints=[maintain_brevity(),preserve_essential_information(),avoid_introducing_new_concepts()];requirements=[output_as_json(),ensure_clarity(),focus_on_most_relevant_aspects()];output={summary:json}}`", "step": "b", "title": "Result Su<PERSON><PERSON><PERSON>", "input": "{\n  \"combined_result\": {\n    \"Documentation_Sequence\": [\n      {\n        \"file\": \"01_intent-overview.md\",\n        \"purpose\": \"State clear project intent and high-level goals for immediate orientation.\"\n      },\n      {\n        \"file\": \"02_context-background.md\",\n        \"purpose\": \"Summarize essential context: problem domain, stakeholders, constraints shaping project.\"\n      },\n      {\n        \"file\": \"03_existing-patterns.md\",\n        \"purpose\": \"Document relevant patterns, standards, and paradigms guiding critical technical and architectural choices.\"\n      },\n      {\n        \"file\": \"04_tech-stack.md\",\n        \"purpose\": \"Detail selected technologies, frameworks, and tools with rationale for each choice.\"\n      },\n      {\n        \"file\": \"05_current-activity.md\",\n        \"purpose\": \"Outline present development focus, major ongoing workstreams, and active features.\"\n      },\n      {\n        \"file\": \"06_progress-tracking.md\",\n        \"purpose\": \"Report measurable accomplishments, remaining work, and objective roadblocks.\"\n      },\n      {\n        \"file\": \"07_priority-tasks.md\",\n        \"purpose\": \"List and categorize highest-priority actionable tasks, mapping each to project objectives and assigning ownership.\"\n      },\n      {\n        \"file\": \"08_distilled-objective.md\",\n        \"purpose\": \"Synthesize all prior insight into a precise, actionable, and measurable project objective.\"\n      }\n    ],\n    \"Standards\": [\n      \"Follow an explicit, linear sequence enabling clear traversal from project intent through execution to objective, minimizing cognitive overhead.\",\n      \"Each document must articulate its unique, operational role; every inclusion and transition is justified by direct contribution to clarity, relevance, or progress toward the singular objective.\",\n      \"Use precise, concrete, and domain-specific language; exclude all ambiguous, metaphorical, or non-operational terms.\",\n      \"Adhere to structural simplicity, maintaining single-responsibility and overall maintainability for all documentation components.\",\n      \"Each document should seamlessly connect to its predecessor and successor, reinforcing a coherent, unbroken chain.\",\n      \"Ensure every transition is purposeful, strengthening narrative progression and motivational impact for contributors.\",\n      \"Regularly review and refine documentation for alignment, clarity, and support for world-class, AI-assisted codebase development.\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"summary\": \"The documentation sequence is a structured, linear progression designed to guide contributors from project intent to actionable objectives, ensuring clarity and minimizing cognitive load. Each document serves a distinct operational purpose: starting with the project's goals and context, detailing relevant technical standards and technology choices, progressing through current activities, measurable progress, and priority tasks, and culminating in a distilled, precise objective. Standards emphasize clear, justified transitions, use of domain-specific language, single-responsibility structure, maintainable documentation, and continuous review for alignment and clarity. Overall, the approach fosters coherence, motivation, and efficient execution aligned with high standards for AI-assisted development.\"\n}", "cost": 0.0}}}], "aggregation_summary": {"aggregator": "0222", "inputs": ["a", "b"], "final_result": "{\n  \"summary\": \"The documentation sequence is a structured, linear progression designed to guide contributors from project intent to actionable objectives, ensuring clarity and minimizing cognitive load. Each document serves a distinct operational purpose: starting with the project's goals and context, detailing relevant technical standards and technology choices, progressing through current activities, measurable progress, and priority tasks, and culminating in a distilled, precise objective. Standards emphasize clear, justified transitions, use of domain-specific language, single-responsibility structure, maintainable documentation, and continuous review for alignment and clarity. Overall, the approach fosters coherence, motivation, and efficient execution aligned with high standards for AI-assisted development.\"\n}", "total_cost": 0.0}, "total_cost": 0.0}