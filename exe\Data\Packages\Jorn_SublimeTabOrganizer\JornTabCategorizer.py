import sublime
import sublime_plugin
import json
import datetime
import os
from .lib.get.categories import categorize_window_views, TAB_CATEGORIES
from .lib.get.session import TabSessionManager

# Simple utility functions for tab organization

# Map Sublime Text syntaxes to file extensions
EXTENSION_MAPPING = {
    "Bash": "bash", "Batch File": "bat", "C": "c", "C++": "cpp", "Command Prompt": "cmd",
    "CSS": "css", "Dockerfile": "dockerfile", "Go": "go", "Haskell": "hs", "HTML": "html",
    "INI": "ini", "Java": "java", "JavaScript": "js", "JSON": "json", "Kotlin": "kt",
    "Less": "less", "Lua": "lua", "Makefile": "mk", "Markdown": "md", "MaxScript": "ms",
    "Nginx Config": "conf", "NSS": "nss", "Objective-C": "m", "Perl": "pl", "PHP": "php",
    "Plain Text": "txt", "PowerShell": "ps1", "Python": "py", "Ruby": "rb", "Rust": "rs",
    "Scala": "scala", "SCSS": "scss", "ShellScript": "sh", "SQL": "sql", "Swift": "swift",
    "TypeScript": "ts", "Visual Basic": "vb", "XML": "xml", "YAML": "yaml",
    "Git Ignore": "gitignore", "Git Attributes": "gitattributes", "EditorConfig": "editorconfig",
    "Diff": "diff", "Patch": "patch", "Log": "log", "TOML": "toml", "Rust": "rs",
    "Clojure": "clj", "Dart": "dart", "Elixir": "ex", "Erlang": "erl", "F#": "fs",
    "GraphQL": "graphql", "Groovy": "groovy", "Julia": "jl", "Lisp": "lisp", "OCaml": "ml",
    "R": "r", "Racket": "rkt", "Scheme": "scm", "Shell Script": "sh", "TCL": "tcl",
    "Terraform": "tf", "TeX": "tex", "VHDL": "vhdl", "Verilog": "v"
}

class JornTabCategorizerCommand(sublime_plugin.WindowCommand):
    # Class variable to control debug output
    debug_mode = False

    def run(self, display_results=True, output_format="table", sort_by=None,
            export_to_file=False, import_from_file=False, file_path=None,
            columns=None, debug=False):
        try:
            # Set debug mode for this instance
            JornTabCategorizerCommand.debug_mode = debug

            # Log initial command parameters if in debug mode
            self._debug_log(f"Running JornTabCategorizer with parameters: display_results={display_results}, "
                          f"output_format={output_format}, sort_by={sort_by}, export_to_file={export_to_file}, "
                          f"import_from_file={import_from_file}, file_path={file_path}, debug={debug}")
            # Define default columns if not specified
            if not columns:
                columns = [
                    "Save Count", "Depth", "Last Saved", "Ext", "File",
                    "Status", "Group", "Index", "Modified", "Lines", "Size",
                    "Last Access", "Category", "Path",
                ]

            # Import from file if requested
            if import_from_file:
                if file_path:
                    # Use the provided file path
                    self._import_from_markdown(file_path)
                else:
                    # Always use the default file path
                    default_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "tab_organization.md")
                    if os.path.exists(default_path):
                        self._import_from_markdown(default_path)
                    else:
                        sublime.error_message(f"Tab organization file not found: {default_path}")
                return

            # Get categorized views
            categorized_views = categorize_window_views(self.window, include_details=True)

            # Create a flat list of all views with their details
            all_views = []
            for category, items in categorized_views.items():
                for view, details in items:
                    # Add the category to the details
                    details["category"] = category
                    # Store the view ID for later reference
                    details["view_id"] = view.id()
                    all_views.append((view, details))

            # Sort the views if requested
            if sort_by:
                sort_key = sort_by.lower().replace(" ", "_")

                # Special handling for session metrics
                if sort_key == "last_saved":
                    # Sort by time since save (most recent first)
                    all_views.sort(key=lambda x: x[1].get('time_since_save_minutes', float('inf')))
                elif sort_key == "save_count":
                    # Sort by save count (highest first)
                    all_views.sort(key=lambda x: x[1].get('save_count', 0), reverse=True)
                elif sort_key == "activity":
                    # Sort by activity score (highest first)
                    all_views.sort(key=lambda x: x[1].get('activity_score', 0), reverse=True)
                else:
                    # Default sorting
                    all_views.sort(key=lambda x: str(x[1].get(sort_key, "")))

            # Export to file if requested
            if export_to_file:
                # Always use the same file name for consistency
                file_path = os.path.join(
                    os.path.dirname(os.path.abspath(__file__)),
                    "tab_organization.md"
                )
                self._export_to_markdown(all_views, file_path, columns)
                # Open the file for editing
                self.window.open_file(file_path)
                # Show a helpful message
                sublime.status_message("Tab data exported. Save the file to apply changes.")
                return

            # Display results if requested
            if display_results:
                if output_format == "json":
                    self._display_results_json(categorized_views, all_views)
                else:
                    # Default to flat table for all formats
                    self._display_results_flat_table(all_views, columns)

        except Exception as e:
            print(f"[JornTabCategorizer] Error: {e}")
            import traceback
            print(traceback.format_exc())

    def _display_results_flat_table(self, all_views, columns=None):
        output_view = self.window.create_output_panel("jorn_tab_categorizer_flat")
        output_view.set_read_only(False)
        output_view.run_command("select_all")
        output_view.run_command("right_delete")

        panel_content = f"# Tab Overview ({len(all_views)} tabs)\n\n"

        # Define the default columns if none provided
        if not columns:
            columns = [
                "Save Count", "Depth", "Last Saved", "Ext", "File",
                "Status", "Group", "Index", "Modified", "Lines", "Size",
                "Last Access", "Category", "Path",
            ]

        # Map column names to their widths
        column_widths = {
            "File": 30,
            "Ext": 8,
            "Group": 5,
            "Index": 5,
            "Lines": 6,
            "Size": 10,
            "Modified": 19,
            "Last Access": 12,
            "Last Saved": 12,
            "Save Count": 10,
            "Status": 10,
            "Category": 20,
            "Depth": 5,
            "Path": 80
        }

        # Create a list of tuples with column name and width, preserving the order from columns parameter
        table_columns = [(col, column_widths.get(col, 15)) for col in columns]

        # Store column widths for consistent justification
        col_widths = [width for _, width in table_columns]

        # Create header row
        header = "| "
        separator = "| "
        for i, (col_name, width) in enumerate(table_columns):
            header += col_name.ljust(width) + " | "
            separator += "-" * width + " | "

        panel_content += header + "\n"
        panel_content += separator + "\n"

        # Add table rows
        for view, details in all_views:
            # Process all view details first
            self._process_view_details(view, details)

            # Create a list to store all the cell values based on requested columns
            cells = []

            # Add cells based on requested columns - this preserves the order from columns parameter
            for col_name, _ in table_columns:
                if col_name == "File":
                    cells.append(details.get('basename', 'Untitled'))
                elif col_name == "Ext":
                    cells.append(details.get('extension', ''))
                elif col_name == "Group":
                    cells.append(str(details.get('group', 'N/A')))
                elif col_name == "Index":
                    cells.append(str(details.get('index', 'N/A')))
                elif col_name == "Lines":
                    cells.append(str(details.get('line_count', 'N/A')))
                elif col_name == "Size":
                    cells.append(details.get('size_str', 'N/A'))
                elif col_name == "Modified":
                    cells.append(str(details.get('modified_time_str', 'N/A')))
                elif col_name == "Last Access":
                    cells.append(str(details.get('time_since_access_str', 'N/A')))
                elif col_name == "Status":
                    cells.append(details.get('status_str', 'N/A'))
                elif col_name == "Category":
                    cells.append(details.get('category', 'N/A'))
                elif col_name == "Depth":
                    cells.append(str(details.get('depth', 'N/A')))
                elif col_name == "Last Saved":
                    cells.append(details.get('time_since_save', 'N/A'))
                elif col_name == "Save Count":
                    cells.append(str(details.get('save_count', 0)))
                elif col_name == "Path":
                    cells.append(str(details.get('path', 'N/A')))
                else:
                    # For any custom columns, try to get the value from details
                    key = col_name.lower().replace(" ", "_")
                    cells.append(str(details.get(key, 'N/A')))

            # Build the row with proper justification
            row = "| "
            for i, cell in enumerate(cells):
                row += cell.ljust(col_widths[i]) + " | "

            panel_content += row + "\n"

        output_view.run_command("append", {"characters": panel_content})
        output_view.set_read_only(True)
        self.window.run_command("show_panel", {"panel": "output.jorn_tab_categorizer_flat"})

    def _process_view_details(self, view, details):
        """Process and extract all details from a view"""
        # File name and path
        file_name = view.file_name()
        if file_name:
            details['path'] = file_name

            # Calculate path depth
            # Split the path by directory separator and count components
            # Normalize the path first to handle any platform-specific issues
            norm_path = os.path.normpath(file_name)
            path_parts = norm_path.split(os.sep)
            # Subtract 1 if the path starts with a drive letter on Windows (e.g., C:)
            depth_adjustment = 1 if (os.name == 'nt' and ':' in path_parts[0]) else 0
            path_depth = len(path_parts) - depth_adjustment
            details['depth'] = path_depth

            # Try to get relative path from project folders
            window = self.window
            project_folders = window.folders()

            # If we have project folders, try to get a relative path
            if project_folders:
                for folder in project_folders:
                    if file_name.startswith(folder):
                        try:
                            rel_path = os.path.relpath(file_name, folder)
                            details['relative_path'] = rel_path
                            break
                        except:
                            pass
        else:
            details['path'] = 'N/A'
            details['depth'] = 0  # Unsaved files have no path depth

        # Add session metrics
        session_manager = TabSessionManager.get_instance()
        metrics = session_manager.get_tab_metrics(view.id())

        # Add metrics to details
        details['save_count'] = metrics.get('save_count', 0)
        details['time_since_save'] = session_manager.get_time_since_save(view.id())
        details['time_since_save_minutes'] = session_manager.get_time_since_save_minutes(view.id())
        details['activation_count'] = metrics.get('activation_count', 0)
        details['activity_score'] = session_manager.calculate_activity_score(view.id())

        # Extension - prioritize syntax over file extension for all files
        ext = ''

        # First try to get the syntax for all files (including untitled and dotfiles)
        syntax = view.settings().get('syntax', '')
        if syntax:
            # Extract the syntax name from the path
            # Format is usually like "Packages/Python/Python.sublime-syntax"
            syntax_parts = syntax.split('/')
            if len(syntax_parts) > 1:
                # Get the last part and remove .sublime-syntax or .tmLanguage
                syntax_name = syntax_parts[-1].replace('.sublime-syntax', '').replace('.tmLanguage', '')

                # Look up the syntax in our mapping
                for key, value in EXTENSION_MAPPING.items():
                    if key.lower() in syntax_name.lower() or syntax_name.lower() in key.lower():
                        ext = value
                        break

                # If not found in mapping, use the syntax name
                if not ext:
                    ext = syntax_name.lower()

        # If no syntax found from settings, try to get the scope name
        if not ext and view.size() > 0:
            scope = view.scope_name(0)
            if scope:
                # Extract the main scope part (e.g., "source.python")
                scope_parts = scope.split('.')
                if len(scope_parts) > 1:
                    scope_type = scope_parts[1]  # e.g., "python" from "source.python"

                    # Look up the scope in our mapping
                    for key, value in EXTENSION_MAPPING.items():
                        if key.lower() == scope_type.lower() or scope_type.lower() in key.lower():
                            ext = value
                            break

                    # If not found in mapping, use the scope type
                    if not ext:
                        ext = scope_type

        # If still no syntax, fall back to file extension for saved files
        if not ext and file_name:
            basename = os.path.basename(file_name)
            _, file_ext = os.path.splitext(basename)

            # For normal files, use the extension without the dot
            if file_ext:
                ext = file_ext[1:] if file_ext.startswith('.') else file_ext

            # Special handling for dotfiles like .gitignore
            if not ext and basename.startswith('.'):
                # Try to get a more meaningful name for common dotfiles
                if basename == '.gitignore':
                    ext = 'gitignore'
                elif basename == '.gitattributes':
                    ext = 'gitattributes'
                elif basename == '.editorconfig':
                    ext = 'editorconfig'
                elif basename == '.env':
                    ext = 'env'
                else:
                    # Just use the basename without the dot
                    ext = basename[1:]

        # Store extension in details
        details['extension'] = ext

        # Size calculation
        if view:
            # Get the actual content
            content = view.substr(sublime.Region(0, view.size()))
            char_count = len(content)
            details['size_bytes'] = char_count

            size_kb = char_count / 1024

            if size_kb < 1:
                size_str = "< 1 KB"
            elif size_kb < 1024:
                size_str = f"{size_kb:.1f} KB"
            else:
                size_str = f"{size_kb/1024:.1f} MB"
            details['size_str'] = size_str
            details['size_kb'] = size_kb

        # Status
        status = []
        if details.get('is_dirty', False):
            status.append('Dirty')
        if details.get('is_read_only', False):
            status.append('ReadOnly')
        if not details.get('file_name'):
            status.append('Unsaved')
        if details.get('file_name') and not details.get('is_in_project', False):
            status.append('External')

        status_str = ','.join(status) if status else 'Clean'
        details['status_str'] = status_str

        # Last access
        last_access = details.get('time_since_access_str', 'N/A')
        # Convert None or 'None' to 'N/A'
        if last_access is None or last_access == 'None':
            last_access = 'N/A'
        details['time_since_access_str'] = last_access

        return details

    def _display_results_json(self, categorized_views, all_views=None):
        output_view = self.window.create_output_panel("jorn_tab_categorizer_json")
        output_view.set_read_only(False)
        output_view.run_command("select_all")
        output_view.run_command("right_delete")

        # Convert to serializable format
        serializable_data = {
            "by_category": {},
            "all_views": []
        }

        # Process categorized views
        for category, items in categorized_views.items():
            serializable_data["by_category"][category] = []
            for view, details in items:
                # Remove non-serializable items
                serialized_details = {k: v for k, v in details.items() if k != 'view'}

                # Convert datetime objects to strings
                for key, value in serialized_details.items():
                    if isinstance(value, datetime.datetime):
                        serialized_details[key] = value.isoformat()

                serializable_data["by_category"][category].append(serialized_details)

        # Process all views if provided
        if all_views:
            for view, details in all_views:
                # Remove non-serializable items
                serialized_details = {k: v for k, v in details.items() if k != 'view'}

                # Convert datetime objects to strings
                for key, value in serialized_details.items():
                    if isinstance(value, datetime.datetime):
                        serialized_details[key] = value.isoformat()

                serializable_data["all_views"].append(serialized_details)

        # Convert to JSON with pretty formatting
        json_str = json.dumps(serializable_data, indent=2)

        output_view.run_command("append", {"characters": json_str})
        output_view.set_read_only(True)
        self.window.run_command("show_panel", {"panel": "output.jorn_tab_categorizer_json"})

    def _export_to_markdown(self, all_views, file_path, columns):
        """Export tab data to a markdown table file"""
        try:
            # Debug log the column order
            self._debug_log(f"Exporting to markdown with columns: {columns}")

            # Process all view details first
            for view, details in all_views:
                self._process_view_details(view, details)

            # Create the markdown table
            table_content = ""

            # Calculate column widths for better alignment
            col_widths = {}
            for col_name in columns:
                # Start with the column name length
                col_widths[col_name] = len(col_name)

                # Check all rows for this column to find the maximum width
                for view, details in all_views:
                    if col_name == "File":
                        cell_value = details.get('basename', 'Untitled')
                    elif col_name == "Ext":
                        cell_value = details.get('extension', '')
                    elif col_name == "Group":
                        cell_value = str(details.get('group', 'N/A'))
                    elif col_name == "Index":
                        cell_value = str(details.get('index', 'N/A'))
                    elif col_name == "Lines":
                        cell_value = str(details.get('line_count', 'N/A'))
                    elif col_name == "Size":
                        cell_value = details.get('size_str', 'N/A')
                    elif col_name == "Modified":
                        cell_value = str(details.get('modified_time_str', 'N/A'))
                    elif col_name == "Last Access":
                        cell_value = str(details.get('time_since_access_str', 'N/A'))
                    elif col_name == "Status":
                        cell_value = details.get('status_str', 'N/A')
                    elif col_name == "Category":
                        cell_value = details.get('category', 'N/A')
                    elif col_name == "Depth":
                        cell_value = str(details.get('depth', 'N/A'))
                    elif col_name == "Last Saved":
                        cell_value = details.get('time_since_save', 'N/A')
                    elif col_name == "Save Count":
                        cell_value = str(details.get('save_count', 0))
                    elif col_name == "Path":
                        cell_value = str(details.get('path', 'N/A'))
                    else:
                        # For any custom columns, try to get the value from details
                        key = col_name.lower().replace(" ", "_")
                        cell_value = str(details.get(key, 'N/A'))

                    # Update the maximum width for this column
                    col_widths[col_name] = max(col_widths[col_name], len(str(cell_value)))

            # Create header row
            header = "| "
            separator = "| "
            for col_name in columns:
                header += col_name + " | "
                separator += "-" * len(col_name) + " | "

            table_content += header + "\n"
            table_content += separator + "\n"

            # Add table rows
            for view, details in all_views:
                row = "| "
                for col_name in columns:
                    if col_name == "File":
                        cell = details.get('basename', 'Untitled')
                    elif col_name == "Ext":
                        cell = details.get('extension', '')
                    elif col_name == "Group":
                        cell = str(details.get('group', 'N/A'))
                    elif col_name == "Index":
                        cell = str(details.get('index', 'N/A'))
                    elif col_name == "Lines":
                        cell = str(details.get('line_count', 'N/A'))
                    elif col_name == "Size":
                        cell = details.get('size_str', 'N/A')
                    elif col_name == "Modified":
                        cell = str(details.get('modified_time_str', 'N/A'))
                    elif col_name == "Last Access":
                        cell = str(details.get('time_since_access_str', 'N/A'))
                    elif col_name == "Status":
                        cell = details.get('status_str', 'N/A')
                    elif col_name == "Category":
                        cell = details.get('category', 'N/A')
                    elif col_name == "Depth":
                        cell = str(details.get('depth', 'N/A'))
                    elif col_name == "Last Saved":
                        cell = details.get('time_since_save', 'N/A')
                    elif col_name == "Save Count":
                        cell = str(details.get('save_count', 0))
                    elif col_name == "Path":
                        cell = str(details.get('path', 'N/A'))
                    else:
                        # For any custom columns, try to get the value from details
                        key = col_name.lower().replace(" ", "_")
                        cell = str(details.get(key, 'N/A'))

                    row += cell + " | "

                table_content += row + "\n"

            # Write to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(table_content)

            sublime.status_message(f"Tab data exported to {file_path}")
            return True
        except Exception as e:
            sublime.error_message(f"Error exporting tab data: {str(e)}")
            return False

    def _import_from_markdown(self, file_path):
        """Import tab data from a markdown table file and reorganize tabs"""
        try:
            self._debug_log(f"Starting import from {file_path}")

            # Read the markdown file
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            self._debug_log(f"File content length: {len(content)} characters")

            # Parse the markdown table
            lines = content.strip().split('\n')
            self._debug_log(f"Found {len(lines)} lines in the file")

            if len(lines) < 3:
                sublime.error_message("Invalid markdown table format")
                self._debug_log("Error: File has fewer than 3 lines")
                return False

            # Extract header
            header_line = lines[0].strip()
            self._debug_log(f"Header line: {header_line}")

            if not header_line.startswith('|') or not header_line.endswith('|'):
                sublime.error_message("Invalid markdown table header")
                self._debug_log("Error: Header line doesn't start and end with |")
                return False

            # Parse header columns
            header_cols = [col.strip() for col in header_line.strip('|').split('|')]
            self._debug_log(f"Found {len(header_cols)} columns: {header_cols}")

            # Find the indices of important columns
            group_idx = None
            index_idx = None
            file_idx = None
            path_idx = None

            for i, col in enumerate(header_cols):
                if col.strip() == "Group":
                    group_idx = i
                elif col.strip() == "Index":
                    index_idx = i
                elif col.strip() == "File":
                    file_idx = i
                elif col.strip() == "Path":
                    path_idx = i

            self._debug_log(f"Column indices - Group: {group_idx}, Index: {index_idx}, File: {file_idx}, Path: {path_idx}")

            # Only Group column is required - Index can be derived from row order if missing
            if group_idx is None:
                sublime.error_message("Table must contain a 'Group' column")
                self._debug_log("Error: Missing required Group column")
                return False

            # Log whether we'll use explicit indices or row order
            if index_idx is None:
                self._debug_log("Index column not found - will use row order as index")
            else:
                self._debug_log("Using explicit Index column values")

            # Skip the separator line
            data_lines = lines[2:]
            self._debug_log(f"Found {len(data_lines)} data lines")

            # Parse the data rows
            tab_data = []
            for i, line in enumerate(data_lines):
                if not line.strip() or not line.startswith('|') or not line.endswith('|'):
                    self._debug_log(f"Skipping invalid line {i+3}: {line}")
                    continue

                cols = [col.strip() for col in line.strip('|').split('|')]
                if len(cols) != len(header_cols):
                    self._debug_log(f"Skipping line {i+3} with wrong column count: {len(cols)} vs {len(header_cols)}")
                    continue

                row_data = {}
                for j, col in enumerate(cols):
                    row_data[header_cols[j]] = col

                tab_data.append(row_data)
                self._debug_log(f"Parsed row {i+3}: Group={row_data.get('Group', 'N/A')}, Index={row_data.get('Index', 'N/A')}, File={row_data.get('File', 'N/A')}")

            self._debug_log(f"Successfully parsed {len(tab_data)} rows")

            # Get all views
            all_views = []
            for view in self.window.views():
                # Process view details to help with matching
                details = {}
                self._process_view_details(view, details)
                all_views.append((view, details))
                self._debug_log(f"Found view: {details.get('basename', 'Unknown')}, Path: {details.get('path', 'N/A')}")

            self._debug_log(f"Found {len(all_views)} views in the window")

            # First, create a mapping of views to their target positions
            view_targets = []

            # Process each row in the imported data
            for i, row in enumerate(tab_data):
                try:
                    target_group = int(row["Group"])

                    # Use row order as index if Index column is missing
                    if index_idx is None:
                        # Use the row number as the index
                        target_index = i
                        self._debug_log(f"Processing row {i}: Target Group={target_group}, Using row order as Index={target_index}, File={row.get('File', 'N/A')}")
                    else:
                        target_index = int(row["Index"])
                        self._debug_log(f"Processing row {i}: Target Group={target_group}, Explicit Index={target_index}, File={row.get('File', 'N/A')}")

                    # Find the view that matches this row
                    matching_view = None

                    # If we have a path, use that to find the view (most reliable)
                    if path_idx is not None and row[header_cols[path_idx]] != 'N/A':
                        path = row[header_cols[path_idx]]
                        self._debug_log(f"Trying to match by path: {path}")
                        for view, details in all_views:
                            if view.file_name() and os.path.normpath(view.file_name()) == os.path.normpath(path):
                                matching_view = view
                                self._debug_log(f"Found match by path: {details.get('basename', 'Unknown')}")
                                break

                    # If no match by path and we have a filename, try to match by filename
                    if not matching_view and file_idx is not None:
                        filename = row[header_cols[file_idx]]
                        self._debug_log(f"Trying to match by filename: {filename}")
                        for view, details in all_views:
                            view_name = os.path.basename(view.file_name()) if view.file_name() else 'Untitled'
                            if view_name == filename:
                                # For untitled files, we need to be more careful
                                if view_name == 'Untitled':
                                    # Try to match by multiple attributes
                                    match_score = 0

                                    # Match by line count
                                    if 'Lines' in row and 'line_count' in details:
                                        if str(details['line_count']) == row['Lines']:
                                            match_score += 3
                                            self._debug_log(f"Line count match for Untitled: {row['Lines']}")

                                    # Match by size
                                    if 'Size' in row and 'size_str' in details:
                                        if details['size_str'] == row['Size']:
                                            match_score += 2
                                            self._debug_log(f"Size match for Untitled: {row['Size']}")

                                    # Match by extension/syntax
                                    if 'Ext' in row and 'extension' in details:
                                        if details['extension'] == row['Ext']:
                                            match_score += 2
                                            self._debug_log(f"Extension match for Untitled: {row['Ext']}")

                                    # If we have a good match score, use this view
                                    if match_score >= 3:
                                        matching_view = view
                                        self._debug_log(f"Found match for Untitled with score {match_score}")
                                        break
                                else:
                                    matching_view = view
                                    self._debug_log(f"Found match by filename: {filename}")
                                    break

                    # If we still don't have a match, try to match by current position
                    if not matching_view and 'Group' in row and 'Index' in row:
                        current_group = int(row['Group'])
                        current_index = int(row['Index'])
                        self._debug_log(f"Trying to match by current position: Group {current_group}, Index {current_index}")

                        # Find a view at this position
                        for view, details in all_views:
                            view_group, view_index = self.window.get_view_index(view)
                            if view_group == current_group and view_index == current_index:
                                matching_view = view
                                self._debug_log(f"Found match by current position: {details.get('basename', 'Unknown')}")
                                break

                    # If we found a matching view, add it to our targets
                    if matching_view:
                        view_targets.append((matching_view, target_group, target_index))
                        self._debug_log(f"Added to targets: {row.get('File', 'Unknown')} -> Group {target_group}, Index {target_index}")
                    else:
                        self._debug_log(f"No matching view found for row {i}: {row.get('File', 'Unknown')}")
                except Exception as e:
                    self._debug_log(f"Error processing row {i}: {row} - {str(e)}")
                    import traceback
                    if JornTabCategorizerCommand.debug_mode:
                        print(traceback.format_exc())

            self._debug_log(f"Created {len(view_targets)} view targets")

            # Sort the targets by group and index to ensure we move them in the right order
            view_targets.sort(key=lambda x: (x[1], x[2]))

            # Use a comprehensive approach based on the reference code
            self._debug_log("Using comprehensive approach to reorganize tabs")

            # First, ensure we have enough groups
            max_group = max([target_group for _, target_group, _ in view_targets], default=0)
            current_groups = self.window.num_groups()
            self._debug_log(f"Need {max_group + 1} groups, currently have {current_groups}")

            # Only create a new layout if we don't have enough groups
            if current_groups <= max_group:
                self._debug_log(f"Need more groups: have {current_groups}, need {max_group + 1}")

                # Create a simple columns layout with the required number of groups
                self._debug_log(f"Creating layout with {max_group + 1} groups")
                self._ensure_enough_groups(max_group + 1)

                # Wait for layout to be applied
                sublime.set_timeout(lambda: None, 200)
            else:
                self._debug_log(f"Current layout has enough groups: {current_groups} >= {max_group + 1}")
                # No need to change the layout

            # Step 1: Organize views by group
            views_by_group = {}
            for i in range(max_group + 1):
                views_by_group[i] = []

            # Add views to their target groups
            for view, target_group, target_index in view_targets:
                views_by_group[target_group].append((view, target_index))

            # Sort views within each group by their target index
            for group in views_by_group:
                views_by_group[group].sort(key=lambda x: x[1])
                self._debug_log(f"Group {group} will have {len(views_by_group[group])} views")

            # Step 2: First try to assign all views at once using set_group_views
            try:
                self._debug_log("Attempting to set all groups at once")
                for group, view_list in views_by_group.items():
                    if view_list:
                        # Extract just the views (without indices)
                        views_only = [v[0] for v in view_list]

                        # Focus on this group
                        self.window.focus_group(group)
                        self._debug_log(f"Focused on group {group}")

                        # Set all views for this group
                        self.window.set_group_views(group, views_only)
                        self._debug_log(f"Set {len(views_only)} views for group {group}")

                # Verify the results
                self._debug_log("Verifying after bulk assignment")
                all_correct = True
                for view, target_group, target_index in view_targets:
                    final_group, final_index = self.window.get_view_index(view)
                    self._debug_log(f"View {view.name() or 'Untitled'}: Target Group {target_group}, Index {target_index} -> Final Group {final_group}, Index {final_index}")
                    if final_group != target_group:
                        all_correct = False

                if all_correct:
                    self._debug_log("Bulk assignment successful")
                    return True

                self._debug_log("Bulk assignment didn't work completely, trying individual moves")
            except Exception as e:
                self._debug_log(f"Error during bulk assignment: {str(e)}")
                self._debug_log("Falling back to individual moves")

            # Step 3: If bulk assignment failed, try individual moves
            for group, view_list in views_by_group.items():
                # First focus on this group
                self.window.focus_group(group)
                self._debug_log(f"Focused on group {group}")

                # Process each view for this group
                for i, (view, target_index) in enumerate(view_list):
                    try:
                        # Get current position
                        current_group, current_index = self.window.get_view_index(view)
                        self._debug_log(f"Moving view {view.name() or 'Untitled'} from Group {current_group}, Index {current_index} to Group {group}, Index {target_index}")

                        # Focus the view
                        self.window.focus_view(view)
                        self._debug_log(f"Focused view: {view.name() or 'Untitled'}")

                        # If we need to change groups
                        if current_group != group:
                            self._debug_log(f"Moving to group {group}")

                            # Try direct API first
                            self.window.set_view_index(view, group, target_index)

                            # Verify the move
                            new_group, new_index = self.window.get_view_index(view)
                            self._debug_log(f"After direct move: view is now at Group {new_group}, Index {new_index}")

                            # If direct API failed, try UI command
                            if new_group != group:
                                self._debug_log(f"Direct move failed, trying UI command")
                                self.window.run_command("move_to_group", {"group": group})

                                # Verify again
                                new_group, new_index = self.window.get_view_index(view)
                                self._debug_log(f"After UI command: view is now at Group {new_group}, Index {new_index}")

                            # Update current position
                            current_group, current_index = new_group, new_index

                        # Now reorder within the group if needed
                        if current_index != target_index:
                            self._debug_log(f"Reordering within group from index {current_index} to {target_index}")

                            # Try direct API first
                            self.window.set_view_index(view, current_group, target_index)

                            # Verify the move
                            _, new_index = self.window.get_view_index(view)
                            self._debug_log(f"After direct reorder: view is now at Index {new_index}")

                            # If direct API failed, try tab movement commands
                            if new_index != target_index:
                                self._debug_log(f"Direct reorder failed, trying tab movement commands")

                                # We need to move the tab multiple times to get it to the right position
                                moves_needed = abs(new_index - target_index)
                                direction = "left" if new_index > target_index else "right"

                                self._debug_log(f"Need to move {moves_needed} times {direction}")
                                for j in range(moves_needed):
                                    self.window.run_command(f"move_tab_{direction}")
                                    # Small delay between moves
                                    sublime.set_timeout(lambda: None, 50)

                                # Verify final position
                                _, final_index = self.window.get_view_index(view)
                                self._debug_log(f"After tab movement: view is now at Index {final_index}")

                        # Add a delay between processing views
                        sublime.set_timeout(lambda: None, 100)

                    except Exception as e:
                        self._debug_log(f"Error moving individual view: {str(e)}")
                        import traceback
                        if JornTabCategorizerCommand.debug_mode:
                            print(traceback.format_exc())
                        continue

            # Final verification
            self._debug_log("Final verification of all views")
            for i, (view, target_group, target_index) in enumerate(view_targets):
                final_group, final_index = self.window.get_view_index(view)
                self._debug_log(f"View {i} {view.name() or 'Untitled'}: Target Group {target_group}, Index {target_index} -> Final Group {final_group}, Index {final_index}")

            sublime.status_message(f"Tabs reorganized based on {file_path}")
            self._debug_log("Import completed successfully")

            # Make sure the tab_organization.md file is the active tab after import
            def focus_on_markdown_file():
                # Find the view for the markdown file
                for v in self.window.views():
                    if v.file_name() == file_path:
                        self.window.focus_view(v)
                        print(f"[DEBUG] Focused back on {file_path}")
                        break

            # Add a small delay to ensure all tab movements have completed
            sublime.set_timeout(focus_on_markdown_file, 300)

            return True
        except Exception as e:
            sublime.error_message(f"Error importing tab data: {str(e)}")
            import traceback
            print(f"[DEBUG] Critical error during import: {str(e)}")
            print(traceback.format_exc())
            return False

    def _debug_log(self, message):
        """Print debug messages only when debug mode is enabled"""
        if JornTabCategorizerCommand.debug_mode:
            print(f"[DEBUG] {message}")

    def _ensure_enough_groups(self, needed_groups):
        """Ensure we have enough groups for tab organization"""
        try:
            current_groups = self.window.num_groups()

            # If we already have enough groups, no need to change anything
            if current_groups >= needed_groups:
                self._debug_log(f"Already have enough groups: {current_groups} >= {needed_groups}")
                return True

            # Create a simple columns layout
            self._debug_log(f"Creating a columns layout with {needed_groups} groups")
            cols = [i / needed_groups for i in range(needed_groups + 1)]
            rows = [0.0, 1.0]
            cells = [[i, 0, i + 1, 1] for i in range(needed_groups)]

            layout = {"cols": cols, "rows": rows, "cells": cells}

            # Apply the layout
            self.window.set_layout(layout)
            self._debug_log(f"Applied layout with {needed_groups} groups")

            return True
        except Exception as e:
            self._debug_log(f"Error ensuring enough groups: {str(e)}")
            import traceback
            if JornTabCategorizerCommand.debug_mode:
                print(traceback.format_exc())

            # Try a simpler approach - just add groups one by one
            try:
                self._debug_log("Falling back to adding groups one by one")
                current_groups = self.window.num_groups()
                groups_to_add = needed_groups - current_groups

                for _ in range(groups_to_add):
                    self.window.run_command("new_pane", {"move": False})
                    # Small delay between adding groups
                    sublime.set_timeout(lambda: None, 50)

                return True
            except Exception as e2:
                self._debug_log(f"Error adding groups: {str(e2)}")
                return False

class InsertContentCommand(sublime_plugin.TextCommand):
    def run(self, edit, content):
        self.view.insert(edit, 0, content)

class EraseViewContentsCommand(sublime_plugin.TextCommand):
    def run(self, edit):
        self.view.erase(edit, sublime.Region(0, self.view.size()))

class JornTabOrganizerListener(sublime_plugin.EventListener):
    """Listen for tab events and track session metrics"""

    def on_post_save(self, view):
        """Called after a file is saved"""
        try:
            # Get the file path
            file_path = view.file_name()
            if not file_path:
                return

            # Record save event in session manager
            session_manager = TabSessionManager.get_instance()
            session_manager.record_save(view.id())

            # Check if debug mode is enabled
            debug_mode = JornTabCategorizerCommand.debug_mode
            if debug_mode:
                print(f"[DEBUG] Tab saved: {file_path}")
                print(f"[DEBUG] Save count: {session_manager.get_tab_metrics(view.id())['save_count']}")

            # Check if this is the tab organization file
            file_name = os.path.basename(file_path)
            if file_name == "tab_organization.md":
                if debug_mode:
                    print(f"[DEBUG] Tab organization file saved: {file_path}")

                # Get the window
                window = view.window()
                if not window:
                    return

                # Run the import command
                if debug_mode:
                    print("[DEBUG] Automatically triggering tab import")

                window.run_command("jorn_tab_categorizer", {
                    "import_from_file": True,
                    "file_path": file_path,
                    "debug": debug_mode
                })

                # Show a status message
                sublime.status_message("Tabs reorganized based on saved file")

                # Make sure the tab_organization.md file is the active tab after import
                # Use a small delay to ensure the import has completed
                def focus_on_markdown_file():
                    # Find the view for tab_organization.md
                    for v in window.views():
                        if v.file_name() == file_path:
                            window.focus_view(v)
                            if debug_mode:
                                print("[DEBUG] Focused back on tab_organization.md")
                            break

                # Add a small delay to ensure the import has completed
                sublime.set_timeout(focus_on_markdown_file, 300)
        except Exception as e:
            print(f"[ERROR] Error in on_post_save: {str(e)}")
            import traceback
            print(traceback.format_exc())

    def on_activated(self, view):
        """Called when a view is activated"""
        try:
            # Record activation event
            session_manager = TabSessionManager.get_instance()
            session_manager.record_activation(view.id())

            # Debug output if enabled
            if hasattr(JornTabCategorizerCommand, 'debug_mode') and JornTabCategorizerCommand.debug_mode:
                metrics = session_manager.get_tab_metrics(view.id())
                print(f"[DEBUG] Tab activated: {view.file_name() or 'Untitled'}")
                print(f"[DEBUG] Activation count: {metrics['activation_count']}")
        except Exception as e:
            print(f"[ERROR] Error in on_activated: {str(e)}")
            import traceback
            print(traceback.format_exc())
