import win32gui
import win32con

def get_all_windows():
    windows = []
    def enum_windows(hwnd, result):
        windows.append((hwnd, win32gui.GetWindowText(hwnd), win32gui.GetClassName(hwnd)))
    win32gui.EnumWindows(enum_windows, [])
    return windows

# def get_all_windows():
#     """Return a list of all windows."""
#     windows = []
#     def enum_windows(hwnd, result):
#         windows.append(get_hwnd_data(hwnd))
#     win32gui.EnumWindows(enum_windows, [])
#     return wind

# Function to initialize or focus a specific window
def focus_window(hwnd):
    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)  # Restore the window if it's minimized.
    win32gui.SetForegroundWindow(hwnd)

# Example usage
if __name__ == "__main__":
    print("Enumerating all visible windows:")
    for hwnd, title, xxx in get_all_windows():
        print(f"Window Handle: {hwnd}, Title: {title}")



# # Import modules
# import os
# import sys
# import re
# import time
# import random
# import ctypes
# import urllib.parse
# import json
# from enum import Enum
# # Error handling
# # import traceback
# # import pywintypes

# # Import pywin32 modules
# import win32com.client
# import win32com.shell.shellcon as shellcon
# import win32con
# import win32gui
# import win32api
# import win32process


# def categorize_window(hwnd_class, hwnd_visibility):
#     # Categorizes the window based on its class and visibility.
#     if hwnd_class.startswith("tooltips_class32"):
#         return "Tooltip"
#     elif hwnd_class == "Shell_TrayWnd":
#         return "Taskbar"
#     elif hwnd_visibility and not hwnd_class.startswith("SysShadow"):
#         return "Normal"
#     else:
#         return "Other"

# # get window data
# def get_hwnd_data(hwnd):
#     # Get the current monitor
#     monitor_handle = win32api.MonitorFromWindow(hwnd, win32con.MONITOR_DEFAULTTONEAREST)
#     monitor_info = win32api.GetMonitorInfo(monitor_handle)
#     monitor_device = (monitor_info["Device"])

#     # Get general window data: Intermediate values used to compute state/position/size
#     hwnd_placement = win32gui.GetWindowPlacement(hwnd)
#     hwnd_rect = win32gui.GetWindowRect(hwnd)
#     hwnd_controls_state = hwnd_placement[1]
#     hwnd_position = (hwnd_rect[0], hwnd_rect[1])
#     hwnd_size = (hwnd_rect[2] - hwnd_rect[0], hwnd_rect[3] - hwnd_rect[1])

#     # Get general hwnd (Error if 'GetWindowPlacement' or 'GetWindowRect')
#     hwnd_visibility_state = win32gui.IsWindowVisible(hwnd)
#     hwnd_title = win32gui.GetWindowText(hwnd)
#     hwnd_class = win32gui.GetClassName(hwnd)

#     # Categorize the window
#     hwnd_category = categorize_window(hwnd_class, hwnd_visibility_state)


#     # Prepare data for windows with a process handle
#     hwnd_process_path = None
#     hwnd_process_id = None

#     # win32api: Retrieve the executable filename by accessing the current window's process
#     # hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
#     # hwnd_process_handle = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION, False, hwnd_process_id)

#     # CTYPES: Retrieve the process handle of the window
#     hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
#     hwnd_process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
#     hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(hwnd_process_query, False, hwnd_process_id)
#     # If a process handle was optained
#     if hwnd_process_handle:
#         try:
#             hwnd_process_path = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
#         except pywintypes.error:
#             print(traceback.format_exc())


#     # Return a dictionary with keys and values for each piece of information
#     return {
#         "hwnd":hwnd,
#         "title":hwnd_title,
#         "class":hwnd_class,
#         "monitor":monitor_device,
#         "visibility":hwnd_visibility_state,
#         "controls_state":hwnd_controls_state,
#         "position":hwnd_position,
#         "size":hwnd_size,
#         "placement":hwnd_placement,
#         "rect":hwnd_rect,
#         "process_path":hwnd_process_path,
#         "process_id":hwnd_process_id,
#         "category": hwnd_category,
#     }




# def write_windows_to_json(file_path):
#     try:
#         windows_data = get_all_windows()
#         with open(file_path, 'w') as file:
#             json.dump(windows_data, file, indent=4)
#         print(f"Data successfully written to {file_path}")
#     except Exception as e:
#         print(f"Error occurred while writing to JSON: {e}")

# # Example usage
# # write_windows_to_json("window_data.json")

# windows = get_all_windows()
# for x in windows:
# #     # print(x.keys())
#     print(x)
# # time.sleep(99999)
