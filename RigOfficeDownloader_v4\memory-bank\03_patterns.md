## Distilled Highlights
- Three-stage workflow: Documents → Files → Downloads
- Uses JSON for data storage and Markdown for user interfaces
- Implements filter chains for document/file selection
- Employs metadata-driven file naming and organization

# 03_patterns.md

## System Architecture

The RigOfficeDownloader follows a **stage-based processing pipeline** with user checkpoints:

```
Documents → Files → Downloads
```

1. **Document Stage**: Retrieve and filter document metadata
2. **File Stage**: Identify and select files associated with documents
3. **Download Stage**: Retrieve and organize selected files

## Core Design Patterns

### Data Flow Pattern
```
Web scraping → JSON storage → Markdown interface → User selection → Automated downloads
```

This pattern enables:
- Clear separation between data retrieval, processing, and presentation
- Persistence of intermediate results (resilience)
- User intervention at strategic checkpoints

### User Interaction Model
1. **Interactive Menu**: Central controller for all operations
2. **Markdown Review Interface**: Human-readable document/file selection
3. **Configuration Controls**: Parameter adjustment and filter setup

### Metadata Management
- **JSON as Data Store**: Structured storage of all metadata
- **Markdown as Interface**: Human-editable representation of JSON data
- **Bidirectional Conversion**: Synchronized JSON ↔ Markdown transformations

## Data Structures

### Document Entity
Represents a retrievable document with metadata (exact fields to be documented separately)

### File Entity
Represents a downloadable file with metadata including:
- Parent document reference
- File-specific attributes
- Download location
- Naming schema

### Filter Chain
Configurable sequence of inclusion/exclusion patterns applied to document and file metadata

## Organization Schema

### File Naming Convention
Uses metadata to generate consistent, informative filenames:
- Based on document/file metadata fields
- Supports subfolder hierarchy from slash notation ('/')

### Directory Structure
```
outputs/
├── data/               # Metadata control files
│   ├── <rig>-a-docs.json
│   ├── <rig>-a-docs.md
│   ├── <rig>-b-files.json
│   └── <rig>-b-files.md
└── downloads/          # Final organized files
    └── <rig>/
        ├── <subfolder>/
        │   └── <item_generated_name>.<ext>
        └── ...
```

## Operational Patterns

### Error Handling
- Graceful recovery from network/parsing errors
- Detailed logging for troubleshooting
- Deduplication of downloads

### Stage Isolation
Each processing stage has:
- Distinct inputs and outputs
- Clear boundaries for user intervention
- Separate error handling

### Configuration Management
- Run-time parameter adjustment
- Persistent settings between sessions
- Filter chain configuration and storage
