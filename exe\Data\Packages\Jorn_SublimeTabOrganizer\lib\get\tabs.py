import os
import time
import datetime
import sublime
import hashlib

def format_time_duration(seconds):
    """Format a time duration in seconds to a human-readable string."""
    if seconds is None:
        return "N/A"

    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    elif seconds < 86400:
        hours = seconds / 3600
        return f"{hours:.1f}h"
    else:
        days = seconds / 86400
        return f"{days:.1f}d"

def all_views(window=None):
    if window:
        return window.views()

    views = []
    for window in sublime.windows():
        views.extend(window.views())
    return views

def view_details(view):
    if not view:
        return {}

    file_name = view.file_name()
    basename = os.path.basename(file_name) if file_name else "Untitled"

    group, index = -1, -1
    window = view.window()
    if window:
        for g in range(window.num_groups()):
            views = window.views_in_group(g)
            if view in views:
                group = g
                index = views.index(view)
                break

    # Get content size - always available from the view
    content_size_bytes = view.size()
    content_size_kb = round(content_size_bytes / 1024, 2)
    content_size_mb = round(content_size_bytes / (1024 * 1024), 2)

    # Get file stats if available
    file_stats = {
        "size_bytes": content_size_bytes,
        "size_kb": content_size_kb,
        "size_mb": content_size_mb,
    }

    # Only add file stats if the file exists
    if file_name and os.path.exists(file_name):
        try:
            stats = os.stat(file_name)
            file_stats.update({
                "created_time": stats.st_ctime,
                "created_time_str": datetime.datetime.fromtimestamp(stats.st_ctime).strftime('%Y.%m.%d-kl.%H.%M'),
                "modified_time": stats.st_mtime,
                "modified_time_str": datetime.datetime.fromtimestamp(stats.st_mtime).strftime('%Y.%m.%d-kl.%H.%M'),
                "accessed_time": stats.st_atime,
                "accessed_time_str": datetime.datetime.fromtimestamp(stats.st_atime).strftime('%Y.%m.%d-kl.%H.%M'),
            })
        except Exception:
            pass

    # Get relative path if in project
    relative_path = None
    directory = os.path.dirname(file_name) if file_name else None

    if file_name and window:
        try:
            project_folders = window.folders()
            for folder in project_folders:
                if file_name.startswith(folder):
                    relative_path = os.path.relpath(file_name, folder)
                    break
        except Exception as e:
            print(f"Error getting relative path: {str(e)}")

    # Get last accessed time from view settings
    last_accessed = view.settings().get('jorn_last_accessed_time')
    last_accessed_str = None
    if last_accessed:
        last_accessed_str = datetime.datetime.fromtimestamp(last_accessed).strftime('%Y.%m.%d-kl.%H.%M')
        time_since_access = time.time() - last_accessed
        time_since_access_str = format_time_duration(time_since_access)
    else:
        time_since_access = None
        time_since_access_str = 'N/A'  # Use 'N/A' instead of None

    result = {
        "view": view,
        "id": view.id(),
        "file_name": file_name,
        "basename": basename,
        "directory": os.path.dirname(file_name) if file_name else None,
        "relative_path": relative_path,
        "group": group,
        "index": index,
        "syntax": view.settings().get('syntax', '').split('/')[-1].split('.')[0],
        "line_count": view.rowcol(view.size())[0] + 1,
        "char_count": view.size(),
        "is_dirty": view.is_dirty(),
        "is_in_project": is_in_project(view),
        "is_read_only": view.is_read_only(),
        "is_scratch": view.is_scratch(),
        "last_accessed": last_accessed,
        "last_accessed_str": last_accessed_str,
        "time_since_access": time_since_access,
        "time_since_access_str": time_since_access_str,
        "content_hash": content_hash(view),
        **file_stats
    }

    return result

def content_hash(view):
    content = view.substr(sublime.Region(0, view.size()))
    return hashlib.md5(content.encode('utf-8')).hexdigest()

def is_in_project(view):
    if not view or not view.file_name():
        return False

    window = view.window()
    if not window:
        return False

    project_folders = window.folders()
    file_path = view.file_name()

    for folder in project_folders:
        if file_path.startswith(folder):
            return True

    return False

def views_by_group(window):
    result = {}
    for group in range(window.num_groups()):
        result[group] = window.views_in_group(group)
    return result

def active_view_in_group(window, group):
    return window.active_view_in_group(group)
