
# Template Structure

```
# FILE ORGANIZATION
templates/
├── {system_id}-a-{system_name}-{component_function}.md
├── {system_id}-b-{system_name}-{component_function}.md
└── {system_id}-c-{system_name}-{component_function}.md

# EXAMPLES
templates/
├── 0001-a-rephrase-instruction-converter.md
├── 0001-b-rephrase-essence-distiller.md
└── etc

# HIERARCHICAL STRUCTURE
System ({system_id})
├── Template A ({component_function})
│   ├── [Title]
│   ├── Interpretation
│   └── `{Transformation}`
├── Template B ({component_function})
└── Template C ({component_function})

# TEMPLATE FORMAT
[Title] Interpretation Execute as: `{Transformation}`
  │      │              │         └─ Machine-parsable parameters
  │      │              └─ Standard connector phrase
  │      └─ Human-readable instructions
  └─ Template identifier

# COMPONENT VISUALIZATION

┌─ Title ─────────────────────────────────────┐
│ [Instruction Converter]                     │
└────────────────────────────────────────────┬┘
                                             │
┌─ Interpretation ───────────────────────┐   │
│ Your goal is not to **answer** the     │   │
│ input prompt, but to **rephrase** it,  │   │
│ and to do so by the parameters defined │   │
│ *inherently* within this message.      │   │
│ Execute as:                            │   │
└───────────────────────────────────────┬┘   │
                                        │    │
┌─ Transformation ───────────────────┐  │    │
│ `{                                 │  │    │
│   role=instruction_converter;      │  │    │
│   input=[original_text:str];       │◄─┴────┘
│   process=[
│     strip_first_person_references(),
│     convert_statements_to_directives(),
│     identify_key_actions(),
│     ...
│   ];
│   constraints=[
│     deliver_clear_actionable_commands(),
│     preserve_original_sequence(),
│     ...
│   ];
│   requirements=[
│     remove_self_references(),
│     use_command_voice(),
│     ...
│   ];
│   output={instruction_format:str}
│ }`
└─────────────────────────────────────┘

# TRANSFORMATION STRUCTURE

┌─ Role ──────────────────────────────────────┐
│ role={function_identifier}                  │
│ # Defines template's primary function       │
└────────────────────────────────────────────┬┘
                                             │
┌─ Input ─────────────────────────────────┐  │
│ input=[{parameter}:{type}]              │  │
│ # Specifies input parameters and types  │  │
└─────────────────────────────────────────┘  │
                                             │
┌─ Process ───────────────────────────────┐  │
│ process=[                               │  │
│   {operation_1}(),                      │  │
│   {operation_2}(),                      │◄─┘
│   ...
│ ]
│ # Defines processing operations
└─────────────────────────────────────────┘

┌─ Constraints ─────────────────────────────┐
│ constraints=[                             │
│   {constraint_1}(),                       │
│   {constraint_2}(),                       │
│   ...                                     │
│ ]                                         │
│ # Sets operational boundaries             │
└──────────────────────────────────────────┬┘
                                           │
┌─ Requirements ──────────────────────┐    │
│ requirements=[                      │    │
│   {requirement_1}(),                │    │
│   {requirement_2}(),                │    │
│   ...                               │    │
│ ]                                   │    │
│ # Defines mandatory behaviors       │    │
└────────────────────────────────────┬┘    │
                                     │     │
┌─ Output ─────────────────────┐     │     │
│ output={parameter:{type}}    │◄────┴─────┘
│ # Specifies return format    │
└─────────────────────────────┘

# METADATA
Template:
  keywords: "{keyword_1}|{keyword_2}|{keyword_3}"
  template_id: "{system_id}-{step}-{system_name}-{component_function}"

System:
  sequence_id: "{system_id}"
  steps: [
    "{system_id}-a-{system_name}-{component_function}",
    "{system_id}-b-{system_name}-{component_function}",
    ...
  ]
```

## Example Templates

```
[Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.
`{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`
```

```
[<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.
`{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`
```

```
[Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:
`{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`
```

```
[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`
```

```
[Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`
```


# Template Structure Guide

## Overview
This guide documents the standardized structure for creating templates in the Template-Based Instruction System. Following this structure ensures consistency and enables the automatic processing of templates by the catalog generator.

## Template File Structure
Each template is stored as a markdown (.md) file and follows this standardized three-part structure:
```
[Title] Interpretation text `{transformation}`
```

### Components
1. __[TITLE]__: Enclosed in square brackets `[]`, defining the template's purpose.
   - Should be concise, descriptive, and follow title case formatting
   - Examples: `[Instruction Converter]`, `[Essence Distillation]`
2. __[INTERPRETATION]__: Plain text immediately following the title that describes what the template does.
   - Should clearly explain the template's function in natural language
   - Can include formatting like **bold**, *italic*, or other markdown elements
   - Provides context for human readers to understand the template's purpose
3. __[TRANSFORMATION]__: JSON-like structure enclosed in backticks with curly braces `\`{...}\``, defining the execution logic.
   - Contains the structured representation of the transformation process
   - Uses a consistent semi-colon separated key-value format

## Transformation Structure
The transformation component follows this standardized format:
```
`{
  role=<role_name>;
  input=[<input_params>];
  process=[<process_steps>];
  constraints=[<constraints>];
  requirements=[<requirements>];
  output={<output_format>}
}`
```

### Transformation Components
> [ROLE]: Defines the functional role of the template
   - Example: `role=essence_distiller`
> [INPUT]: Specifies the expected input format and parameters
   - Uses array syntax with descriptive parameter names
   - Example: `input=[original:any]`
> [PROCESS]: Lists the processing steps to be executed in order
   - Uses array syntax with function-like step definitions
   - Can include parameters within step definitions
   - Example: `process=[identify_core_intent(), strip_non_essential_elements()]`
> [constraints (optional)]: Specifies limitations or boundaries for the transformation
   - Uses array syntax with directive-like constraints
   - Example: `constraints=[deliver_clear_actionable_commands(), preserve_original_sequence()]`
> [requirements (optional)]: Defines mandatory aspects of the transformation
   - Uses array syntax with imperative requirements
   - Example: `requirements=[remove_self_references(), use_command_voice()]`
> [OUTPUT]: Specifies the expected output format
   - Uses object syntax with typed output parameters
   - Example: `output={distilled_essence:any}`

## Template Naming Convention
Templates follow a consistent naming convention that indicates their sequence and position:
```
<sequence_id>-<step>-<descriptive_name>.md
```

### Naming Components
> [SEQUENCE_ID]: A numeric identifier (e.g., 0001, 0002) that groups related templates
   - Four-digit format with leading zeros
   - Unique per sequence
> [step (optional)]: A lowercase letter (a, b, c, d, e) that indicates the position within a sequence
   - Only used for templates that are part of multi-step sequences
   - Follows alphabetical order to determine execution sequence
> [DESCRIPTIVE-NAME]: A hyphenated name that describes the template's purpose
   - All lowercase
   - Words separated by hyphens
   - Should be concise but descriptive

### Examples
- Single template: `0001-instructionconverter.md`
- Sequence templates:
  - `0002-a-essence-distillation.md`
  - `0002-b-exposing-coherence.md`
  - `0002-c-precision-enhancement.md`
  - `0002-d-structured-transformation.md`
  - `0002-e-achieving-self-explanation.md`

## Template Examples

### Example 1: Simple Template
```markdown
[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`
```

### Example 2: Sequence Step Template
```markdown
[Essence Distillation] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`
```

## Creating New Templates
To create a new template:
1. Determine if it should be a standalone template or part of a sequence
2. Assign an appropriate sequence_id (and step letter if part of a sequence)
3. Create a descriptive name using hyphenated lowercase words
4. Define the title that clearly indicates the template's purpose
5. Write the interpretation text that explains what the template does
6. Structure the transformation logic using the standardized format
7. Save the file with the proper naming convention
8. Run the catalog generator to include the new template in the JSON catalog

## Guidelines for Effective Templates
1. __[CLARITY]__: Each component should clearly communicate its purpose and functionality
2. __[SPECIFICITY]__: Be specific about input/output formats and processing steps
3. __[MODULARITY]__: Design templates to perform discrete, focused transformations
4. __[COMPOSABILITY]__: For sequence templates, ensure outputs from one step can serve as inputs to the next
5. __[CONSISTENCY]__: Follow the standardized structure and naming conventions exactly
6. __[SELF-DOCUMENTATION]__: The interpretation text should provide sufficient context for understanding
7. __[FUNCTIONAL COMPLETENESS]__: Ensure the transformation logic includes all necessary components

## Integration with Catalog Generator
The catalog generator extracts metadata from template files based on specific patterns. It is essential to follow the template structure exactly to ensure proper extraction.
The primary pattern used for extraction is:
```
\[(.*?)\]\s*(.*?)\s*(`\{.*?\}`)
```
This pattern extracts:
1. The title from within square brackets
2. The interpretation text following the title
3. The transformation within backticks and curly braces
Any deviation from this structure may result in improper extraction by the catalog generator.
