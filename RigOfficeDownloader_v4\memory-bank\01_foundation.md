## Distilled Highlights
- RigOfficeDownloader automates document retrieval from NOV's RigDoc system
- Core purpose is to save 75%+ engineering time by eliminating tedious manual tasks
- Project identity: "Complexity is the enemy of execution — Optimized for engineers, by engineers"

# 01_foundation.md

## Core Mission
The RigOfficeDownloader exists to **eliminate tedious manual document retrieval** from NOV's RigDoc system through intelligent automation, preserving 75%+ of engineering time for high-value tasks.

## Project Values
- **Time efficiency**: Convert 4-6 hours of manual work to under 1 hour of semi-automated work
- **Accuracy**: Replace error-prone manual organization with metadata-driven automation
- **Transparency**: Provide clear checkpoints and audit trails through JSON/Markdown
- **Engineer-centric**: Designed specifically for the workflow needs of engineering teams

## Long-term Vision
Create a reliable, maintainable automation solution that:
- Continuously improves the file retrieval workflow
- Adapts to changes in the RigDoc system
- Expands capabilities while maintaining simplicity
- Serves as a model for other automation tools in the engineering workflow

## Project Identity
"Complexity is the enemy of execution — Optimized for engineers, by engineers."
