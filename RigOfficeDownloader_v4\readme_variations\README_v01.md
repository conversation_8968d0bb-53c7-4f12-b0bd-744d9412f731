# RigOfficeDownloader

## Overview
RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.

## Key Features
- Three-stage workflow: Documents -> Files -> Downloads
- Interactive menu for flexible execution
- User control points via Markdown interfaces
- Smart file organization with subfolder support
- Configurable filter chains

## Setup & Usage
1. Run `py_venv_init.bat` to create the Python environment
2. Run `RigOfficeDownloader-v4.bat` to start the application
3. Use the interactive menu to configure and execute workflow steps

## Benefits
- Reduces documentation gathering time by 75%+
- Maintains consistent file organization
- Provides user control at key decision points
- Preserves document context and relationships

## Requirements
- Windows OS
- Python 3.6+
- Chrome browser (for Selenium automation)
