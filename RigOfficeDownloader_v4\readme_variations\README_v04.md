# RigOfficeDownloader
> Automate document retrieval from NOV's RigDoc system

## What This Tool Does
RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.

## Quick Start Guide
1. **Setup**: Run `py_venv_init.bat` to create the Python environment
2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application
3. **Configure**: Enter your rig number and search URLs when prompted
4. **Run**: Follow the numbered menu to execute each step of the workflow

## Workflow Steps Explained
0. **Change Parameters**: Update rig number and search URLs
1. **Configure Filters**: Set up automatic document/file selection rules
2. **Fetch Documents**: Retrieve document metadata from RigDoc
3. **Review Documents**: Edit the Markdown file to select which documents to process
4. **Import Selections**: Load your document selections
5. **Fetch Files**: Get file metadata for selected documents
6. **Review Files**: Edit the Markdown file to select which files to download
7. **Import File Selections**: Load your file selections
8. **Download Files**: Retrieve the selected files

## Tips for Success
- Use filters to automatically pre-select relevant documents
- Review the Markdown files carefully before proceeding to the next step
- Files will be organized in subfolders based on '/' in their generated names

## Need Help?
Check the source code comments for detailed information about each function and workflow step.
