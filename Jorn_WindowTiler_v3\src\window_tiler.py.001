#!/usr/bin/env python3
"""
Window Tiler

A utility for dynamically arranging windows on Windows operating systems based on their type
(process name, window class), enabling efficient workspace organization across multiple monitors.

This script consolidates the following functionality into one file:
- Monitor detection and representation
- Window detection, type classification, and manipulation
- Tiling algorithms for grid layouts, cascading, split, etc.
- Command-line interface for listing and tiling windows
"""

import argparse
import sys
import os
import re
import ctypes
import urllib.parse

# External modules for Windows interaction
import win32api
import win32gui
import win32con
import win32process
import win32com.client
import win32com.shell.shellcon as shellcon


# ----------------------------------------------------------------------------------------
# 1. Type Definitions (from core/types.py)
# ----------------------------------------------------------------------------------------
from enum import Enum, auto

class WindowType(Enum):
    """
    Classifies windows based on their type, process, or characteristics.
    Used for filtering windows in tiling operations.
    """
    # Basic window types
    UNSPECIFIED = auto()  # Default type when no specific type can be determined
    NORMAL = auto()       # Standard application window
    SYSTEM = auto()       # System windows (Task Manager, etc.)
    UTILITY = auto()      # Utility windows, toolbars, etc.
    UNKNOWN = auto()      # Unknown window type
    UNLINKED = auto()     # Window without associated process

    # Explorer specific types
    EXPLORER_NORMAL = auto()   # Normal file explorer window
    EXPLORER_SPECIAL = auto()  # Special folder explorer window (This PC, Control Panel, etc.)

    # Browser types
    BROWSER = auto()           # Generic browser window
    BROWSER_CHROME = auto()    # Chrome browser window
    BROWSER_EDGE = auto()      # Edge browser window
    BROWSER_FIREFOX = auto()   # Firefox browser window

    # Application specific types
    TERMINAL = auto()          # Terminal window (cmd, PowerShell, etc.)
    EDITOR = auto()            # Text editor window
    IDE = auto()               # Integrated development environment
    DOCUMENT = auto()          # Document window (Word, PDF viewer, etc.)
    MEDIA = auto()             # Media player window


class WindowState(Enum):
    """
    Represents the current state of a window.
    """
    NORMAL = auto()      # Window is in normal state
    MINIMIZED = auto()   # Window is minimized
    MAXIMIZED = auto()   # Window is maximized
    FULLSCREEN = auto()  # Window is in fullscreen mode
    HIDDEN = auto()      # Window is hidden


# ----------------------------------------------------------------------------------------
# 2. Utility Functions (from utils.py)
# ----------------------------------------------------------------------------------------
def get_shell_windows():
    """
    Get shell window instances and special folder mappings.

    Returns:
        tuple: (shell_window_mapping, special_folders_mapping) where:
            - shell_window_mapping: Dict mapping window handles to shell instances
            - special_folders_mapping: Dict mapping special folder names to paths
    """
    shell = win32com.client.Dispatch('Shell.Application')
    shell_windows = shell.Windows()

    # Map window handles to shell instances
    shell_mapping = {win.HWND: win for win in shell_windows}

    # Gather special folder constants
    csidl_constants = [
        getattr(shellcon, name) for name in dir(shellcon)
        if name.startswith("CSIDL_")
    ]
    namespaces = [shell.Namespace(constant) for constant in csidl_constants]
    valid_namespaces = [ns for ns in namespaces if hasattr(ns, 'Application')]
    special_folders = [[ns.Self.Name, ns.Self.Path] for ns in valid_namespaces]
    special_folders_mapping = {item[0]: item[1] for item in special_folders}

    return shell_mapping, special_folders_mapping


def get_process_info(hwnd):
    """
    Get process information for a window.

    Args:
        hwnd: Window handle

    Returns:
        dict: Process information or None if not available
    """
    try:
        _, process_id = win32process.GetWindowThreadProcessId(hwnd)
        process_access = win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ
        process_handle = ctypes.windll.kernel32.OpenProcess(process_access, False, process_id)

        if not process_handle:
            return None

        try:
            exe_path = win32process.GetModuleFileNameEx(process_handle, 0)
            exe_name = os.path.basename(exe_path)
            return {
                'id': process_id,
                'handle': process_handle,
                'path': exe_path,
                'name': exe_name
            }
        except:
            pass
        finally:
            ctypes.windll.kernel32.CloseHandle(process_handle)

    except Exception:
        pass

    return None


def get_explorer_info(hwnd, shell_mapping=None, special_folders=None):
    """
    Get information about Explorer windows.

    Args:
        hwnd: Window handle
        shell_mapping: Optional shell window mapping
        special_folders: Optional special folders mapping

    Returns:
        dict: Explorer window information or None if not applicable
    """
    if win32gui.GetClassName(hwnd) != 'CabinetWClass':
        return None

    if shell_mapping is None or special_folders is None:
        shell_mapping, special_folders = get_shell_windows()

    title = win32gui.GetWindowText(hwnd)
    result = {
        'path': None,
        'type': None,
        'is_special': False
    }

    shell_win = shell_mapping.get(hwnd)
    if not shell_win:
        return result

    try:
        # Handle special folders
        if title in special_folders:
            result['is_special'] = True
            result['path'] = special_folders[title]
            # Check if path is a GUID
            is_guid = bool(re.search(r'::{[\w-]*}', result['path']))
            result['is_guid'] = is_guid

        # Handle normal folders
        elif hasattr(shell_win, 'LocationURL') and shell_win.LocationURL:
            result['path'] = shell_win.LocationURL.replace('file:///', '')
            result['path'] = os.path.normpath(urllib.parse.unquote(result['path']))
            result['is_special'] = False

            # Folder view info if available
            try:
                doc = shell_win.Document
                result['view_info'] = {
                    'icon_size': doc.IconSize,
                    'view_mode': doc.CurrentViewMode
                }
            except:
                pass
    except Exception:
        pass

    return result


def determine_window_type(hwnd):
    """
    Determine the type of a window based on its properties.

    Args:
        hwnd: Window handle

    Returns:
        WindowType: The determined window type
    """
    if not hwnd or not win32gui.IsWindow(hwnd):
        return WindowType.UNKNOWN
    if not win32gui.IsWindowVisible(hwnd):
        return WindowType.UNKNOWN

    window_class = win32gui.GetClassName(hwnd)
    window_title = win32gui.GetWindowText(hwnd)
    if not window_title:
        return WindowType.UNKNOWN

    process_info = get_process_info(hwnd)

    # Explorer
    if window_class == 'CabinetWClass':
        explorer_info = get_explorer_info(hwnd)
        if explorer_info and explorer_info.get('is_special'):
            return WindowType.EXPLORER_SPECIAL
        return WindowType.EXPLORER_NORMAL

    # If we have process info, check by name
    if process_info and 'name' in process_info:
        process_name = process_info['name'].lower()
        # Browsers
        if 'chrome.exe' in process_name:
            return WindowType.BROWSER_CHROME
        elif 'msedge.exe' in process_name:
            return WindowType.BROWSER_EDGE
        elif 'firefox.exe' in process_name:
            return WindowType.BROWSER_FIREFOX
        elif any(name in process_name for name in ['iexplore.exe', 'safari.exe', 'opera.exe']):
            return WindowType.BROWSER

        # Terminals
        if any(term in process_name for term in ['cmd.exe', 'powershell.exe', 'windowsterminal.exe']):
            return WindowType.TERMINAL

        # Editors
        if any(name in process_name for name in ['notepad.exe', 'code.exe', 'sublime_text.exe']):
            return WindowType.EDITOR

        # IDEs
        if any(ide in process_name for ide in ['devenv.exe', 'pycharm64.exe', 'idea64.exe']):
            return WindowType.IDE

        # If nothing matched, consider it a "normal" window
        return WindowType.NORMAL

    # If there's no process info
    return WindowType.UNLINKED


# ----------------------------------------------------------------------------------------
# 3. Monitor Module (from core/monitor.py)
# ----------------------------------------------------------------------------------------
class Monitor:
    """
    Represents a physical display monitor in the system.

    Attributes:
        handle: The monitor handle
        is_primary: Whether this is the primary monitor
        device: Device name associated with the monitor
        monitor_area: Full monitor area coordinates (x1, y1, x2, y2)
        work_area: Work area coordinates (x1, y1, x2, y2), excluding taskbar
    """

    def __init__(self, monitor_handle, monitor_info):
        self.handle = monitor_handle
        self.is_primary = monitor_info['Flags'] == 1
        self.device = monitor_info['Device']
        self._set_areas(monitor_info['Monitor'], monitor_info['Work'])

    def _set_areas(self, monitor_area, work_area):
        self.monitor_area = monitor_area
        self.work_area = work_area
        self.monitor_area_dict = self._convert_to_dict(monitor_area)
        self.work_area_dict = self._convert_to_dict(work_area)

    def _convert_to_dict(self, area):
        return {
            'x': area[0],
            'y': area[1],
            'width': area[2] - area[0],
            'height': area[3] - area[1]
        }

    def get_dimensions(self):
        return {
            'width': self.monitor_area[2] - self.monitor_area[0],
            'height': self.monitor_area[3] - self.monitor_area[1]
        }

    def get_work_area(self):
        return self._convert_to_dict(self.work_area)

    def contains_point(self, x, y):
        return (self.monitor_area[0] <= x <= self.monitor_area[2] and
                self.monitor_area[1] <= y <= self.monitor_area[3])

    def point_to_relative(self, x, y):
        dimensions = self.get_dimensions()
        relative_x = (x - self.monitor_area[0]) / dimensions['width']
        relative_y = (y - self.monitor_area[1]) / dimensions['height']
        return (relative_x, relative_y)

    def relative_to_point(self, rel_x, rel_y):
        dimensions = self.get_dimensions()
        abs_x = self.monitor_area[0] + int(rel_x * dimensions['width'])
        abs_y = self.monitor_area[1] + int(rel_y * dimensions['height'])
        return (abs_x, abs_y)


def get_all_monitors():
    """
    Get all available monitors in the system.

    Returns:
        dict: Dictionary mapping monitor handles to Monitor objects
    """
    monitors = {}
    for mon in win32api.EnumDisplayMonitors(None, None):
        monitor_handle = mon[0]
        monitor_info = win32api.GetMonitorInfo(monitor_handle)
        monitor_obj = Monitor(monitor_handle, monitor_info)
        monitors[monitor_handle] = monitor_obj
    return monitors


def get_primary_monitor():
    """
    Get the primary monitor.

    Returns:
        Monitor: The primary monitor or None if not found
    """
    monitors = get_all_monitors()
    for monitor in monitors.values():
        if monitor.is_primary:
            return monitor
    if monitors:
        return next(iter(monitors.values()))
    return None


# ----------------------------------------------------------------------------------------
# 4. Window Module (from core/window.py)
# ----------------------------------------------------------------------------------------
class Window:
    """
    Represents a window in the system with enhanced properties and manipulation methods.

    Attributes:
        hwnd: Window handle
        title: Window title
        class_name: Window class name
        dimensions: Window dimensions (x, y, width, height)
        window_type: Type of window (WindowType enum)
        process_info: Dictionary with process information
        explorer_info: Additional info for Explorer windows
    """

    def __init__(self, hwnd, detect_type=True):
        self.hwnd = hwnd
        self.title = win32gui.GetWindowText(hwnd)
        self.class_name = win32gui.GetClassName(hwnd)

        self.update_dimensions()

        self.window_type = None
        self.process_info = None
        self.explorer_info = None

        if detect_type:
            self.detect_type()

    def update_dimensions(self):
        rect = win32gui.GetWindowRect(self.hwnd)
        self.dimensions = {
            'x': rect[0],
            'y': rect[1],
            'width': rect[2] - rect[0],
            'height': rect[3] - rect[1]
        }

    def detect_type(self):
        self.process_info = get_process_info(self.hwnd)
        if self.class_name == 'CabinetWClass':
            self.explorer_info = get_explorer_info(self.hwnd)
        self.window_type = determine_window_type(self.hwnd)

    def get_state(self):
        if not win32gui.IsWindowVisible(self.hwnd):
            return WindowState.HIDDEN
        if win32gui.IsIconic(self.hwnd):
            return WindowState.MINIMIZED
        if win32gui.IsZoomed(self.hwnd):
            return WindowState.MAXIMIZED
        if self.is_full_screen():
            return WindowState.FULLSCREEN
        return WindowState.NORMAL

    def tile(self, monitor, position, size):
        """
        Position and size the window on a specific monitor.

        Args:
            monitor: Monitor object to position the window on
            position: (x, y) with relative position (0-1)
            size: (width, height) with relative size (0-1)
        """
        dimensions = monitor.get_dimensions()
        new_x = monitor.monitor_area[0] + int(position[0] * dimensions['width'])
        new_y = monitor.monitor_area[1] + int(position[1] * dimensions['height'])
        new_width = int(size[0] * dimensions['width'])
        new_height = int(size[1] * dimensions['height'])

        win32gui.MoveWindow(self.hwnd, new_x, new_y, new_width, new_height, True)
        self.update_dimensions()

    def maximize_within_monitor(self, monitor):
        self.tile(monitor, (0, 0), (1, 1))

    def center_within_monitor(self, monitor):
        dimensions = monitor.get_dimensions()
        x_position = (dimensions['width'] - self.dimensions['width']) / 2
        y_position = (dimensions['height'] - self.dimensions['height']) / 2
        x_ratio = x_position / dimensions['width']
        y_ratio = y_position / dimensions['height']
        width_ratio = self.dimensions['width'] / dimensions['width']
        height_ratio = self.dimensions['height'] / dimensions['height']
        self.tile(monitor, (x_ratio, y_ratio), (width_ratio, height_ratio))

    def toggle_visibility(self):
        if win32gui.IsWindowVisible(self.hwnd):
            win32gui.ShowWindow(self.hwnd, win32con.SW_HIDE)
        else:
            win32gui.ShowWindow(self.hwnd, win32con.SW_SHOW)

    def bring_to_front(self):
        win32gui.SetWindowPos(
            self.hwnd,
            win32con.HWND_TOP,
            self.dimensions['x'],
            self.dimensions['y'],
            self.dimensions['width'],
            self.dimensions['height'],
            win32con.SWP_NOSIZE | win32con.SWP_NOMOVE
        )

    def focus(self):
        if win32gui.IsIconic(self.hwnd):
            win32gui.ShowWindow(self.hwnd, win32con.SW_RESTORE)
        win32gui.SetForegroundWindow(self.hwnd)

    def is_focused(self):
        return win32gui.GetForegroundWindow() == self.hwnd

    def is_visible(self):
        return win32gui.IsWindowVisible(self.hwnd)

    def is_enabled(self):
        return win32gui.IsWindowEnabled(self.hwnd)

    def is_minimized(self):
        return win32gui.IsIconic(self.hwnd)

    def is_maximized(self):
        return win32gui.IsZoomed(self.hwnd)

    def is_full_screen(self):
        screen_width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
        screen_height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)
        rect = win32gui.GetWindowRect(self.hwnd)
        return (
            rect[0] == 0 and
            rect[1] == 0 and
            rect[2] == screen_width and
            rect[3] == screen_height
        )

    def resize_and_move(self, x, y, width, height):
        win32gui.MoveWindow(self.hwnd, x, y, width, height, True)
        self.update_dimensions()

    def __str__(self):
        return f"Window(hwnd={self.hwnd}, title='{self.title}', type={self.window_type})"

    def __repr__(self):
        return self.__str__()


def get_all_windows(visible_only=False, with_titles_only=True, detect_types=False):
    """
    Get all windows in the system.

    Args:
        visible_only: Whether to include only visible windows
        with_titles_only: Whether to include only windows with titles
        detect_types: Whether to detect window types

    Returns:
        dict: Dictionary mapping window handles to Window objects
    """
    windows = {}

    system_classes = [
        'Default IME', 'MSCTFIME UI', 'CiceroUIWndFrame',
        'GDI+ Window', 'MediaContextNotificationWindow',
        'SystemResourceNotifyWindow', '#32770',
        'DesktopWindowXamlSource', 'DDE Server Window',
        'Windows.UI.Core.CoreWindow'
    ]

    def enum_windows_callback(hwnd, _):
        if visible_only and not win32gui.IsWindowVisible(hwnd):
            return True
        if with_titles_only and not win32gui.GetWindowText(hwnd):
            return True

        class_name = win32gui.GetClassName(hwnd)
        if any(c in class_name for c in system_classes):
            return True

        title = win32gui.GetWindowText(hwnd)
        if title in ["Task Switching", "Program Manager", "Windows Input Experience"] or title.startswith('Default IME'):
            return True

        windows[hwnd] = Window(hwnd, detect_type=detect_types)
        return True

    win32gui.EnumWindows(enum_windows_callback, None)
    return windows


def get_windows_by_type(window_type=None, process_name=None, window_class=None, visible_only=False):
    """
    Get windows filtered by type, process name, or class.

    Args:
        window_type: WindowType to filter by
        process_name: Process name to filter by
        window_class: Window class to filter by
        visible_only: Whether to include only visible windows

    Returns:
        list: List of Window objects matching the criteria
    """
    all_wins = get_all_windows(visible_only=visible_only, detect_types=True).values()
    matching = []

    for win in all_wins:
        if window_type and win.window_type != window_type:
            continue
        if process_name and win.process_info:
            if process_name.lower() not in win.process_info.get('name', '').lower():
                continue
        if window_class and win.class_name != window_class:
            continue
        matching.append(win)

    return matching


# ----------------------------------------------------------------------------------------
# 5. Tiler Module (from core/tiler.py)
# ----------------------------------------------------------------------------------------
class Tiler:
    """
    Handles the arrangement of windows in various layouts.

    Attributes:
        monitors: Dictionary of monitors to use for tiling
    """

    def __init__(self, monitors):
        self.monitors = monitors

    def tile_grid(self, monitor, windows, rows=2, columns=2, column_ratios=None, row_ratios=None):
        """
        Arrange windows in a grid layout on a specific monitor.

        Args:
            monitor: Monitor to tile windows on
            windows: List of Window objects to arrange
            rows: Number of rows in the grid
            columns: Number of columns in the grid
            column_ratios: Optional list of column width ratios (must sum to 1.0)
            row_ratios: Optional list of row height ratios (must sum to 1.0)
        """
        if rows <= 0 or columns <= 0:
            raise ValueError("Rows and columns must be greater than 0")

        if column_ratios and len(column_ratios) != columns:
            raise ValueError("Length of column_ratios must match 'columns'")
        if row_ratios and len(row_ratios) != rows:
            raise ValueError("Length of row_ratios must match 'rows'")
        if column_ratios and abs(sum(column_ratios) - 1.0) > 1e-5:
            raise ValueError("Column ratios must sum to 1.0")
        if row_ratios and abs(sum(row_ratios) - 1.0) > 1e-5:
            raise ValueError("Row ratios must sum to 1.0")

        default_column_width = 1.0 / columns
        default_row_height = 1.0 / rows

        for i, window in enumerate(windows):
            if i >= rows * columns:
                break

            col = i % columns
            row = i // columns

            x_ratio = sum(column_ratios[:col]) if column_ratios else col * default_column_width
            y_ratio = sum(row_ratios[:row]) if row_ratios else row * default_row_height

            width_ratio = column_ratios[col] if column_ratios else default_column_width
            height_ratio = row_ratios[row] if row_ratios else default_row_height

            window.tile(monitor, (x_ratio, y_ratio), (width_ratio, height_ratio))

    def tile_by_type(self, monitor, window_type=None, process_name=None, window_class=None,
                     rows=2, columns=2, column_ratios=None, row_ratios=None):
        """
        Filter windows by criteria and arrange them in a grid layout.
        """
        windows = get_windows_by_type(window_type, process_name, window_class)
        self.tile_grid(monitor, windows, rows, columns, column_ratios, row_ratios)

    def maximize_all(self, monitor, windows):
        for window in windows:
            window.maximize_within_monitor(monitor)

    def cascade(self, monitor, windows, offset_x=30, offset_y=30):
        monitor_dims = monitor.get_dimensions()
        base_width = int(monitor_dims['width'] * 0.8)
        base_height = int(monitor_dims['height'] * 0.8)

        start_x = monitor.monitor_area[0]
        start_y = monitor.monitor_area[1]

        for i, window in enumerate(windows):
            x = start_x + (i * offset_x)
            y = start_y + (i * offset_y)

            # Wrap around if exceeding monitor boundaries
            if x + base_width > monitor.monitor_area[2]:
                x = start_x
            if y + base_height > monitor.monitor_area[3]:
                y = start_y

            window.resize_and_move(x, y, base_width, base_height)

    def split(self, monitor, primary_window, secondary_windows, primary_ratio=0.6, orientation='vertical'):
        if orientation not in ['vertical', 'horizontal']:
            raise ValueError("Orientation must be 'vertical' or 'horizontal'")

        secondary_count = len(secondary_windows)
        if secondary_count == 0:
            primary_window.maximize_within_monitor(monitor)
            return

        if orientation == 'vertical':
            # Primary on the left
            primary_window.tile(monitor, (0, 0), (primary_ratio, 1.0))

            secondary_width = 1.0 - primary_ratio
            secondary_height = 1.0 / secondary_count
            for i, window in enumerate(secondary_windows):
                y_position = i * secondary_height
                window.tile(monitor, (primary_ratio, y_position),
                            (secondary_width, secondary_height))
        else:
            # Primary on top
            primary_window.tile(monitor, (0, 0), (1.0, primary_ratio))

            secondary_width = 1.0 / secondary_count
            secondary_height = 1.0 - primary_ratio
            for i, window in enumerate(secondary_windows):
                x_position = i * secondary_width
                window.tile(monitor, (x_position, primary_ratio),
                            (secondary_width, secondary_height))


def tile_windows(monitor, windows, rows=2, columns=2, column_ratios=None, row_ratios=None):
    """
    Simple helper to tile windows in a grid layout.
    """
    tiler = Tiler({monitor.handle: monitor})
    tiler.tile_grid(monitor, windows, rows, columns, column_ratios, row_ratios)


# ----------------------------------------------------------------------------------------
# 6. Main / CLI (from main.py)
# ----------------------------------------------------------------------------------------
def tile_by_type(window_type=None, process_name=None, window_class=None,
                 rows=2, columns=2, monitor_index=0, primary_only=True, visible_only=False):
    """
    Tile windows matching specified criteria.
    """
    monitors = get_all_monitors()
    target_monitor = None

    if primary_only:
        for m in monitors.values():
            if m.is_primary:
                target_monitor = m
                break
    else:
        if monitor_index < len(monitors):
            target_monitor = list(monitors.values())[monitor_index]

    if not target_monitor and monitors:
        target_monitor = list(monitors.values())[0]

    if not target_monitor:
        print("No monitors found")
        return

    windows = get_windows_by_type(window_type, process_name, window_class, visible_only=visible_only)
    if not windows:
        if process_name:
            print(f"No windows found matching process: {process_name}")
        elif window_class:
            print(f"No windows found matching class: {window_class}")
        else:
            print("No windows found matching criteria")
        return

    tiler = Tiler(monitors)
    tiler.tile_grid(target_monitor, windows, rows, columns)
    print(f"Tiled {len(windows)} windows in a {rows}x{columns} grid")


def list_monitors():
    monitors = get_all_monitors()
    print(f"Found {len(monitors)} monitors:")
    print("-" * 80)
    for i, m in enumerate(monitors.values()):
        dims = m.get_dimensions()
        primary_text = " (Primary)" if m.is_primary else ""
        print(f"{i+1}. Monitor{primary_text} - {dims['width']}x{dims['height']} - {m.device}")
    print("-" * 80)


def list_windows(show_types=False, show_classes=False):
    windows = get_all_windows(detect_types=show_types).values()
    print(f"Found {len(windows)} windows:")
    print("-" * 80)
    for i, w in enumerate(windows):
        info = f"{i+1}. \"{w.title}\" (hwnd={w.hwnd})"
        if show_types and w.window_type:
            info += f" [Type: {w.window_type.name}]"
        if show_classes:
            info += f" [Class: {w.class_name}]"
        print(info)
    print("-" * 80)


def tile_browsers(rows=2, columns=2):
    print("Tiling all browser windows...")
    all_browsers = []
    all_browsers.extend(get_windows_by_type(WindowType.BROWSER, visible_only=False))
    all_browsers.extend(get_windows_by_type(WindowType.BROWSER_CHROME, visible_only=False))
    all_browsers.extend(get_windows_by_type(WindowType.BROWSER_EDGE, visible_only=False))
    all_browsers.extend(get_windows_by_type(WindowType.BROWSER_FIREFOX, visible_only=False))

    if not all_browsers:
        print("No browser windows found")
        return

    monitors = get_all_monitors()
    target_monitor = None
    for m in monitors.values():
        if m.is_primary:
            target_monitor = m
            break
    if not target_monitor and monitors:
        target_monitor = list(monitors.values())[0]
    if not target_monitor:
        print("No monitors found")
        return

    tiler = Tiler(monitors)
    tiler.tile_grid(target_monitor, all_browsers, rows, columns)
    print(f"Tiled {len(all_browsers)} browser windows in a {rows}x{columns} grid")


def tile_explorer(rows=2, columns=2):
    print("Tiling all Explorer windows...")
    all_explorers = []
    all_explorers.extend(get_windows_by_type(WindowType.EXPLORER_NORMAL, visible_only=False))
    all_explorers.extend(get_windows_by_type(WindowType.EXPLORER_SPECIAL, visible_only=False))

    if not all_explorers:
        print("No Explorer windows found")
        return

    monitors = get_all_monitors()
    target_monitor = None
    for m in monitors.values():
        if m.is_primary:
            target_monitor = m
            break
    if not target_monitor and monitors:
        target_monitor = list(monitors.values())[0]
    if not target_monitor:
        print("No monitors found")
        return

    tiler = Tiler(monitors)
    tiler.tile_grid(target_monitor, all_explorers, rows, columns)
    print(f"Tiled {len(all_explorers)} Explorer windows in a {rows}x{columns} grid")


def tile_terminals(rows=2, columns=2):
    print("Tiling all terminal windows...")
    all_terminals = get_windows_by_type(WindowType.TERMINAL, visible_only=False)

    if not all_terminals:
        print("No terminal windows found")
        return

    monitors = get_all_monitors()
    target_monitor = None
    for m in monitors.values():
        if m.is_primary:
            target_monitor = m
            break
    if not target_monitor and monitors:
        target_monitor = list(monitors.values())[0]
    if not target_monitor:
        print("No monitors found")
        return

    tiler = Tiler(monitors)
    tiler.tile_grid(target_monitor, all_terminals, rows, columns)
    print(f"Tiled {len(all_terminals)} terminal windows in a {rows}x{columns} grid")


def parse_args():
    parser = argparse.ArgumentParser(description='Window Tiler - Arrange windows by type')
    parser.add_argument('--rows', type=int, default=2, help='Number of rows in the grid')
    parser.add_argument('--columns', type=int, default=2, help='Number of columns in the grid')
    parser.add_argument('--monitor', type=int, default=0, help='Monitor index to use (0 for primary)')

    subparsers = parser.add_subparsers(dest='command', help='Command to execute')

    # list_windows command
    list_cmd = subparsers.add_parser('list', help='List all visible windows')
    list_cmd.add_argument('--types', action='store_true', help='Show window types')
    list_cmd.add_argument('--classes', action='store_true', help='Show window classes')
    list_cmd.add_argument('--all', action='store_true', help='Include all windows, not just visible ones')

    # list_monitors command
    subparsers.add_parser('monitors', help='List all monitors')

    # tile_process command
    proc_cmd = subparsers.add_parser('process', help='Tile windows by process name')
    proc_cmd.add_argument('name', help='Process name to filter by')

    # tile_class command
    class_cmd = subparsers.add_parser('class', help='Tile windows by class name')
    class_cmd.add_argument('name', help='Window class name to filter by')

    # tile_browsers command
    subparsers.add_parser('browsers', help='Tile all browser windows')

    # tile_explorer command
    subparsers.add_parser('explorer', help='Tile all Explorer windows')

    # tile_terminals command
    subparsers.add_parser('terminals', help='Tile all terminal windows')

    return parser.parse_args()


def main():
    args = parse_args()

    if args.command == 'list':
        # Use visible_only=False if --all is provided
        visible_only = not getattr(args, 'all', False)
        windows = get_all_windows(visible_only=visible_only, detect_types=args.types).values()

        print(f"Found {len(windows)} windows:")
        print("-" * 80)
        for i, w in enumerate(windows):
            info = f"{i+1}. \"{w.title}\" (hwnd={w.hwnd})"
            if args.types and w.window_type:
                info += f" [Type: {w.window_type.name}]"
            if args.classes:
                info += f" [Class: {w.class_name}]"
            print(info)
        print("-" * 80)

    elif args.command == 'monitors':
        list_monitors()

    elif args.command == 'process':
        tile_by_type(process_name=args.name, rows=args.rows, columns=args.columns,
                     monitor_index=args.monitor, visible_only=False)

    elif args.command == 'class':
        tile_by_type(window_class=args.name, rows=args.rows, columns=args.columns,
                     monitor_index=args.monitor, visible_only=False)

    elif args.command == 'browsers':
        tile_browsers(args.rows, args.columns)

    elif args.command == 'explorer':
        tile_explorer(args.rows, args.columns)

    elif args.command == 'terminals':
        tile_terminals(args.rows, args.columns)

    else:
        # Default: show a basic list with types
        list_windows(show_types=True)
        print("\nUse --help for more information on available commands")


if __name__ == '__main__':
    main()
