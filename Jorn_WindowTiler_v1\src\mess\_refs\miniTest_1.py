# import pywinauto

# # app = pywinauto.Application().connect(path=r"c:/windows/system32/notepad.exe")
# # app = pywinauto.Application().connect(title_re='.*rosjekt_VirtualEnvironment_Python*')
# # app = pywinauto.Application().connect(process=2341)
# app = pywinauto.Application().connect(title_re=".*Notepad", class_name="Notepad")

# # print(dir(app))
# # print(app.Window_)
# print(dir(app))
# window = app.My_Window
# element = window.element_info
# print(app.element_info)
# # com_object = element.QueryInterface(pywinauto.uia_defines.UIA_ELEMENT_IID)
# com_object = window.QueryInterface(pywinauto.uia_defines.IUIAutomationElement)
from pywinauto import Application

# Start the application
app = Application().start("notepad.exe")

# Get the main window
window = app.window(title="Untitled - Notepad")

# Get the size of the window
size = window.rectangle()
window.rectangle = size

print(size)
