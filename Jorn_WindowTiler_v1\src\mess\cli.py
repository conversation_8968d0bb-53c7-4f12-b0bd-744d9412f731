# cli.py

import argparse

def parse_args():
    parser = argparse.ArgumentParser(description="Py_BookmarkFolderizer: Organize your bookmarks into a folder structure and vice versa.")
    parser.add_argument(
        "input",
        help="The input file path for bookmarks.html or bookmarks.json."
    )
    parser.add_argument(
        "output",
        help="The output directory path where the filesystem will be created."
    )
    parser.add_argument(
        "--type",
        choices=['html', 'json'],
        default='html',
        help="The type of the input file."
    )
    # Add any additional arguments here

    return parser.parse_args()
