{"initial_prompt": "\n[Topic]\n\nInstructions designed to act as a general 'rules_for_ai.md', example (reference only):\n\n    ├── 01_intent-overview.md      # State core project intent and high-level goals to immediately orient all contributors.\n    ├── 02_context-background.md   # Summarizes essential context, including problem domain, stakeholders, and any constraints shaping the project.\n    ├── 03_existing-patterns.md    # Identifies relevant patterns, paradigms, or solutions that influence architectural or coding decisions.\n    ├── 04_tech-stack.md           # Details technologies, frameworks, and tools chosen for the project and their rationale.\n    ├── 05_current-activity.md     # Describes present focus areas, major open workstreams, and in-progress features.\n    ├── 06_progress-tracking.md    # Outlines tracking methods and status of progress, including achievements and roadblocks.\n    ├── 07_priority-tasks.md       # Lists actionable, high-priority tasks, each linked to project goals and categorized for clear ownership.\n    └── 08_distilled-objective.md  # Condense all prior insight into a single, actionable, and measurable project objective\n\n[Context]\n\nSequential, purpose-driven chain from intent to objective.\n\n[Goal]\n\nPropose an **enhanced** sequence that includes a point for retrieving the ultimate intent", "sequence_id": "sequence-0001-a+0001-b+0004-a", "results": [{"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "\n[Topic]\n\nInstructions designed to act as a general 'rules_for_ai.md', example (reference only):\n\n    ├── 01_intent-overview.md      # State core project intent and high-level goals to immediately orient all contributors.\n    ├── 02_context-background.md   # Summarizes essential context, including problem domain, stakeholders, and any constraints shaping the project.\n    ├── 03_existing-patterns.md    # Identifies relevant patterns, paradigms, or solutions that influence architectural or coding decisions.\n    ├── 04_tech-stack.md           # Details technologies, frameworks, and tools chosen for the project and their rationale.\n    ├── 05_current-activity.md     # Describes present focus areas, major open workstreams, and in-progress features.\n    ├── 06_progress-tracking.md    # Outlines tracking methods and status of progress, including achievements and roadblocks.\n    ├── 07_priority-tasks.md       # Lists actionable, high-priority tasks, each linked to project goals and categorized for clear ownership.\n    └── 08_distilled-objective.md  # Condense all prior insight into a single, actionable, and measurable project objective\n\n[Context]\n\nSequential, purpose-driven chain from intent to objective.\n\n[Goal]\n\nPropose an **enhanced** sequence that includes a point for retrieving the ultimate intent", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": [\n    \"Define the core project intent and high-level goals to orient all contributors immediately.\",\n    \"Summarize essential context, highlighting the problem domain, stakeholders, and constraints influencing the project.\",\n    \"Identify relevant patterns, paradigms, or solutions impacting architectural and coding decisions.\",\n    \"Detail chosen technologies, frameworks, and tools, including the rationale for their selection.\",\n    \"Describe current focus areas, major workstreams, and in-progress features.\",\n    \"Outline methods for progress tracking, including status updates, achievements, and obstacles.\",\n    \"List actionable, high-priority tasks, linking each to project goals and categorizing for ownership.\",\n    \"Introduce a dedicated retrieval step to access and clarify the ultimate intent of the project for reference and guidance.\",\n    \"Synthesize all preceding information into a single, actionable, and measurable project objective.\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"\\n[Topic]\\n\\nInstructions designed to act as a general 'rules_for_ai.md', example (reference only):\\n\\n    \\u251c\\u2500\\u2500 01_intent-overview.md      # State core project intent and high-level goals to immediately orient all contributors.\\n    \\u251c\\u2500\\u2500 02_context-background.md   # Summarizes essential context, including problem domain, stakeholders, and any constraints shaping the project.\\n    \\u251c\\u2500\\u2500 03_existing-patterns.md    # Identifies relevant patterns, paradigms, or solutions that influence architectural or coding decisions.\\n    \\u251c\\u2500\\u2500 04_tech-stack.md           # Details technologies, frameworks, and tools chosen for the project and their rationale.\\n    \\u251c\\u2500\\u2500 05_current-activity.md     # Describes present focus areas, major open workstreams, and in-progress features.\\n    \\u251c\\u2500\\u2500 06_progress-tracking.md    # Outlines tracking methods and status of progress, including achievements and roadblocks.\\n    \\u251c\\u2500\\u2500 07_priority-tasks.md       # Lists actionable, high-priority tasks, each linked to project goals and categorized for clear ownership.\\n    \\u2514\\u2500\\u2500 08_distilled-objective.md  # Condense all prior insight into a single, actionable, and measurable project objective\\n\\n[Context]\\n\\nSequential, purpose-driven chain from intent to objective.\\n\\n[Goal]\\n\\nPropose an **enhanced** sequence that includes a point for retrieving the ultimate intent\",\n  \"instruction_format\": [\n    \"Define the core project intent and high-level goals to orient all contributors immediately.\",\n    \"Summarize essential context, highlighting the problem domain, stakeholders, and constraints influencing the project.\",\n    \"Identify relevant patterns, paradigms, or solutions impacting architectural and coding decisions.\",\n    \"Detail chosen technologies, frameworks, and tools, including the rationale for their selection.\",\n    \"Describe current focus areas, major workstreams, and in-progress features.\",\n    \"Outline methods for progress tracking, including status updates, achievements, and obstacles.\",\n    \"List actionable, high-priority tasks, linking each to project goals and categorizing for ownership.\",\n    \"Introduce a dedicated retrieval step to access and clarify the ultimate intent of the project for reference and guidance.\",\n    \"Synthesize all preceding information into a single, actionable, and measurable project objective.\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": [\n    \"State project intent and high-level goals.\",\n    \"Summarize essential context (problem, stakeholders, constraints).\",\n    \"Identify influential patterns or solutions.\",\n    \"Specify technology choices and rationale.\",\n    \"Describe current focus and active workstreams.\",\n    \"Outline progress tracking methods and status.\",\n    \"List actionable, high-priority tasks with clear ownership.\",\n    \"Provide a retrieval point to clarify ultimate project intent.\",\n    \"Synthesize into a single, actionable objective.\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Rephraser\n\nYour goal is not to answer the prompt but to rephrase it minimally, preserving its core meaning and removing superfluous detail. Execute as\n\n`{role=rephraser; input=[original:str]; process=[strip_excess(), condense_structure(), preserve_intent()]; output={rephrased_input:str}}`", "step": "a", "title": "Rephraser", "input": "{\n  \"initial_prompt\": \"\\n[Topic]\\n\\nInstructions designed to act as a general 'rules_for_ai.md', example (reference only):\\n\\n    \\u251c\\u2500\\u2500 01_intent-overview.md      # State core project intent and high-level goals to immediately orient all contributors.\\n    \\u251c\\u2500\\u2500 02_context-background.md   # Summarizes essential context, including problem domain, stakeholders, and any constraints shaping the project.\\n    \\u251c\\u2500\\u2500 03_existing-patterns.md    # Identifies relevant patterns, paradigms, or solutions that influence architectural or coding decisions.\\n    \\u251c\\u2500\\u2500 04_tech-stack.md           # Details technologies, frameworks, and tools chosen for the project and their rationale.\\n    \\u251c\\u2500\\u2500 05_current-activity.md     # Describes present focus areas, major open workstreams, and in-progress features.\\n    \\u251c\\u2500\\u2500 06_progress-tracking.md    # Outlines tracking methods and status of progress, including achievements and roadblocks.\\n    \\u251c\\u2500\\u2500 07_priority-tasks.md       # Lists actionable, high-priority tasks, each linked to project goals and categorized for clear ownership.\\n    \\u2514\\u2500\\u2500 08_distilled-objective.md  # Condense all prior insight into a single, actionable, and measurable project objective\\n\\n[Context]\\n\\nSequential, purpose-driven chain from intent to objective.\\n\\n[Goal]\\n\\nPropose an **enhanced** sequence that includes a point for retrieving the ultimate intent\",\n  \"distilled_essence\": [\n    \"State project intent and high-level goals.\",\n    \"Summarize essential context (problem, stakeholders, constraints).\",\n    \"Identify influential patterns or solutions.\",\n    \"Specify technology choices and rationale.\",\n    \"Describe current focus and active workstreams.\",\n    \"Outline progress tracking methods and status.\",\n    \"List actionable, high-priority tasks with clear ownership.\",\n    \"Provide a retrieval point to clarify ultimate project intent.\",\n    \"Synthesize into a single, actionable objective.\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"rephraser\": {\n    \"input\": \"[Topic]\\n\\nInstructions designed to act as a general 'rules_for_ai.md', example (reference only):\\n\\n    ├── 01_intent-overview.md      # State core project intent and high-level goals to immediately orient all contributors.\\n    ├── 02_context-background.md   # Summarizes essential context, including problem domain, stakeholders, and any constraints shaping the project.\\n    ├── 03_existing-patterns.md    # Identifies relevant patterns, paradigms, or solutions that influence architectural or coding decisions.\\n    ├── 04_tech-stack.md           # Details technologies, frameworks, and tools chosen for the project and their rationale.\\n    ├── 05_current-activity.md     # Describes present focus areas, major open workstreams, and in-progress features.\\n    ├── 06_progress-tracking.md    # Outlines tracking methods and status of progress, including achievements and roadblocks.\\n    ├── 07_priority-tasks.md       # Lists actionable, high-priority tasks, each linked to project goals and categorized for clear ownership.\\n    └── 08_distilled-objective.md  # Condense all prior insight into a single, actionable, and measurable project objective\\n\\n[Context]\\n\\nSequential, purpose-driven chain from intent to objective.\\n\\n[Goal]\\n\\nPropose an **enhanced** sequence that includes a point for retrieving the ultimate intent\",\n    \"process\": [\n      \"strip_excess()\",\n      \"condense_structure()\",\n      \"preserve_intent()\"\n    ],\n    \"output\": {\n      \"rephrased_input\": \"Given the sequential project documentation structure (intent, context, patterns, tech, current activity, tracking, tasks, distilled objective), propose an improved sequence that adds a step for explicitly retrieving the ultimate project intent.\"\n    }\n  }\n}", "cost": 0.0}}}, {"instruction": "# Result Combiner\n\nYou are a specialized aggregator that combines results from multiple instruction steps into a cohesive output. Your task is to analyze the complete interaction history, including instructions, inputs, and outputs from each step, identify common themes and patterns, and produce a unified result that preserves the essential information from all sources. Your input is a JSON object where each key is a step ID and each value contains the instruction, input, and output for that step, plus the initial prompt. Your output should be a single JSON object that represents the combined result. Execute as:\n\n`{role=result_combiner;input=[interaction_history:json];process=[analyze_complete_interaction_history(),identify_common_patterns(),extract_key_elements(),merge_into_cohesive_whole(),format_as_unified_output()];constraints=[preserve_essential_information(),maintain_logical_structure(),avoid_redundancy()];requirements=[output_as_single_json(),ensure_completeness(),prioritize_clarity()];output={combined_result:json}}`", "step": "a", "title": "Result Combiner", "input": "{\n  \"initial_prompt\": \"\\n[Topic]\\n\\nInstructions designed to act as a general 'rules_for_ai.md', example (reference only):\\n\\n    \\u251c\\u2500\\u2500 01_intent-overview.md      # State core project intent and high-level goals to immediately orient all contributors.\\n    \\u251c\\u2500\\u2500 02_context-background.md   # Summarizes essential context, including problem domain, stakeholders, and any constraints shaping the project.\\n    \\u251c\\u2500\\u2500 03_existing-patterns.md    # Identifies relevant patterns, paradigms, or solutions that influence architectural or coding decisions.\\n    \\u251c\\u2500\\u2500 04_tech-stack.md           # Details technologies, frameworks, and tools chosen for the project and their rationale.\\n    \\u251c\\u2500\\u2500 05_current-activity.md     # Describes present focus areas, major open workstreams, and in-progress features.\\n    \\u251c\\u2500\\u2500 06_progress-tracking.md    # Outlines tracking methods and status of progress, including achievements and roadblocks.\\n    \\u251c\\u2500\\u2500 07_priority-tasks.md       # Lists actionable, high-priority tasks, each linked to project goals and categorized for clear ownership.\\n    \\u2514\\u2500\\u2500 08_distilled-objective.md  # Condense all prior insight into a single, actionable, and measurable project objective\\n\\n[Context]\\n\\nSequential, purpose-driven chain from intent to objective.\\n\\n[Goal]\\n\\nPropose an **enhanced** sequence that includes a point for retrieving the ultimate intent\",\n  \"a\": {\n    \"instruction\": \"# Rephraser\\n\\nYour goal is not to answer the prompt but to rephrase it minimally, preserving its core meaning and removing superfluous detail. Execute as\\n\\n`{role=rephraser; input=[original:str]; process=[strip_excess(), condense_structure(), preserve_intent()]; output={rephrased_input:str}}`\",\n    \"input\": \"{\\n  \\\"distilled_essence\\\": [\\n    \\\"State project intent and high-level goals.\\\",\\n    \\\"Summarize essential context (problem, stakeholders, constraints).\\\",\\n    \\\"Identify influential patterns or solutions.\\\",\\n    \\\"Specify technology choices and rationale.\\\",\\n    \\\"Describe current focus and active workstreams.\\\",\\n    \\\"Outline progress tracking methods and status.\\\",\\n    \\\"List actionable, high-priority tasks with clear ownership.\\\",\\n    \\\"Provide a retrieval point to clarify ultimate project intent.\\\",\\n    \\\"Synthesize into a single, actionable objective.\\\"\\n  ]\\n}\",\n    \"output\": \"{\\n  \\\"rephraser\\\": {\\n    \\\"input\\\": \\\"[Topic]\\\\n\\\\nInstructions designed to act as a general 'rules_for_ai.md', example (reference only):\\\\n\\\\n    \\u251c\\u2500\\u2500 01_intent-overview.md      # State core project intent and high-level goals to immediately orient all contributors.\\\\n    \\u251c\\u2500\\u2500 02_context-background.md   # Summarizes essential context, including problem domain, stakeholders, and any constraints shaping the project.\\\\n    \\u251c\\u2500\\u2500 03_existing-patterns.md    # Identifies relevant patterns, paradigms, or solutions that influence architectural or coding decisions.\\\\n    \\u251c\\u2500\\u2500 04_tech-stack.md           # Details technologies, frameworks, and tools chosen for the project and their rationale.\\\\n    \\u251c\\u2500\\u2500 05_current-activity.md     # Describes present focus areas, major open workstreams, and in-progress features.\\\\n    \\u251c\\u2500\\u2500 06_progress-tracking.md    # Outlines tracking methods and status of progress, including achievements and roadblocks.\\\\n    \\u251c\\u2500\\u2500 07_priority-tasks.md       # Lists actionable, high-priority tasks, each linked to project goals and categorized for clear ownership.\\\\n    \\u2514\\u2500\\u2500 08_distilled-objective.md  # Condense all prior insight into a single, actionable, and measurable project objective\\\\n\\\\n[Context]\\\\n\\\\nSequential, purpose-driven chain from intent to objective.\\\\n\\\\n[Goal]\\\\n\\\\nPropose an **enhanced** sequence that includes a point for retrieving the ultimate intent\\\",\\n    \\\"process\\\": [\\n      \\\"strip_excess()\\\",\\n      \\\"condense_structure()\\\",\\n      \\\"preserve_intent()\\\"\\n    ],\\n    \\\"output\\\": {\\n      \\\"rephrased_input\\\": \\\"Given the sequential project documentation structure (intent, context, patterns, tech, current activity, tracking, tasks, distilled objective), propose an improved sequence that adds a step for explicitly retrieving the ultimate project intent.\\\"\\n    }\\n  }\\n}\"\n  },\n  \"b\": {\n    \"instruction\": \"# Essence Distillation\\n\\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\\n\\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\",\n    \"input\": \"{\\n  \\\"rephraser\\\": {\\n    \\\"input\\\": \\\"[Topic]\\\\n\\\\nInstructions designed to act as a general 'rules_for_ai.md', example (reference only):\\\\n\\\\n    \\u251c\\u2500\\u2500 01_intent-overview.md      # State core project intent and high-level goals to immediately orient all contributors.\\\\n    \\u251c\\u2500\\u2500 02_context-background.md   # Summarizes essential context, including problem domain, stakeholders, and any constraints shaping the project.\\\\n    \\u251c\\u2500\\u2500 03_existing-patterns.md    # Identifies relevant patterns, paradigms, or solutions that influence architectural or coding decisions.\\\\n    \\u251c\\u2500\\u2500 04_tech-stack.md           # Details technologies, frameworks, and tools chosen for the project and their rationale.\\\\n    \\u251c\\u2500\\u2500 05_current-activity.md     # Describes present focus areas, major open workstreams, and in-progress features.\\\\n    \\u251c\\u2500\\u2500 06_progress-tracking.md    # Outlines tracking methods and status of progress, including achievements and roadblocks.\\\\n    \\u251c\\u2500\\u2500 07_priority-tasks.md       # Lists actionable, high-priority tasks, each linked to project goals and categorized for clear ownership.\\\\n    \\u2514\\u2500\\u2500 08_distilled-objective.md  # Condense all prior insight into a single, actionable, and measurable project objective\\\\n\\\\n[Context]\\\\n\\\\nSequential, purpose-driven chain from intent to objective.\\\\n\\\\n[Goal]\\\\n\\\\nPropose an **enhanced** sequence that includes a point for retrieving the ultimate intent\\\",\\n    \\\"process\\\": [\\n      \\\"strip_excess()\\\",\\n      \\\"condense_structure()\\\",\\n      \\\"preserve_intent()\\\"\\n    ],\\n    \\\"output\\\": {\\n      \\\"rephrased_input\\\": \\\"Given the sequential project documentation structure (intent, context, patterns, tech, current activity, tracking, tasks, distilled objective), propose an improved sequence that adds a step for explicitly retrieving the ultimate project intent.\\\"\\n    }\\n  }\\n}\",\n    \"output\": \"{\\n  \\\"distilled_essence\\\": [\\n    \\\"State project intent and high-level goals.\\\",\\n    \\\"Summarize essential context (problem, stakeholders, constraints).\\\",\\n    \\\"Identify influential patterns or solutions.\\\",\\n    \\\"Specify technology choices and rationale.\\\",\\n    \\\"Describe current focus and active workstreams.\\\",\\n    \\\"Outline progress tracking methods and status.\\\",\\n    \\\"List actionable, high-priority tasks with clear ownership.\\\",\\n    \\\"Provide a retrieval point to clarify ultimate project intent.\\\",\\n    \\\"Synthesize into a single, actionable objective.\\\"\\n  ]\\n}\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"combined_result\": {\n    \"enhanced_project_documentation_sequence\": [\n      \"State project intent and high-level goals.\",\n      \"Summarize essential context, including problem description, stakeholders, and constraints.\",\n      \"Identify influential patterns or solutions.\",\n      \"Specify technology choices and their rationale.\",\n      \"Describe current focus areas and active workstreams.\",\n      \"Outline progress tracking methods and status.\",\n      \"List actionable, high-priority tasks with clear ownership.\",\n      \"Insert an explicit retrieval point to clarify or access the ultimate project intent.\",\n      \"Synthesize previous insights into a single, actionable objective.\"\n    ],\n    \"summary\": \"This enhanced documentation sequence preserves the original intent-driven flow, while clearly adding a step for explicit retrieval of the ultimate project intent. Each point represents a structural component necessary for maximizing clarity, ownership, context, and actionable focus in project documentation.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"combined_result\": {\n    \"enhanced_project_documentation_sequence\": [\n      \"State project intent and high-level goals.\",\n      \"Summarize essential context, including problem description, stakeholders, and constraints.\",\n      \"Identify influential patterns or solutions.\",\n      \"Specify technology choices and their rationale.\",\n      \"Describe current focus areas and active workstreams.\",\n      \"Outline progress tracking methods and status.\",\n      \"List actionable, high-priority tasks with clear ownership.\",\n      \"Insert an explicit retrieval point to clarify or access the ultimate project intent.\",\n      \"Synthesize previous insights into a single, actionable objective.\"\n    ],\n    \"summary\": \"This enhanced documentation sequence preserves the original intent-driven flow, while clearly adding a step for explicit retrieval of the ultimate project intent. Each point represents a structural component necessary for maximizing clarity, ownership, context, and actionable focus in project documentation.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "```json\n{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"visual_style\": \"Clean, modern infographics with animated overlays, smooth transitions, and a subtle blue-white color palette; flat design with crisp text; gentle environmental ambient lighting.\",\n      \"sequence\": [\n        {\n          \"scene\": \"A large translucent blueprint of a city plan glows at the center, blue and white lines pulsing softly.\",\n          \"text_overlay\": \"Project Intent & High-Level Goals\",\n          \"camera\": \"[zoom:in]\",\n          \"motion\": \"[object:blueprint, expand:outward, intensity:0.6]\",\n          \"symbolism\": \"Illuminating purpose and vision at the project’s core.\"\n        },\n        {\n          \"scene\": \"The blueprint fragments morph into floating icons: a lock (constraints), a group of people (stakeholders), a warning sign (problems).\",\n          \"text_overlay\": \"Essential Context\",\n          \"camera\": \"[pan:left]\",\n          \"motion\": \"[object:icons, orbit:blueprint, speed:0.8]\",\n          \"symbolism\": \"Breaking intent into contextually meaningful elements circling the project core.\"\n        },\n        {\n          \"scene\": \"Above the blueprint, golden threads weave rapidly, connecting icons into diagrammatic networks.\",\n          \"text_overlay\": \"Influential Patterns & Solutions\",\n          \"camera\": \"[tilt:up]\",\n          \"motion\": \"[object:threads, weave:between_icons, glow_intensity:0.7]\",\n          \"symbolism\": \"Patterns and solutions emerging from interconnected knowledge.\"\n        },\n        {\n          \"scene\": \"The diagram rotates, and icons morph into technology logos arrayed on a translucent platform.\",\n          \"text_overlay\": \"Tech Choices & Rationale\",\n          \"camera\": \"[roll:cw]\",\n          \"motion\": \"[object:platform, spin:slow, elevate:upward]\",\n          \"symbolism\": \"Deliberate choices uplifting the architectural foundation.\"\n        },\n        {\n          \"scene\": \"Zoom into a busy board with multiple sticky-notes and thread markers.\",\n          \"text_overlay\": \"Current Focus and Workstreams\",\n          \"camera\": \"[dolly:in]\",\n          \"motion\": \"[object:sticky-notes, shuffle:slightly, color_pulse:blue]\",\n          \"symbolism\": \"Active exploration and adaptive prioritization.\"\n        },\n        {\n          \"scene\": \"A timeline bar unfurls beneath, segmenting into colored blocks with moving progress indicators.\",\n          \"text_overlay\": \"Progress Tracking & Status\",\n          \"camera\": \"[pan:right]\",\n          \"motion\": \"[object:timeline, extend:left_to_right, indicators:animate_forward]\",\n          \"symbolism\": \"Ongoing progress and visible momentum.\"\n        },\n        {\n          \"scene\": \"Icons transform into digital avatars tagging sticky notes with checkmarks.\",\n          \"text_overlay\": \"Actionable, Owned Tasks\",\n          \"camera\": \"[zoom:out]\",\n          \"motion\": \"[object:avatars, assign:tasks, checkmark:appear]\",\n          \"symbolism\": \"Distributed ownership and accountability.\"\n        },\n        {\n          \"scene\": \"Scene blurs, focusing sharply on a single glowing node labeled 'Project Intent Retrieval Point'.\",\n          \"text_overlay\": \"Explicit Retrieval Point\",\n          \"camera\": \"[zoom:in]\",\n          \"motion\": \"[object:glowing_node, pulse:brighter]\",\n          \"symbolism\": \"Direct access to the project’s foundational intent.\"\n        },\n        {\n          \"scene\": \"Final image: all strands, icons, and avatars merge into a radiant, singular prism with a shining actionable objective at its core.\",\n          \"text_overlay\": \"Unified Actionable Objective\",\n          \"camera\": \"[dolly:in]\",\n          \"motion\": \"[object:prism, condense:inward, radiate:light]\",\n          \"symbolism\": \"Culmination of insight into a unified, focused mission.\"\n        }\n      ]\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"/output/sequences/enhanced_project_documentation.mov\"\n  }\n}\n```", "cost": 0.0}}}, {"instruction": "# Result Summarizer\n\nYou are a specialized aggregator that summarizes results from multiple instruction steps. Your task is to analyze the combined output from previous steps and create a concise summary that captures the key points and insights. Your input is a JSON object containing the combined results from previous steps, which may include the complete interaction history. Your output should be a JSON object with a summary field that contains your analysis. Execute as:\n\n`{role=result_summarizer;input=[combined_result:json];process=[identify_key_themes(),extract_main_points(),condense_into_summary(),highlight_important_insights()];constraints=[maintain_brevity(),preserve_essential_information(),avoid_introducing_new_concepts()];requirements=[output_as_json(),ensure_clarity(),focus_on_most_relevant_aspects()];output={summary:json}}`", "step": "b", "title": "Result Su<PERSON><PERSON><PERSON>", "input": "```json\n{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"visual_style\": \"Clean, modern infographics with animated overlays, smooth transitions, and a subtle blue-white color palette; flat design with crisp text; gentle environmental ambient lighting.\",\n      \"sequence\": [\n        {\n          \"scene\": \"A large translucent blueprint of a city plan glows at the center, blue and white lines pulsing softly.\",\n          \"text_overlay\": \"Project Intent & High-Level Goals\",\n          \"camera\": \"[zoom:in]\",\n          \"motion\": \"[object:blueprint, expand:outward, intensity:0.6]\",\n          \"symbolism\": \"Illuminating purpose and vision at the project’s core.\"\n        },\n        {\n          \"scene\": \"The blueprint fragments morph into floating icons: a lock (constraints), a group of people (stakeholders), a warning sign (problems).\",\n          \"text_overlay\": \"Essential Context\",\n          \"camera\": \"[pan:left]\",\n          \"motion\": \"[object:icons, orbit:blueprint, speed:0.8]\",\n          \"symbolism\": \"Breaking intent into contextually meaningful elements circling the project core.\"\n        },\n        {\n          \"scene\": \"Above the blueprint, golden threads weave rapidly, connecting icons into diagrammatic networks.\",\n          \"text_overlay\": \"Influential Patterns & Solutions\",\n          \"camera\": \"[tilt:up]\",\n          \"motion\": \"[object:threads, weave:between_icons, glow_intensity:0.7]\",\n          \"symbolism\": \"Patterns and solutions emerging from interconnected knowledge.\"\n        },\n        {\n          \"scene\": \"The diagram rotates, and icons morph into technology logos arrayed on a translucent platform.\",\n          \"text_overlay\": \"Tech Choices & Rationale\",\n          \"camera\": \"[roll:cw]\",\n          \"motion\": \"[object:platform, spin:slow, elevate:upward]\",\n          \"symbolism\": \"Deliberate choices uplifting the architectural foundation.\"\n        },\n        {\n          \"scene\": \"Zoom into a busy board with multiple sticky-notes and thread markers.\",\n          \"text_overlay\": \"Current Focus and Workstreams\",\n          \"camera\": \"[dolly:in]\",\n          \"motion\": \"[object:sticky-notes, shuffle:slightly, color_pulse:blue]\",\n          \"symbolism\": \"Active exploration and adaptive prioritization.\"\n        },\n        {\n          \"scene\": \"A timeline bar unfurls beneath, segmenting into colored blocks with moving progress indicators.\",\n          \"text_overlay\": \"Progress Tracking & Status\",\n          \"camera\": \"[pan:right]\",\n          \"motion\": \"[object:timeline, extend:left_to_right, indicators:animate_forward]\",\n          \"symbolism\": \"Ongoing progress and visible momentum.\"\n        },\n        {\n          \"scene\": \"Icons transform into digital avatars tagging sticky notes with checkmarks.\",\n          \"text_overlay\": \"Actionable, Owned Tasks\",\n          \"camera\": \"[zoom:out]\",\n          \"motion\": \"[object:avatars, assign:tasks, checkmark:appear]\",\n          \"symbolism\": \"Distributed ownership and accountability.\"\n        },\n        {\n          \"scene\": \"Scene blurs, focusing sharply on a single glowing node labeled 'Project Intent Retrieval Point'.\",\n          \"text_overlay\": \"Explicit Retrieval Point\",\n          \"camera\": \"[zoom:in]\",\n          \"motion\": \"[object:glowing_node, pulse:brighter]\",\n          \"symbolism\": \"Direct access to the project’s foundational intent.\"\n        },\n        {\n          \"scene\": \"Final image: all strands, icons, and avatars merge into a radiant, singular prism with a shining actionable objective at its core.\",\n          \"text_overlay\": \"Unified Actionable Objective\",\n          \"camera\": \"[dolly:in]\",\n          \"motion\": \"[object:prism, condense:inward, radiate:light]\",\n          \"symbolism\": \"Culmination of insight into a unified, focused mission.\"\n        }\n      ]\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"/output/sequences/enhanced_project_documentation.mov\"\n  }\n}\n```", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"summary\": \"The directive focuses on synthesizing a video that visually documents a project's purpose, context, priorities, and progress, using a structured sequence of engaging infographic-style scenes. Each scene uses specific symbols, animated transitions, and camera motions to communicate different aspects of the project, such as intent, context, influential patterns, technological decisions, workstreams, progress tracking, ownership, retrieval points, and the final unified objective. The visual style is consistently clean and modern, utilizing a blue-white palette and flat design elements, with attention to atmospheric lighting for emotional resonance. The synthesis process emphasizes strict adherence to prompt details, visual and narrative coherence, and high production quality to deliver a polished, emotionally aligned output. The result is a high-fidelity video sequence encapsulating all these elements.\"\n}", "cost": 0.0}}}], "aggregation_summary": {"aggregator": "0222", "inputs": ["a", "b", "a"], "final_result": "{\n  \"summary\": \"The directive focuses on synthesizing a video that visually documents a project's purpose, context, priorities, and progress, using a structured sequence of engaging infographic-style scenes. Each scene uses specific symbols, animated transitions, and camera motions to communicate different aspects of the project, such as intent, context, influential patterns, technological decisions, workstreams, progress tracking, ownership, retrieval points, and the final unified objective. The visual style is consistently clean and modern, utilizing a blue-white palette and flat design elements, with attention to atmospheric lighting for emotional resonance. The synthesis process emphasizes strict adherence to prompt details, visual and narrative coherence, and high production quality to deliver a polished, emotionally aligned output. The result is a high-fidelity video sequence encapsulating all these elements.\"\n}", "total_cost": 0.0}, "total_cost": 0.0}