{"auto_complete": {"selected_items": [["commun", "communication"], ["playf", "playfully"], ["consis", "consistenly"], ["sep", "sep2\t(Custom) # ============================================================================="], ["da", "DARK"], ["Everyth", "Everything"], ["ex", "expand"], ["pr", "print"], ["acce", "accents_2"], ["foregr", "foreground_3"], ["str", "strikethrough"], ["e", "ex\t(Custom) Exit with message (variable)"], ["Req", "VenvRequirementsTxt"], ["venva", "VenvActivate"], ["PROPE", "property_keys"], ["prop", "property_keys"], ["datacla", "dataclasses"], ["highli", "highlighted"]]}, "buffers": [], "build_system": "", "build_system_choices": [], "build_varint": "", "command_palette": {"height": 0.0, "last_filter": "", "selected_items": [["min", "Jorn - Minify Text"], ["mini", "Jorn - Minify Text"], ["interv", "Jorn: Time Interval Actions - Run Now"], ["interva", "Jorn: Time Interval Actions - Run Now"], ["del com", "Jorn - Delete Comments"], ["orga", "Jorn: Organize Views by Directory"], ["install", "Package Control: Install Package"], ["remove", "Package Control: Remove Package"], ["preview", "Markdown Preview: Preview in Browser"], ["comme", "Jorn - Select Comments"], ["sort", "SortBy: Length of lines"], ["markdow pre", "Markdown Preview: Preview in Browser"], ["load", "Origami: <PERSON><PERSON> Saved Layout"], ["load la", "Origami: <PERSON><PERSON> Saved Layout"], ["comma", "Text Pastry: Command-Line"], ["INSTA", "Package Control: Install Package"], ["extra", "PackageResourceViewer: Extract Package"], ["expand", "Selection: Expand to <PERSON><PERSON>"], ["EXTRACT", "PackageResourceViewer: Extract Package"], ["REMOVE PA", "Package Control: Remove Package"], ["disab", "Package Control: Disable Package"], ["inst", "Package Control: Install Package"], ["color", "UI: Select Color Scheme"], ["layou load", "Origami: <PERSON><PERSON> Saved Layout"], ["disa", "Package Control: Disable Package"], ["enabl", "Package Control: Enable Package"], ["sidebar", "Side Bar: Disable Syncing"], ["disabl", "Package Control: Disable Package"], ["sortby reg", "SortBy: Regular expression"], ["insta", "Package Control: Install Package"], ["COMME", "Jorn - Delete Comments"], ["LOAD", "Origami: <PERSON><PERSON> Saved Layout"], ["REIND", "Indentation: Reindent Lines"], ["focus on", "Origami: Focus on Pane on the Right"], ["restor", "Origami: <PERSON><PERSON> Saved Layout"], ["remo", "Package Control: Remove Package"], ["extr", "PackageResourceViewer: Extract Package"], ["INSTALL", "Colorsublime: Install Theme"], ["comments", "Jorn - Delete Comments"], ["remove p", "Package Control: Remove Package"], ["emo", "Emoji: Insert Emoji"], ["emoj", "Emoji: Insert Emoji"], ["instal", "Package Control: Install Package"], ["log", "Set Syntax: Log Highlight - example"], ["jorn comment", "Jorn - Delete All Comments"], ["jorn comments", "Jorn - Select All Comments"], ["aligntab", "AlignTab"], ["random", "Random:Word"], ["scope", "ScopeAlways: Settings"], ["", "Arithmetic"], ["theme", "UI: Select Theme"], ["scheme", "UI: Select Color Scheme"], ["snippe", "Snippet: (Custom) print(str(variable_name))"], ["them", "UI: Select Theme"], ["the", "UI: Select Theme"], ["open", "PackageResourceViewer: Open Resource"], ["open ", "PackageResourceViewer: Open Resource"], ["open re", "PackageResourceViewer: Open Resource"], ["list", "Package Control: List Unmanaged Packages"], ["ins", "Package Control: Install Package"], ["reso ope", "PackageResourceViewer: Open Resource"], ["rsv", "PackageResourceViewer: Extract Package"], ["autohi", "Jorn - AutoHideSidebar: Toggle"], ["<PERSON><PERSON>", "Jorn - AutoHideSidebar: Toggle"], ["color sch", "UI: Select Color Scheme"], ["res", "PackageResourceViewer: Open Resource"], ["resou", "PackageResourceViewer: Extract Package"], ["schem", "UI: Select Color Scheme"], ["packa", "Preferences: Package Control Settings"], ["enab", "Package Control: Enable Package"], ["sortta", "Jorn - <PERSON>rt <PERSON>bs by File Name"], ["sync", "SyncScroll: Toggle Current View Scroll Sync"], ["sort ta", "Sort Tabs: <PERSON>u"], ["remov", "Package Control: Remove Package"]], "width": 0.0}, "console": {"height": 289.2, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/C/Users/<USER>/Desktop/SCRATCH/2025.05.25-kl.13.45--WindowTiler/WindowTiler"], "file_history": ["/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_py_windowtiler.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_py_pinterestdownloader.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/LIB/png/ico_youtube.png", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_app_everything.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_5_menus/wip_menus/mnu_Processes.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_4_groups/grp_jorn_actions_git.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_5_menus/mnu_user_jorn_scripts_python.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_5_menus/mnu_user_jorn_scripts.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_5_menus/mnu_actions_user_create.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__BraindumpOutlet/.gitignore", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__BraindumpInterface/src/main.py", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_5_menus/mnu_user_jorn_braindump.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__BraindumpInterface/dumps/2025.05.17-kl.22.48--md.md", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__BraindumpInterface/dumps/2025.05.17-kl.22.50--braindump.md", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_py_braindumpinterface.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_py_youtubedownloader.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__PinterestDownloader/main.py", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Apps/app_vscode/exe/data/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/dbghelp.py", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_py_projectgenerator.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__PinterestDownloader/main.bat", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__ProjectGenerator/src/templates/project_files/.jinja-gitignore", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_py_dirtreegenerator.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_4_groups/grp_jorn_dirs_DESKTOP-8058SHG_flow.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__BraindumpInterface/.new_hashes.py", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Videos/Transcribe/NA-Aperture-All The Time We Were Wrong.txt", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__CleanupSpace/main.py", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_py_directorycleaner.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__BraindumpOutlet/src/.gitignore", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/LIB/bat/git_history_graph.bat", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_6_contexts/ctx_explorer.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_6_contexts/ctx_taskbar.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_5_menus/mnu_user_jorn_dirs.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_bat_githistorygraph.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_5_menus/mnu_user_jorn_apps_everything.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_1_init/_a_constants/def_keys.nss", "/C/Users/<USER>/Desktop/SCRATCH/2025.05.06-kl.14.18--/.gitignore", "/C/Users/<USER>/Desktop/SCRATCH/2025.05.04-kl.13.54--rlweb-compare/dev_rl-website/SCENARIO.md", "/C/Users/<USER>/AppData/Local/Temp/tmpD4A6.tmp.ts", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/prj/playground/rl-website_bolt/src/data/services.ts", "/C/Users/<USER>/Desktop/SCRATCH/2025.05.04-kl.13.54--rlweb-compare/2025.05.04-kl.13.dirtree.md", "/C/Users/<USER>/Desktop/SCRATCH/2025.05.04-kl.13.54--rlweb-compare/dev_rl-website/dev_rl-website.dirtree.md", "/C/Users/<USER>/AppData/Local/Temp/tmp68D2.tmp.md", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_5_menus/mnu_actions_user_create.sync-conflict-20250501-142958-T3X7SRA.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_5_menus/mnu_user_jorn_dirs_common.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/rlweb_ext_repos/2025.04.13-dsk-003-d.kl11__len[63].md", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_4_groups/grp_jorn_dirs_nas_networkdrives.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_5_menus/mnu_scratchpad.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_4_groups/grp_jorn_dirs_nas_work.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_py_consolidatelogs.nss", "/C/Users/<USER>/Desktop/SCRATCH/2025.04.30-kl.09.24--/outputs/data/R5385.advancedsearch.008-a-docs.md", "/C/Users/<USER>/Desktop/SCRATCH/2025.04.30-kl.09.24--/outputs/data/R5385-a-docs.json", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_4_groups/grp_actions_user_create.nss", "/C/Users/<USER>/Desktop/SCRATCH/2025.04.30-kl.09.24--/requirements.txt", "/C/Users/<USER>/Desktop/SCRATCH/2025.04.30-kl.09.24--/.gitignore", "/C/Users/<USER>/Desktop/SCRATCH/2025.04.30-kl.09.24--/outputs/data/R5385.advancedsearch.008-a-docs.json", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__ConsolidateLogs/src/consolidated_logs/2025.04.29/2025.04.29-kl.10.44.chat.vscode_cline.json", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_4_groups/grp_jorn_dirs_nas_flow.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_app_sys_cmd.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Apps/app_vscode/user/roo-code-settings.json", "/?DESKTOP-8058SHG/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/prj/rl-website_web/project/memory-bank/essential-context.md", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_1_init/_a_constants/def_bools.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_1_init/_a_constants/def_icons.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_1_init/_a_constants/def_paths.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_1_init/_a_constants/def_tooltips.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_1_init/_a_constants/def_uris.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_1_init/_c_overrides/__update_everything64.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_1_init/_b_config/cfg_settings.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_1_init/_a_constants/def_colors.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_app_vscode.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_app_vscode_debug.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Apps/app_vscode/exe/data/extensions/extensions.json", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Apps/app_vscode/exe/data/extensions/.obsolete", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_app_vlc.nss", "/C/Users/<USER>/Desktop/SCRATCH/interaction/interaction.md", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_app_sublimetext.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/Python/2025.03.30-dsk-001-a.kl21__len[648].md", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/app_chrome_nucnuc2/2025.03.30-dsk-001-a.kl22__len[510].md", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_app_blender.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_WindowWorskapceManager/git-history-graph.bat", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Notes/markdown_diff_syntax.md", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Utils_Todo/Py_WindowTiler/SaveWindowLayout_V3/reference_utils/2025.03.24 - 17.27 - .bat", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/LIB/bat/git_history_graph.exampleoutput.md", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_bat_pyvenvterminal.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/LIB/bat/py_venv_terminal.bat", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_py_renamewitheditor.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_app_cursor.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/shell.log", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_dir_sys_appdata.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_6_contexts/ctx_desktop.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_6_contexts/ctx_taskbar_nuc.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_app_aftereffects.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_6_contexts/ctx_everything64.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_5_menus/mnu_prj_perspektiv.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_4_groups/grp_jorn_dirs_nas_android.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/New folder/_6_contexts/_6_contexts.md", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_app_sublimemerge.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_py_closeduplicatewindows.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_6_contexts/ctx_titlebar.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/LIB/bat/py_venv_git_commit_all.bat", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_py_audiofromvideoextractor.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__AudioFromVideoExtractor/prompt_2025.01.20-a-1.md", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__AudioFromVideoExtractor/main.py", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__AudioAndVideoCombiner/main.py", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__AudioAndVideoCombiner/prompt_2025.01.20-a-1.md", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_py_audioandvideocombiner.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/__SHELL__.sublime-project", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/New folder/_6_contexts/ctx_desktop.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_app_gitrestoremtime.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_py_youtubeplaylistexporter.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_5_menus/mnu_sys_debug_commands.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_app_comfyui.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_1_init/_c_overrides/mod_icons.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_1_init/_c_overrides/mod_visibility.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_1_init/_c_overrides/mod_positions.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_2_variables/old/var_commands_ps1.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_2_variables/old/var_commands_py.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_2_variables/old/var_directories.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_2_variables/old/var_executables.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_2_variables/old/var_commands_cmd.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_2_variables/old/var_paths.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/shell.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_py_speechtotext.nss", "/D/Dokumenter O.l/Documens/Privat/Dokumenter/Tanker/AI, ChatGPT, osv/ChatGPT_Notes.py", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/NSS/_3_items/itm_app_audacity.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/src/NSS/_6_contexts/ctx_taskbar_nuc.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/src/NSS/_1_init/_a_constants/def_keys.nss", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/src/src.md", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__SHELL__/exe/src/shell.nss"], "find": {"height": 29.0}, "find_in_files": {"height": 658.4, "where_history": []}, "find_state": {"case_sensitive": false, "find_history": [], "highlight": true, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": [], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": []}], "incremental_find": {"height": 29.0}, "input": {"height": 243.333333333}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.diff_views": {"height": 716.0}, "output.exec": {"height": 174.0}, "output.find_results": {"height": 0.0}, "output.mdpopups": {"height": 0.0}, "output.project_environment_log": {"height": 0.0}, "pinned_build_system": "__SHELL__.sublime-project", "project": "WindowTiler.sublime-project", "replace": {"height": 54.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["ever", "__SHELL__\\exe\\NSS\\_3_items\\itm_app_everything.nss"], ["create", "__SHELL__\\exe\\NSS\\_5_menus\\mnu_actions_user_create.nss"], ["youtu", "__SHELL__\\exe\\NSS\\_3_items\\itm_py_youtubedownloader.nss"], ["youtub", "__SHELL__\\exe\\LIB\\png\\ico_youtube.png"], ["git", "__SHELL__\\exe\\NSS\\_4_groups\\grp_jorn_actions_git.nss"], ["project", "__SHELL__\\exe\\NSS\\_3_items\\itm_py_projectgenerator.nss"], ["director", "__SHELL__\\exe\\NSS\\_3_items\\itm_py_directorycleaner.nss"], ["every", "__SHELL__\\exe\\NSS\\_5_menus\\mnu_user_jorn_apps_everything.nss"], ["process", "__SHELL__\\exe\\NSS\\_5_menus\\wip_menus\\mnu_Processes.nss"], ["itm_bat_githistorygraph", "__SHELL__\\exe\\NSS\\_3_items\\itm_bat_githistorygraph.nss"], ["graph", "__SHELL__\\exe\\LIB\\bat\\git_history_graph.bat"], ["mnu_user_jorn_dirs", "__SHELL__\\exe\\NSS\\_5_menus\\mnu_user_jorn_dirs_common.nss"], ["desktop", "__SHELL__\\exe\\NSS\\_4_groups\\grp_jorn_dirs_DESKTOP-8058SHG_flow.nss"], ["consol", "__SHELL__\\exe\\NSS\\_3_items\\itm_py_consolidatelogs.nss"], ["app cmd", "__SHELL__\\exe\\NSS\\_3_items\\itm_app_sys_cmd.nss"], ["jorn flo", "__SHELL__\\exe\\NSS\\_4_groups\\grp_jorn_dirs_nas_flow.nss"], ["scrat", "__SHELL__\\exe\\NSS\\_5_menus\\mnu_scratchpad.nss"], ["_everyt", "__SHELL__\\exe\\NSS\\_5_menus\\mnu_user_jorn_apps_everything.nss"], ["app_ev", "__SHELL__\\exe\\NSS\\_3_items\\itm_app_everything.nss"], ["VSCODE", "__SHELL__\\exe\\NSS\\_3_items\\itm_app_vscode.nss"], ["vscode", "__SHELL__\\exe\\NSS\\_3_items\\itm_app_vscode_debug.nss"], ["mnu_scratchpad", "__SHELL__\\exe\\NSS\\_5_menus\\mnu_scratchpad.nss"], ["app ever", "__SHELL__\\exe\\NSS\\_3_items\\itm_app_everything.nss"], ["itm_app_sublimetext", "__SHELL__\\exe\\NSS\\_3_items\\itm_app_sublimetext.nss"], ["app vlc", "__SHELL__\\exe\\NSS\\_3_items\\itm_app_vlc.nss"], ["explorer", "__SHELL__\\exe\\NSS\\_6_contexts\\ctx_explorer.nss"], ["venv", "__SHELL__\\exe\\LIB\\bat\\py_venv_terminal.bat"], ["scripts", "__SHELL__\\exe\\NSS\\_5_menus\\mnu_user_jorn_scripts_python.nss"], ["rena", "__SHELL__\\exe\\NSS\\_3_items\\itm_py_renamewitheditor.nss"], ["app vsc", "__SHELL__\\exe\\NSS\\_3_items\\itm_app_vscode.nss"], ["cursor", "__SHELL__\\exe\\NSS\\_3_items\\itm_app_cursor.nss"], ["blend", "__SHELL__\\exe\\NSS\\_3_items\\itm_app_blender.nss"], ["3ds", "__SHELL__\\exe\\NSS\\_3_items\\itm_dir_sys_appdata.nss"], ["git co", "__SHELL__\\exe\\NSS\\_4_groups\\grp_jorn_actions_git.nss"], ["processe", "__SHELL__\\exe\\NSS\\_5_menus\\wip_menus\\mnu_Processes.nss"], ["mnu_user_jorn_dirs_common", "__SHELL__\\exe\\NSS\\_5_menus\\mnu_user_jorn_dirs_common.nss"], ["itm_py_audiofromvideoextractor", "__SHELL__\\exe\\NSS\\_3_items\\itm_py_audiofromvideoextractor.nss"], ["itm_py_audioandvideocombiner", "__SHELL__\\exe\\NSS\\_3_items\\itm_py_audioandvideocombiner.nss"], ["itm_py_closeduplicatewindows", "__SHELL__\\exe\\NSS\\_3_items\\itm_py_closeduplicatewindows.nss"], ["audio", "__SHELL__\\exe\\NSS\\_3_items\\itm_py_audioandvideocombiner.nss"], ["app subl", "__SHELL__\\exe\\NSS\\_3_items\\itm_app_sublimetext.nss"], ["grp_jorn_dirs_nas_android", "__SHELL__\\exe\\NSS\\_4_groups\\grp_jorn_dirs_nas_android.nss"], ["everyt", "__SHELL__\\exe\\NSS\\_6_contexts\\ctx_everything64.nss"], ["3d", "__SHELL__\\exe\\NSS\\_3_items\\itm_app_3dsmax.nss"], ["speexc", "__SHELL__\\exe\\NSS\\_3_items\\itm_py_speechtotext.nss"], ["gpt4", "__SHELL__\\exe\\NSS\\_3_items\\user_apps\\itm_app_gpt4all.nss"], ["scratchp", "exe\\NSS\\_5_menus\\jorn_menus\\mnu_scratchpad.nss"], ["itm_dir_jorn_scratch", "exe\\NSS\\_3_items\\user_dirs\\itm_dir_jorn_scratch.nss"], ["rename", "exe\\NSS\\_3_items\\user_scripts\\itm_py_renamewitheditor.nss"], ["compresso", "exe\\NSS\\_3_items\\user_scripts\\itm_py_videocompressor.nss"], ["keys", "exe\\NSS\\_1_init\\_a_defaults\\def_keys.nss"], ["audaci", "exe\\NSS\\_3_items\\user_apps\\itm_app_audacity.nss"], ["pip insta", "exe\\LIB\\bat\\py_venv_pip_install.bat"], ["grp_jorn_urls_gpts", "exe\\NSS\\_4_groups\\jorn_urls\\grp_jorn_urls_gpts.nss"], ["mnu_user_jorn_urls.nss", "exe\\NSS\\_5_menus\\jorn_menus\\mnu_user_jorn_urls.nss"], ["itm_app_sys_osk.nss", "exe\\NSS\\_3_items\\system_apps\\itm_app_sys_osk.nss"], ["mnu_user_jorn_apps", "exe\\NSS\\_5_menus\\jorn_menus\\mnu_user_jorn_apps_git.nss"], ["speec", "exe\\NSS\\_3_items\\user_scripts\\itm_py_speechtotext.nss"], ["_py_", "exe\\NSS\\_3_items\\user_scripts\\itm_py_renamewitheditor.nss"], ["renamewi", "exe\\NSS\\_3_items\\user_scripts\\itm_py_renamewitheditor.nss"], ["itm_app_vlc", "exe\\NSS\\_3_items\\user_apps\\itm_app_vlc.nss"], ["scratch", "exe\\NSS\\_5_menus\\jorn_menus\\mnu_scratchpad.nss"], ["itm_app_telegram", "exe\\NSS\\_3_items\\user_apps\\itm_app_telegram.nss"], ["py_", "exe\\NSS\\_3_items\\user_scripts\\itm_py_markdowngenerator.nss"], ["grp_jorn_actions_git", "exe\\NSS\\_4_groups\\jorn_actions\\grp_jorn_actions_git.nss"], ["mnu_user_jorn_apps_git", "exe\\NSS\\_5_menus\\jorn_menus\\mnu_user_jorn_apps_git.nss"], ["git nss", "exe\\NSS\\_3_items\\user_scripts\\itm_py_gitsizeanalyzer.nss"], ["app_", "exe\\NSS\\_3_items\\user_apps\\itm_app_claude.nss"], ["3dsma", "exe\\NSS\\_3_items\\user_apps\\itm_app_3dsmax.nss"], ["app blend", "exe\\NSS\\_3_items\\user_apps\\itm_app_blender.nss"], ["itm_py_bookmarkfolderizer", "exe\\NSS\\_3_items\\user_scripts\\itm_py_bookmarkfolderizer.nss"], ["script", "exe\\NSS\\_5_menus\\jorn_menus\\mnu_user_jorn_scripts.nss"], ["mnu scra", "exe\\NSS\\_5_menus\\jorn_menus\\mnu_scratchpad.nss"], ["calc", "exe\\NSS\\_3_items\\system_apps\\itm_app_sys_calc.nss"], ["grp_sys_dirs_common", "exe\\NSS\\_4_groups\\system_folders\\grp_sys_dirs_common.nss"], ["mnu_sys_debug_commands", "exe\\NSS\\_5_menus\\system_menus\\mnu_sys_debug_commands.nss"], ["mnu_sys_actions_windows", "exe\\NSS\\_5_menus\\system_menus\\mnu_sys_actions_windows.nss"], ["mnu crea", "exe\\NSS\\_5_menus\\user_menus\\mnu_actions_user_create.nss"], ["mnu_sys_actions_clipboard", "exe\\NSS\\_5_menus\\system_menus\\mnu_sys_actions_clipboard.nss"], ["itm_action_sys_copypath", "exe\\NSS\\_3_items\\system_actions\\itm_action_sys_copypath.nss"], ["vlc", "exe\\NSS\\_3_items\\user_apps\\itm_app_vlc.nss"], ["itm_py_projectgenerator", "exe\\NSS\\_3_items\\user_scripts\\itm_py_projectgenerator.nss"], ["mnu_user_jorn_scripts", "exe\\NSS\\_5_menus\\jorn_menus\\mnu_user_jorn_scripts.nss"], ["mnu_actions_user_create", "exe\\NSS\\_5_menus\\user_menus\\mnu_actions_user_create.nss"], ["itm_dir_sys_desktop", "exe\\NSS\\_3_items\\system_dirs\\itm_dir_sys_desktop.nss"], ["mn micr", "exe\\NSS\\_5_menus\\system_menus\\mnu_apps_microsoft.nss"], ["log", "exe\\shell.log"], ["grp_jorn_dirs_nas_networkdrives", "exe\\NSS\\_4_groups\\jorn_dirs\\grp_jorn_dirs_nas_networkdrives.nss"], ["mnu jorn", "exe\\NSS\\_5_menus\\jorn_menus\\mnu_user_jorn_dirs.nss"], ["itm_app_audacity", "exe\\NSS\\_3_items\\user_apps\\itm_app_audacity.nss"], ["uri", "user\\notes\\MS-URI-Handlers.nss"], ["urls", "exe\\NSS\\_4_groups\\jorn_urls\\grp_jorn_urls_gpts.nss"], ["scra", "exe\\NSS\\_5_menus\\jorn_menus\\mnu_scratchpad.nss"], ["registe", "exe\\__REGISTER.BAT"], ["process nss", "exe\\NSS\\_5_menus\\wip_menus\\mnu_Processes.nss"], ["grp_jorn_dirs_nas_flow", "exe\\NSS\\_4_groups\\jorn_dirs\\grp_jorn_dirs_nas_flow.nss"], ["grp_jorn_dirs_nas_workflow", "exe\\NSS\\_4_groups\\jorn_dirs\\grp_jorn_dirs_nas_workflow.nss"], ["mn create", "exe\\NSS\\_5_menus\\user_menus\\mnu_actions_user_create.nss"], ["blender", "exe\\NSS\\_3_items\\user_apps\\itm_app_blender.nss"], ["termina", "user\\unsorted\\todo\\grp_terminal.nss"], ["term", "user\\refs\\terminal.nss"], ["tmp", "exe\\NSS\\_5_menus\\wip_menus\\mnu_TMP.nss"], ["snippe", "user\\notes\\nilesoft_shell_snippets.nss"], ["7z", "exe\\NSS\\_5_menus\\jorn_menus\\mnu_user_jorn_apps_7zip.nss"], ["itm_dir_thispc", "exe\\NSS\\_2_items\\system_dirs\\itm_dir_sys_thispc.nss"], ["dir desktop", "exe\\NSS\\_2_items\\system_dirs\\itm_dir_desktop.nss"], ["itm_dir_jorn_shell", "exe\\NSS\\_2_items\\user_dirs\\itm_dir_jorn_shell.nss"], ["grp git", "exe\\NSS\\_3_groups\\jorn_actions\\grp_jorn_actions_git.nss"], ["__4_menus\\mnu_user_jorn_apps_git.nss", "exe\\NSS\\_4_menus\\jorn_menus\\mnu_user_jorn_apps_git.nss"], ["copyp", "exe\\NSS\\_2_items\\system_actions\\itm_action_sys_copypath.nss"], ["py_bookmarkfolderizer", "exe\\NSS\\_2_items\\user_scripts\\itm_py_bookmarkfolderizer.nss"], ["grp_sys_dirs_cloud", "exe\\NSS\\_3_groups\\system_folders\\grp_sys_dirs_cloud.nss"], ["grp_debug_user_shell", "exe\\NSS\\_3_groups\\user_debug\\grp_debug_user_shell.nss"], ["grp commo", "exe\\NSS\\_3_groups\\system_folders\\grp_sys_dirs_common.nss"], ["nas cloud", "exe\\NSS\\_3_groups\\jorn_dirs\\grp_jorn_dirs_nas_cloud.nss"], ["dir nas wor", "exe\\NSS\\_3_groups\\jorn_dirs\\grp_jorn_dirs_nas_workflow.nss"], ["dir she", "exe\\NSS\\_2_items\\user_dirs\\itm_dir_jorn_shell.nss"], ["websit", "exe\\NSS\\_3_groups\\jorn_urls\\grp_jorn_urls_websites.nss"], ["grp_sys_debug_tooltips", "exe\\NSS\\_3_groups\\user_debug\\grp_sys_debug_tooltips.nss"], ["mnu_AppLibrary", "exe\\NSS\\_4_menus\\wip_menus\\mnu_AppLibrary.nss"], ["dirs_sys", "NSS\\_3_menus\\submenus\\mnu_dirs_sys_common.nss"], ["grp_actions_user_git", "NSS\\_2_setup\\_2_items\\grp_actions_user_git.nss"], ["gpts", "NSS\\_2_setup\\_2_items\\grp_urls_jorn_gpts.nss"], ["_DIRS_US", "NSS\\_2_setup\\_2_items\\grp_dirs_user_jorn_git_portal.nss"], ["grp_dirs_user_jorn_nas_workflow", "NSS\\_2_setup\\_2_items\\grp_dirs_user_jorn_nas_workflow.nss"], ["cloud", "NSS\\_2_setup\\_2_items\\grp_dirs_user_jorn_nas_cloud.nss"], ["grp_dirs_user_jorn_sync", "NSS\\_2_setup\\_2_items\\grp_dirs_user_jorn_sync.nss"], ["theme", "imports_OLD\\theme.nss"]], "width": 0.0}, "select_project": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": false, "show_open_files": true, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 399.0, "status_bar_visible": true, "template_settings": {}}