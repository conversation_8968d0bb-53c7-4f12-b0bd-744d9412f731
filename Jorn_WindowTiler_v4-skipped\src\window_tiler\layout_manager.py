"""
Layout Manager module for arranging windows in different patterns.
"""


class LayoutManager:
    """
    Handles different window arrangement strategies.
    """

    def __init__(self):
        self.layouts = {}  # For future saved layout presets

    def apply_grid_layout(self, windows, monitor, rows=2, cols=2, restore_minimized=True):
        """
        Arrange windows in a grid layout on the specified monitor.
        
        Args:
            windows: List of Window objects to arrange
            monitor: Monitor object to arrange windows on
            rows: Number of rows in the grid
            cols: Number of columns in the grid
            restore_minimized: Whether to restore minimized windows
            
        Returns:
            Number of windows successfully arranged
        """
        if not windows:
            return 0

        left, top, right, bottom = monitor.monitor_area
        tw = right - left
        th = bottom - top

        n = min(len(windows), rows*cols)
        for i, w in enumerate(windows[:n]):
            row = i // cols
            col = i % cols
            x = left + int(col * (tw / cols))
            y = top + int(row * (th / rows))
            wth = int(tw / cols)
            hth = int(th / rows)
            try:
                w.move_and_resize(x, y, wth, hth, restore_minimized=restore_minimized)
            except Exception as e:
                print(f"Error tiling window {w.title}: {e}")

        return n

    def create_layout_preset(self, name, config):
        """
        Create a named layout preset for later use.
        
        Args:
            name: Name of the layout preset
            config: Configuration dictionary for the layout
            
        Returns:
            True if the preset was created successfully
        """
        self.layouts[name] = config
        return True

    def apply_custom_layout(self, windows, monitor, layout_name=None, layout_config=None):
        """
        Apply a custom layout to the windows.
        Can use either a named preset or a provided configuration.
        
        Args:
            windows: List of Window objects to arrange
            monitor: Monitor object to arrange windows on
            layout_name: Name of a preset layout
            layout_config: Custom layout configuration (overrides layout_name)
            
        Returns:
            Number of windows successfully arranged
        """
        if not windows:
            return 0

        # If layout_config is provided, use it; otherwise look up the named preset
        config = layout_config
        if not config and layout_name in self.layouts:
            config = self.layouts[layout_name]

        if not config:
            # Default to grid layout
            return self.apply_grid_layout(windows, monitor)

        # In the future, this would handle different layout types based on config
        layout_type = config.get('type', 'grid')
        
        if layout_type == 'grid':
            rows = config.get('rows', 2)
            cols = config.get('cols', 2)
            restore_minimized = config.get('restore_minimized', True)
            return self.apply_grid_layout(windows, monitor, rows, cols, restore_minimized)
            
        # Future layout types would be handled here
        return 0
