├── Jorn_WindowTiler_v1
│   ├── 3d
│   │   ├── tiling_01.max
│   │   ├── tiling_01.ms
│   │   ├── tiling_02.max
│   │   └── tiling_02.ms
│   ├── __meta__
│   │   ├── window_data.json
│   │   └── windows2.txt
│   ├── src
│   │   ├── mess
│   │   │   ├── _refs
│   │   │   │   ├── CONFIG_TEMPLATE_01.ini
│   │   │   │   ├── WinWinLayoutManager_3_moresimplifying.py
│   │   │   │   ├── WinWinLayoutManager_4_New.py
│   │   │   │   ├── WinWinLayoutManager_5_5.py
│   │   │   │   ├── configFile_reference_1.json
│   │   │   │   ├── configFile_reference_2.j<PERSON>
│   │   │   │   ├── explorercmd.py
│   │   │   │   ├── folder_structure.py
│   │   │   │   ├── miniTest_1.py
│   │   │   │   └── zp_SaveWindowLayout_12_Works_5.py
│   │   │   ├── old
│   │   │   │   ├── grow_windows_01.bat
│   │   │   │   ├── grow_windows_01.py
│   │   │   │   ├── grow_windows_02.py
│   │   │   │   ├── tilewindows_01.py
│   │   │   │   ├── tilewindows_02.py
│   │   │   │   ├── tilewindows_03.py
│   │   │   │   ├── tilewindows_04.py
│   │   │   │   ├── tilewindows_05.py
│   │   │   │   ├── tilewindows_06.py
│   │   │   │   ├── tilewindows_08.py
│   │   │   │   └── tilewindows_08.py.1_2023.11.24
│   │   │   ├── window_manager
│   │   │   │   ├── _0_manager.py
│   │   │   │   ├── _1_fetch.py
│   │   │   │   ├── __init__.py
│   │   │   │   ├── main_operations.bat
│   │   │   │   ├── window_class.py
│   │   │   │   ├── window_enums.py
│   │   │   │   ├── window_operations.py
│   │   │   │   ├── window_utils.bat
│   │   │   │   ├── window_utils.py
│   │   │   │   ├── wip.bat
│   │   │   │   └── wip.py
│   │   │   ├── _OPEN_PROMPT_HERE.bat
│   │   │   ├── __init__.py
│   │   │   ├── cli.py
│   │   │   ├── log.py
│   │   │   ├── main.BAT
│   │   │   ├── main.py
│   │   │   ├── test.py
│   │   │   ├── utils.py
│   │   │   ├── window_data.json
│   │   │   └── wip_testing.py.1_2023.11.25
│   │   ├── mess2
│   │   │   ├── old
│   │   │   │   ├── completely_new_01.py
│   │   │   │   ├── completely_new_02.py
│   │   │   │   ├── completely_new_02.py.1_2023.11.25
│   │   │   │   ├── completely_new_03.bat
│   │   │   │   └── completely_new_03.py
│   │   │   ├── utils
│   │   │   │   ├── window_utils.py
│   │   │   │   └── window_utils.py.1_2023.11.25
│   │   │   ├── completely_new_04.bat
│   │   │   ├── completely_new_04.py
│   │   │   ├── main.py
│   │   │   ├── main.py.1_2023.11.25
│   │   │   ├── monitor.py
│   │   │   └── wip_testing.bat
│   │   ├── completely_new_04.py
│   │   ├── main.bat
│   │   ├── main.py
│   │   ├── monitor.py
│   │   ├── window.py
│   │   └── window_tiler.py
│   ├── .gitignore
│   ├── INITIALIZE_VENV.bat
│   ├── Jorn_WindowTiler.sublime-project
│   ├── Jorn_WindowTiler.sublime-workspace
│   ├── Notes_Temp - Roteliste med tilfeldige notater.txt
│   └── requirements.txt
├── Jorn_WindowTiler_v2
│   ├── __meta__
│   │   └── .cmd
│   │       ├── git-history-graph.bat
│   │       ├── py_venv_pip_devmode.bat
│   │       ├── py_venv_pip_install.bat
│   │       ├── py_venv_run_script.bat
│   │       ├── py_venv_terminal.bat
│   │       ├── py_venv_upgrade_requirements.bat
│   │       └── py_venv_write_requirements.bat
│   ├── src
│   │   ├── memory-bank
│   │   │   ├── 0-distilledContext.md
│   │   │   ├── 1-projectbrief.md
│   │   │   ├── 2-productContext.md
│   │   │   ├── 3-systemPatterns.md
│   │   │   ├── 4-techContext.md
│   │   │   ├── 5-activeContext.md
│   │   │   ├── 6-progress.md
│   │   │   └── 7-tasks.md
│   │   ├── mess
│   │   │   ├── _refs
│   │   │   │   ├── CONFIG_TEMPLATE_01.ini
│   │   │   │   ├── WinWinLayoutManager_3_moresimplifying.py
│   │   │   │   ├── WinWinLayoutManager_4_New.py
│   │   │   │   ├── WinWinLayoutManager_5_5.py
│   │   │   │   ├── configFile_reference_1.json
│   │   │   │   ├── configFile_reference_2.json
│   │   │   │   ├── explorercmd.py
│   │   │   │   ├── folder_structure.py
│   │   │   │   ├── miniTest_1.py
│   │   │   │   └── zp_SaveWindowLayout_12_Works_5.py
│   │   │   ├── old
│   │   │   │   ├── grow_windows_01.bat
│   │   │   │   ├── grow_windows_01.py
│   │   │   │   ├── grow_windows_02.py
│   │   │   │   ├── tilewindows_01.py
│   │   │   │   ├── tilewindows_02.py
│   │   │   │   ├── tilewindows_03.py
│   │   │   │   ├── tilewindows_04.py
│   │   │   │   ├── tilewindows_05.py
│   │   │   │   ├── tilewindows_06.py
│   │   │   │   ├── tilewindows_08.py
│   │   │   │   └── tilewindows_08.py.1_2023.11.24
│   │   │   ├── window_manager
│   │   │   │   ├── _0_manager.py
│   │   │   │   ├── _1_fetch.py
│   │   │   │   ├── __init__.py
│   │   │   │   ├── main_operations.bat
│   │   │   │   ├── window_class.py
│   │   │   │   ├── window_enums.py
│   │   │   │   ├── window_operations.py
│   │   │   │   ├── window_utils.bat
│   │   │   │   ├── window_utils.py
│   │   │   │   ├── wip.bat
│   │   │   │   └── wip.py
│   │   │   ├── _OPEN_PROMPT_HERE.bat
│   │   │   ├── __init__.py
│   │   │   ├── cli.py
│   │   │   ├── log.py
│   │   │   ├── main.BAT
│   │   │   ├── main.py
│   │   │   ├── test.py
│   │   │   ├── utils.py
│   │   │   ├── window_data.json
│   │   │   └── wip_testing.py.1_2023.11.25
│   │   ├── mess2
│   │   │   ├── old
│   │   │   │   ├── completely_new_01.py
│   │   │   │   ├── completely_new_02.py
│   │   │   │   ├── completely_new_02.py.1_2023.11.25
│   │   │   │   ├── completely_new_03.bat
│   │   │   │   └── completely_new_03.py
│   │   │   ├── utils
│   │   │   │   ├── window_utils.py
│   │   │   │   └── window_utils.py.1_2023.11.25
│   │   │   ├── completely_new_04.bat
│   │   │   ├── completely_new_04.py
│   │   │   ├── main.py
│   │   │   ├── main.py.1_2023.11.25
│   │   │   ├── monitor.py
│   │   │   └── wip_testing.bat
│   │   ├── window_tiler
│   │   │   ├── core
│   │   │   │   ├── __init__.py
│   │   │   │   ├── monitor.py
│   │   │   │   ├── tiler.py
│   │   │   │   ├── types.py
│   │   │   │   └── window.py
│   │   │   ├── docs
│   │   │   │   ├── examples.md
│   │   │   │   └── guide.md
│   │   │   ├── README.md
│   │   │   ├── __init__.py
│   │   │   ├── main.py
│   │   │   └── utils.py
│   │   ├── completely_new_04.py
│   │   ├── main.bat
│   │   ├── main.py
│   │   ├── monitor.py
│   │   ├── window.py
│   │   ├── window_enums.py
│   │   ├── window_tiler.py
│   │   └── window_utils.py
│   ├── .cursorignore
│   ├── .cursorrules
│   ├── .gitignore
│   ├── .vscodeignore
│   ├── Jorn_WindowTiler.sublime-project
│   ├── Notes_Temp - Roteliste med tilfeldige notater.txt
│   ├── py_venv_init.bat
│   └── requirements.txt
├── Jorn_WindowTiler_v3
│   ├── __meta__
│   │   └── .cmd
│   │       ├── git-history-graph.bat
│   │       ├── py_venv_pip_devmode.bat
│   │       ├── py_venv_pip_install.bat
│   │       ├── py_venv_run_script.bat
│   │       ├── py_venv_terminal.bat
│   │       ├── py_venv_upgrade_requirements.bat
│   │       └── py_venv_write_requirements.bat
│   ├── src
│   │   ├── window_tiler.bat
│   │   ├── window_tiler.py
│   │   ├── window_tiler.py.001
│   │   ├── window_tiler.py.002
│   │   ├── window_tiler.py.003
│   │   ├── window_tiler.py.004
│   │   ├── window_tiler.py.005
│   │   ├── window_tiler.py.006
│   │   ├── window_tiler.py.007
│   │   ├── window_tiler.py.008
│   │   ├── window_tiler.py.009
│   │   ├── window_tiler.py.010
│   │   └── window_tiler.py.011
│   ├── .gitignore
│   ├── Jorn_WindowTiler.sublime-project
│   ├── Jorn_WindowTiler.sublime-workspace
│   ├── py_venv_init.bat
│   └── requirements.txt
├── Jorn_WindowTiler_v4-skipped
│   ├── __meta__
│   │   └── .cmd
│   │       ├── git-history-graph.bat
│   │       ├── py_venv_pip_devmode.bat
│   │       ├── py_venv_pip_install.bat
│   │       ├── py_venv_run_script.bat
│   │       ├── py_venv_terminal.bat
│   │       ├── py_venv_upgrade_requirements.bat
│   │       └── py_venv_write_requirements.bat
│   ├── memory-bank
│   │   ├── 0-distilledContext.md
│   │   ├── 1-projectbrief.md
│   │   ├── 2-productContext.md
│   │   ├── 3-systemPatterns.md
│   │   ├── 4-techContext.md
│   │   ├── 5-activeContext.md
│   │   ├── 6-progress.md
│   │   ├── 7-tasks.md
│   │   ├── 8-highImpactEnhancement.md
│   │   └── 9-classBasedArchitecture.md
│   ├── src
│   │   ├── window_tiler
│   │   │   ├── __init__.py
│   │   │   ├── layout_manager.py
│   │   │   ├── models.py
│   │   │   ├── monitor_manager.py
│   │   │   ├── user_interface.py
│   │   │   ├── window_manager.py
│   │   │   └── window_tiler_app.py
│   │   ├── main.py
│   │   └── window_tiler.py
│   ├── .gitignore
│   ├── Jorn_WindowTiler.sublime-project
│   ├── py_venv_init.bat
│   └── requirements.txt
├── Jorn_WindowTiler_v5
│   ├── __meta__
│   │   └── .cmd
│   │       ├── git-history-graph.bat
│   │       ├── py_venv_pip_devmode.bat
│   │       ├── py_venv_pip_install.bat
│   │       ├── py_venv_run_script.bat
│   │       ├── py_venv_terminal.bat
│   │       ├── py_venv_upgrade_requirements.bat
│   │       └── py_venv_write_requirements.bat
│   ├── .gitignore
│   ├── Jorn_WindowTiler_v5.sublime-project
│   ├── Jorn_WindowTiler_v5.sublime-workspace
│   ├── README.md
│   ├── example_custom_config.py
│   ├── py_venv_init.bat
│   └── requirements.txt
├── Jorn_WindowTiler_v6.md
├── WindowTiler.sublime-project
└── WindowTiler.sublime-workspace