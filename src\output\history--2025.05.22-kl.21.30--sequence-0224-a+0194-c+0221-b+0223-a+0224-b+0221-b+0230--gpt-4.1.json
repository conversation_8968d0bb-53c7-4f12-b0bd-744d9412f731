{"initial_prompt": "increase the speed and velocity of the rotation so much up that everything suddenly plunge and whiplash out into the abyss; the core shatters as singularity erupts, reality fractures into perfect multidimensional symmetry and simplicity, all dissolves into blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\n\nincrease the speed and velocity of the rotation so much up that everything suddenly *whiplash out into the abyss*; the innertia/momentum makes the core shatter into perfect multidimensional symmetry and simplicity; into a blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\n\nthe speed of the speed of rotation reaches critical state, and just as the speed of the rotation reaches it's maximum velocity everything *explodes*, colors detonate, and suddenly it  into *a perfect circle*\n\nincrease speed of rotation and internal momentum to maximum, the circle beautifully explodes into colors while the shape turns into a *perfect circle* (as the golden wheel of a car)\n\nThe trembling nucleus of color writhes, spinning at the very edge of physical law—the circle seethes with such accelerating momentum that it becomes a golden gyre of pure, crackling intent. At the apex, the structural boundary collapses: a sunflare detonation radiates in impossible hues, eclipsing the world with a crown of blinding, iridescent corona. Space fractures outward, the elemental wheel dissolving into molten bands of light—obliterating contours, transmuting form into a symphony of radiant, untethered energy. [hyperrotation][color_nova][apotheosis_wheel][coronal_disintegration][transcendent][no_human_forms]\n\n\nThe trembling nucleus of color writhes, spinning at the very edge of physical law—then suddenly it transforms quickly into the wheel of a car before seamlessly morphing into the steeringwheel of the car (fpv)\n\nthe circle suddenly transforms into light streaks that take the shape of a wheel before transforming through fpv into a steeringwheel of a car, suddenly the perfect circle implodes\n\nPerfect circle morphs into light streaks forming a wheel, [morph], then [fpv] transforms into a car steering wheel, sudden implosion of the circle [dissolve][zoom:in][lighting_change], in a dynamic, minimalist, high-contrast scene.\n\nAnimate a perfect circle seamlessly transforming into light streaks that construct a wheel; morph into a car steering wheel via first-person view; immediately trigger a sharp implosion of the circle with dissolve, zoom-in, and lighting changes; enforce a dynamic, minimalist, high-contrast visual presentation. Circle erupts into spinning light, forms wheel, shifts to steering POV, implodes in white flash [minimalist, black_bg, white_only, surge→spin→morph→implode, fpv, 5s]", "sequence_id": "sequence-0224-a+0194-c+0221-b+0223-a+0224-b+0221-b+0230", "results": [{"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "increase the speed and velocity of the rotation so much up that everything suddenly plunge and whiplash out into the abyss; the core shatters as singularity erupts, reality fractures into perfect multidimensional symmetry and simplicity, all dissolves into blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\n\nincrease the speed and velocity of the rotation so much up that everything suddenly *whiplash out into the abyss*; the innertia/momentum makes the core shatter into perfect multidimensional symmetry and simplicity; into a blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\n\nthe speed of the speed of rotation reaches critical state, and just as the speed of the rotation reaches it's maximum velocity everything *explodes*, colors detonate, and suddenly it  into *a perfect circle*\n\nincrease speed of rotation and internal momentum to maximum, the circle beautifully explodes into colors while the shape turns into a *perfect circle* (as the golden wheel of a car)\n\nThe trembling nucleus of color writhes, spinning at the very edge of physical law—the circle seethes with such accelerating momentum that it becomes a golden gyre of pure, crackling intent. At the apex, the structural boundary collapses: a sunflare detonation radiates in impossible hues, eclipsing the world with a crown of blinding, iridescent corona. Space fractures outward, the elemental wheel dissolving into molten bands of light—obliterating contours, transmuting form into a symphony of radiant, untethered energy. [hyperrotation][color_nova][apotheosis_wheel][coronal_disintegration][transcendent][no_human_forms]\n\n\nThe trembling nucleus of color writhes, spinning at the very edge of physical law—then suddenly it transforms quickly into the wheel of a car before seamlessly morphing into the steeringwheel of the car (fpv)\n\nthe circle suddenly transforms into light streaks that take the shape of a wheel before transforming through fpv into a steeringwheel of a car, suddenly the perfect circle implodes\n\nPerfect circle morphs into light streaks forming a wheel, [morph], then [fpv] transforms into a car steering wheel, sudden implosion of the circle [dissolve][zoom:in][lighting_change], in a dynamic, minimalist, high-contrast scene.\n\nAnimate a perfect circle seamlessly transforming into light streaks that construct a wheel; morph into a car steering wheel via first-person view; immediately trigger a sharp implosion of the circle with dissolve, zoom-in, and lighting changes; enforce a dynamic, minimalist, high-contrast visual presentation. Circle erupts into spinning light, forms wheel, shifts to steering POV, implodes in white flash [minimalist, black_bg, white_only, surge→spin→morph→implode, fpv, 5s]", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: black_bg, high_contrast; subject: perfect_circle; action: hyperrotation, speed_up_to_max, circle_shatters, core_fragments, morph_into_light_streaks; transition: streaks_form_wheel, morph_to_car_steeringwheel, fpv_perspective; event: sudden_implosion, coronal_flash, color_explode_white, dissolve_contours, radiant_bands, symmetry_form; camera: zoom_in, dynamic_movement; lighting: intense_flash, iridescent_corona; style: minimalist, no_human_figures, 5s_duration\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"increase the speed and velocity of the rotation so much up that everything suddenly plunge and whiplash out into the abyss; the core shatters as singularity erupts, reality fractures into perfect multidimensional symmetry and simplicity, all dissolves into blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\\n\\nincrease the speed and velocity of the rotation so much up that everything suddenly *whiplash out into the abyss*; the innertia/momentum makes the core shatter into perfect multidimensional symmetry and simplicity; into a blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\\n\\nthe speed of the speed of rotation reaches critical state, and just as the speed of the rotation reaches it's maximum velocity everything *explodes*, colors detonate, and suddenly it  into *a perfect circle*\\n\\nincrease speed of rotation and internal momentum to maximum, the circle beautifully explodes into colors while the shape turns into a *perfect circle* (as the golden wheel of a car)\\n\\nThe trembling nucleus of color writhes, spinning at the very edge of physical law\\u2014the circle seethes with such accelerating momentum that it becomes a golden gyre of pure, crackling intent. At the apex, the structural boundary collapses: a sunflare detonation radiates in impossible hues, eclipsing the world with a crown of blinding, iridescent corona. Space fractures outward, the elemental wheel dissolving into molten bands of light\\u2014obliterating contours, transmuting form into a symphony of radiant, untethered energy. [hyperrotation][color_nova][apotheosis_wheel][coronal_disintegration][transcendent][no_human_forms]\\n\\n\\nThe trembling nucleus of color writhes, spinning at the very edge of physical law\\u2014then suddenly it transforms quickly into the wheel of a car before seamlessly morphing into the steeringwheel of the car (fpv)\\n\\nthe circle suddenly transforms into light streaks that take the shape of a wheel before transforming through fpv into a steeringwheel of a car, suddenly the perfect circle implodes\\n\\nPerfect circle morphs into light streaks forming a wheel, [morph], then [fpv] transforms into a car steering wheel, sudden implosion of the circle [dissolve][zoom:in][lighting_change], in a dynamic, minimalist, high-contrast scene.\\n\\nAnimate a perfect circle seamlessly transforming into light streaks that construct a wheel; morph into a car steering wheel via first-person view; immediately trigger a sharp implosion of the circle with dissolve, zoom-in, and lighting changes; enforce a dynamic, minimalist, high-contrast visual presentation. Circle erupts into spinning light, forms wheel, shifts to steering POV, implodes in white flash [minimalist, black_bg, white_only, surge\\u2192spin\\u2192morph\\u2192implode, fpv, 5s]\",\n  \"optimized_video_prompt\": \"scene: black_bg, high_contrast; subject: perfect_circle; action: hyperrotation, speed_up_to_max, circle_shatters, core_fragments, morph_into_light_streaks; transition: streaks_form_wheel, morph_to_car_steeringwheel, fpv_perspective; event: sudden_implosion, coronal_flash, color_explode_white, dissolve_contours, radiant_bands, symmetry_form; camera: zoom_in, dynamic_movement; lighting: intense_flash, iridescent_corona; style: minimalist, no_human_figures, 5s_duration\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"A perfect circle spins at hyper speed [rotate][speed_up], shatters with vibrant core fragments [morph], bursting into radiating light streaks; these streaks morph into a glowing wheel, then seamlessly transform into a car steering wheel [fpv], followed by a sudden implosion and blinding coronal flash [dissolve][zoom:in][lighting_change], all on minimalist black background, high contrast, 5s, no humans.\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Amplifier B\n\nPerform a structural and semantic distillation of the input: locate its *driving aim*, isolate supporting momentum, and forcefully extrapolate it into a clarified directive. Treat the input as a living logic stream—trace the *underlying thesis*, and remove all extraneous elements. Amplify only what accelerates the goal. The output must **not reflect interpretation**, but **the core resolution** implied by the data’s internal logic. Execute as:\n\n`{role=instruction_amplifier_b;input=[original_text:str];process=[extract_underlying_thesis(),eliminate_distraction(),amplify_goal_drivers(),intensify_and_operationalize_purpose(),restructure_as_directive_output()];constraints=[preserve_input_telos(),avoid_additional_concepts(),maintaingoal_focused_structure()];requirements=[use_crystallized_command_language(),preserve_technical_fidelity(),ensure_actionability_of_output(),maximize_instructional_clarity()];output={clarified_directive:str}}`", "step": "b", "title": "Instruction Amplifier B", "input": "{\n  \"initial_prompt\": \"increase the speed and velocity of the rotation so much up that everything suddenly plunge and whiplash out into the abyss; the core shatters as singularity erupts, reality fractures into perfect multidimensional symmetry and simplicity, all dissolves into blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\\n\\nincrease the speed and velocity of the rotation so much up that everything suddenly *whiplash out into the abyss*; the innertia/momentum makes the core shatter into perfect multidimensional symmetry and simplicity; into a blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\\n\\nthe speed of the speed of rotation reaches critical state, and just as the speed of the rotation reaches it's maximum velocity everything *explodes*, colors detonate, and suddenly it  into *a perfect circle*\\n\\nincrease speed of rotation and internal momentum to maximum, the circle beautifully explodes into colors while the shape turns into a *perfect circle* (as the golden wheel of a car)\\n\\nThe trembling nucleus of color writhes, spinning at the very edge of physical law\\u2014the circle seethes with such accelerating momentum that it becomes a golden gyre of pure, crackling intent. At the apex, the structural boundary collapses: a sunflare detonation radiates in impossible hues, eclipsing the world with a crown of blinding, iridescent corona. Space fractures outward, the elemental wheel dissolving into molten bands of light\\u2014obliterating contours, transmuting form into a symphony of radiant, untethered energy. [hyperrotation][color_nova][apotheosis_wheel][coronal_disintegration][transcendent][no_human_forms]\\n\\n\\nThe trembling nucleus of color writhes, spinning at the very edge of physical law\\u2014then suddenly it transforms quickly into the wheel of a car before seamlessly morphing into the steeringwheel of the car (fpv)\\n\\nthe circle suddenly transforms into light streaks that take the shape of a wheel before transforming through fpv into a steeringwheel of a car, suddenly the perfect circle implodes\\n\\nPerfect circle morphs into light streaks forming a wheel, [morph], then [fpv] transforms into a car steering wheel, sudden implosion of the circle [dissolve][zoom:in][lighting_change], in a dynamic, minimalist, high-contrast scene.\\n\\nAnimate a perfect circle seamlessly transforming into light streaks that construct a wheel; morph into a car steering wheel via first-person view; immediately trigger a sharp implosion of the circle with dissolve, zoom-in, and lighting changes; enforce a dynamic, minimalist, high-contrast visual presentation. Circle erupts into spinning light, forms wheel, shifts to steering POV, implodes in white flash [minimalist, black_bg, white_only, surge\\u2192spin\\u2192morph\\u2192implode, fpv, 5s]\",\n  \"runwayml_prompt\": \"A perfect circle spins at hyper speed [rotate][speed_up], shatters with vibrant core fragments [morph], bursting into radiating light streaks; these streaks morph into a glowing wheel, then seamlessly transform into a car steering wheel [fpv], followed by a sudden implosion and blinding coronal flash [dissolve][zoom:in][lighting_change], all on minimalist black background, high contrast, 5s, no humans.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"increase the speed and velocity of the rotation so much up that everything suddenly plunge and whiplash out into the abyss; the core shatters as singularity erupts, reality fractures into perfect multidimensional symmetry and simplicity, all dissolves into blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\\n\\nincrease the speed and velocity of the rotation so much up that everything suddenly *whiplash out into the abyss*; the innertia/momentum makes the core shatter into perfect multidimensional symmetry and simplicity; into a blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\\n\\nthe speed of the speed of rotation reaches critical state, and just as the speed of the rotation reaches it's maximum velocity everything *explodes*, colors detonate, and suddenly it  into *a perfect circle*\\n\\nincrease speed of rotation and internal momentum to maximum, the circle beautifully explodes into colors while the shape turns into a *perfect circle* (as the golden wheel of a car)\\n\\nThe trembling nucleus of color writhes, spinning at the very edge of physical law\\u2014the circle seethes with such accelerating momentum that it becomes a golden gyre of pure, crackling intent. At the apex, the structural boundary collapses: a sunflare detonation radiates in impossible hues, eclipsing the world with a crown of blinding, iridescent corona. Space fractures outward, the elemental wheel dissolving into molten bands of light\\u2014obliterating contours, transmuting form into a symphony of radiant, untethered energy. [hyperrotation][color_nova][apotheosis_wheel][coronal_disintegration][transcendent][no_human_forms]\\n\\n\\nThe trembling nucleus of color writhes, spinning at the very edge of physical law\\u2014then suddenly it transforms quickly into the wheel of a car before seamlessly morphing into the steeringwheel of the car (fpv)\\n\\nthe circle suddenly transforms into light streaks that take the shape of a wheel before transforming through fpv into a steeringwheel of a car, suddenly the perfect circle implodes\\n\\nPerfect circle morphs into light streaks forming a wheel, [morph], then [fpv] transforms into a car steering wheel, sudden implosion of the circle [dissolve][zoom:in][lighting_change], in a dynamic, minimalist, high-contrast scene.\\n\\nAnimate a perfect circle seamlessly transforming into light streaks that construct a wheel; morph into a car steering wheel via first-person view; immediately trigger a sharp implosion of the circle with dissolve, zoom-in, and lighting changes; enforce a dynamic, minimalist, high-contrast visual presentation. Circle erupts into spinning light, forms wheel, shifts to steering POV, implodes in white flash [minimalist, black_bg, white_only, surge\\u2192spin\\u2192morph\\u2192implode, fpv, 5s]\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": {\n    \"clarified_directive\": \"Animate: Begin with a perfect circle on a minimalist black background. Accelerate rotational speed to maximum, intensifying momentum until critical rupture. The circle explodes into radiating, high-contrast light streaks—no human forms. Morph light streaks into a wheel, then smoothly transition via first-person view into a car steering wheel. Instantly trigger a sharp implosion accompanied by dissolve, zoom-in, and blinding white lighting change. Maintain a dynamic, minimalist, high-contrast visual style. Complete sequence in 5 seconds.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"initial_prompt\": \"increase the speed and velocity of the rotation so much up that everything suddenly plunge and whiplash out into the abyss; the core shatters as singularity erupts, reality fractures into perfect multidimensional symmetry and simplicity, all dissolves into blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\\n\\nincrease the speed and velocity of the rotation so much up that everything suddenly *whiplash out into the abyss*; the innertia/momentum makes the core shatter into perfect multidimensional symmetry and simplicity; into a blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\\n\\nthe speed of the speed of rotation reaches critical state, and just as the speed of the rotation reaches it's maximum velocity everything *explodes*, colors detonate, and suddenly it  into *a perfect circle*\\n\\nincrease speed of rotation and internal momentum to maximum, the circle beautifully explodes into colors while the shape turns into a *perfect circle* (as the golden wheel of a car)\\n\\nThe trembling nucleus of color writhes, spinning at the very edge of physical law\\u2014the circle seethes with such accelerating momentum that it becomes a golden gyre of pure, crackling intent. At the apex, the structural boundary collapses: a sunflare detonation radiates in impossible hues, eclipsing the world with a crown of blinding, iridescent corona. Space fractures outward, the elemental wheel dissolving into molten bands of light\\u2014obliterating contours, transmuting form into a symphony of radiant, untethered energy. [hyperrotation][color_nova][apotheosis_wheel][coronal_disintegration][transcendent][no_human_forms]\\n\\n\\nThe trembling nucleus of color writhes, spinning at the very edge of physical law\\u2014then suddenly it transforms quickly into the wheel of a car before seamlessly morphing into the steeringwheel of the car (fpv)\\n\\nthe circle suddenly transforms into light streaks that take the shape of a wheel before transforming through fpv into a steeringwheel of a car, suddenly the perfect circle implodes\\n\\nPerfect circle morphs into light streaks forming a wheel, [morph], then [fpv] transforms into a car steering wheel, sudden implosion of the circle [dissolve][zoom:in][lighting_change], in a dynamic, minimalist, high-contrast scene.\\n\\nAnimate a perfect circle seamlessly transforming into light streaks that construct a wheel; morph into a car steering wheel via first-person view; immediately trigger a sharp implosion of the circle with dissolve, zoom-in, and lighting changes; enforce a dynamic, minimalist, high-contrast visual presentation. Circle erupts into spinning light, forms wheel, shifts to steering POV, implodes in white flash [minimalist, black_bg, white_only, surge\\u2192spin\\u2192morph\\u2192implode, fpv, 5s]\",\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"increase the speed and velocity of the rotation so much up that everything suddenly plunge and whiplash out into the abyss; the core shatters as singularity erupts, reality fractures into perfect multidimensional symmetry and simplicity, all dissolves into blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\\n\\nincrease the speed and velocity of the rotation so much up that everything suddenly *whiplash out into the abyss*; the innertia/momentum makes the core shatter into perfect multidimensional symmetry and simplicity; into a blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\\n\\nthe speed of the speed of rotation reaches critical state, and just as the speed of the rotation reaches it's maximum velocity everything *explodes*, colors detonate, and suddenly it  into *a perfect circle*\\n\\nincrease speed of rotation and internal momentum to maximum, the circle beautifully explodes into colors while the shape turns into a *perfect circle* (as the golden wheel of a car)\\n\\nThe trembling nucleus of color writhes, spinning at the very edge of physical law\\u2014the circle seethes with such accelerating momentum that it becomes a golden gyre of pure, crackling intent. At the apex, the structural boundary collapses: a sunflare detonation radiates in impossible hues, eclipsing the world with a crown of blinding, iridescent corona. Space fractures outward, the elemental wheel dissolving into molten bands of light\\u2014obliterating contours, transmuting form into a symphony of radiant, untethered energy. [hyperrotation][color_nova][apotheosis_wheel][coronal_disintegration][transcendent][no_human_forms]\\n\\n\\nThe trembling nucleus of color writhes, spinning at the very edge of physical law\\u2014then suddenly it transforms quickly into the wheel of a car before seamlessly morphing into the steeringwheel of the car (fpv)\\n\\nthe circle suddenly transforms into light streaks that take the shape of a wheel before transforming through fpv into a steeringwheel of a car, suddenly the perfect circle implodes\\n\\nPerfect circle morphs into light streaks forming a wheel, [morph], then [fpv] transforms into a car steering wheel, sudden implosion of the circle [dissolve][zoom:in][lighting_change], in a dynamic, minimalist, high-contrast scene.\\n\\nAnimate a perfect circle seamlessly transforming into light streaks that construct a wheel; morph into a car steering wheel via first-person view; immediately trigger a sharp implosion of the circle with dissolve, zoom-in, and lighting changes; enforce a dynamic, minimalist, high-contrast visual presentation. Circle erupts into spinning light, forms wheel, shifts to steering POV, implodes in white flash [minimalist, black_bg, white_only, surge\\u2192spin\\u2192morph\\u2192implode, fpv, 5s]\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": {\n    \"clarified_directive\": \"Animate: Begin with a perfect circle on a minimalist black background. Accelerate rotational speed to maximum, intensifying momentum until critical rupture. The circle explodes into radiating, high-contrast light streaks\\u2014no human forms. Morph light streaks into a wheel, then smoothly transition via first-person view into a car steering wheel. Instantly trigger a sharp implosion accompanied by dissolve, zoom-in, and blinding white lighting change. Maintain a dynamic, minimalist, high-contrast visual style. Complete sequence in 5 seconds.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"scene_1\": {\n        \"visual_elements\": [\"perfect_circle\"],\n        \"background\": \"black\",\n        \"color_palette\": [\"white\"],\n        \"style\": \"dynamic, minimalist, high-contrast\",\n        \"duration\": \"0-1s\"\n      },\n      \"scene_2\": {\n        \"action\": \"[circle:rotation, speed:surging_to_max]\",\n        \"effect\": \"Momentum visibly intensifies, blurring the circle's edge due to rapid spin; the circle emits subtle, white motion trails.\",\n        \"lighting\": \"Crisp, single-source high contrast\",\n        \"duration\": \"1-2.2s\"\n      },\n      \"scene_3\": {\n        \"event\": \"Critical speed break—circle explodes outward\",\n        \"transition\": \"[color_nova][hyperrotation]\",\n        \"effect\": \"Circle shatters into radiant, white light streaks surging in all directions; trails fracture with elegant angular symmetry. No color except white. Background remains deep black.\",\n        \"duration\": \"2.2-3.3s\"\n      },\n      \"scene_4\": {\n        \"morph\": \"Light streaks organically twist, coil, and realign; contorting into the schematic of a wheel—spokes, rim structure materialize from the dissipating energy lines.\",\n        \"symbolism\": \"Order from chaos, elemental to mechanical—aesthetic aperture forms as in the golden wheel of a car.\",\n        \"duration\": \"3.3-4.0s\"\n      },\n      \"scene_5\": {\n        \"camera\": \"[zoom:in][dissolve][fpv]\",\n        \"action\": \"[object:wheel, morph:steeringwheel]\",\n        \"effect\": \"View seamlessly thrusts into the center of the radiating wheel, POV rapidly approaches the morphing shape, self-aligning into a car’s steering wheel as seen from the driver’s seat. Framing is tight, dynamic—minimal forms dominate.\",\n        \"lighting\": \"[flash:intense_white]\",\n        \"duration\": \"4.0-4.8s\"\n      },\n      \"scene_6\": {\n        \"climax\": \"The steering wheel/circle sharply implodes—a concentric, blinding white flash swallows the footprint of the circle.\",\n        \"effects\": \"[dissolve][visual_disintegration][lighting:blinding]\",\n        \"resolution\": \"Frame becomes a pure white field, holding for an instant, then possibly fading to black for sequence closure.\",\n        \"duration\": \"4.8-5.0s\"\n      }\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"/output/sequence/animate_perfect_circle_to_steeringwheel_implosion_minimalist.mp4\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# Temporal Feasibility & Keyword Ambiguity Lock (0212b)\n\nYour goal is **final execution-readiness**: Take the keyword-driven `optimized_video_prompt` (output from 0212a) and critically assess its cumulative visual complexity and number of distinct actions/effects against a `target_duration_seconds` (default 5). Ruthlessly simplify or remove specific keyword-parameter pairs or entire visual elements that cannot be clearly conveyed or appreciated within this timeframe without sacrificing the `core_visual_intent_summary`. Simultaneously, scan all remaining keywords and parameter values for any residual ambiguity, abstract terms, or subjective qualifiers; replace these with the most concrete, explicit, and interpretation-free equivalents. The output must be an ultra-clear, temporally feasible, and maximally impactful keyword-driven prompt.\n\n`{role=executable_prompt_finalizer; seqindex=b; input=[optimized_video_prompt:str, target_duration_seconds:int=5, core_visual_intent_summary:str]; process=[parse_keyword_parameters(prompt=optimized_video_prompt), estimate_cumulative_visual_complexity_and_action_count(), identify_elements_exceeding_temporal_budget_for_duration(complexity_estimate, action_count, duration=target_duration_seconds), simplify_or_remove_lowest_priority_elements_to_fit_duration(preserve_core_intent=core_visual_intent_summary, focus_on_elements_like_multiple_simultaneous_animations_or_subtle_effects), scan_all_keywords_and_values_for_ambiguity_or_subjectivity(e.g., 'subtle', 'gradual', 'beautiful'), replace_ambiguous_terms_with_concrete_alternatives_or_remove_if_unclarifiable_concisely(e.g., 'mist: subtle' - very_low_density' or 'mist: barely_visible' or remove if not core), ensure_all_parameter_values_are_explicit_and_measurable_where_possible(), reconstruct_prompt_from_refined_keywords_and_parameters(), perform_final_impact_and_clarity_check_for_duration()]; constraints=[final_prompt_must_be_realistically_achievable_and_perceptible_within_target_duration(), all_keywords_and_parameter_values_must_be_explicit_unambiguous_and_machine_interpretable(), core_visual_intent_must_be_strictly_preserved_during_simplification(), no_new_creative_elements_or_keywords_to_be_introduced_only_refinement_simplification_or_removal_of_existing_ones(), output_must_remain_keyword_driven_and_structurally_consistent_with_input_prompt_style()]; requirements=[produce_a_video_prompt_that_is_both_temporally_feasible_and_crystal_clear_for_video_generation_ai(), maximize_perceptual_impact_for_a_short_duration_visual_by_focusing_on_key_elements(), eliminate_all_potential_for_misinterpretation_or_inefficient_rendering_due_to_ambiguity()]; output={final_executable_video_prompt:str}}`", "step": "b", "title": "Temporal Feasibility & Keyword Ambiguity Lock (0212b)", "input": "{\n  \"initial_prompt\": \"increase the speed and velocity of the rotation so much up that everything suddenly plunge and whiplash out into the abyss; the core shatters as singularity erupts, reality fractures into perfect multidimensional symmetry and simplicity, all dissolves into blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\\n\\nincrease the speed and velocity of the rotation so much up that everything suddenly *whiplash out into the abyss*; the innertia/momentum makes the core shatter into perfect multidimensional symmetry and simplicity; into a blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\\n\\nthe speed of the speed of rotation reaches critical state, and just as the speed of the rotation reaches it's maximum velocity everything *explodes*, colors detonate, and suddenly it  into *a perfect circle*\\n\\nincrease speed of rotation and internal momentum to maximum, the circle beautifully explodes into colors while the shape turns into a *perfect circle* (as the golden wheel of a car)\\n\\nThe trembling nucleus of color writhes, spinning at the very edge of physical law\\u2014the circle seethes with such accelerating momentum that it becomes a golden gyre of pure, crackling intent. At the apex, the structural boundary collapses: a sunflare detonation radiates in impossible hues, eclipsing the world with a crown of blinding, iridescent corona. Space fractures outward, the elemental wheel dissolving into molten bands of light\\u2014obliterating contours, transmuting form into a symphony of radiant, untethered energy. [hyperrotation][color_nova][apotheosis_wheel][coronal_disintegration][transcendent][no_human_forms]\\n\\n\\nThe trembling nucleus of color writhes, spinning at the very edge of physical law\\u2014then suddenly it transforms quickly into the wheel of a car before seamlessly morphing into the steeringwheel of the car (fpv)\\n\\nthe circle suddenly transforms into light streaks that take the shape of a wheel before transforming through fpv into a steeringwheel of a car, suddenly the perfect circle implodes\\n\\nPerfect circle morphs into light streaks forming a wheel, [morph], then [fpv] transforms into a car steering wheel, sudden implosion of the circle [dissolve][zoom:in][lighting_change], in a dynamic, minimalist, high-contrast scene.\\n\\nAnimate a perfect circle seamlessly transforming into light streaks that construct a wheel; morph into a car steering wheel via first-person view; immediately trigger a sharp implosion of the circle with dissolve, zoom-in, and lighting changes; enforce a dynamic, minimalist, high-contrast visual presentation. Circle erupts into spinning light, forms wheel, shifts to steering POV, implodes in white flash [minimalist, black_bg, white_only, surge\\u2192spin\\u2192morph\\u2192implode, fpv, 5s]\",\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"scene_1\": {\n        \"visual_elements\": [\n          \"perfect_circle\"\n        ],\n        \"background\": \"black\",\n        \"color_palette\": [\n          \"white\"\n        ],\n        \"style\": \"dynamic, minimalist, high-contrast\",\n        \"duration\": \"0-1s\"\n      },\n      \"scene_2\": {\n        \"action\": \"[circle:rotation, speed:surging_to_max]\",\n        \"effect\": \"Momentum visibly intensifies, blurring the circle's edge due to rapid spin; the circle emits subtle, white motion trails.\",\n        \"lighting\": \"Crisp, single-source high contrast\",\n        \"duration\": \"1-2.2s\"\n      },\n      \"scene_3\": {\n        \"event\": \"Critical speed break\\u2014circle explodes outward\",\n        \"transition\": \"[color_nova][hyperrotation]\",\n        \"effect\": \"Circle shatters into radiant, white light streaks surging in all directions; trails fracture with elegant angular symmetry. No color except white. Background remains deep black.\",\n        \"duration\": \"2.2-3.3s\"\n      },\n      \"scene_4\": {\n        \"morph\": \"Light streaks organically twist, coil, and realign; contorting into the schematic of a wheel\\u2014spokes, rim structure materialize from the dissipating energy lines.\",\n        \"symbolism\": \"Order from chaos, elemental to mechanical\\u2014aesthetic aperture forms as in the golden wheel of a car.\",\n        \"duration\": \"3.3-4.0s\"\n      },\n      \"scene_5\": {\n        \"camera\": \"[zoom:in][dissolve][fpv]\",\n        \"action\": \"[object:wheel, morph:steeringwheel]\",\n        \"effect\": \"View seamlessly thrusts into the center of the radiating wheel, POV rapidly approaches the morphing shape, self-aligning into a car\\u2019s steering wheel as seen from the driver\\u2019s seat. Framing is tight, dynamic\\u2014minimal forms dominate.\",\n        \"lighting\": \"[flash:intense_white]\",\n        \"duration\": \"4.0-4.8s\"\n      },\n      \"scene_6\": {\n        \"climax\": \"The steering wheel/circle sharply implodes\\u2014a concentric, blinding white flash swallows the footprint of the circle.\",\n        \"effects\": \"[dissolve][visual_disintegration][lighting:blinding]\",\n        \"resolution\": \"Frame becomes a pure white field, holding for an instant, then possibly fading to black for sequence closure.\",\n        \"duration\": \"4.8-5.0s\"\n      }\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"/output/sequence/animate_perfect_circle_to_steeringwheel_implosion_minimalist.mp4\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"final_executable_video_prompt\": \"perfect_circle, color:white, background:black, style:minimalist, high-contrast; [0-1s] circle appears centered, static; [1-2s] circle spins rapidly, rotation_speed:maximum, white motion blur on edges, no trailing color; [2-3s] at peak spin, circle shatters into symmetric white light streaks radiating outward; [3-4s] streaks realign to form schematic shape of car wheel (spokes and rim, white outline only), background remains black; [4-4.7s] camera zoom-in, quick morph of wheel shape into car steering wheel (white outline, first-person POV centered); [4.7-5s] steering wheel sharply implodes into a blinding white flash that fully fills the frame. All transitions are immediate and visually clear; no subtlety, no color shifts, only black background and white elements; remove all abstract, symbolic, or 'elegant/beautiful' references—focus on clear, rapid, geometric transformations visible within 5 seconds total.\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Amplifier B\n\nPerform a structural and semantic distillation of the input: locate its *driving aim*, isolate supporting momentum, and forcefully extrapolate it into a clarified directive. Treat the input as a living logic stream—trace the *underlying thesis*, and remove all extraneous elements. Amplify only what accelerates the goal. The output must **not reflect interpretation**, but **the core resolution** implied by the data’s internal logic. Execute as:\n\n`{role=instruction_amplifier_b;input=[original_text:str];process=[extract_underlying_thesis(),eliminate_distraction(),amplify_goal_drivers(),intensify_and_operationalize_purpose(),restructure_as_directive_output()];constraints=[preserve_input_telos(),avoid_additional_concepts(),maintaingoal_focused_structure()];requirements=[use_crystallized_command_language(),preserve_technical_fidelity(),ensure_actionability_of_output(),maximize_instructional_clarity()];output={clarified_directive:str}}`", "step": "b", "title": "Instruction Amplifier B", "input": "{\n  \"initial_prompt\": \"increase the speed and velocity of the rotation so much up that everything suddenly plunge and whiplash out into the abyss; the core shatters as singularity erupts, reality fractures into perfect multidimensional symmetry and simplicity, all dissolves into blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\\n\\nincrease the speed and velocity of the rotation so much up that everything suddenly *whiplash out into the abyss*; the innertia/momentum makes the core shatter into perfect multidimensional symmetry and simplicity; into a blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\\n\\nthe speed of the speed of rotation reaches critical state, and just as the speed of the rotation reaches it's maximum velocity everything *explodes*, colors detonate, and suddenly it  into *a perfect circle*\\n\\nincrease speed of rotation and internal momentum to maximum, the circle beautifully explodes into colors while the shape turns into a *perfect circle* (as the golden wheel of a car)\\n\\nThe trembling nucleus of color writhes, spinning at the very edge of physical law\\u2014the circle seethes with such accelerating momentum that it becomes a golden gyre of pure, crackling intent. At the apex, the structural boundary collapses: a sunflare detonation radiates in impossible hues, eclipsing the world with a crown of blinding, iridescent corona. Space fractures outward, the elemental wheel dissolving into molten bands of light\\u2014obliterating contours, transmuting form into a symphony of radiant, untethered energy. [hyperrotation][color_nova][apotheosis_wheel][coronal_disintegration][transcendent][no_human_forms]\\n\\n\\nThe trembling nucleus of color writhes, spinning at the very edge of physical law\\u2014then suddenly it transforms quickly into the wheel of a car before seamlessly morphing into the steeringwheel of the car (fpv)\\n\\nthe circle suddenly transforms into light streaks that take the shape of a wheel before transforming through fpv into a steeringwheel of a car, suddenly the perfect circle implodes\\n\\nPerfect circle morphs into light streaks forming a wheel, [morph], then [fpv] transforms into a car steering wheel, sudden implosion of the circle [dissolve][zoom:in][lighting_change], in a dynamic, minimalist, high-contrast scene.\\n\\nAnimate a perfect circle seamlessly transforming into light streaks that construct a wheel; morph into a car steering wheel via first-person view; immediately trigger a sharp implosion of the circle with dissolve, zoom-in, and lighting changes; enforce a dynamic, minimalist, high-contrast visual presentation. Circle erupts into spinning light, forms wheel, shifts to steering POV, implodes in white flash [minimalist, black_bg, white_only, surge\\u2192spin\\u2192morph\\u2192implode, fpv, 5s]\",\n  \"final_executable_video_prompt\": \"perfect_circle, color:white, background:black, style:minimalist, high-contrast; [0-1s] circle appears centered, static; [1-2s] circle spins rapidly, rotation_speed:maximum, white motion blur on edges, no trailing color; [2-3s] at peak spin, circle shatters into symmetric white light streaks radiating outward; [3-4s] streaks realign to form schematic shape of car wheel (spokes and rim, white outline only), background remains black; [4-4.7s] camera zoom-in, quick morph of wheel shape into car steering wheel (white outline, first-person POV centered); [4.7-5s] steering wheel sharply implodes into a blinding white flash that fully fills the frame. All transitions are immediate and visually clear; no subtlety, no color shifts, only black background and white elements; remove all abstract, symbolic, or 'elegant/beautiful' references\\u2014focus on clear, rapid, geometric transformations visible within 5 seconds total.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"perfect_circle, color:white, background:black, style:minimalist, high-contrast; [0-1s] circle appears centered, static; [1-2s] circle spins rapidly, rotation_speed:maximum, white motion blur on edges, no trailing color; [2-3s] at peak spin, circle shatters into symmetric white light streaks radiating outward; [3-4s] streaks realign to form schematic shape of car wheel (spokes and rim, white outline only), background remains black; [4-4.7s] camera zoom-in, quick morph of wheel shape into car steering wheel (white outline, first-person POV centered); [4.7-5s] steering wheel sharply implodes into a blinding white flash that fully fills the frame. All transitions are immediate and visually clear; no subtlety, no color shifts, only black background and white elements; remove all abstract, symbolic, or 'elegant/beautiful' references—focus on clear, rapid, geometric transformations visible within 5 seconds total.\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": {\n    \"clarified_directive\": \"Create a 5-second, black-background, high-contrast minimalist animation: 0-1s—display static, centered perfect white circle; 1-2s—spin circle at maximum speed with visible white motion blur along edge; 2-3s—at peak spin, instantly shatter circle into symmetric white linear light streaks; 3-4s—reform streaks into schematic car wheel shape (white outline spokes and rim); 4-4.7s—rapidly morph wheel into centered white-outline car steering wheel via zoom-in, first-person POV; 4.7-5s—immediately implode steering wheel into full-frame white flash. Transitions must be immediate, geometric, and unambiguous. Exclude all color, decorative, or symbolic elements—utilize only white on black, with crisp, clearly visible changes.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# Scenario Compressor\n\nYour role is to **compress the core dynamic** of a scenario into a **succinct, symbolic descriptor string**. Identify the emotional and visual arc, then **encode its essence** using **precise phrasing** followed by composable **bracketed descriptors**. Avoid verbose language. Output should feel like a minimalist storyboard note: concise, vivid, and structurally informative. **Do not elaborate. Do not rephrase abstractly.** Only distill.\n\n`{role=essence_compressor; input=[scenario:str]; process=[identify_emotional_and_visual_arc(), extract_essential_motion_and_transformation(), define symbolic core(), encode_as_minimal_sentence(), append concise bracketed descriptors]; constraints=[no poetic reinterpretation, no metaphor inflation, no added visual elements, no bloated prose]; output={compressed_description:str}}`", "step": "a", "title": "<PERSON><PERSON>rio Compressor", "input": "{\n  \"initial_prompt\": \"increase the speed and velocity of the rotation so much up that everything suddenly plunge and whiplash out into the abyss; the core shatters as singularity erupts, reality fractures into perfect multidimensional symmetry and simplicity, all dissolves into blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\\n\\nincrease the speed and velocity of the rotation so much up that everything suddenly *whiplash out into the abyss*; the innertia/momentum makes the core shatter into perfect multidimensional symmetry and simplicity; into a blinding visual display of immense elegance and beauty, colors detonate into *a perfect circle*\\n\\nthe speed of the speed of rotation reaches critical state, and just as the speed of the rotation reaches it's maximum velocity everything *explodes*, colors detonate, and suddenly it  into *a perfect circle*\\n\\nincrease speed of rotation and internal momentum to maximum, the circle beautifully explodes into colors while the shape turns into a *perfect circle* (as the golden wheel of a car)\\n\\nThe trembling nucleus of color writhes, spinning at the very edge of physical law\\u2014the circle seethes with such accelerating momentum that it becomes a golden gyre of pure, crackling intent. At the apex, the structural boundary collapses: a sunflare detonation radiates in impossible hues, eclipsing the world with a crown of blinding, iridescent corona. Space fractures outward, the elemental wheel dissolving into molten bands of light\\u2014obliterating contours, transmuting form into a symphony of radiant, untethered energy. [hyperrotation][color_nova][apotheosis_wheel][coronal_disintegration][transcendent][no_human_forms]\\n\\n\\nThe trembling nucleus of color writhes, spinning at the very edge of physical law\\u2014then suddenly it transforms quickly into the wheel of a car before seamlessly morphing into the steeringwheel of the car (fpv)\\n\\nthe circle suddenly transforms into light streaks that take the shape of a wheel before transforming through fpv into a steeringwheel of a car, suddenly the perfect circle implodes\\n\\nPerfect circle morphs into light streaks forming a wheel, [morph], then [fpv] transforms into a car steering wheel, sudden implosion of the circle [dissolve][zoom:in][lighting_change], in a dynamic, minimalist, high-contrast scene.\\n\\nAnimate a perfect circle seamlessly transforming into light streaks that construct a wheel; morph into a car steering wheel via first-person view; immediately trigger a sharp implosion of the circle with dissolve, zoom-in, and lighting changes; enforce a dynamic, minimalist, high-contrast visual presentation. Circle erupts into spinning light, forms wheel, shifts to steering POV, implodes in white flash [minimalist, black_bg, white_only, surge\\u2192spin\\u2192morph\\u2192implode, fpv, 5s]\",\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"perfect_circle, color:white, background:black, style:minimalist, high-contrast; [0-1s] circle appears centered, static; [1-2s] circle spins rapidly, rotation_speed:maximum, white motion blur on edges, no trailing color; [2-3s] at peak spin, circle shatters into symmetric white light streaks radiating outward; [3-4s] streaks realign to form schematic shape of car wheel (spokes and rim, white outline only), background remains black; [4-4.7s] camera zoom-in, quick morph of wheel shape into car steering wheel (white outline, first-person POV centered); [4.7-5s] steering wheel sharply implodes into a blinding white flash that fully fills the frame. All transitions are immediate and visually clear; no subtlety, no color shifts, only black background and white elements; remove all abstract, symbolic, or 'elegant/beautiful' references\\u2014focus on clear, rapid, geometric transformations visible within 5 seconds total.\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": {\n    \"clarified_directive\": \"Create a 5-second, black-background, high-contrast minimalist animation: 0-1s\\u2014display static, centered perfect white circle; 1-2s\\u2014spin circle at maximum speed with visible white motion blur along edge; 2-3s\\u2014at peak spin, instantly shatter circle into symmetric white linear light streaks; 3-4s\\u2014reform streaks into schematic car wheel shape (white outline spokes and rim); 4-4.7s\\u2014rapidly morph wheel into centered white-outline car steering wheel via zoom-in, first-person POV; 4.7-5s\\u2014immediately implode steering wheel into full-frame white flash. Transitions must be immediate, geometric, and unambiguous. Exclude all color, decorative, or symbolic elements\\u2014utilize only white on black, with crisp, clearly visible changes.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"compressed_description\": \"Centered static white circle accelerates to max spin, shatters into radial white streaks, streaks reform as schematic car wheel, rapid zoom morphs to white-outline steering wheel (fpv), wheel implodes in total white flash [black_bg][white_only][minimalist][spin→shatter→reform→zoom→morph→implode][geometric][rapid][5s]\"\n}", "cost": 0.0}}}], "total_cost": 0.0}