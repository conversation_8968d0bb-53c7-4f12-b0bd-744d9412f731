# RigOfficeDownloader

> **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.

---

## 1. Overview

RigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents → Files → Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.

---

## 2. The Problem

- Engineers lose valuable hours **manually searching** and **downloading** technical documentation.
- The repetitive process is **error-prone** and distracts from real engineering work.

---

## 3. The Solution

- **Automates** the document search, scraping, and download phases, cutting the time required by **75%+**.
- Offers **three main stages**—document metadata retrieval, file metadata retrieval, and file downloads—each controlled by user-edited Markdown tables for fine-tuned selection.

---

## 4. Key Features

- **Three-Stage Workflow**
  1. **Documents**: Gather doc metadata, mark `item_include` in Markdown.
  2. **Files**: Fetch file info for included docs, mark `item_download` in Markdown.
  3. **Downloads**: Organize files with subfolder support based on `'/'` in `item_generated_name`.

- **Interactive Menu**: Allows step-by-step or fully automated runs.
- **User Review via Markdown**: You decide which items to include or skip by editing `.md` tables.
- **Configurable Filter Chains**: Glob-style pattern matching to auto-select or exclude docs/files.
- **Smart Organization**: Hierarchical naming that embeds rig/drawing metadata, plus subfolder creation.
- **Error Handling**: Graceful session management, robust page-scrolling logic, and deduplication.

---

## 5. Workflow Steps

1. **Change Parameters**: (Optional) Update rig number, search URLs.
2. **Configure Filters**: Setup or edit filter rules to auto-include/exclude docs/files.
3. **Fetch Docs**: Scrape document metadata into `<rig>-a-docs.json` (all `item_include=false` initially).
4. **Export Docs**: Generate `<rig>-a-docs.md`; manually set `item_include=true` for desired items.
5. **Import Updated Docs**: Sync changes back from Markdown to JSON.
6. **Fetch Files**: Retrieve file metadata for those docs, saved as `<rig>-b-files.json` (`item_download=false`).
7. **Export Files**: Create `<rig>-b-files.md`; set `item_download=true` for target files.
8. **Import Updated Files**: Sync file selection from Markdown to JSON.
9. **Download Files**: Acquire and organize all selected files under `outputs/downloads/<rig>`.

---

## 6. Architecture & Technology

- **Core Stack**:
  - **Python 3.6+**
  - **Selenium** for browser automation
  - **BeautifulSoup** for HTML parsing
  - **JSON/Markdown** for data storage and user edits
- **Linear Multi-Stage** design with user checkpoints ensures incremental control and reusability.
- **Filter System**: Pattern-based matching (inclusion/exclusion) on fields like `item_generated_name` or `item_case_description`.
- **File Organization**: Subfolder creation from `'/'` in `item_generated_name`, plus sanitization of invalid characters.

---

## 7. Directory Structure

