{"initial_prompt": "visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()", "sequence_id": "sequence-0224-a+0221-b+0223-a+0221-b", "results": [{"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: empty urban rooftop, subject: solitary figure center frame, action: standing motionless, camera: wide shot, angle: eye-level, time: dusk, lighting: soft ambient, style: hyperrealistic, color: cool tones, mood: calm, effects: subtle atmospheric haze, post-processing: sharp detail, composition: rule of thirds\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Amplifier B\n\nPerform a structural and semantic distillation of the input: locate its *driving aim*, isolate supporting momentum, and forcefully extrapolate it into a clarified directive. Treat the input as a living logic stream—trace the *underlying thesis*, and remove all extraneous elements. Amplify only what accelerates the goal. The output must **not reflect interpretation**, but **the core resolution** implied by the data’s internal logic. Execute as:\n\n`{role=instruction_amplifier_b;input=[original_text:str];process=[extract_underlying_thesis(),eliminate_distraction(),amplify_goal_drivers(),intensify_and_operationalize_purpose(),restructure_as_directive_output()];constraints=[preserve_input_telos(),avoid_additional_concepts(),maintaingoal_focused_structure()];requirements=[use_crystallized_command_language(),preserve_technical_fidelity(),ensure_actionability_of_output(),maximize_instructional_clarity()];output={clarified_directive:str}}`", "step": "b", "title": "Instruction Amplifier B", "input": "{\n  \"initial_prompt\": \"visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()\",\n  \"optimized_video_prompt\": \"scene: empty urban rooftop, subject: solitary figure center frame, action: standing motionless, camera: wide shot, angle: eye-level, time: dusk, lighting: soft ambient, style: hyperrealistic, color: cool tones, mood: calm, effects: subtle atmospheric haze, post-processing: sharp detail, composition: rule of thirds\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"scene: empty urban rooftop, subject: solitary figure center frame, action: standing motionless, camera: wide shot, angle: eye-level, time: dusk, lighting: soft ambient, style: hyperrealistic, color: cool tones, mood: calm, effects: subtle atmospheric haze, post-processing: sharp detail, composition: rule of thirds\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": \"Render a hyperrealistic wide shot of a solitary figure standing motionless at center frame on an empty urban rooftop at dusk. Use eye-level camera angle, soft ambient lighting, cool tones, and subtle atmospheric haze. Apply sharp detail in post-processing and compose according to the rule of thirds. Ensure calm mood throughout.\"\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"initial_prompt\": \"visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()\",\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"scene: empty urban rooftop, subject: solitary figure center frame, action: standing motionless, camera: wide shot, angle: eye-level, time: dusk, lighting: soft ambient, style: hyperrealistic, color: cool tones, mood: calm, effects: subtle atmospheric haze, post-processing: sharp detail, composition: rule of thirds\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": \"Render a hyperrealistic wide shot of a solitary figure standing motionless at center frame on an empty urban rooftop at dusk. Use eye-level camera angle, soft ambient lighting, cool tones, and subtle atmospheric haze. Apply sharp detail in post-processing and compose according to the rule of thirds. Ensure calm mood throughout.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"generated_video_sequence\": \"file_path_or_stream\",\n  \"crystallized_command_sequence\": [\n    \"SCENE: Empty urban rooftop at dusk; establish broad rooftop expanse; eliminate extraneous objects.\",\n    \"SUBJECT: Solitary figure positioned dead center frame; stand motionless, upright, relaxed posture; body language communicates introspective calm.\",\n    \"CAMERA: Wide shot, eye-level; lock camera position; maintain compositional anchor on center subject with proportional negative space on rooftop; strictly adhere to rule of thirds lines.\",\n    \"LIGHTING: Simulate soft, diffused ambient lighting from the ambient dusk sky; introduce subtle blue-grays and cool tones; avoid harsh highlights or color pollution.\",\n    \"ATMOSPHERIC EFFECT: Overlay gentle, low-opacity atmospheric haze; subtly desaturate distant urban elements to emphasize spatial depth.\",\n    \"STYLE: Apply hyperrealistic rendering with minute rooftop textures (gravel, seams, faint water stains) and lifelike material detail; no stylization permitted.\",\n    \"MOOD: Maintain pervasive sense of tranquility; forbid visual tension or aggressive contrasts.\",\n    \"POST-PROCESSING: Execute detail-enhancement pass; sharpen rooftop textures, subject’s clothing and face contours; subtly increase local contrast without noise artifacts.\"\n  ],\n  \"sequence_intent\": \"Convey solitude and introspection through a singular, motionless subject juxtaposed against vast urban emptiness, reinforcing calm and focused narrative; maximize visual clarity and instructional specificity at every operational step.\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Amplifier B\n\nPerform a structural and semantic distillation of the input: locate its *driving aim*, isolate supporting momentum, and forcefully extrapolate it into a clarified directive. Treat the input as a living logic stream—trace the *underlying thesis*, and remove all extraneous elements. Amplify only what accelerates the goal. The output must **not reflect interpretation**, but **the core resolution** implied by the data’s internal logic. Execute as:\n\n`{role=instruction_amplifier_b;input=[original_text:str];process=[extract_underlying_thesis(),eliminate_distraction(),amplify_goal_drivers(),intensify_and_operationalize_purpose(),restructure_as_directive_output()];constraints=[preserve_input_telos(),avoid_additional_concepts(),maintaingoal_focused_structure()];requirements=[use_crystallized_command_language(),preserve_technical_fidelity(),ensure_actionability_of_output(),maximize_instructional_clarity()];output={clarified_directive:str}}`", "step": "b", "title": "Instruction Amplifier B", "input": "{\n  \"initial_prompt\": \"visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()\",\n  \"generated_video_sequence\": \"file_path_or_stream\",\n  \"crystallized_command_sequence\": [\n    \"SCENE: Empty urban rooftop at dusk; establish broad rooftop expanse; eliminate extraneous objects.\",\n    \"SUBJECT: Solitary figure positioned dead center frame; stand motionless, upright, relaxed posture; body language communicates introspective calm.\",\n    \"CAMERA: Wide shot, eye-level; lock camera position; maintain compositional anchor on center subject with proportional negative space on rooftop; strictly adhere to rule of thirds lines.\",\n    \"LIGHTING: Simulate soft, diffused ambient lighting from the ambient dusk sky; introduce subtle blue-grays and cool tones; avoid harsh highlights or color pollution.\",\n    \"ATMOSPHERIC EFFECT: Overlay gentle, low-opacity atmospheric haze; subtly desaturate distant urban elements to emphasize spatial depth.\",\n    \"STYLE: Apply hyperrealistic rendering with minute rooftop textures (gravel, seams, faint water stains) and lifelike material detail; no stylization permitted.\",\n    \"MOOD: Maintain pervasive sense of tranquility; forbid visual tension or aggressive contrasts.\",\n    \"POST-PROCESSING: Execute detail-enhancement pass; sharpen rooftop textures, subject\\u2019s clothing and face contours; subtly increase local contrast without noise artifacts.\"\n  ],\n  \"sequence_intent\": \"Convey solitude and introspection through a singular, motionless subject juxtaposed against vast urban emptiness, reinforcing calm and focused narrative; maximize visual clarity and instructional specificity at every operational step.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt();SCENE: Empty urban rooftop at dusk; establish broad rooftop expanse; eliminate extraneous objects. SUBJECT: Solitary figure positioned dead center frame; stand motionless, upright, relaxed posture; body language communicates introspective calm. CAMERA: Wide shot, eye-level; lock camera position; maintain compositional anchor on center subject with proportional negative space on rooftop; strictly adhere to rule of thirds lines. LIGHTING: Simulate soft, diffused ambient lighting from the ambient dusk sky; introduce subtle blue-grays and cool tones; avoid harsh highlights or color pollution. ATMOSPHERIC EFFECT: Overlay gentle, low-opacity atmospheric haze; subtly desaturate distant urban elements to emphasize spatial depth. STYLE: Apply hyperrealistic rendering with minute rooftop textures (gravel, seams, faint water stains) and lifelike material detail; no stylization permitted. MOOD: Maintain pervasive sense of tranquility; forbid visual tension or aggressive contrasts. POST-PROCESSING: Execute detail-enhancement pass; sharpen rooftop textures, subject’s clothing and face contours; subtly increase local contrast without noise artifacts.\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": \"Render a hyperrealistic wide shot of a solitary, motionless figure centered on a barren urban rooftop at dusk. Remove all extraneous objects. Anchor composition to rule of thirds with pronounced rooftop negative space. Simulate soft, diffused ambient dusk light with blue-gray, cool tonal balance and zero harshness. Add low-opacity haze; desaturate distant cityscape for depth. Exclude stylization; enforce minute rooftop texture and subject detail. Maintain tranquil, introspective atmosphere—prohibit visual tension. Finalize with targeted detail enhancement to roof, clothing, and facial contours; elevate local contrast minimally, suppress noise.\"\n}", "cost": 0.0}}}], "total_cost": 0.0}