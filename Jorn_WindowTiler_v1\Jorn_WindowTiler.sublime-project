{
    "folders": [
        {
            "name": "[ <PERSON><PERSON>_WindowTiler ]",
            "path": "./.venv",
            "folder_exclude_patterns": [
                "*"
            ],
            "file_exclude_patterns": [
                "*"
            ]
        },
        // {
            //   "name": "Generated Files",
            //   "path": "src/out/Debug/gen",
            // },
        {
            "name": "Root",
            "path": ".",
            "folder_exclude_patterns":
            [
                "src",
            ],
        },
        {
            "name": "src",
            "path": "src",
            "file_exclude_patterns":
            [
                "*.vcproj",
                "*.vcxproj",
                "*.sln",
                "*.gitignore",
                "*.gitmodules",
                "*.vcxproj.*",
            ],
            "folder_exclude_patterns":
            [
                "build",
                "out",
                "third_party",
                ".git",
            ],
        },
    ],
    "build_systems": [
        {
            "name": "-> Jorn_WindowTiler \t <Py_Prj>",
            "shell_cmd": "\"%APPDATA%/_py_venvs/Jo<PERSON>_WindowTiler__Python310/venv/Scripts/python.exe\" -u \"$file\"",
            "working_dir": "${folder:${project_path:${file_path}}}",
            "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)",
            "selector": "source.python"
        }
    ]
}
