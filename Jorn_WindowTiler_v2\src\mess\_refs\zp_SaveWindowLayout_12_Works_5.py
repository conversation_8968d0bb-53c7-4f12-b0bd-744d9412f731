# Import modules
import sys
import os
import time
import json
import subprocess
import urllib.parse
import win32api
import win32gui
import win32com.client
import win32con
import argparse
from tabulate import tabulate

# # Create a parser object and add valid arguments
# inputArguments_Parser = argparse.ArgumentParser()
# inputArguments_Parser.add_argument('-f', '--filename', required=True, help='File to process')
# inputArguments_Parser.add_argument('-o', '--output', help='Output file')
# inputArguments_Parser.add_argument('-v', '--verbose', action='store_true', help='Verbose output')
# inputArguments_Parser.add_argument('-c', '--choice',  required=True,choices=['item1', 'item2', 'item3'], help='Choose an item')
# # inputArguments_Parser.error = lambda message: print('Error: {}\n'.format(message))
# # Access and retrieve the arguments
# inputArgs = inputArguments_Parser.parse_args()
# filename = inputArgs.filename
# output = inputArgs.output
# verbose = inputArgs.verbose


# NOTE: THIS ONLY WORK FOR EXPLORER-WINDOWS
# Save/Load Explorer Windows (Paths/Locations/Sizes)

# TODO:
# - Legge inn view-settings
# - Comment out hwnd_handle in json
# - AlwaysOnTop er noe som kunne ha vært greit å kunne gjøre på f.eks. nettleser, finne en løsning


# Resize terminal window (PosX, PosY, Width, Height)
os.system("TITLE Window Layout (Saver/Loader)")
win32gui.MoveWindow(win32gui.GetForegroundWindow(), 160, 0, 1600, 720, True)

# Constant: Specify path to the layout-file
layoutFile_FilePath = "WindowLayout.hwndLayout.json"

# Constant: Specify name of the keys to use in the layout-file (for readability purposes) [Hwnd_WindowState: 1:Normal 2:Minimized 3:Maximized]
# layoutFile_Keys = ['Hwnd_Handle', 'Hwnd_Type', 'Hwnd_Title', 'Hwnd_OpenPath', 'Hwnd_Z-Index', 'Hwnd_WindowState', 'Hwnd_AlwaysOnTop', 'Hwnd_Pos', 'Hwnd_Size']
layoutFile_Keys = ['Hwnd_Handle', 'Hwnd_Title', 'Hwnd_OpenPath', 'Hwnd_Z-Index', 'Hwnd_WindowState', 'Hwnd_AlwaysOnTop', 'Hwnd_Pos', 'Hwnd_Size']

# If ApplyLayout: Specify whether or not to close windows that are not specified in layoutFile
closeUnspecifiedWindows = True

# If ApplyLayout: Specify whether or not to create specified windows if they are not already open
createWindowsThatDontExist = True

# If ApplyLayout: Specify whether to include special folders when applying/loading layouts and when closing windows
includeSpecialFolders = True


# Specify the CLSID (Class Identifier) for COM-Object: ShellWindowsPermalink (alternatively 'Shell.Application' could be used)
shellWindows_CLSID = '{9BA05972-F6A8-11CF-A442-00A0C90A8F39}'
# Create a dynamic list of COM objects through the ShellWindows class (continually tracks open windows in the system)
shellWindows_Instances = win32com.client.Dispatch(shellWindows_CLSID)


# FUNCTION: RETRIEVE AND RETURN THE LAYOUT-DATA FOR (CURRENTLY OPEN) FILE-EXPLORER WINDOWS
def fnExplorerWindows_GetExistingLayout():
    # Create a list to store the layout data of each window in
    currentLayout_HwndData = []
    # For each instance of currently open ShellWindows (File Explorer Window)
    for currInstance in shellWindows_Instances:
        # currInstance: Retrieve data from the current window: ('Handle' | 'Title' | 'Path')
        currWin_Handle = currInstance.HWND
        currWin_Title  = win32gui.GetWindowText(currWin_Handle)
        currWin_Path   = urllib.parse.unquote(currInstance.LocationURL).lstrip('/').replace("\\","/")
        # currWin_Path: Manual handling of "shell" windows (using CLSID's)
        currWin_Path = ('shell:::{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}' if (currWin_Title == 'Desktop') else currWin_Path)
        currWin_Path = ('shell:::{20D04FE0-3AEA-1069-A2D8-08002B30309D}' if (currWin_Title == 'This PC') else currWin_Path)
        currWin_Path = ('shell:::{645FF040-5081-101B-9F08-00AA002F954E}' if (currWin_Title == 'Recycle Bin') else currWin_Path)
        # currWin_Path: Manual handling of "special" windows (using CLSID's)
        currWin_Path = ('shell:::{5399E694-6CE5-4D6C-8FCE-1D8870FDCBA0}' if (currWin_Title == 'All Control Panel Items') else currWin_Path)
        currWin_Path = ('shell:::{7007ACC7-3202-11D1-AAD2-00805FC1270E}' if (currWin_Title == 'Network Connections') else currWin_Path)
        # currInstance: If a path has been retrieved
        if (currWin_Path != ''):
            # currInstance: Retrieve data from the current window: (WindowState:1/2/3 | Position:[X,Y] | Size:[X,Y])
            currWin_Placement   = list(win32gui.GetWindowPlacement(currWin_Handle))
            currWin_WindowState = currWin_Placement[1]
            currWin_Coordinates = list(currWin_Placement[4])
            currWin_Pos  = ([currWin_Coordinates[0], currWin_Coordinates[1]])
            currWin_Size = ([currWin_Coordinates[2] - currWin_Pos[0], currWin_Coordinates[3] - currWin_Pos[1]])
            # Append current window-data to the result: ('Handle' | 'Title' | 'Path' | WindowState:1/2/3 | Position:[X,Y] | Size:[X,Y])
            currentLayout_HwndData.append([currWin_Handle, currWin_Title, currWin_Path, 0, currWin_WindowState, False, currWin_Pos, currWin_Size])

    # Collect the window-handles of retrieved items in a separate list (used as reference for retrieving their z-index)
    currentItems_windowHandles = [currItem[0] for currItem in currentLayout_HwndData]
    # Retrieve the z-order of the retrieved items (using a while-loop to ensure traversing through all of the windows)
    currentItems_zIndex = []
    currWin_hwndHandle = win32gui.GetTopWindow(None)
    while (currWin_hwndHandle != 0):
        if currWin_hwndHandle in currentItems_windowHandles: currentItems_zIndex.append(currWin_hwndHandle)
        currWin_hwndHandle = win32gui.GetWindow(currWin_hwndHandle, win32con.GW_HWNDNEXT)
    # Add the z-index to their corresponding item (by window-handle) in layout-data
    currentLayout_HwndData = [(currItem[:3] + [currentItems_zIndex.index(currItem[0])] + currItem[4:]) for currItem in currentLayout_HwndData]


    # Sort the layout-data by z-order and copy the data into a dictionary
    currentLayout_List = sorted(currentLayout_HwndData, key=lambda currItem: currItem[3])
    currentLayout_Dict = [{currKey:itemValue for currKey, itemValue in zip(layoutFile_Keys, currItem)} for currItem in currentLayout_List]
    # Return the result [Result[0]:(List), Result[1]:(Dictionary)]
    return [currentLayout_List, currentLayout_Dict]


# FUNCTION: LOAD AND APPLY LAYOUT (ON FILE-EXPLORER-WINDOWS) FROM SPECIFIED LAYOUTFILE
def fnExplorerWindows_ApplyLayout(importedLayout_List):
    # [COMPARE IMPORTED LAYOUT WITH CURRENT LAYOUT]
    # Retrieve the existing layout (data on all shell-windows already open)
    existingLayout_List = (fnExplorerWindows_GetExistingLayout())[0]
    # Initialize variables for dictionary/list-comprehension (for efficiently consolidating data between existing/imported layout)
    existingLayout_itemIndexLookup = {tuple(currItem[1:3]):currIndex for currIndex, currItem in enumerate(existingLayout_List)}
    importedLayout_itemIndexes = [tuple(currItem[1:3]) for currItem in importedLayout_List]
    # Compare existing/imported layout and generate pairs of item-indexes based on whether their title/path match or not [[0]:ExistingLayoutIndexes, [1]:ImportLayoutIndexes]
    pairedLayouts_hwndMatched_IndexPairs = [[existingLayout_itemIndexLookup[currItem], currIndex] for currIndex, currItem in enumerate(importedLayout_itemIndexes) if currItem in existingLayout_itemIndexLookup]
    # Retrieve unmatched/remaining item-indexes from existing/imported layout ([ExistingLayout-Indexes] | [ImportedLayout-Indexes])
    existingLayout_hwndUnmatched_Indexes = [currIndex for currIndex in range(len(existingLayout_List)) if currIndex not in [currPair[0] for currPair in pairedLayouts_hwndMatched_IndexPairs]]
    importedLayout_hwndRemaining_Indexes = [currIndex for currIndex in range(len(importedLayout_List)) if currIndex not in [currPair[1] for currPair in pairedLayouts_hwndMatched_IndexPairs]]
    # Retrieve the actual window-data for the layout to apply (separated by corresponding type of action)
    createLayout_Windows_Update = [([existingLayout_List[currItem[0]][0]] + importedLayout_List[currItem[1]][1:]) for currItem in pairedLayouts_hwndMatched_IndexPairs]
    createLayout_Windows_Create = [importedLayout_List[currItem] for currItem in importedLayout_hwndRemaining_Indexes]
    createLayout_Windows_Unused = [existingLayout_List[currItem] for currItem in existingLayout_hwndUnmatched_Indexes]

    # Initialize variables for printing information (from the resulting action performed on them)
    appliedWindowAction_Skipped   = (createLayout_Windows_Unused if not closeUnspecifiedWindows else [])
    appliedWindowAction_Closed    = (createLayout_Windows_Unused if closeUnspecifiedWindows else [])
    appliedWindowAction_Created   = []
    appliedWindowAction_Unchanged = []
    appliedWindowAction_Updated   = []

    # [CLOSE UNMATCHED WINDOWS]
    # If closeUnspecifiedWindows: Close all remaining unused windows in the existing layout
    [win32api.SendMessage(currItem[0], win32con.WM_CLOSE, 0, 0) if closeUnspecifiedWindows else None for currItem in createLayout_Windows_Unused]

    # [CREATE REMAINING WINDOWS]
    # Collect all existing window-handles in a separate list (used to prevent retrieving the same window more than once)
    windowHandles_Processed = [currItem[0] for currItem in createLayout_Windows_Update]
    # If windows specified in the layout file (which don't already exist) should be created
    if createWindowsThatDontExist and len(createLayout_Windows_Create):
        # For each item in createLayout_Windows_Create (windows specified in layout-data that isn't already open)
        for currIndex, currItem in enumerate(createLayout_Windows_Create):
            # Open a (minimized) window for the current item-path
            openPathCommand = ('start /min explorer %s' % currItem[2])
            openPathCommandResult = subprocess.Popen(openPathCommand, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            # Use a while-loop to wait for the newly created window to initialize
            while True:
                # Retrieve any window-handles that hasn't already been processed
                windowHandles_Current = [currInstance.HWND for currInstance in shellWindows_Instances if currInstance.HWND]
                windowHandles_hwndFound = [currHandle for currHandle in windowHandles_Current if currHandle not in windowHandles_Processed]
                # If a new window-handle has been created (and retrieved)
                if len(windowHandles_hwndFound):
                    # Update the current item in createLayout_Windows_Create and add the window-handle to windowHandles_Processed
                    createLayout_Windows_Create[currIndex][0] = windowHandles_hwndFound[0]
                    windowHandles_Processed.append(windowHandles_hwndFound[0])
                    appliedWindowAction_Created.append(createLayout_Windows_Create[currIndex])
                    break


    # [APPLY LAYOUT]
    # Combine imported items into one list (ordered by z-index)
    layoutData_List = (createLayout_Windows_Update + createLayout_Windows_Create)
    layoutData_List = sorted(layoutData_List, key=lambda currItem: currItem[3], reverse=True)
    # For each item in layoutData_List (these are now windows that are currently open)
    for currItem in layoutData_List:
        # Extract current window-data and convert it to valid format for SetWindowPlacement to work
        currWin_WindowState = currItem[4]
        currWin_Coordinates = tuple([currItem[6][0], currItem[6][1], (currItem[6][0] + currItem[7][0]), (currItem[6][1] + currItem[7][1])])
        currWin_Placement = tuple([0, currItem[4], (-1, -1), (-1, -1), currWin_Coordinates])
        # Determine whether the current window is already correct or not
        currWin_Identical = (currWin_Placement == (win32gui.GetWindowPlacement(currItem[0])))
        appliedWindowAction_Unchanged.append(currItem) if currWin_Identical else appliedWindowAction_Updated.append(currItem)
        # Apply layout for the current item
        currWin_TopMost = (win32con.HWND_NOTOPMOST if not currItem[5] else win32con.HWND_TOPMOST)
        win32gui.SetWindowPos(currItem[0], win32con.HWND_TOPMOST, 0, 0, 0, 0, win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
        win32gui.SetWindowPos(currItem[0], currWin_TopMost, 0, 0, 0, 0, win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
        win32gui.SetWindowPlacement(currItem[0], currWin_Placement)

    # [PRINT INFORMATION]
    # Remove duplicates
    appliedWindowAction_Updated = [currItem for currItem in appliedWindowAction_Updated if currItem not in appliedWindowAction_Created]
    # Print information
    appliedLayout_Headers = ['SKIPPED', 'CLOSED', 'CREATED', 'UNCHANGED', 'UPDATED']
    appliedLayout_Actions = []
    appliedLayout_Actions.append(sorted(appliedWindowAction_Skipped, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions.append(sorted(appliedWindowAction_Closed, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions.append(sorted(appliedWindowAction_Created, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions.append(sorted(appliedWindowAction_Unchanged, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions.append(sorted(appliedWindowAction_Updated, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions_sortedPairs = sorted(zip(appliedLayout_Headers, appliedLayout_Actions), key=lambda x: len(x[1]), reverse=False)
    appliedLayout_Headers = [currItem[0] for currItem in appliedLayout_Actions_sortedPairs]
    appliedLayout_Actions = [currItem[1] for currItem in appliedLayout_Actions_sortedPairs]
    for currIndex, currItem in enumerate(appliedLayout_Actions):
        print('[%s: %s WINDOWS]' % (appliedLayout_Headers[currIndex], len(currItem)))
        print(tabulate(currItem, headers=layoutFile_Keys, tablefmt="rst")) if len(currItem) else ""
        print('\n')

    # # Create a dictionary using a dictionary comprehension
    # applied_layout_dict = {currHeader: currAction for currHeader, currAction in zip(appliedLayout_Headers, appliedLayout_Actions)}
    # print(applied_layout_dict.keys())


    # print('[SKIPPED: %s WINDOWS]' % len(appliedWindowAction_Skipped))
    # (print(tabulate(appliedWindowAction_Skipped, headers=layoutFile_Keys, tablefmt="rst")) if len(appliedWindowAction_Skipped) else "")
    # print('[CLOSED: %s WINDOWS]' % len(appliedWindowAction_Closed))
    # (print(tabulate(appliedWindowAction_Closed, headers=layoutFile_Keys, tablefmt="rst")) if len(appliedWindowAction_Closed) else "")
    # print('[UNCHANGED: %s WINDOWS]' % len(appliedWindowAction_Unchanged))
    # (print(tabulate(appliedWindowAction_Unchanged, headers=layoutFile_Keys, tablefmt="rst")) if len(appliedWindowAction_Unchanged) else "")
    # print('[UPDATED: %s WINDOWS]' % len(appliedWindowAction_Updated))
    # (print(tabulate(appliedWindowAction_Updated, headers=layoutFile_Keys, tablefmt="rst")) if len(appliedWindowAction_Updated) else "")

# --------------------------------------------------------------------------------------------------------------------
# APPLY LAYOUT
# --------------------------------------------------------------------------------------------------------------------
if (len(sys.argv) == 2) and (sys.argv[1] == "Apply"):
    # Open and retrieve data from the specified layoutFile (using the JSON module)
    with open(layoutFile_FilePath, 'r') as inputFile:
        importedLayout_Dict = [{currKey:currItem[currKey] for currKey in layoutFile_Keys} for currItem in json.load(inputFile)]
        importedLayout_List = [[currItem[currKey] for currKey in layoutFile_Keys] for currItem in importedLayout_Dict]
    fnExplorerWindows_ApplyLayout(importedLayout_List)
# --------------------------------------------------------------------------------------------------------------------



# --------------------------------------------------------------------------------------------------------------------
# CREATE LAYOUT
# --------------------------------------------------------------------------------------------------------------------
if (len(sys.argv) == 2) and (sys.argv[1] == "Create"):
    # Retrieve the current layout (as a dict)
    currentLayout_Dict = (fnExplorerWindows_GetExistingLayout())[1]
    # Create and write data to the layout-file (using the JSON module)
    with open(layoutFile_FilePath, 'w') as outputFile:
        json.dump(currentLayout_Dict, outputFile, indent=4, sort_keys=False, separators=(',', ': '))
# --------------------------------------------------------------------------------------------------------------------
