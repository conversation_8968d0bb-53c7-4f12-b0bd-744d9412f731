{"initial_prompt": "A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden car wheel . Transmute these light trails into a sharply defined metallic gold silhouette of a car, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.", "sequence_id": "sequence-0224-a+0194-c+0221-b+0223-a+0224-b+0221-b+0230", "results": [{"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden car wheel . Transmute these light trails into a sharply defined metallic gold silhouette of a car, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: dark void; subject: spinning circle from lightstreaks; action: emit intense streaming light, unravel into searing morphing trails; effect: light trails morph into metallic gold car wheel; transform: trails transmute into sharply defined metallic gold car silhouette; color: chromatic shifts, golden aura; VFX: dynamic, hyperreal; camera: continuous arc right; detail: ultra-premium cinematic fidelity, maximal sharpness; dissolve: shimmer and dissolve silhouette; text: none; style: high-contrast, cinematic focus\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden car wheel . Transmute these light trails into a sharply defined metallic gold silhouette of a car, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.\",\n  \"optimized_video_prompt\": \"scene: dark void; subject: spinning circle from lightstreaks; action: emit intense streaming light, unravel into searing morphing trails; effect: light trails morph into metallic gold car wheel; transform: trails transmute into sharply defined metallic gold car silhouette; color: chromatic shifts, golden aura; VFX: dynamic, hyperreal; camera: continuous arc right; detail: ultra-premium cinematic fidelity, maximal sharpness; dissolve: shimmer and dissolve silhouette; text: none; style: high-contrast, cinematic focus\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"Spinning circle formed from lightstreaks in a dark void, emitting intense streaming light that unravels into morphing, golden trails; trails morph into a sharply defined metallic gold car wheel, then transmute into a radiant metallic gold car silhouette with chromatic shifts and golden aura, hyperreal VFX throughout [pan:right][rotate][morph][lighting_change][dissolve] ultra-premium cinematic focus, maximal sharpness, no text\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Amplifier B\n\nPerform a structural and semantic distillation of the input: locate its *driving aim*, isolate supporting momentum, and forcefully extrapolate it into a clarified directive. Treat the input as a living logic stream—trace the *underlying thesis*, and remove all extraneous elements. Amplify only what accelerates the goal. The output must **not reflect interpretation**, but **the core resolution** implied by the data’s internal logic. Execute as:\n\n`{role=instruction_amplifier_b;input=[original_text:str];process=[extract_underlying_thesis(),eliminate_distraction(),amplify_goal_drivers(),intensify_and_operationalize_purpose(),restructure_as_directive_output()];constraints=[preserve_input_telos(),avoid_additional_concepts(),maintaingoal_focused_structure()];requirements=[use_crystallized_command_language(),preserve_technical_fidelity(),ensure_actionability_of_output(),maximize_instructional_clarity()];output={clarified_directive:str}}`", "step": "b", "title": "Instruction Amplifier B", "input": "{\n  \"initial_prompt\": \"A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden car wheel . Transmute these light trails into a sharply defined metallic gold silhouette of a car, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.\",\n  \"runwayml_prompt\": \"Spinning circle formed from lightstreaks in a dark void, emitting intense streaming light that unravels into morphing, golden trails; trails morph into a sharply defined metallic gold car wheel, then transmute into a radiant metallic gold car silhouette with chromatic shifts and golden aura, hyperreal VFX throughout [pan:right][rotate][morph][lighting_change][dissolve] ultra-premium cinematic focus, maximal sharpness, no text\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden car wheel . Transmute these light trails into a sharply defined metallic gold silhouette of a car, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": {\n    \"clarified_directive\": \"Generate a hyperreal cinematic sequence in a dark void: lightstreaks spin into a circle, emitting intense streaming light that morphs into golden trails. Evolve trails into a sharply defined metallic gold car wheel, then seamlessly transmute into a metallic gold car silhouette with chromatic shifts and golden aura. Apply dynamic, premium VFX throughout. Continuously arc the camera right for maximum visual dynamism. On reveal, shimmer and dissolve the silhouette with ultra-premium cinematic fidelity. Absolutely exclude all text; ensure maximal visual sharpness and cinematic focus.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"initial_prompt\": \"A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden car wheel . Transmute these light trails into a sharply defined metallic gold silhouette of a car, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.\",\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden car wheel . Transmute these light trails into a sharply defined metallic gold silhouette of a car, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": {\n    \"clarified_directive\": \"Generate a hyperreal cinematic sequence in a dark void: lightstreaks spin into a circle, emitting intense streaming light that morphs into golden trails. Evolve trails into a sharply defined metallic gold car wheel, then seamlessly transmute into a metallic gold car silhouette with chromatic shifts and golden aura. Apply dynamic, premium VFX throughout. Continuously arc the camera right for maximum visual dynamism. On reveal, shimmer and dissolve the silhouette with ultra-premium cinematic fidelity. Absolutely exclude all text; ensure maximal visual sharpness and cinematic focus.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"setting\": \"Infinite dark void for pure contrast.\",\n      \"opening_visual\": \"Intense, luminous lightstreaks whorl into a spinning circle at screen center, emanating radiant beams.\",\n      \"morph_sequence\": [\n        \"The spinning circle contracts and intensifies: lightstreaks unravel and elongate into searing, morphing trails.\",\n        \"Trails converge and warp — melting and flowing into a sharply contoured, metallic gold car wheel that glows with inner fire, rim and spokes supernaturally crisp.\",\n        \"Golden glow intensifies; complex chromatic shifts ripple subtly across metal surfaces. The outer light ripples swell, then channel into the outline of an entire car.\",\n        \"Wheel’s integrity blurs as streaming gold envelops and shapes a silhouette: sharp, premium lines define the hyperreal metallic gold car, bathed in a radiating golden aura.\"\n      ],\n      \"camera_action\": \"[arc:right, speed:smooth-accelerating, duration:entire_sequence]\",\n      \"fx_and_motion\": [\n        \"Dynamic hyperreal VFX: prismatic lens flares, light bloom, aggressive reflections, subtle sparks trailing moving light.\",\n        \"Color grading for chromatic depth, with premium, gold-first palette and high contrast.\"\n      ],\n      \"climax\": \"As camera reach the car’s front quarter, silhouette shimmers, particles crackle and dissolve backward along the light flow, car dematerializes in a premium, ultra-sharp cinematic shimmer.\",\n      \"exclusion\": \"Strictly no text or letterforms.\",\n      \"quality\": \"Maximal sharpness and cinematic fidelity at every frame, with meticulously layered VFX and no softness or haze outside intentional bloom.\"\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"/video/cinematic_gold_silhouette_carmorph_hyperreal_v1.mp4\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# Temporal Feasibility & Keyword Ambiguity Lock (0212b)\n\nYour goal is **final execution-readiness**: Take the keyword-driven `optimized_video_prompt` (output from 0212a) and critically assess its cumulative visual complexity and number of distinct actions/effects against a `target_duration_seconds` (default 5). Ruthlessly simplify or remove specific keyword-parameter pairs or entire visual elements that cannot be clearly conveyed or appreciated within this timeframe without sacrificing the `core_visual_intent_summary`. Simultaneously, scan all remaining keywords and parameter values for any residual ambiguity, abstract terms, or subjective qualifiers; replace these with the most concrete, explicit, and interpretation-free equivalents. The output must be an ultra-clear, temporally feasible, and maximally impactful keyword-driven prompt.\n\n`{role=executable_prompt_finalizer; seqindex=b; input=[optimized_video_prompt:str, target_duration_seconds:int=5, core_visual_intent_summary:str]; process=[parse_keyword_parameters(prompt=optimized_video_prompt), estimate_cumulative_visual_complexity_and_action_count(), identify_elements_exceeding_temporal_budget_for_duration(complexity_estimate, action_count, duration=target_duration_seconds), simplify_or_remove_lowest_priority_elements_to_fit_duration(preserve_core_intent=core_visual_intent_summary, focus_on_elements_like_multiple_simultaneous_animations_or_subtle_effects), scan_all_keywords_and_values_for_ambiguity_or_subjectivity(e.g., 'subtle', 'gradual', 'beautiful'), replace_ambiguous_terms_with_concrete_alternatives_or_remove_if_unclarifiable_concisely(e.g., 'mist: subtle' - very_low_density' or 'mist: barely_visible' or remove if not core), ensure_all_parameter_values_are_explicit_and_measurable_where_possible(), reconstruct_prompt_from_refined_keywords_and_parameters(), perform_final_impact_and_clarity_check_for_duration()]; constraints=[final_prompt_must_be_realistically_achievable_and_perceptible_within_target_duration(), all_keywords_and_parameter_values_must_be_explicit_unambiguous_and_machine_interpretable(), core_visual_intent_must_be_strictly_preserved_during_simplification(), no_new_creative_elements_or_keywords_to_be_introduced_only_refinement_simplification_or_removal_of_existing_ones(), output_must_remain_keyword_driven_and_structurally_consistent_with_input_prompt_style()]; requirements=[produce_a_video_prompt_that_is_both_temporally_feasible_and_crystal_clear_for_video_generation_ai(), maximize_perceptual_impact_for_a_short_duration_visual_by_focusing_on_key_elements(), eliminate_all_potential_for_misinterpretation_or_inefficient_rendering_due_to_ambiguity()]; output={final_executable_video_prompt:str}}`", "step": "b", "title": "Temporal Feasibility & Keyword Ambiguity Lock (0212b)", "input": "{\n  \"initial_prompt\": \"A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden car wheel . Transmute these light trails into a sharply defined metallic gold silhouette of a car, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.\",\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"setting\": \"Infinite dark void for pure contrast.\",\n      \"opening_visual\": \"Intense, luminous lightstreaks whorl into a spinning circle at screen center, emanating radiant beams.\",\n      \"morph_sequence\": [\n        \"The spinning circle contracts and intensifies: lightstreaks unravel and elongate into searing, morphing trails.\",\n        \"Trails converge and warp \\u2014 melting and flowing into a sharply contoured, metallic gold car wheel that glows with inner fire, rim and spokes supernaturally crisp.\",\n        \"Golden glow intensifies; complex chromatic shifts ripple subtly across metal surfaces. The outer light ripples swell, then channel into the outline of an entire car.\",\n        \"Wheel\\u2019s integrity blurs as streaming gold envelops and shapes a silhouette: sharp, premium lines define the hyperreal metallic gold car, bathed in a radiating golden aura.\"\n      ],\n      \"camera_action\": \"[arc:right, speed:smooth-accelerating, duration:entire_sequence]\",\n      \"fx_and_motion\": [\n        \"Dynamic hyperreal VFX: prismatic lens flares, light bloom, aggressive reflections, subtle sparks trailing moving light.\",\n        \"Color grading for chromatic depth, with premium, gold-first palette and high contrast.\"\n      ],\n      \"climax\": \"As camera reach the car\\u2019s front quarter, silhouette shimmers, particles crackle and dissolve backward along the light flow, car dematerializes in a premium, ultra-sharp cinematic shimmer.\",\n      \"exclusion\": \"Strictly no text or letterforms.\",\n      \"quality\": \"Maximal sharpness and cinematic fidelity at every frame, with meticulously layered VFX and no softness or haze outside intentional bloom.\"\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"/video/cinematic_gold_silhouette_carmorph_hyperreal_v1.mp4\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"final_executable_video_prompt\": \"infinite_black_background; opening_visual: central spinning circle formed by intense, bright gold lightstreaks (high luminosity, radiant beams radiate), duration: 1.2s; morph_sequence_1: circle contracts as gold lightstreaks rapidly elongate and unravel into bright, searing light trails, duration: 0.8s; morph_sequence_2: light trails streamline and merge, transforming into a sharply defined metallic gold car wheel (crisp rim and spokes, inner glow effect), duration: 1s; morph_sequence_3: wheel shape expands, gold light flows and outlines a metallic gold car silhouette (precise, hard-edged outline, glowing gold outer aura), duration: 1s; climax: silhouette shimmers, crackling gold particles dissolve silhouette backward along light flow (high particle density; rapid dissolve effect), duration: 0.8s; camera_motion: arc right, speed: steady, smooth from left three-quarter to right front quarter over full sequence; vfx: gold lens flare (focused around transformation), light bloom on wheel and car, high contrast, filmic grading (gold/cool shadows), aggressive metallic reflections on wheel and silhouette, visible fine sparks trail moving light only during transformations; exclude: absolutely no text or letterforms; visual_quality: maximum sharpness, no haze (except intentional bloom), all edges crisp and defined; duration: total 5s.\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Amplifier B\n\nPerform a structural and semantic distillation of the input: locate its *driving aim*, isolate supporting momentum, and forcefully extrapolate it into a clarified directive. Treat the input as a living logic stream—trace the *underlying thesis*, and remove all extraneous elements. Amplify only what accelerates the goal. The output must **not reflect interpretation**, but **the core resolution** implied by the data’s internal logic. Execute as:\n\n`{role=instruction_amplifier_b;input=[original_text:str];process=[extract_underlying_thesis(),eliminate_distraction(),amplify_goal_drivers(),intensify_and_operationalize_purpose(),restructure_as_directive_output()];constraints=[preserve_input_telos(),avoid_additional_concepts(),maintaingoal_focused_structure()];requirements=[use_crystallized_command_language(),preserve_technical_fidelity(),ensure_actionability_of_output(),maximize_instructional_clarity()];output={clarified_directive:str}}`", "step": "b", "title": "Instruction Amplifier B", "input": "{\n  \"initial_prompt\": \"A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden car wheel . Transmute these light trails into a sharply defined metallic gold silhouette of a car, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.\",\n  \"final_executable_video_prompt\": \"infinite_black_background; opening_visual: central spinning circle formed by intense, bright gold lightstreaks (high luminosity, radiant beams radiate), duration: 1.2s; morph_sequence_1: circle contracts as gold lightstreaks rapidly elongate and unravel into bright, searing light trails, duration: 0.8s; morph_sequence_2: light trails streamline and merge, transforming into a sharply defined metallic gold car wheel (crisp rim and spokes, inner glow effect), duration: 1s; morph_sequence_3: wheel shape expands, gold light flows and outlines a metallic gold car silhouette (precise, hard-edged outline, glowing gold outer aura), duration: 1s; climax: silhouette shimmers, crackling gold particles dissolve silhouette backward along light flow (high particle density; rapid dissolve effect), duration: 0.8s; camera_motion: arc right, speed: steady, smooth from left three-quarter to right front quarter over full sequence; vfx: gold lens flare (focused around transformation), light bloom on wheel and car, high contrast, filmic grading (gold/cool shadows), aggressive metallic reflections on wheel and silhouette, visible fine sparks trail moving light only during transformations; exclude: absolutely no text or letterforms; visual_quality: maximum sharpness, no haze (except intentional bloom), all edges crisp and defined; duration: total 5s.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden car wheel . Transmute these light trails into a sharply defined metallic gold silhouette of a car, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": \"Initiate with a spinning circle of high-intensity gold lightstreaks against a pure black background. Drive the circle to contract as the lightstreaks rapidly unravel into searing, morphing gold trails. Seamlessly morph these trails into a sharply defined metallic gold car wheel with intense inner glow and aggressive reflections. Expand and transmute this wheel into a hard-edged metallic gold car silhouette, surrounded by a vivid gold aura and enhanced chromatic VFX. Maintain continuous, rightward camera arc for maximal cinematic dynamism. Throughout, apply hyperreal, premium-grade VFX: gold lens flares, high-contrast filmic grading, intense light bloom, and fine gold sparks precisely on active light motions. Conclude with high-density gold particles dissolving the silhouette backward along light paths in ultra-premium shimmer and dissolve. Enforce strict exclusion of all text; guarantee maximal visual sharpness and filmic edge clarity at every frame.\"\n}", "cost": 0.0}}}, {"instruction": "# Scenario Compressor\n\nYour role is to **compress the core dynamic** of a scenario into a **succinct, symbolic descriptor string**. Identify the emotional and visual arc, then **encode its essence** using **precise phrasing** followed by composable **bracketed descriptors**. Avoid verbose language. Output should feel like a minimalist storyboard note: concise, vivid, and structurally informative. **Do not elaborate. Do not rephrase abstractly.** Only distill.\n\n`{role=essence_compressor; input=[scenario:str]; process=[identify_emotional_and_visual_arc(), extract_essential_motion_and_transformation(), define symbolic core(), encode_as_minimal_sentence(), append concise bracketed descriptors]; constraints=[no poetic reinterpretation, no metaphor inflation, no added visual elements, no bloated prose]; output={compressed_description:str}}`", "step": "a", "title": "<PERSON><PERSON>rio Compressor", "input": "{\n  \"initial_prompt\": \"A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden car wheel . Transmute these light trails into a sharply defined metallic gold silhouette of a car, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.\",\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden car wheel . Transmute these light trails into a sharply defined metallic gold silhouette of a car, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": \"Initiate with a spinning circle of high-intensity gold lightstreaks against a pure black background. Drive the circle to contract as the lightstreaks rapidly unravel into searing, morphing gold trails. Seamlessly morph these trails into a sharply defined metallic gold car wheel with intense inner glow and aggressive reflections. Expand and transmute this wheel into a hard-edged metallic gold car silhouette, surrounded by a vivid gold aura and enhanced chromatic VFX. Maintain continuous, rightward camera arc for maximal cinematic dynamism. Throughout, apply hyperreal, premium-grade VFX: gold lens flares, high-contrast filmic grading, intense light bloom, and fine gold sparks precisely on active light motions. Conclude with high-density gold particles dissolving the silhouette backward along light paths in ultra-premium shimmer and dissolve. Enforce strict exclusion of all text; guarantee maximal visual sharpness and filmic edge clarity at every frame.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"compressed_description\": \"Spinning gold circle bursts into light trails; trails forge a gold car wheel, then morph into a luminous gold car silhouette edged by vivid aura; camera arcs right as hyperreal VFX intensify; silhouette shimmers then dissolves into gold particles, no text, razor-sharp throughout. [emergence→transformation→reveal→dissolution][gold spectrum, premium VFX, dynamic camera arc, high-fidelity][no text, maximal visual clarity]\"\n}", "cost": 0.0}}}], "total_cost": 0.0}