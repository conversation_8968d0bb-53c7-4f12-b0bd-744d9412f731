# RigOfficeDownloader
*Automated Document Retrieval for NOV RigDoc*

## Overview
Eliminates tedious manual document retrieval from NOV's RigDoc system through intelligent automation. Preserves 75%+ engineering time by streamlining the 3D modeling preparation workflow.

## Key Features
```
◼ Three-Stage Workflow  ◼ Interactive Menu  ◼ Human Checkpoints
◼ Metadata-Powered File Organization  ◼ Pattern-Based Filter Chains
◼ Markdown Review Interface  ◼ Smart Browser Automation
```

## Workflow Architecture
**1. Document Retrieval**
```python
fetch_docs() → <rig>-a-docs.json  # Scrape metadata
json_to_md_table() → <rig>-a-docs.md  # Edit item_include=true
md_table_to_json()  # Commit selections
```
**2. File Processing**
```python
fetch_files() → <rig>-b-files.json  # Get file metadata
json_to_md_table() → <rig>-b-files.md  # Set item_download=true
md_table_to_json()  # Finalize choices
```
**3. Smart Download**
```python
download_files()  # Auto-organize with:
• /subfolders from item_generated_name
• Sanitized filenames
• Deduplication
```

## Getting Started
```bash
# 1. Initialize environment
py_venv_init.bat

# 2. Launch utility
RigOfficeDownloader-v4.bat [--auto|--interactive|--config]
```
**First-Run Configuration**
```python
CONFIG = {
    "rig_number": "R0000.020",
    "search_urls": PROJECTINFO_GAD,  # Prebuilt equipment templates
    "filters": [{
        "type": "docs",
        "pattern": "*G000*",
        "field": "item_include",
        "value": True
    }]
}
```

## File Organization
```
outputs/
├── data/               # Metadata control files
│   ├── R1234-a-docs.json
│   ├── R1234-a-docs.md
│   ├── R1234-b-files.json
│   └── R1234-b-files.md
└── downloads/          # Final organized files
    └── R1234/
        ├── Drilling/
        │   └── G0001_REV03--CASE45-DrillFloor.pdf
        └── Topdrive/
            └── ASM-00_REV12--CASE32-TopDriveAssembly.dwg
```

## Interactive Control
```
[0] Change rig number/search URLs    [4] Import doc selections
[1] Configure filter chain           [5] Fetch file metadata
[2] Fetch documents                  [6] Export files to Markdown
[3] Export docs to Markdown          [7] Import file selections
[8] Download files                   [9] Advanced configuration
```

## Why This Tool?
| Manual Process               | Automated Solution           |
|------------------------------|-------------------------------|
| 4-6 hours per project setup  | <1 hour with 75% automation  |
| Error-prone file management  | Metadata-driven organization |
| No version control           | JSON/Markdown audit trail    |
| Context loss between teams   | Standardized naming schema   |

