# RigOfficeDownloader

## Overview
RigOfficeDownloader is an automation tool designed to streamline the process of retrieving and downloading technical documentation from NOV's RigDoc system. It eliminates the tedious, time-consuming process of manual document retrieval, allowing engineers to focus on their primary work.

## Key Features
- **Three-Stage Workflow**: Documents → Files → Downloads
- **Interactive Menu**: Choose which steps to execute
- **User Control Points**: Review and select documents/files via Markdown interfaces
- **Smart File Organization**: Subfolder support based on naming patterns
- **Configurable Filters**: Apply filter chains to automatically select relevant documents

## Workflow
1. **Document Retrieval**: Scrapes document metadata from RigDoc
2. **Document Selection**: Exports to Markdown for user review and selection
3. **File Metadata**: Fetches file information for selected documents
4. **File Selection**: Exports to Markdown for user review and selection
5. **Download**: Downloads selected files with intelligent naming and organization

## Setup
1. Run `py_venv_init.bat` to create and initialize the Python virtual environment
2. The script will:
   - Find available Python installations
   - Create a virtual environment
   - Install required packages from requirements.txt

## Usage
1. Run `RigOfficeDownloader-v4.bat` to start the application
2. Use the interactive menu to:
   - Configure search parameters
   - Run specific workflow steps
   - Apply filter chains
   - Review and select documents/files

## Benefits
- Reduces documentation gathering time by 75%+
- Maintains consistent file organization
- Provides user control at key decision points
- Handles errors gracefully
- Preserves document context and relationships

## File Structure
```
outputs/
├── data/           # JSON and Markdown files for documents and files
└── downloads/      # Downloaded files organized by rig number
```

## Requirements
- Windows OS
- Python 3.6+
- Chrome browser (for Selenium automation)
