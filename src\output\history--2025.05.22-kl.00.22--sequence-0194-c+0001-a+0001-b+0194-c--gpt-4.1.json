{"initial_prompt": " Camera Movement: FPV shot diving into the ocean. Scene Description: The camera dives from a cliff into the ocean, showing the transition from air to water. Additional Details: The scene is extremely vivid, and when transitioning into the water the water changes hue to reds.", "sequence_id": "sequence-0194-c+0001-a+0001-b+0194-c", "results": [{"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": " Camera Movement: FPV shot diving into the ocean. Scene Description: The camera dives from a cliff into the ocean, showing the transition from air to water. Additional Details: The scene is extremely vivid, and when transitioning into the water the water changes hue to reds.", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"[fpv] diving from a high cliff, immersive transition from crisp airy landscape to plunging into vivid ocean, as the camera enters water, liquid hues morph dramatically to deep reds [morph][lighting_change]\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "{\n  \"initial_prompt\": \" Camera Movement: FPV shot diving into the ocean. Scene Description: The camera dives from a cliff into the ocean, showing the transition from air to water. Additional Details: The scene is extremely vivid, and when transitioning into the water the water changes hue to reds.\",\n  \"runwayml_prompt\": \"[fpv] diving from a high cliff, immersive transition from crisp airy landscape to plunging into vivid ocean, as the camera enters water, liquid hues morph dramatically to deep reds [morph][lighting_change]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Create a first-person view (FPV) animation sequence depicting a camera diving from a high cliff into the ocean. Ensure the shot vividly illustrates the transition from air to water, emphasizing an immersive change in environment. As the camera enters the water, dynamically shift the ocean's hue from natural colors to deep reds, highlighting a dramatic morph in lighting and liquid color. Retain high visual clarity and accentuate the vividness of the scene throughout the sequence.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \" Camera Movement: FPV shot diving into the ocean. Scene Description: The camera dives from a cliff into the ocean, showing the transition from air to water. Additional Details: The scene is extremely vivid, and when transitioning into the water the water changes hue to reds.\",\n  \"instruction_format\": \"Create a first-person view (FPV) animation sequence depicting a camera diving from a high cliff into the ocean. Ensure the shot vividly illustrates the transition from air to water, emphasizing an immersive change in environment. As the camera enters the water, dynamically shift the ocean's hue from natural colors to deep reds, highlighting a dramatic morph in lighting and liquid color. Retain high visual clarity and accentuate the vividness of the scene throughout the sequence.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": \"FPV animation: <PERSON> dives off a cliff into the ocean, vividly transitioning from air to water. On entry, water hue shifts dramatically from natural to deep reds, accentuating immersion and visual clarity.\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \" Camera Movement: FPV shot diving into the ocean. Scene Description: The camera dives from a cliff into the ocean, showing the transition from air to water. Additional Details: The scene is extremely vivid, and when transitioning into the water the water changes hue to reds.\",\n  \"distilled_essence\": \"FPV animation: Camera dives off a cliff into the ocean, vividly transitioning from air to water. On entry, water hue shifts dramatically from natural to deep reds, accentuating immersion and visual clarity.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"FPV dive from a cliff into the ocean, [fpv], vividly detailed, [dolly] towards the water, seamless transition from sky to underwater, [lighting_change] as camera enters water, [morph] natural blue hues to deep reds, accentuating immersion and vibrant clarity\"\n}", "cost": 0.0}}}], "total_cost": 0.0}