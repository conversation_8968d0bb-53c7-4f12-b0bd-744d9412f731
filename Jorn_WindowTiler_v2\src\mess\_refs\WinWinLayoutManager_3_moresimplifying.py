# Import modules
import os
import sys
import re
import time
import ctypes
import urllib.parse
from enum import Enum
# Import pywin32 modules
import win32api
import win32com.client
import win32com.shell.shellcon as shellcon
import win32con
import win32gui
import win32process
import ctypes
import pythoncom


# Import ppretty (for displaying the structure of objects/classes)
from ppretty import ppretty

"""
IMPORT CONSTANTS:
Constants are not complete memory addresses, they are constant offset values
that are used in conjunction with functions like win32gui.GetWindowLong to
access specific information stored in memory, by providing the offset at which
the information is stored."
"""
from win32con import CW_USEDEFAULT
from win32con import IDI_APPLICATION
from win32con import IMAGE_ICON
from win32con import LR_DEFAULTSIZE
from win32con import LR_LOADFROMFILE
from win32con import WM_DESTROY
from win32con import WM_USER
from win32con import WS_OVERLAPPED
from win32con import WS_BORDER
from win32con import GWL_STYLE # Window style
from win32con import GWL_EXSTYLE # Extended window style



# GLOBAL FN: Creates a Windows Shell COM object instance and retrieve 'special folder' paths.
def initialize_shell_windows_instance():
    # In Windows, 'Shell.Application' refers to a COM class that provides access
    # to the top-level object of the Windows Shell (shell32.dll), which includes
    # functionality related to special folders, file explorer windows, accessing
    # folder view settings, desktop, taskbar, etc.
    #
    # The following steps are taken:
    # - Create and return a ShellObject instance of the Shell.Application ProgID.
    # - Create a ShellWindows object (contains all open shell-window instances).
    # - Dictionary map {HWND:SHELL} to enable shell-instances to be reached by HWND.
    shell_object_instance = win32com.client.Dispatch('Shell.Application')
    shell_window_instances = shell_object_instance.Windows()
    shell_window_mapping = {shell.HWND: shell for shell in shell_window_instances}

    # In Windows, a 'special folder' refers to a folder represented by an interface
    # rather than a specific path (e.g. 'Desktop', 'Control Panel', etc). They are
    # identified by unique constants called 'CSIDL' (Constant Special Item ID List).
    #
    # The following steps are taken:
    # - Use 'shellcon' to list the 'CSIDL' identifiers and retrieve their constants.
    # - Use 'shell_object_instance' to create shell object namespaces for each constant.
    # - Filter out any invalid namespaces (without a shell Application property).
    # - Retrieve identifier ('Name') and path ('Path') from each namespace.
    # - Dictionary map {'Name':'Path'} to enable path to be reached from 'title'.
    CSIDL_CONSTANTS = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]
    csidl_namespaces = [shell_object_instance.Namespace(constant) for constant in CSIDL_CONSTANTS]
    valid_namespaces = [namespace for namespace in csidl_namespaces if hasattr(namespace, 'Application')]
    special_folders = [[namespace.Self.Name, namespace.Self.Path] for namespace in valid_namespaces]
    special_folders_mapping = {item[0]: item[1] for item in special_folders}

    # Return the result
    return shell_window_mapping, special_folders_mapping



# The 'WindowType' class represents classification of specific window types.
class WindowType(Enum):
    """
    Enumeration class to represent the different types of windows.
    - SPECIAL_FOLDER: explorer window with mapped path to CSIDL constant.
    - NORMAL_FOLDER: explorer window with a retrievable path.
    - UNSPECIFIED: window with a process other than explorer.
    - UNLINKED: window with title and class but no process.
    - UNKNOWN: any remaining windows not matched with a type.
    """
    SPECIAL_FOLDER = 1, 'SPECIAL_FOLDER'
    NORMAL_FOLDER = 2, 'NORMAL_FOLDER'
    UNSPECIFIED = 3, 'UNSPECIFIED'
    UNLINKED = 4, 'UNLINKED'
    UNKNOWN = 5, 'UNKNOWN'



# The 'Window' class represents a window object and holds its properties and methods.
# Each instance stores data on one single window
class Window:
    # Generate a Window object (from 'HWND')
    def __init__(self, hwnd, shell_window_mapping, special_folders_mapping):
        # Generate common window data.
        self.hwnd = hwnd
        self.hwnd_enabled = win32gui.IsWindowEnabled(hwnd)
        self.hwnd_visible = win32gui.IsWindowVisible(hwnd)
        self.hwnd_title = win32gui.GetWindowText(hwnd)
        self.hwnd_class = win32gui.GetClassName(hwnd)
        self._hwnd_placement = win32gui.GetWindowPlacement(hwnd)
        self._hwnd_rect = win32gui.GetWindowRect(hwnd)
        self.hwnd_controls_state = self._hwnd_placement[1]
        self.hwnd_position = (self._hwnd_rect[0], self._hwnd_rect[1])
        self.hwnd_size = ((self._hwnd_rect[2]-self._hwnd_rect[0]), (self._hwnd_rect[3]-self._hwnd_rect[1]))

        # Prepare instance variables for the window type and create-command.
        self.hwnd_type = None
        self.hwnd_create_cmd = None

        # Prepare instance variables for (explorer) folder view options.
        self.folder_icon_size = None
        self.folder_view_mode = None
        self.folder_sort_column = None
        self.folder_group_by = None
        # Prepare instance variable for file selection in folder.
        self.folder_selected_files = None
        self.folder_all_files = None
        self.folder_focused_file = None

        # Prepare instance variables for the window process.
        self.hwnd_process = None
        self.hwnd_process_id = None


        # Retrieve the process handle of the window
        hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(self.hwnd)
        hwnd_process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
        hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(hwnd_process_query, False, hwnd_process_id)
        # If a process handle was optained
        if hwnd_process_handle:
            # Update instance variables with the executable path and id
            self.hwnd_process = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
            self.hwnd_process_id = hwnd_process_id
            # Update 'type' with the identifier 'UNSPECIFIED'.
            self.hwnd_type = WindowType.UNSPECIFIED
        # Else if no process was retrieved, set 'type' to 'UNLINKED'.
        elif not self.hwnd_type:
            self.hwnd_type = WindowType.UNLINKED


        # If this is a Windows File-Explorer Window (typically a folder/directory)
        if self.hwnd_class == 'CabinetWClass':
            # Retrieve the folder path through its shell instance
            hwnd_shell_instance = shell_window_mapping[self.hwnd]
            hwnd_shell_path = hwnd_shell_instance.LocationURL

            # If it's a 'SPECIAL_FOLDER' (explorer window without retrievable path):
            # - Update the instance variable 'type' with the identifier 'SPECIAL_FOLDER'.
            # - Check if the path refers to a GUID (global unique identification number).
            # - Transform the path into a cmd-command (using 'Shell:{GUID}' or 'File:/URI'),
            #   this is to make the path executable (in that it creates the actual window).
            # - Update the instance variable 'create_cmd' with the modified path-command.
            if self.hwnd_title in special_folders_mapping:
                self.hwnd_type = WindowType.SPECIAL_FOLDER
                folder_path = special_folders_mapping[self.hwnd_title]
                folder_path_guid_match = bool(re.search(r'::{[\w-]*}', folder_path))
                folder_path_is_guid = folder_path_guid_match if not os.path.exists(folder_path) else False
                command_prefix = 'Shell:' if folder_path_is_guid else 'File:/'
                create_command = os.path.normpath(urllib.parse.unquote(f'{command_prefix}{folder_path}'))
                self.hwnd_create_cmd = create_command

            # Else it's a 'NORMAL_FOLDER' (explorer window with a retrievable path):
            # - Update the instance variable 'type' with the identifier 'NORMAL_FOLDER'.
            # - Update the instance variable 'create_cmd' with the path (URI).
            # - Update instance variables for folder view options.
            # - Update instance variables for files in folder.
            elif (hwnd_shell_path != ''):
                self.hwnd_type = WindowType.NORMAL_FOLDER
                self.hwnd_create_cmd = os.path.normpath(urllib.parse.unquote(hwnd_shell_path))
                folder_obj = hwnd_shell_instance.Document
                self.folder_icon_size = folder_obj.IconSize
                self.folder_view_mode = folder_obj.CurrentViewMode
                self.folder_sort_column = folder_obj.SortColumns
                self.folder_group_by = folder_obj.GroupBy
                self.folder_selected_files = [file.Name for file in folder_obj.SelectedItems()]
                self.folder_all_files = [file.Name for file in folder_obj.Folder.Items()]
                self.folder_focused_file = folder_obj.FocusedItem.Name if folder_obj.FocusedItem else None

        # If a title and class was found but no type has been retrieved (no associated window process):
        # - Set instance variable 'type' to 'UNLINKED'.
        if self.hwnd_title and self.hwnd_class and not self.hwnd_type:
            self.hwnd_type = WindowType.UNLINKED





class WindowManager:
    def __init__(self):
        # Initialize an empty list to store Window objects.
        self.all_windows = []

        # Initialize condition: Visible (dette blir en input).
        self.visible_windows_only = True

        # Initialize a Windows Shell COM object and retrieve 'special folder' paths.
        self.shell_instance_mapped, self.special_folders_mapped = initialize_shell_windows_instance()


        self.counter = 0

        # Instead of using win32gui.EnumWindows for retrieving all windows, we are using a
        # continous while loop
        # RETRIEVE AND RETURN LAYOUT DATA FOR CURRENTLY OPEN FILE-EXPLORER WINDOWS
        # def GET_WINS_ZORDER():
        #     # Initialize a list to store data for all (currently open) window handles in
        #     window_data_retrieved = []
        #     # Get the handle of the window that is at the top of the z-order (the window on top)
        #     current_window_handle = win32gui.GetTopWindow(None)
        #     # Use a while loop to ensure traversing through all windows (in the z-order)
        #     while current_window_handle:
        #         window_data_retrieved.append(current_window_handle)
        #         # Get the next window in the z-order
        #         current_window_handle = win32gui.GetWindow(current_window_handle, win32con.GW_HWNDNEXT)

        #     # Return the current layout window data (as list with dictionaries for each currently open file-explorer windows)
        #     return window_data_retrieved
        # a = GET_WINS_ZORDER()



# The hwnd argument is the window handle (HWND) of the current window. The data argument is the data argument that was passed to EnumWindows.
# The callback function should return True to continue enumerating windows, or False to stop enumerating.

    # def get_all_open_windows_enumerate(self):
    #     def window_enum_handler(hwnd, ctx):
    #         if win32gui.IsWindowVisible(hwnd):
    #             print ( hex( hwnd ), win32gui.GetWindowText( hwnd ) )

    #     win32gui.EnumWindows( window_enum_handler, None )
    def get_open_windows(self):
        # Reset the list of Window objects
        self.all_windows = []


        # Instead of using
        # Retrieve all currently open windows in the Z order
        # Instead of using enumerating all windows through win32gui.EnumWindows,
        # a continous while loopto enumerate all windows, for retrieving all windows, we are using a
        # continous while loop
        # RETRIEVE AND RETURN LAYOUT DATA FOR CURRENTLY OPEN FILE-EXPLORER WINDOWS
        # Initialize a list to store data for all (currently open) window handles in
        # window_data_retrieved = []

        # # Get the handle of the window that is at the top of the z-order (the window on top)
        # current_hwnd = win32gui.GetTopWindow(None)
        # # Use a while loop to ensure traversing through all windows (in the z-order)
        # while current_hwnd:
        #     window_data_retrieved.append(current_hwnd)
        #     # Get the next window in the z-order
        #     current_hwnd = win32gui.GetWindow(current_hwnd, win32con.GW_HWNDNEXT)

        # # Return the current layout window data (as list with dictionaries for each currently open file-explorer windows)
        # return window_data_retrieved
        # a = GET_WINS_ZORDER()

    def wm_get_open_windows(self):
        # Reset the list of Window objects
        self.all_windows = []
        # Enumerate all windows and pass them to the wm_enumerate_windows_callback method
        win32gui.EnumWindows(self.wm_enumerate_windows_callback, None)

    def wm_enumerate_windows_callback(self, hwnd, _):
        # visibleOnly or AllWindows
        if (self.visible_windows_only and win32gui.IsWindowVisible(hwnd)) or (not self.visible_windows_only):
            # Create a Window object for the current window
            window = Window(hwnd, self.shell_instance_mapped, self.special_folders_mapped)
            # print(window)
            #
            # Get additional type-specific window data for the current window

            # Add the current window to the list of windows
            self.all_windows.append(window)





    def return_windows(self):
        # Reset the list of Window objects
        return self.all_windows






    def wm_save(self, filename):
        # Open the file at the specified filepath in write mode
        # with open(filename, "w", encoding="utf-8") as file:
        with open(filename, "w") as file:
            # Iterate through the list of retrieved windows
            for window in self.all_windows:
                if window.hwnd_type in [WindowType.SPECIAL_FOLDER, WindowType.NORMAL_FOLDER]:
                # if True:
                    print('hwnd: %s' % str(window.hwnd))
                    print('hwnd_visible: %s' % str(window.hwnd_visible))
                    print('hwnd_title: %s' % str(window.hwnd_title))
                    print('hwnd_class: %s' % str(window.hwnd_class))
                    print('hwnd_controls_state: %s' % str(window.hwnd_controls_state))
                    print('hwnd_position: %s' % str(window.hwnd_position))
                    print('hwnd_size: %s' % str(window.hwnd_size))
                    print('hwnd_process: %s' % str(window.hwnd_process))
                    print('hwnd_process_id: %s' % str(window.hwnd_process_id))
                    print('hwnd_create_cmd: %s' % str(window.hwnd_create_cmd))
                    print('hwnd_type: %s' % str(window.hwnd_type))
                    # print('-')
                    print('hwnd_placement: %s' % str(window._hwnd_placement))
                    print('hwnd_rect: %s' % str(window._hwnd_rect))
                    #
                    print('folder_icon_size: %s' % str(window.folder_icon_size))
                    print('folder_view_mode: %s' % str(window.folder_view_mode))
                    print('folder_sort_column: %s' % str(window.folder_sort_column))
                    print('folder_group_by: %s' % str(window.folder_group_by))
                    print('folder_selected_files: %s' % str(window.folder_selected_files))
                    # print('folder_all_files: %s' % str(window.folder_all_files))
                    print('folder_focused_file: %s' % str(window.folder_focused_file))
                    print('--------------------------------------\n')
                    # Save the current window to the file
                    # window.save(file)
                    # file.write(f"\n\n")
                    # file.write(f"{window.type}\n")
                    # file.write(f"{window.title}\n")
                    # file.write(f"{window.class_name}\n")
                    # file.write(f"{window.position[0]}, {window.position[1]}\n")
                    # file.write(f"{window.size[0]}, {window.size[1]}\n")
                    # if window.process:
                    #     file.write(f"{window.process}\n")
                    # if window.path:
                    #     file.write(f"{window.path}\n")

                # print('\n\n')
                # print("[WindowType.UNSPECIFIED, WindowType.UNLINKED, WindowType.UNKNOWN]:")
                # if window.hwnd_type in [WindowType.UNSPECIFIED, WindowType.UNLINKED, WindowType.UNKNOWN]:
                #     # print(win32gui.GetWindow(window.hwnd, GWL_EXSTYLE))
                #     # print('%s: %s' % (window.hwnd, window.title))
                #     # print(win32gui.GetWindowPlacement(window.hwnd))
                #     print('hwnd: %s' % str(window.hwnd))
                #     print('hwnd_visible: %s' % str(window.hwnd_visible))
                #     print('hwnd_title: %s' % str(window.hwnd_title))
                #     print('hwnd_class: %s' % str(window.hwnd_class))
                #     print('hwnd_controls_state: %s' % str(window.hwnd_controls_state))
                #     print('hwnd_position: %s' % str(window.hwnd_position))
                #     print('hwnd_size: %s' % str(window.hwnd_size))
                #     print('hwnd_process: %s' % str(window.hwnd_process))
                #     print('hwnd_process_id: %s' % str(window.hwnd_process_id))
                #     print('hwnd_create_cmd: %s' % str(window.hwnd_create_cmd))
                #     print('hwnd_type: %s' % str(window.hwnd_type))
                #     # print('-')
                #     print('hwnd_placement: %s' % str(window._hwnd_placement))
                #     print('hwnd_rect: %s' % str(window._hwnd_rect))
            print("---------------------")
            print(len(self.all_windows))

# def main(args):
#     # If the first command-line argument is 'save'
#     if args[0] == "save":
#         # Create a new WindowManager object
#         manager = WindowManager()
#         # Update the list of windows in the WindowManager object
#         manager.wm_get_open_windows()
#         # Save the windows to the file at the specified filepath
#         manager.wm_save(args[1])
#     # If the first command-line argument is 'load'
#     elif args[0] == "load":
#         pass


# manager = WindowManager()
# manager.wm_get_open_windows()
# manager.wm_save("windows.txt")
print("WindowManager")
manager = WindowManager()
print("wm_get_open_windows")
manager.wm_get_open_windows()
print("wm_save")
manager.wm_save("windows2.txt")
print("return_windows")
a = manager.return_windows()
for x in a:
    print(x)
print("xxxx")
# print(dir(a))
# print(len(a))
# print(a)
# for x in a:
#     print(x.hwnd_title)
#     print(dir(x))
