import logging
from typing import Any, cast

from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>


# Customized logging class
class Logger(logging.Logger):
    """
    Custom Logger class for the project, extending Python's standard logging.Logger.

    Features:
    - Rich text format and color support using the 'rich' library.
    - Custom logging levels: VERBOSE, ADVANCED, and SUCCESS in addition to standard levels.
    - Special methods for raw text logging, empty line printing, and automatic exit on critical logs.

    Usage:
    - Initialization and verbosity setting:
        from log import logger
        logger.setVerbosity(0)  # INFO level (default)
        logger.setVerbosity(1)  # VERBOSE level
        logger.setVerbosity(2)  # ADVANCED level
        logger.setVerbosity(3)  # DEBUG level
        logger.setVerbosity(quiet=True)  # CRITICAL level only

    - Logging messages:
        logger.debug("Debug message")
        logger.advanced("Advanced message")
        logger.verbose("Verbose message")
        logger.info("Info message")
        logger.success("Success message")
        logger.warning("Warning message")
        logger.error("Error message")
        logger.critical("Critical message")
        logger.exception("Exception message")

    - Additional utilities:
        logger.raw('Raw message', level=Logger.DEBUG)  # Log raw message at DEBUG level
        logger.empty_line()  # Print an empty line at INFO level
        logger.empty_line(log_level=Logger.DEBUG)  # Print an empty line at DEBUG level

    - Integration with argparse for command-line verbosity control:
        parser = argparse.ArgumentParser()
        parser.add_argument("-v", "--verbose", action="count", default=0)
        args = parser.parse_args()
        logger.setVerbosity(args.verbose)

    Methods:
    - setVerbosity(verbose_level: int, quiet: bool): Sets the logging verbosity.
    - debug, info, warning, error, critical, exception: Standard logging methods with rich text support.
    - advanced, verbose, success: Custom logging methods for additional levels.
    - raw: Logs raw text, bypassing standard logging formatting.
    - empty_line: Prints an empty line at the specified log level.

    Note: This class uses the global 'console' object from the 'rich.console' module for output.
    """
    # Predefined log levels
    CRITICAL: int = logging.CRITICAL  # 50
    ERROR: int = logging.ERROR  # 40
    WARNING: int = logging.WARNING  # 30
    SUCCESS: int = 25
    INFO: int = logging.INFO  # 20
    VERBOSE: int = 15
    ADVANCED: int = 13
    DEBUG: int = logging.DEBUG  # 10

    @staticmethod
    def setVerbosity(verbose_level: int = 0, quiet: bool = False):
        """
        Set logging level accordingly to the verbose count or with quiet enable.
        Args:
            verbose_level: Set the verbosity level: 1 = Verbose, 2 = Advanced, 3 = Debug
            quiet: If true, set the verbosity to critical messages only.

        Returns:

        """
        if quiet:
            logger.setLevel(logging.CRITICAL)
        elif verbose_level == 1:
            logger.setLevel(Logger.VERBOSE)
        elif verbose_level == 2:
            logger.setLevel(Logger.ADVANCED)
        elif verbose_level >= 3:
            logger.setLevel(logging.DEBUG)
        else:
            # Default INFO
            logger.setLevel(logging.INFO)

    def debug(self, msg: Any, *args: Any, **kwargs: Any) -> None:
        """Change default debug text format with rich color support"""
        super(Logger, self).debug("{}[D]{} {}".format("[bold yellow3]", "[/bold yellow3]", msg), *args, **kwargs)

    def advanced(self, msg: Any, *args: Any, **kwargs: Any) -> None:
        """Add advanced logging method with text format / rich color support"""
        if self.isEnabledFor(Logger.ADVANCED):
            self._log(Logger.ADVANCED,
                      "{}[A]{} {}".format("[bold yellow3]", "[/bold yellow3]", msg), args, **kwargs)

    def verbose(self, msg: Any, *args: Any, **kwargs: Any) -> None:
        """Add verbose logging method with text format / rich color support"""
        if self.isEnabledFor(Logger.VERBOSE):
            self._log(Logger.VERBOSE,
                      "{}[V]{} {}".format("[bold blue]", "[/bold blue]", msg), args, **kwargs)

    def raw(self, msg: Any, level=VERBOSE, markup=False, highlight=False, emoji=False, rich_parsing=False) -> None:
        """Add raw text logging, used for stream printing."""
        if rich_parsing:
            markup = True
            highlight = True
            emoji = True
        if self.isEnabledFor(level):
            if type(msg) is bytes:
                msg = msg.decode('utf-8', errors="ignore")
            # Raw message are print directly to the console bypassing logging system and auto formatting
            console.print(msg, end='', markup=markup, highlight=highlight, emoji=emoji)

    def info(self, msg: Any, *args: Any, **kwargs: Any) -> None:
        """Change default info text format with rich color support"""
        super(Logger, self).info("{}[*]{} {}".format("[bold blue]", "[/bold blue]", msg), *args, **kwargs)

    def warning(self, msg: Any, *args: Any, **kwargs: Any) -> None:
        """Change default warning text format with rich color support"""
        super(Logger, self).warning("{}[!]{} {}".format("[bold orange3]", "[/bold orange3]", msg), *args, **kwargs)

    def error(self, msg: Any, *args: Any, **kwargs: Any) -> None:
        """Change default error text format with rich color support"""
        super(Logger, self).error("{}[-]{} {}".format("[bold red]", "[/bold red]", msg), *args, **kwargs)

    def exception(self, msg: Any, *args: Any, **kwargs: Any) -> None:
        """Change default exception text format with rich color support"""
        super(Logger, self).exception("{}[x]{} {}".format("[bold red]", "[/bold red]", msg), *args, **kwargs)

    def critical(self, msg: Any, *args: Any, **kwargs: Any) -> None:
        """Change default critical text format with rich color support
        Add auto exit."""
        super(Logger, self).critical("{}[!]{} {}".format("[bold red]", "[/bold red]", msg), *args, **kwargs)
        exit(1)

    def success(self, msg: Any, *args: Any, **kwargs: Any) -> None:
        """Add success logging method with text format / rich color support"""
        if self.isEnabledFor(Logger.SUCCESS):
            self._log(Logger.SUCCESS,
                      "{}[+]{} {}".format("[bold green]", "[/bold green]", msg), args, **kwargs)

    def empty_line(self, log_level: int = logging.INFO) -> None:
        """Print an empty line."""
        import os
        self.raw(os.linesep, level=log_level)


# Global rich console object
console: Console = Console()

# Main logging default config
# Set default Logger class as Logger
logging.setLoggerClass(Logger)

# Add new level to the logging config
logging.addLevelName(Logger.VERBOSE, "VERBOSE")
logging.addLevelName(Logger.SUCCESS, "SUCCESS")
logging.addLevelName(Logger.ADVANCED, "ADVANCED")
# Logging setup using RichHandler and minimalist text format
logging.basicConfig(
    format="%(message)s",
    handlers=[RichHandler(rich_tracebacks=True,
                          show_time=False,
                          markup=True,
                          show_level=True,
                          show_path=True,
                          console=console)]
)

# Global logger object
logger: Logger = cast(Logger, logging.getLogger("main"))
# Default log level
logger.setLevel(logging.INFO)
