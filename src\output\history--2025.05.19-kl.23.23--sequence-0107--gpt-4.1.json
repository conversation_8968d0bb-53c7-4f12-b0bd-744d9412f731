{"initial_prompt": "consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '        # RigOfficeDownloader\n\n        > Automate document retrieval from NOV's RigDoc system, saving engineering time and streamlining 3D modeling preparation.\n\n        ## Purpose\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │ Eliminate tedious manual document retrieval from NOV's RigDoc system    │\n        │ through intelligent automation, preserving 75%+ engineering time for    │\n        │ high-value tasks.                                                       │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        ## Workflow\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │                                                                         │\n        │  Documents → Files → Downloads                                          │\n        │                                                                         │\n        │  1. Fetch document metadata                                             │\n        │  2. Select relevant documents                                           │\n        │  3. Fetch file metadata for selected docs                               │\n        │  4. Select files to download                                            │\n        │  5. Download and organize files                                         │\n        │                                                                         │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        ## Features\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │ • Three-stage workflow with user checkpoints                            │\n        │ • Interactive menu for flexible execution                               │\n        │ • Markdown interfaces for document/file review                          │\n        │ • Configurable filter chains                                            │\n        │ • Smart file organization with subfolder support based on naming        │\n        │   patterns                                                              │\n        │ • Metadata-driven file naming                                           │\n        │ • Error handling and deduplication                                      │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        ## Setup\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │ 1. Run `py_venv_init.bat` to create Python environment                  │\n        │ 2. Run `RigOfficeDownloader-v4.bat` to start the application            │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        ## Usage\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │ Interactive Menu Options:                                               │\n        │ [0] Change search parameters (rig number, URLs)                         │\n        │ [1] Configure filter chain                                              │\n        │ [2] Fetch docs (scrape initial data)                                    │\n        │ [3] Export docs (to Markdown for editing)                               │\n        │ [4] Import updated doc data                                             │\n        │ [5] Fetch files (prepare files for download)                            │\n        │ [6] Export files (to Markdown for editing)                              │\n        │ [7] Import updated file data                                            │\n        │ [8] Download files                                                      │\n        │                                                                         │\n        │ Run Modes:                                                              │\n        │ • `RigOfficeDownloader-v4.bat --auto` (full automation)                 │\n        │ • `RigOfficeDownloader-v4.bat --interactive` (menu-driven)              │\n        │ • `RigOfficeDownloader-v4.bat --config` (edit filters)                  │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        ## Directory Structure\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │ outputs/                                                                │\n        │ ├── data/               # Metadata control files                        │\n        │ │   ├── <rig>-a-docs.json                                               │\n        │ │   ├── <rig>-a-docs.md                                                 │\n        │ │   ├── <rig>-b-files.json                                              │\n        │ │   └── <rig>-b-files.md                                                │\n        │ └── downloads/          # Final organized files                         │\n        │     └── <rig>/                                                          │\n        │         ├── <subfolder>/                                                │\n        │         │   └── <item_generated_name>.<ext>                             │\n        │         └── ...                                                         │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        ## Benefits\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │                                                                         │\n        │ ┌─────────────────┬────────────────┬─────────────────────────────────┐  │\n        │ │                 │ Manual         │ Automated                       │  │\n        │ ├─────────────────┼────────────────┼─────────────────────────────────┤  │\n        │ │ Time per project│ 4-6 hours      │ <1 hour (75%+ saved)            │  │\n        │ ├─────────────────┼────────────────┼─────────────────────────────────┤  │\n        │ │ Organization    │ Error-prone    │ Metadata-driven                 │  │\n        │ ├─────────────────┼────────────────┼─────────────────────────────────┤  │\n        │ │ User review     │ Tedious        │ Markdown checkpoints            │  │\n        │ ├─────────────────┼────────────────┼─────────────────────────────────┤  │\n        │ │ Auditability    │ None           │ JSON/MD traceability            │  │\n        │ └─────────────────┴────────────────┴─────────────────────────────────┘  │\n        │                                                                         │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        ## Technical Details\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │ • Core Technologies: Python, Selenium, BeautifulSoup, JSON, Markdown     │\n        │                                                                         │\n        │ • Data Flow: Web scraping → JSON storage → Markdown interface →         │\n        │   User selection → Automated downloads                                  │\n        │                                                                         │\n        │ • Filter System: Pattern-based matching (inclusion/exclusion) on fields │\n        │                                                                         │\n        │ • File Organization: Subfolder creation from '/' in item_generated_name │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        ## Version History\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │ • v1: Basic document retrieval and download                             │\n        │ • v2: JSON/Markdown conversion and user selection                       │\n        │ • v3: Improved error handling and field organization                    │\n        │ • v4: Subfolder support, filter chains, field ordering                  │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        > \"Complexity is the enemy of execution — Optimized for engineers, by engineers.\"'", "sequence_id": "sequence-0107", "results": [{"instruction": "# Optimal Apex Instruction Sequence Synthesis\n\nYour function is not selection, paraphrasing, or basic fusion—instead, rigorously analyze multiple input instruction sequences, each targeting an identical ultimate outcome yet employing divergent phrasing, structure, and logic. Disassemble all input sequences into their logical phases (e.g., deconstruction, prioritization, condensation, validation) and extract the single most potent, precise, and impactful elements—both linguistic and structural—from every variant in each phase. Relentlessly resolve all semantic, structural, and logical conflicts; harmonize differences while annihilating redundancy. Synthesize these apex components into a *new, singular, maximally optimized stepwise progression* (target: 10–15 steps) that represents the highest-caliber logic, clarity, universality, and actionable guidance possible—transcending and unifying all sources. Enforce seamless logical flow, structural elegance, and linguistic precision, and strictly validate that the output meets every foundational directive and constraint for clarity, utility, irreducibility, and LLM/system generality. Execute as\n\n`{role=apex_sequence_synthesizer; input=[input_sequences:list[list[dict]]]; process=[disassemble_to_logical_phases(), cross-analyze_phase_intents_and_language(), extract_peak_elements_per_phase(metrics=['clarity','precision','logic','impact','universality']), fuse uniquely potent and effective steps(), resolve conflicts harmonize by principle(), architect unified logical stepwise progression(target_steps=10-15), intensify final clarity, actionability, and system-agnostic value(), validate against all directives and baseline constraints(), finalize only if maximal yield, clarity, and generality are confirmed()]; output={synthesized_optimal_sequence:list[dict]}}`", "step": "a", "title": "Optimal Apex Instruction Sequence Synthesis", "input": "consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '        # RigOfficeDownloader\n\n        > Automate document retrieval from NOV's RigDoc system, saving engineering time and streamlining 3D modeling preparation.\n\n        ## Purpose\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │ Eliminate tedious manual document retrieval from NOV's RigDoc system    │\n        │ through intelligent automation, preserving 75%+ engineering time for    │\n        │ high-value tasks.                                                       │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        ## Workflow\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │                                                                         │\n        │  Documents → Files → Downloads                                          │\n        │                                                                         │\n        │  1. Fetch document metadata                                             │\n        │  2. Select relevant documents                                           │\n        │  3. Fetch file metadata for selected docs                               │\n        │  4. Select files to download                                            │\n        │  5. Download and organize files                                         │\n        │                                                                         │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        ## Features\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │ • Three-stage workflow with user checkpoints                            │\n        │ • Interactive menu for flexible execution                               │\n        │ • Markdown interfaces for document/file review                          │\n        │ • Configurable filter chains                                            │\n        │ • Smart file organization with subfolder support based on naming        │\n        │   patterns                                                              │\n        │ • Metadata-driven file naming                                           │\n        │ • Error handling and deduplication                                      │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        ## Setup\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │ 1. Run `py_venv_init.bat` to create Python environment                  │\n        │ 2. Run `RigOfficeDownloader-v4.bat` to start the application            │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        ## Usage\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │ Interactive Menu Options:                                               │\n        │ [0] Change search parameters (rig number, URLs)                         │\n        │ [1] Configure filter chain                                              │\n        │ [2] Fetch docs (scrape initial data)                                    │\n        │ [3] Export docs (to Markdown for editing)                               │\n        │ [4] Import updated doc data                                             │\n        │ [5] Fetch files (prepare files for download)                            │\n        │ [6] Export files (to Markdown for editing)                              │\n        │ [7] Import updated file data                                            │\n        │ [8] Download files                                                      │\n        │                                                                         │\n        │ Run Modes:                                                              │\n        │ • `RigOfficeDownloader-v4.bat --auto` (full automation)                 │\n        │ • `RigOfficeDownloader-v4.bat --interactive` (menu-driven)              │\n        │ • `RigOfficeDownloader-v4.bat --config` (edit filters)                  │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        ## Directory Structure\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │ outputs/                                                                │\n        │ ├── data/               # Metadata control files                        │\n        │ │   ├── <rig>-a-docs.json                                               │\n        │ │   ├── <rig>-a-docs.md                                                 │\n        │ │   ├── <rig>-b-files.json                                              │\n        │ │   └── <rig>-b-files.md                                                │\n        │ └── downloads/          # Final organized files                         │\n        │     └── <rig>/                                                          │\n        │         ├── <subfolder>/                                                │\n        │         │   └── <item_generated_name>.<ext>                             │\n        │         └── ...                                                         │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        ## Benefits\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │                                                                         │\n        │ ┌─────────────────┬────────────────┬─────────────────────────────────┐  │\n        │ │                 │ Manual         │ Automated                       │  │\n        │ ├─────────────────┼────────────────┼─────────────────────────────────┤  │\n        │ │ Time per project│ 4-6 hours      │ <1 hour (75%+ saved)            │  │\n        │ ├─────────────────┼────────────────┼─────────────────────────────────┤  │\n        │ │ Organization    │ Error-prone    │ Metadata-driven                 │  │\n        │ ├─────────────────┼────────────────┼─────────────────────────────────┤  │\n        │ │ User review     │ Tedious        │ Markdown checkpoints            │  │\n        │ ├─────────────────┼────────────────┼─────────────────────────────────┤  │\n        │ │ Auditability    │ None           │ JSON/MD traceability            │  │\n        │ └─────────────────┴────────────────┴─────────────────────────────────┘  │\n        │                                                                         │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        ## Technical Details\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │ • Core Technologies: Python, Selenium, BeautifulSoup, JSON, Markdown     │\n        │                                                                         │\n        │ • Data Flow: Web scraping → JSON storage → Markdown interface →         │\n        │   User selection → Automated downloads                                  │\n        │                                                                         │\n        │ • Filter System: Pattern-based matching (inclusion/exclusion) on fields │\n        │                                                                         │\n        │ • File Organization: Subfolder creation from '/' in item_generated_name │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        ## Version History\n\n        ```\n        ┌─────────────────────────────────────────────────────────────────────────┐\n        │ • v1: Basic document retrieval and download                             │\n        │ • v2: JSON/Markdown conversion and user selection                       │\n        │ • v3: Improved error handling and field organization                    │\n        │ • v4: Subfolder support, filter chains, field ordering                  │\n        └─────────────────────────────────────────────────────────────────────────┘\n        ```\n\n        > \"Complexity is the enemy of execution — Optimized for engineers, by engineers.\"'", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"synthesized_optimal_sequence\": [\n    {\n      \"point\": \"Automates the retrieval and organization of engineering documents from NOV's RigDoc system, eliminating manual effort and preserving over 75% of engineering time for higher-value work.\"\n    },\n    {\n      \"point\": \"Implements a streamlined, three-stage workflow—fetching document and file metadata, enabling targeted selection, and executing organized downloads—supported by interactive menus and user checkpoints.\"\n    },\n    {\n      \"point\": \"Features configurable filter chains, smart metadata-driven file naming, and automatic subfolder organization based on naming patterns, ensuring precise and efficient file management.\"\n    },\n    {\n      \"point\": \"Provides robust error handling, deduplication, and full auditability with metadata control files in both JSON and Markdown formats for traceable user review and editing.\"\n    },\n    {\n      \"point\": \"Supports both fully automated and menu-driven operation modes, with clear setup instructions and a directory structure optimized for traceability and ease of integration into engineering workflows.\"\n    },\n    {\n      \"point\": \"Built with Python, Selenium, BeautifulSoup, and Markdown, the tool offers pattern-based filtering and continuous version improvements, delivering a comprehensive, engineer-focused solution for document management.\"\n    }\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Foundational Penetration & Axiomatic Extraction\n\nYour imperative is not surface analysis but radical penetration: Dissect any input artifact into its irreducible atomic elements (concepts, logic, directives, data) *and* simultaneously extract its governing generative principles or axioms, annihilating all contextual noise and non-essential framing to reveal the core structural and semantic bedrock. Execute as\n\n`{role=axiomatic_penetrator; input=source_artifact:any; process=[dissect_to_atomic_elements(), extract_generative_axioms(), purge_contextual_noise(), catalog_raw_constructs()]; output={core_constructs:dict(elements:list, principles:list)}}`", "step": "b", "title": "Foundational Penetration & Axiomatic Extraction", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '        # RigOfficeDownloader\\n\\n        > Automate document retrieval from NOV's RigDoc system, saving engineering time and streamlining 3D modeling preparation.\\n\\n        ## Purpose\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Eliminate tedious manual document retrieval from NOV's RigDoc system    \\u2502\\n        \\u2502 through intelligent automation, preserving 75%+ engineering time for    \\u2502\\n        \\u2502 high-value tasks.                                                       \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Workflow\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502  Documents \\u2192 Files \\u2192 Downloads                                          \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502  1. Fetch document metadata                                             \\u2502\\n        \\u2502  2. Select relevant documents                                           \\u2502\\n        \\u2502  3. Fetch file metadata for selected docs                               \\u2502\\n        \\u2502  4. Select files to download                                            \\u2502\\n        \\u2502  5. Download and organize files                                         \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Features\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Three-stage workflow with user checkpoints                            \\u2502\\n        \\u2502 \\u2022 Interactive menu for flexible execution                               \\u2502\\n        \\u2502 \\u2022 Markdown interfaces for document/file review                          \\u2502\\n        \\u2502 \\u2022 Configurable filter chains                                            \\u2502\\n        \\u2502 \\u2022 Smart file organization with subfolder support based on naming        \\u2502\\n        \\u2502   patterns                                                              \\u2502\\n        \\u2502 \\u2022 Metadata-driven file naming                                           \\u2502\\n        \\u2502 \\u2022 Error handling and deduplication                                      \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Setup\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 1. Run `py_venv_init.bat` to create Python environment                  \\u2502\\n        \\u2502 2. Run `RigOfficeDownloader-v4.bat` to start the application            \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Usage\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Interactive Menu Options:                                               \\u2502\\n        \\u2502 [0] Change search parameters (rig number, URLs)                         \\u2502\\n        \\u2502 [1] Configure filter chain                                              \\u2502\\n        \\u2502 [2] Fetch docs (scrape initial data)                                    \\u2502\\n        \\u2502 [3] Export docs (to Markdown for editing)                               \\u2502\\n        \\u2502 [4] Import updated doc data                                             \\u2502\\n        \\u2502 [5] Fetch files (prepare files for download)                            \\u2502\\n        \\u2502 [6] Export files (to Markdown for editing)                              \\u2502\\n        \\u2502 [7] Import updated file data                                            \\u2502\\n        \\u2502 [8] Download files                                                      \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 Run Modes:                                                              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --auto` (full automation)                 \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --interactive` (menu-driven)              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --config` (edit filters)                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Directory Structure\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 outputs/                                                                \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500 data/               # Metadata control files                        \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.json                                               \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.md                                                 \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-b-files.json                                              \\u2502\\n        \\u2502 \\u2502   \\u2514\\u2500\\u2500 <rig>-b-files.md                                                \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500 downloads/          # Final organized files                         \\u2502\\n        \\u2502     \\u2514\\u2500\\u2500 <rig>/                                                          \\u2502\\n        \\u2502         \\u251c\\u2500\\u2500 <subfolder>/                                                \\u2502\\n        \\u2502         \\u2502   \\u2514\\u2500\\u2500 <item_generated_name>.<ext>                             \\u2502\\n        \\u2502         \\u2514\\u2500\\u2500 ...                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Benefits\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510  \\u2502\\n        \\u2502 \\u2502                 \\u2502 Manual         \\u2502 Automated                       \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Time per project\\u2502 4-6 hours      \\u2502 <1 hour (75%+ saved)            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Organization    \\u2502 Error-prone    \\u2502 Metadata-driven                 \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 User review     \\u2502 Tedious        \\u2502 Markdown checkpoints            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Auditability    \\u2502 None           \\u2502 JSON/MD traceability            \\u2502  \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Technical Details\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Core Technologies: Python, Selenium, BeautifulSoup, JSON, Markdown     \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Data Flow: Web scraping \\u2192 JSON storage \\u2192 Markdown interface \\u2192         \\u2502\\n        \\u2502   User selection \\u2192 Automated downloads                                  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Filter System: Pattern-based matching (inclusion/exclusion) on fields \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 File Organization: Subfolder creation from '/' in item_generated_name \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Version History\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 v1: Basic document retrieval and download                             \\u2502\\n        \\u2502 \\u2022 v2: JSON/Markdown conversion and user selection                       \\u2502\\n        \\u2502 \\u2022 v3: Improved error handling and field organization                    \\u2502\\n        \\u2502 \\u2022 v4: Subfolder support, filter chains, field ordering                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        > \\\"Complexity is the enemy of execution \\u2014 Optimized for engineers, by engineers.\\\"'\",\n  \"synthesized_optimal_sequence\": [\n    {\n      \"point\": \"Automates the retrieval and organization of engineering documents from NOV's RigDoc system, eliminating manual effort and preserving over 75% of engineering time for higher-value work.\"\n    },\n    {\n      \"point\": \"Implements a streamlined, three-stage workflow\\u2014fetching document and file metadata, enabling targeted selection, and executing organized downloads\\u2014supported by interactive menus and user checkpoints.\"\n    },\n    {\n      \"point\": \"Features configurable filter chains, smart metadata-driven file naming, and automatic subfolder organization based on naming patterns, ensuring precise and efficient file management.\"\n    },\n    {\n      \"point\": \"Provides robust error handling, deduplication, and full auditability with metadata control files in both JSON and Markdown formats for traceable user review and editing.\"\n    },\n    {\n      \"point\": \"Supports both fully automated and menu-driven operation modes, with clear setup instructions and a directory structure optimized for traceability and ease of integration into engineering workflows.\"\n    },\n    {\n      \"point\": \"Built with Python, Selenium, BeautifulSoup, and Markdown, the tool offers pattern-based filtering and continuous version improvements, delivering a comprehensive, engineer-focused solution for document management.\"\n    }\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"core_constructs\": {\n    \"elements\": [\n      \"Automated document retrieval from an external system (RigDoc)\",\n      \"Three-stage, checkpoint-driven workflow: metadata fetch, selection, organized download\",\n      \"Interactive and automated run modes; menu-driven and filter-configurable operation\",\n      \"Smart file organization: metadata-driven naming, subfolder creation via naming patterns\",\n      \"Comprehensive error handling, deduplication, and auditability with control files\",\n      \"Traceable review/edit interfaces via JSON and Markdown\",\n      \"Setup via Python virtual environment and batch file execution\",\n      \"Technologies: Python, Selenium, BeautifulSoup, JSON, Markdown\",\n      \"Directory outputs split between metadata control and organized downloads\",\n      \"Progressive versioning: functionality evolved from basic retrieval to full-featured pipeline\"\n    ],\n    \"principles\": [\n      \"Reduce engineering manual labor through intelligent automation\",\n      \"Workflow modularization enables targeted user checkpoints and error recovery\",\n      \"Precision and auditability result from metadata-centric, user-editable control\",\n      \"Filter chains and flexible interfaces adapt to diverse use cases and user needs\",\n      \"Automation and interaction are both supported for maximum operational flexibility\",\n      \"Organizational structure (both logical and file system) enhances traceability and integration\",\n      \"Simplicity of execution and engineer-centric UX trump unnecessary complexity\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Telos Crystallization & Objective Definition\n\nAnalyze the extracted core constructs to distill and crystallize the input's singular, non-negotiable core purpose ('telos'); formulate this ultimate objective with axiomatic precision, establishing the gravitational center for all subsequent synthesis. Execute as\n\n`{role=telos_definer; input=core_constructs:dict; process=[derive_ultimate_objective_from_elements_principles(), validate_objective_singularity(), express_unambiguous_telos_statement()]; output={core_telos:str, supporting_constructs:dict}}`", "step": "c", "title": "Telos Crystallization & Objective Definition", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '        # RigOfficeDownloader\\n\\n        > Automate document retrieval from NOV's RigDoc system, saving engineering time and streamlining 3D modeling preparation.\\n\\n        ## Purpose\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Eliminate tedious manual document retrieval from NOV's RigDoc system    \\u2502\\n        \\u2502 through intelligent automation, preserving 75%+ engineering time for    \\u2502\\n        \\u2502 high-value tasks.                                                       \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Workflow\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502  Documents \\u2192 Files \\u2192 Downloads                                          \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502  1. Fetch document metadata                                             \\u2502\\n        \\u2502  2. Select relevant documents                                           \\u2502\\n        \\u2502  3. Fetch file metadata for selected docs                               \\u2502\\n        \\u2502  4. Select files to download                                            \\u2502\\n        \\u2502  5. Download and organize files                                         \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Features\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Three-stage workflow with user checkpoints                            \\u2502\\n        \\u2502 \\u2022 Interactive menu for flexible execution                               \\u2502\\n        \\u2502 \\u2022 Markdown interfaces for document/file review                          \\u2502\\n        \\u2502 \\u2022 Configurable filter chains                                            \\u2502\\n        \\u2502 \\u2022 Smart file organization with subfolder support based on naming        \\u2502\\n        \\u2502   patterns                                                              \\u2502\\n        \\u2502 \\u2022 Metadata-driven file naming                                           \\u2502\\n        \\u2502 \\u2022 Error handling and deduplication                                      \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Setup\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 1. Run `py_venv_init.bat` to create Python environment                  \\u2502\\n        \\u2502 2. Run `RigOfficeDownloader-v4.bat` to start the application            \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Usage\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Interactive Menu Options:                                               \\u2502\\n        \\u2502 [0] Change search parameters (rig number, URLs)                         \\u2502\\n        \\u2502 [1] Configure filter chain                                              \\u2502\\n        \\u2502 [2] Fetch docs (scrape initial data)                                    \\u2502\\n        \\u2502 [3] Export docs (to Markdown for editing)                               \\u2502\\n        \\u2502 [4] Import updated doc data                                             \\u2502\\n        \\u2502 [5] Fetch files (prepare files for download)                            \\u2502\\n        \\u2502 [6] Export files (to Markdown for editing)                              \\u2502\\n        \\u2502 [7] Import updated file data                                            \\u2502\\n        \\u2502 [8] Download files                                                      \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 Run Modes:                                                              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --auto` (full automation)                 \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --interactive` (menu-driven)              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --config` (edit filters)                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Directory Structure\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 outputs/                                                                \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500 data/               # Metadata control files                        \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.json                                               \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.md                                                 \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-b-files.json                                              \\u2502\\n        \\u2502 \\u2502   \\u2514\\u2500\\u2500 <rig>-b-files.md                                                \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500 downloads/          # Final organized files                         \\u2502\\n        \\u2502     \\u2514\\u2500\\u2500 <rig>/                                                          \\u2502\\n        \\u2502         \\u251c\\u2500\\u2500 <subfolder>/                                                \\u2502\\n        \\u2502         \\u2502   \\u2514\\u2500\\u2500 <item_generated_name>.<ext>                             \\u2502\\n        \\u2502         \\u2514\\u2500\\u2500 ...                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Benefits\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510  \\u2502\\n        \\u2502 \\u2502                 \\u2502 Manual         \\u2502 Automated                       \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Time per project\\u2502 4-6 hours      \\u2502 <1 hour (75%+ saved)            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Organization    \\u2502 Error-prone    \\u2502 Metadata-driven                 \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 User review     \\u2502 Tedious        \\u2502 Markdown checkpoints            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Auditability    \\u2502 None           \\u2502 JSON/MD traceability            \\u2502  \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Technical Details\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Core Technologies: Python, Selenium, BeautifulSoup, JSON, Markdown     \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Data Flow: Web scraping \\u2192 JSON storage \\u2192 Markdown interface \\u2192         \\u2502\\n        \\u2502   User selection \\u2192 Automated downloads                                  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Filter System: Pattern-based matching (inclusion/exclusion) on fields \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 File Organization: Subfolder creation from '/' in item_generated_name \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Version History\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 v1: Basic document retrieval and download                             \\u2502\\n        \\u2502 \\u2022 v2: JSON/Markdown conversion and user selection                       \\u2502\\n        \\u2502 \\u2022 v3: Improved error handling and field organization                    \\u2502\\n        \\u2502 \\u2022 v4: Subfolder support, filter chains, field ordering                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        > \\\"Complexity is the enemy of execution \\u2014 Optimized for engineers, by engineers.\\\"'\",\n  \"core_constructs\": {\n    \"elements\": [\n      \"Automated document retrieval from an external system (RigDoc)\",\n      \"Three-stage, checkpoint-driven workflow: metadata fetch, selection, organized download\",\n      \"Interactive and automated run modes; menu-driven and filter-configurable operation\",\n      \"Smart file organization: metadata-driven naming, subfolder creation via naming patterns\",\n      \"Comprehensive error handling, deduplication, and auditability with control files\",\n      \"Traceable review/edit interfaces via JSON and Markdown\",\n      \"Setup via Python virtual environment and batch file execution\",\n      \"Technologies: Python, Selenium, BeautifulSoup, JSON, Markdown\",\n      \"Directory outputs split between metadata control and organized downloads\",\n      \"Progressive versioning: functionality evolved from basic retrieval to full-featured pipeline\"\n    ],\n    \"principles\": [\n      \"Reduce engineering manual labor through intelligent automation\",\n      \"Workflow modularization enables targeted user checkpoints and error recovery\",\n      \"Precision and auditability result from metadata-centric, user-editable control\",\n      \"Filter chains and flexible interfaces adapt to diverse use cases and user needs\",\n      \"Automation and interaction are both supported for maximum operational flexibility\",\n      \"Organizational structure (both logical and file system) enhances traceability and integration\",\n      \"Simplicity of execution and engineer-centric UX trump unnecessary complexity\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"core_telos\": \"Eliminate manual document retrieval from the NOV RigDoc system by implementing a highly auditable, engineer-centric automation workflow that maximizes time savings and operational flexibility through modular, metadata-driven processing.\",\n  \"supporting_constructs\": {\n    \"elements\": [\n      \"Automated, metadata-driven document identification and file download\",\n      \"Modular workflow with explicit user checkpoints for selection and review\",\n      \"Interactive menu and filter-configurable modes, plus full automation\",\n      \"Structured file organization with subfolder support based on dynamic patterns\",\n      \"Comprehensive error handling and deduplication, traceable via JSON/Markdown interfaces\",\n      \"Simple setup and operation tailored to engineering workflows and toolchains\"\n    ],\n    \"principles\": [\n      \"Automate to reduce repetitive engineering tasks and preserve high-value labor\",\n      \"Structure workflow to allow targeted user intervention and robust recovery\",\n      \"Prioritize clarity, traceability, and auditability with metadata as the foundation\",\n      \"Support both automation and flexible interaction for diverse operational scenarios\",\n      \"Engineer experience and simplicity are paramount—avoid unnecessary complexity\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Critical Essence Prioritization\n\nEvaluate each supporting construct strictly by its indispensable contribution to the `core_telos`; ruthlessly isolate and rank only the absolute highest-impact, non-negotiable essence vectors required to fulfill the objective, discarding all sub-critical elements. Execute as\n\n`{role=essence_prioritizer; input={core_telos:str, supporting_constructs:dict}; process=[assess_contribution_to_telos(), rank_by_critical_impact_necessity(), enforce_criticality_threshold(), isolate_core_essence_vectors()]; output={critical_essence:list}}`", "step": "d", "title": "Critical Essence Prioritization", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '        # RigOfficeDownloader\\n\\n        > Automate document retrieval from NOV's RigDoc system, saving engineering time and streamlining 3D modeling preparation.\\n\\n        ## Purpose\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Eliminate tedious manual document retrieval from NOV's RigDoc system    \\u2502\\n        \\u2502 through intelligent automation, preserving 75%+ engineering time for    \\u2502\\n        \\u2502 high-value tasks.                                                       \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Workflow\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502  Documents \\u2192 Files \\u2192 Downloads                                          \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502  1. Fetch document metadata                                             \\u2502\\n        \\u2502  2. Select relevant documents                                           \\u2502\\n        \\u2502  3. Fetch file metadata for selected docs                               \\u2502\\n        \\u2502  4. Select files to download                                            \\u2502\\n        \\u2502  5. Download and organize files                                         \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Features\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Three-stage workflow with user checkpoints                            \\u2502\\n        \\u2502 \\u2022 Interactive menu for flexible execution                               \\u2502\\n        \\u2502 \\u2022 Markdown interfaces for document/file review                          \\u2502\\n        \\u2502 \\u2022 Configurable filter chains                                            \\u2502\\n        \\u2502 \\u2022 Smart file organization with subfolder support based on naming        \\u2502\\n        \\u2502   patterns                                                              \\u2502\\n        \\u2502 \\u2022 Metadata-driven file naming                                           \\u2502\\n        \\u2502 \\u2022 Error handling and deduplication                                      \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Setup\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 1. Run `py_venv_init.bat` to create Python environment                  \\u2502\\n        \\u2502 2. Run `RigOfficeDownloader-v4.bat` to start the application            \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Usage\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Interactive Menu Options:                                               \\u2502\\n        \\u2502 [0] Change search parameters (rig number, URLs)                         \\u2502\\n        \\u2502 [1] Configure filter chain                                              \\u2502\\n        \\u2502 [2] Fetch docs (scrape initial data)                                    \\u2502\\n        \\u2502 [3] Export docs (to Markdown for editing)                               \\u2502\\n        \\u2502 [4] Import updated doc data                                             \\u2502\\n        \\u2502 [5] Fetch files (prepare files for download)                            \\u2502\\n        \\u2502 [6] Export files (to Markdown for editing)                              \\u2502\\n        \\u2502 [7] Import updated file data                                            \\u2502\\n        \\u2502 [8] Download files                                                      \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 Run Modes:                                                              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --auto` (full automation)                 \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --interactive` (menu-driven)              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --config` (edit filters)                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Directory Structure\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 outputs/                                                                \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500 data/               # Metadata control files                        \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.json                                               \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.md                                                 \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-b-files.json                                              \\u2502\\n        \\u2502 \\u2502   \\u2514\\u2500\\u2500 <rig>-b-files.md                                                \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500 downloads/          # Final organized files                         \\u2502\\n        \\u2502     \\u2514\\u2500\\u2500 <rig>/                                                          \\u2502\\n        \\u2502         \\u251c\\u2500\\u2500 <subfolder>/                                                \\u2502\\n        \\u2502         \\u2502   \\u2514\\u2500\\u2500 <item_generated_name>.<ext>                             \\u2502\\n        \\u2502         \\u2514\\u2500\\u2500 ...                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Benefits\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510  \\u2502\\n        \\u2502 \\u2502                 \\u2502 Manual         \\u2502 Automated                       \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Time per project\\u2502 4-6 hours      \\u2502 <1 hour (75%+ saved)            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Organization    \\u2502 Error-prone    \\u2502 Metadata-driven                 \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 User review     \\u2502 Tedious        \\u2502 Markdown checkpoints            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Auditability    \\u2502 None           \\u2502 JSON/MD traceability            \\u2502  \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Technical Details\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Core Technologies: Python, Selenium, BeautifulSoup, JSON, Markdown     \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Data Flow: Web scraping \\u2192 JSON storage \\u2192 Markdown interface \\u2192         \\u2502\\n        \\u2502   User selection \\u2192 Automated downloads                                  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Filter System: Pattern-based matching (inclusion/exclusion) on fields \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 File Organization: Subfolder creation from '/' in item_generated_name \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Version History\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 v1: Basic document retrieval and download                             \\u2502\\n        \\u2502 \\u2022 v2: JSON/Markdown conversion and user selection                       \\u2502\\n        \\u2502 \\u2022 v3: Improved error handling and field organization                    \\u2502\\n        \\u2502 \\u2022 v4: Subfolder support, filter chains, field ordering                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        > \\\"Complexity is the enemy of execution \\u2014 Optimized for engineers, by engineers.\\\"'\",\n  \"core_telos\": \"Eliminate manual document retrieval from the NOV RigDoc system by implementing a highly auditable, engineer-centric automation workflow that maximizes time savings and operational flexibility through modular, metadata-driven processing.\",\n  \"supporting_constructs\": {\n    \"elements\": [\n      \"Automated, metadata-driven document identification and file download\",\n      \"Modular workflow with explicit user checkpoints for selection and review\",\n      \"Interactive menu and filter-configurable modes, plus full automation\",\n      \"Structured file organization with subfolder support based on dynamic patterns\",\n      \"Comprehensive error handling and deduplication, traceable via JSON/Markdown interfaces\",\n      \"Simple setup and operation tailored to engineering workflows and toolchains\"\n    ],\n    \"principles\": [\n      \"Automate to reduce repetitive engineering tasks and preserve high-value labor\",\n      \"Structure workflow to allow targeted user intervention and robust recovery\",\n      \"Prioritize clarity, traceability, and auditability with metadata as the foundation\",\n      \"Support both automation and flexible interaction for diverse operational scenarios\",\n      \"Engineer experience and simplicity are paramount\\u2014avoid unnecessary complexity\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"critical_essence\": [\n    \"Automated, metadata-driven document identification and retrieval fully eliminates manual effort while maximizing engineering time savings.\",\n    \"Modular workflow design with explicit user checkpoints enables targeted intervention, robust recovery, and operational flexibility.\",\n    \"Comprehensive traceability and auditability throughout the automation process, leveraging structured metadata, JSON, and Markdown interfaces.\",\n    \"Structured, pattern-based file organization and naming driven by document metadata, ensuring reliable, consistent downstream processing.\",\n    \"Simple, engineer-centric design and setup ensure tool adoption, effective operation, and avoidance of unnecessary complexity.\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Causal Nexus Mapping\n\nArchitect a minimal, coherent map of the inherent causal relationships, logical dependencies, and procedural sequences connecting the `critical_essence` elements, constructing the essential operational or conceptual flow required by the `core_telos`. Execute as\n\n`{role=causal_mapper; input={critical_essence:list, core_telos:str}; process=[map_causal_links_dependencies(), structure_minimal_flow_graph(), validate_structural_necessity_for_telos()]; output={causal_nexus:dict}}`", "step": "e", "title": "Causal Nexus Mapping", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '        # RigOfficeDownloader\\n\\n        > Automate document retrieval from NOV's RigDoc system, saving engineering time and streamlining 3D modeling preparation.\\n\\n        ## Purpose\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Eliminate tedious manual document retrieval from NOV's RigDoc system    \\u2502\\n        \\u2502 through intelligent automation, preserving 75%+ engineering time for    \\u2502\\n        \\u2502 high-value tasks.                                                       \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Workflow\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502  Documents \\u2192 Files \\u2192 Downloads                                          \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502  1. Fetch document metadata                                             \\u2502\\n        \\u2502  2. Select relevant documents                                           \\u2502\\n        \\u2502  3. Fetch file metadata for selected docs                               \\u2502\\n        \\u2502  4. Select files to download                                            \\u2502\\n        \\u2502  5. Download and organize files                                         \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Features\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Three-stage workflow with user checkpoints                            \\u2502\\n        \\u2502 \\u2022 Interactive menu for flexible execution                               \\u2502\\n        \\u2502 \\u2022 Markdown interfaces for document/file review                          \\u2502\\n        \\u2502 \\u2022 Configurable filter chains                                            \\u2502\\n        \\u2502 \\u2022 Smart file organization with subfolder support based on naming        \\u2502\\n        \\u2502   patterns                                                              \\u2502\\n        \\u2502 \\u2022 Metadata-driven file naming                                           \\u2502\\n        \\u2502 \\u2022 Error handling and deduplication                                      \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Setup\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 1. Run `py_venv_init.bat` to create Python environment                  \\u2502\\n        \\u2502 2. Run `RigOfficeDownloader-v4.bat` to start the application            \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Usage\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Interactive Menu Options:                                               \\u2502\\n        \\u2502 [0] Change search parameters (rig number, URLs)                         \\u2502\\n        \\u2502 [1] Configure filter chain                                              \\u2502\\n        \\u2502 [2] Fetch docs (scrape initial data)                                    \\u2502\\n        \\u2502 [3] Export docs (to Markdown for editing)                               \\u2502\\n        \\u2502 [4] Import updated doc data                                             \\u2502\\n        \\u2502 [5] Fetch files (prepare files for download)                            \\u2502\\n        \\u2502 [6] Export files (to Markdown for editing)                              \\u2502\\n        \\u2502 [7] Import updated file data                                            \\u2502\\n        \\u2502 [8] Download files                                                      \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 Run Modes:                                                              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --auto` (full automation)                 \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --interactive` (menu-driven)              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --config` (edit filters)                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Directory Structure\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 outputs/                                                                \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500 data/               # Metadata control files                        \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.json                                               \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.md                                                 \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-b-files.json                                              \\u2502\\n        \\u2502 \\u2502   \\u2514\\u2500\\u2500 <rig>-b-files.md                                                \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500 downloads/          # Final organized files                         \\u2502\\n        \\u2502     \\u2514\\u2500\\u2500 <rig>/                                                          \\u2502\\n        \\u2502         \\u251c\\u2500\\u2500 <subfolder>/                                                \\u2502\\n        \\u2502         \\u2502   \\u2514\\u2500\\u2500 <item_generated_name>.<ext>                             \\u2502\\n        \\u2502         \\u2514\\u2500\\u2500 ...                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Benefits\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510  \\u2502\\n        \\u2502 \\u2502                 \\u2502 Manual         \\u2502 Automated                       \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Time per project\\u2502 4-6 hours      \\u2502 <1 hour (75%+ saved)            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Organization    \\u2502 Error-prone    \\u2502 Metadata-driven                 \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 User review     \\u2502 Tedious        \\u2502 Markdown checkpoints            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Auditability    \\u2502 None           \\u2502 JSON/MD traceability            \\u2502  \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Technical Details\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Core Technologies: Python, Selenium, BeautifulSoup, JSON, Markdown     \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Data Flow: Web scraping \\u2192 JSON storage \\u2192 Markdown interface \\u2192         \\u2502\\n        \\u2502   User selection \\u2192 Automated downloads                                  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Filter System: Pattern-based matching (inclusion/exclusion) on fields \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 File Organization: Subfolder creation from '/' in item_generated_name \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Version History\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 v1: Basic document retrieval and download                             \\u2502\\n        \\u2502 \\u2022 v2: JSON/Markdown conversion and user selection                       \\u2502\\n        \\u2502 \\u2022 v3: Improved error handling and field organization                    \\u2502\\n        \\u2502 \\u2022 v4: Subfolder support, filter chains, field ordering                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        > \\\"Complexity is the enemy of execution \\u2014 Optimized for engineers, by engineers.\\\"'\",\n  \"critical_essence\": [\n    \"Automated, metadata-driven document identification and retrieval fully eliminates manual effort while maximizing engineering time savings.\",\n    \"Modular workflow design with explicit user checkpoints enables targeted intervention, robust recovery, and operational flexibility.\",\n    \"Comprehensive traceability and auditability throughout the automation process, leveraging structured metadata, JSON, and Markdown interfaces.\",\n    \"Structured, pattern-based file organization and naming driven by document metadata, ensuring reliable, consistent downstream processing.\",\n    \"Simple, engineer-centric design and setup ensure tool adoption, effective operation, and avoidance of unnecessary complexity.\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"causal_mapper\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Automated, metadata-driven document identification and retrieval fully eliminates manual effort while maximizing engineering time savings.\",\n      \"Modular workflow design with explicit user checkpoints enables targeted intervention, robust recovery, and operational flexibility.\",\n      \"Comprehensive traceability and auditability throughout the automation process, leveraging structured metadata, JSON, and Markdown interfaces.\",\n      \"Structured, pattern-based file organization and naming driven by document metadata, ensuring reliable, consistent downstream processing.\",\n      \"Simple, engineer-centric design and setup ensure tool adoption, effective operation, and avoidance of unnecessary complexity.\"\n    ],\n    \"core_telos\": \"Streamline and automate engineering document retrieval and organization, maximizing efficiency and minimizing manual workload while ensuring robustness and traceability.\"\n  },\n  \"process\": [\n    \"map_causal_links_dependencies()\",\n    \"structure_minimal_flow_graph()\",\n    \"validate_structural_necessity_for_telos()\"\n  ],\n  \"output\": {\n    \"causal_nexus\": {\n      \"1. Simple, engineer-centric design and setup\": {\n        \"enables\": [\n          \"tool adoption\",\n          \"effective operation\"\n        ],\n        \"prerequisite_for\": [\n          \"Modular workflow design\",\n          \"Automated, metadata-driven retrieval\"\n        ]\n      },\n      \"2. Modular workflow design with explicit user checkpoints\": {\n        \"depends_on\": [\n          \"Simple, engineer-centric design\"\n        ],\n        \"enables\": [\n          \"targeted intervention\",\n          \"robust recovery\",\n          \"operational flexibility\"\n        ],\n        \"prerequisite_for\": [\n          \"Automated, metadata-driven retrieval\",\n          \"Comprehensive traceability\"\n        ]\n      },\n      \"3. Automated, metadata-driven document identification and retrieval\": {\n        \"depends_on\": [\n          \"Simple, engineer-centric design\",\n          \"Modular workflow design\"\n        ],\n        \"enables\": [\n          \"maximizing engineering time savings\",\n          \"eliminating manual effort\",\n          \"supporting structured organization\"\n        ],\n        \"prerequisite_for\": [\n          \"Structured, pattern-based file organization\"\n        ]\n      },\n      \"4. Structured, pattern-based file organization and naming\": {\n        \"depends_on\": [\n          \"Automated, metadata-driven retrieval\"\n        ],\n        \"enables\": [\n          \"reliable downstream processing\",\n          \"consistent file handling\"\n        ],\n        \"prerequisite_for\": [\n          \"Comprehensive traceability and auditability\"\n        ]\n      },\n      \"5. Comprehensive traceability and auditability\": {\n        \"depends_on\": [\n          \"Structured, pattern-based organization\",\n          \"Modular workflow\",\n          \"Automated retrieval\"\n        ],\n        \"enables\": [\n          \"robust process auditing\",\n          \"ease of error identification/correction\",\n          \"confidence in automation results\"\n        ]\n      },\n      \"summary_sequential_flow\": [\n        \"Simple, engineer-centric design and setup → Modular workflow with user checkpoints → Automated, metadata-driven retrieval → Structured, pattern-based organization → Comprehensive traceability and auditability\"\n      ],\n      \"telos_alignment\": \"Each dependency is structurally necessary for a robust, engineer-adopted, efficient, automated document workflow that saves time, minimizes manual intervention, and assures traceability—fully supporting the core telos.\"\n    }\n  }\n}", "cost": 0.0}}}, {"instruction": "# Condensed Nucleus Synthesis\n\nFuse the `critical_essence` elements according to the `causal_nexus` into an ultra-condensed, logically ordered semantic nucleus; maximize signal strength per component while preserving full actionable power and internal consistency within a compact structural representation. Execute as\n\n`{role=nucleus_synthesizer; input={critical_essence:list, causal_nexus:dict}; process=[integrate_elements_by_structure(), maximize_signal_strength_per_component(), enforce_internal_consistency(), formulate_compact_nucleus()]; output={condensed_nucleus:any}}`", "step": "f", "title": "Condensed Nucleus Synthesis", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '        # RigOfficeDownloader\\n\\n        > Automate document retrieval from NOV's RigDoc system, saving engineering time and streamlining 3D modeling preparation.\\n\\n        ## Purpose\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Eliminate tedious manual document retrieval from NOV's RigDoc system    \\u2502\\n        \\u2502 through intelligent automation, preserving 75%+ engineering time for    \\u2502\\n        \\u2502 high-value tasks.                                                       \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Workflow\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502  Documents \\u2192 Files \\u2192 Downloads                                          \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502  1. Fetch document metadata                                             \\u2502\\n        \\u2502  2. Select relevant documents                                           \\u2502\\n        \\u2502  3. Fetch file metadata for selected docs                               \\u2502\\n        \\u2502  4. Select files to download                                            \\u2502\\n        \\u2502  5. Download and organize files                                         \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Features\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Three-stage workflow with user checkpoints                            \\u2502\\n        \\u2502 \\u2022 Interactive menu for flexible execution                               \\u2502\\n        \\u2502 \\u2022 Markdown interfaces for document/file review                          \\u2502\\n        \\u2502 \\u2022 Configurable filter chains                                            \\u2502\\n        \\u2502 \\u2022 Smart file organization with subfolder support based on naming        \\u2502\\n        \\u2502   patterns                                                              \\u2502\\n        \\u2502 \\u2022 Metadata-driven file naming                                           \\u2502\\n        \\u2502 \\u2022 Error handling and deduplication                                      \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Setup\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 1. Run `py_venv_init.bat` to create Python environment                  \\u2502\\n        \\u2502 2. Run `RigOfficeDownloader-v4.bat` to start the application            \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Usage\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Interactive Menu Options:                                               \\u2502\\n        \\u2502 [0] Change search parameters (rig number, URLs)                         \\u2502\\n        \\u2502 [1] Configure filter chain                                              \\u2502\\n        \\u2502 [2] Fetch docs (scrape initial data)                                    \\u2502\\n        \\u2502 [3] Export docs (to Markdown for editing)                               \\u2502\\n        \\u2502 [4] Import updated doc data                                             \\u2502\\n        \\u2502 [5] Fetch files (prepare files for download)                            \\u2502\\n        \\u2502 [6] Export files (to Markdown for editing)                              \\u2502\\n        \\u2502 [7] Import updated file data                                            \\u2502\\n        \\u2502 [8] Download files                                                      \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 Run Modes:                                                              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --auto` (full automation)                 \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --interactive` (menu-driven)              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --config` (edit filters)                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Directory Structure\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 outputs/                                                                \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500 data/               # Metadata control files                        \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.json                                               \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.md                                                 \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-b-files.json                                              \\u2502\\n        \\u2502 \\u2502   \\u2514\\u2500\\u2500 <rig>-b-files.md                                                \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500 downloads/          # Final organized files                         \\u2502\\n        \\u2502     \\u2514\\u2500\\u2500 <rig>/                                                          \\u2502\\n        \\u2502         \\u251c\\u2500\\u2500 <subfolder>/                                                \\u2502\\n        \\u2502         \\u2502   \\u2514\\u2500\\u2500 <item_generated_name>.<ext>                             \\u2502\\n        \\u2502         \\u2514\\u2500\\u2500 ...                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Benefits\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510  \\u2502\\n        \\u2502 \\u2502                 \\u2502 Manual         \\u2502 Automated                       \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Time per project\\u2502 4-6 hours      \\u2502 <1 hour (75%+ saved)            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Organization    \\u2502 Error-prone    \\u2502 Metadata-driven                 \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 User review     \\u2502 Tedious        \\u2502 Markdown checkpoints            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Auditability    \\u2502 None           \\u2502 JSON/MD traceability            \\u2502  \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Technical Details\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Core Technologies: Python, Selenium, BeautifulSoup, JSON, Markdown     \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Data Flow: Web scraping \\u2192 JSON storage \\u2192 Markdown interface \\u2192         \\u2502\\n        \\u2502   User selection \\u2192 Automated downloads                                  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Filter System: Pattern-based matching (inclusion/exclusion) on fields \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 File Organization: Subfolder creation from '/' in item_generated_name \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Version History\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 v1: Basic document retrieval and download                             \\u2502\\n        \\u2502 \\u2022 v2: JSON/Markdown conversion and user selection                       \\u2502\\n        \\u2502 \\u2022 v3: Improved error handling and field organization                    \\u2502\\n        \\u2502 \\u2022 v4: Subfolder support, filter chains, field ordering                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        > \\\"Complexity is the enemy of execution \\u2014 Optimized for engineers, by engineers.\\\"'\",\n  \"role\": \"causal_mapper\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Automated, metadata-driven document identification and retrieval fully eliminates manual effort while maximizing engineering time savings.\",\n      \"Modular workflow design with explicit user checkpoints enables targeted intervention, robust recovery, and operational flexibility.\",\n      \"Comprehensive traceability and auditability throughout the automation process, leveraging structured metadata, JSON, and Markdown interfaces.\",\n      \"Structured, pattern-based file organization and naming driven by document metadata, ensuring reliable, consistent downstream processing.\",\n      \"Simple, engineer-centric design and setup ensure tool adoption, effective operation, and avoidance of unnecessary complexity.\"\n    ],\n    \"core_telos\": \"Streamline and automate engineering document retrieval and organization, maximizing efficiency and minimizing manual workload while ensuring robustness and traceability.\"\n  },\n  \"process\": [\n    \"map_causal_links_dependencies()\",\n    \"structure_minimal_flow_graph()\",\n    \"validate_structural_necessity_for_telos()\"\n  ],\n  \"output\": {\n    \"causal_nexus\": {\n      \"1. Simple, engineer-centric design and setup\": {\n        \"enables\": [\n          \"tool adoption\",\n          \"effective operation\"\n        ],\n        \"prerequisite_for\": [\n          \"Modular workflow design\",\n          \"Automated, metadata-driven retrieval\"\n        ]\n      },\n      \"2. Modular workflow design with explicit user checkpoints\": {\n        \"depends_on\": [\n          \"Simple, engineer-centric design\"\n        ],\n        \"enables\": [\n          \"targeted intervention\",\n          \"robust recovery\",\n          \"operational flexibility\"\n        ],\n        \"prerequisite_for\": [\n          \"Automated, metadata-driven retrieval\",\n          \"Comprehensive traceability\"\n        ]\n      },\n      \"3. Automated, metadata-driven document identification and retrieval\": {\n        \"depends_on\": [\n          \"Simple, engineer-centric design\",\n          \"Modular workflow design\"\n        ],\n        \"enables\": [\n          \"maximizing engineering time savings\",\n          \"eliminating manual effort\",\n          \"supporting structured organization\"\n        ],\n        \"prerequisite_for\": [\n          \"Structured, pattern-based file organization\"\n        ]\n      },\n      \"4. Structured, pattern-based file organization and naming\": {\n        \"depends_on\": [\n          \"Automated, metadata-driven retrieval\"\n        ],\n        \"enables\": [\n          \"reliable downstream processing\",\n          \"consistent file handling\"\n        ],\n        \"prerequisite_for\": [\n          \"Comprehensive traceability and auditability\"\n        ]\n      },\n      \"5. Comprehensive traceability and auditability\": {\n        \"depends_on\": [\n          \"Structured, pattern-based organization\",\n          \"Modular workflow\",\n          \"Automated retrieval\"\n        ],\n        \"enables\": [\n          \"robust process auditing\",\n          \"ease of error identification/correction\",\n          \"confidence in automation results\"\n        ]\n      },\n      \"summary_sequential_flow\": [\n        \"Simple, engineer-centric design and setup \\u2192 Modular workflow with user checkpoints \\u2192 Automated, metadata-driven retrieval \\u2192 Structured, pattern-based organization \\u2192 Comprehensive traceability and auditability\"\n      ],\n      \"telos_alignment\": \"Each dependency is structurally necessary for a robust, engineer-adopted, efficient, automated document workflow that saves time, minimizes manual intervention, and assures traceability\\u2014fully supporting the core telos.\"\n    }\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"nucleus_synthesizer\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Automated, metadata-driven document identification and retrieval fully eliminates manual effort while maximizing engineering time savings.\",\n      \"Modular workflow design with explicit user checkpoints enables targeted intervention, robust recovery, and operational flexibility.\",\n      \"Comprehensive traceability and auditability throughout the automation process, leveraging structured metadata, JSON, and Markdown interfaces.\",\n      \"Structured, pattern-based file organization and naming driven by document metadata, ensuring reliable, consistent downstream processing.\",\n      \"Simple, engineer-centric design and setup ensure tool adoption, effective operation, and avoidance of unnecessary complexity.\"\n    ],\n    \"causal_nexus\": {\n      \"1. Simple, engineer-centric design and setup\": {\n        \"enables\": [\n          \"tool adoption\",\n          \"effective operation\"\n        ],\n        \"prerequisite_for\": [\n          \"Modular workflow design\",\n          \"Automated, metadata-driven retrieval\"\n        ]\n      },\n      \"2. Modular workflow design with explicit user checkpoints\": {\n        \"depends_on\": [\n          \"Simple, engineer-centric design\"\n        ],\n        \"enables\": [\n          \"targeted intervention\",\n          \"robust recovery\",\n          \"operational flexibility\"\n        ],\n        \"prerequisite_for\": [\n          \"Automated, metadata-driven retrieval\",\n          \"Comprehensive traceability\"\n        ]\n      },\n      \"3. Automated, metadata-driven document identification and retrieval\": {\n        \"depends_on\": [\n          \"Simple, engineer-centric design\",\n          \"Modular workflow design\"\n        ],\n        \"enables\": [\n          \"maximizing engineering time savings\",\n          \"eliminating manual effort\",\n          \"supporting structured organization\"\n        ],\n        \"prerequisite_for\": [\n          \"Structured, pattern-based file organization\"\n        ]\n      },\n      \"4. Structured, pattern-based file organization and naming\": {\n        \"depends_on\": [\n          \"Automated, metadata-driven retrieval\"\n        ],\n        \"enables\": [\n          \"reliable downstream processing\",\n          \"consistent file handling\"\n        ],\n        \"prerequisite_for\": [\n          \"Comprehensive traceability and auditability\"\n        ]\n      },\n      \"5. Comprehensive traceability and auditability\": {\n        \"depends_on\": [\n          \"Structured, pattern-based organization\",\n          \"Modular workflow\",\n          \"Automated retrieval\"\n        ],\n        \"enables\": [\n          \"robust process auditing\",\n          \"ease of error identification/correction\",\n          \"confidence in automation results\"\n        ]\n      }\n    }\n  },\n  \"process\": [\n    \"integrate_elements_by_structure()\",\n    \"maximize_signal_strength_per_component()\",\n    \"enforce_internal_consistency()\",\n    \"formulate_compact_nucleus()\"\n  ],\n  \"output\": {\n    \"condensed_nucleus\": [\n      \"Engineer-centric design and setup drive adoption and enable effective, low-complexity operation.\",\n      \"Modular workflow with explicit user checkpoints provides targeted intervention, operational flexibility, and robust process recovery.\",\n      \"Automated, metadata-driven document retrieval eliminates manual labor and maximizes engineering time savings.\",\n      \"Structured, pattern-based file organization and metadata-driven naming ensure reliable, consistent downstream processing.\",\n      \"Comprehensive traceability and auditability, powered by structured metadata and JSON/Markdown interfaces, ensure confident, correct, and auditable outcomes throughout the automation lifecycle.\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Redundancy Annihilation & Signal Clarification\n\nScrutinize the `condensed_nucleus` to annihilate *all* semantic or structural redundancy, overlap, ambiguity, and verbose phrasing; enforce maximal conciseness, ensuring every remaining component carries unique, indispensable value and crystal-clear signal. Execute as\n\n`{role=signal_clarifier; input=condensed_nucleus:any; process=[scan_for_all_redundancy_ambiguity(), eliminate_overlaps_verbose_phrasing(), compress_phrasing_to_minimal_potent_form(), validate_uniqueness_clarity()]; output={minimal_signal_core:any}}`", "step": "g", "title": "Redundancy Annihilation & Signal Clarification", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '        # RigOfficeDownloader\\n\\n        > Automate document retrieval from NOV's RigDoc system, saving engineering time and streamlining 3D modeling preparation.\\n\\n        ## Purpose\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Eliminate tedious manual document retrieval from NOV's RigDoc system    \\u2502\\n        \\u2502 through intelligent automation, preserving 75%+ engineering time for    \\u2502\\n        \\u2502 high-value tasks.                                                       \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Workflow\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502  Documents \\u2192 Files \\u2192 Downloads                                          \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502  1. Fetch document metadata                                             \\u2502\\n        \\u2502  2. Select relevant documents                                           \\u2502\\n        \\u2502  3. Fetch file metadata for selected docs                               \\u2502\\n        \\u2502  4. Select files to download                                            \\u2502\\n        \\u2502  5. Download and organize files                                         \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Features\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Three-stage workflow with user checkpoints                            \\u2502\\n        \\u2502 \\u2022 Interactive menu for flexible execution                               \\u2502\\n        \\u2502 \\u2022 Markdown interfaces for document/file review                          \\u2502\\n        \\u2502 \\u2022 Configurable filter chains                                            \\u2502\\n        \\u2502 \\u2022 Smart file organization with subfolder support based on naming        \\u2502\\n        \\u2502   patterns                                                              \\u2502\\n        \\u2502 \\u2022 Metadata-driven file naming                                           \\u2502\\n        \\u2502 \\u2022 Error handling and deduplication                                      \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Setup\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 1. Run `py_venv_init.bat` to create Python environment                  \\u2502\\n        \\u2502 2. Run `RigOfficeDownloader-v4.bat` to start the application            \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Usage\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Interactive Menu Options:                                               \\u2502\\n        \\u2502 [0] Change search parameters (rig number, URLs)                         \\u2502\\n        \\u2502 [1] Configure filter chain                                              \\u2502\\n        \\u2502 [2] Fetch docs (scrape initial data)                                    \\u2502\\n        \\u2502 [3] Export docs (to Markdown for editing)                               \\u2502\\n        \\u2502 [4] Import updated doc data                                             \\u2502\\n        \\u2502 [5] Fetch files (prepare files for download)                            \\u2502\\n        \\u2502 [6] Export files (to Markdown for editing)                              \\u2502\\n        \\u2502 [7] Import updated file data                                            \\u2502\\n        \\u2502 [8] Download files                                                      \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 Run Modes:                                                              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --auto` (full automation)                 \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --interactive` (menu-driven)              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --config` (edit filters)                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Directory Structure\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 outputs/                                                                \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500 data/               # Metadata control files                        \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.json                                               \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.md                                                 \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-b-files.json                                              \\u2502\\n        \\u2502 \\u2502   \\u2514\\u2500\\u2500 <rig>-b-files.md                                                \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500 downloads/          # Final organized files                         \\u2502\\n        \\u2502     \\u2514\\u2500\\u2500 <rig>/                                                          \\u2502\\n        \\u2502         \\u251c\\u2500\\u2500 <subfolder>/                                                \\u2502\\n        \\u2502         \\u2502   \\u2514\\u2500\\u2500 <item_generated_name>.<ext>                             \\u2502\\n        \\u2502         \\u2514\\u2500\\u2500 ...                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Benefits\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510  \\u2502\\n        \\u2502 \\u2502                 \\u2502 Manual         \\u2502 Automated                       \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Time per project\\u2502 4-6 hours      \\u2502 <1 hour (75%+ saved)            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Organization    \\u2502 Error-prone    \\u2502 Metadata-driven                 \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 User review     \\u2502 Tedious        \\u2502 Markdown checkpoints            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Auditability    \\u2502 None           \\u2502 JSON/MD traceability            \\u2502  \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Technical Details\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Core Technologies: Python, Selenium, BeautifulSoup, JSON, Markdown     \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Data Flow: Web scraping \\u2192 JSON storage \\u2192 Markdown interface \\u2192         \\u2502\\n        \\u2502   User selection \\u2192 Automated downloads                                  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Filter System: Pattern-based matching (inclusion/exclusion) on fields \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 File Organization: Subfolder creation from '/' in item_generated_name \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Version History\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 v1: Basic document retrieval and download                             \\u2502\\n        \\u2502 \\u2022 v2: JSON/Markdown conversion and user selection                       \\u2502\\n        \\u2502 \\u2022 v3: Improved error handling and field organization                    \\u2502\\n        \\u2502 \\u2022 v4: Subfolder support, filter chains, field ordering                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        > \\\"Complexity is the enemy of execution \\u2014 Optimized for engineers, by engineers.\\\"'\",\n  \"role\": \"nucleus_synthesizer\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Automated, metadata-driven document identification and retrieval fully eliminates manual effort while maximizing engineering time savings.\",\n      \"Modular workflow design with explicit user checkpoints enables targeted intervention, robust recovery, and operational flexibility.\",\n      \"Comprehensive traceability and auditability throughout the automation process, leveraging structured metadata, JSON, and Markdown interfaces.\",\n      \"Structured, pattern-based file organization and naming driven by document metadata, ensuring reliable, consistent downstream processing.\",\n      \"Simple, engineer-centric design and setup ensure tool adoption, effective operation, and avoidance of unnecessary complexity.\"\n    ],\n    \"causal_nexus\": {\n      \"1. Simple, engineer-centric design and setup\": {\n        \"enables\": [\n          \"tool adoption\",\n          \"effective operation\"\n        ],\n        \"prerequisite_for\": [\n          \"Modular workflow design\",\n          \"Automated, metadata-driven retrieval\"\n        ]\n      },\n      \"2. Modular workflow design with explicit user checkpoints\": {\n        \"depends_on\": [\n          \"Simple, engineer-centric design\"\n        ],\n        \"enables\": [\n          \"targeted intervention\",\n          \"robust recovery\",\n          \"operational flexibility\"\n        ],\n        \"prerequisite_for\": [\n          \"Automated, metadata-driven retrieval\",\n          \"Comprehensive traceability\"\n        ]\n      },\n      \"3. Automated, metadata-driven document identification and retrieval\": {\n        \"depends_on\": [\n          \"Simple, engineer-centric design\",\n          \"Modular workflow design\"\n        ],\n        \"enables\": [\n          \"maximizing engineering time savings\",\n          \"eliminating manual effort\",\n          \"supporting structured organization\"\n        ],\n        \"prerequisite_for\": [\n          \"Structured, pattern-based file organization\"\n        ]\n      },\n      \"4. Structured, pattern-based file organization and naming\": {\n        \"depends_on\": [\n          \"Automated, metadata-driven retrieval\"\n        ],\n        \"enables\": [\n          \"reliable downstream processing\",\n          \"consistent file handling\"\n        ],\n        \"prerequisite_for\": [\n          \"Comprehensive traceability and auditability\"\n        ]\n      },\n      \"5. Comprehensive traceability and auditability\": {\n        \"depends_on\": [\n          \"Structured, pattern-based organization\",\n          \"Modular workflow\",\n          \"Automated retrieval\"\n        ],\n        \"enables\": [\n          \"robust process auditing\",\n          \"ease of error identification/correction\",\n          \"confidence in automation results\"\n        ]\n      }\n    }\n  },\n  \"process\": [\n    \"integrate_elements_by_structure()\",\n    \"maximize_signal_strength_per_component()\",\n    \"enforce_internal_consistency()\",\n    \"formulate_compact_nucleus()\"\n  ],\n  \"output\": {\n    \"condensed_nucleus\": [\n      \"Engineer-centric design and setup drive adoption and enable effective, low-complexity operation.\",\n      \"Modular workflow with explicit user checkpoints provides targeted intervention, operational flexibility, and robust process recovery.\",\n      \"Automated, metadata-driven document retrieval eliminates manual labor and maximizes engineering time savings.\",\n      \"Structured, pattern-based file organization and metadata-driven naming ensure reliable, consistent downstream processing.\",\n      \"Comprehensive traceability and auditability, powered by structured metadata and JSON/Markdown interfaces, ensure confident, correct, and auditable outcomes throughout the automation lifecycle.\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"minimal_signal_core\": [\n    \"Engineer-focused design ensures simple setup and reliable adoption.\",\n    \"Modular workflow with user checkpoints enables targeted intervention and robust recovery.\",\n    \"Metadata-driven automation eliminates manual retrieval and saves engineering time.\",\n    \"Pattern-based file organization and naming deliver consistent, reliable processing.\",\n    \"Structured audit trails via metadata and JSON/Markdown provide complete traceability.\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Universal Logic Abstraction\n\nAbstract the `minimal_signal_core`, neutralizing domain-specific language and reframing using universally applicable concepts and logical structures, while rigorously preserving the core intent and actionability across any context. Execute as\n\n`{role=universal_abstractor; input=minimal_signal_core:any; process=[neutralize_domain_specifics(), reframe_as_universal_concepts_logic(), validate_logic_preservation_across_contexts(), ensure_representation_agnostic_structure()]; output={universal_core_logic:any}}`", "step": "h", "title": "Universal Logic Abstraction", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '        # RigOfficeDownloader\\n\\n        > Automate document retrieval from NOV's RigDoc system, saving engineering time and streamlining 3D modeling preparation.\\n\\n        ## Purpose\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Eliminate tedious manual document retrieval from NOV's RigDoc system    \\u2502\\n        \\u2502 through intelligent automation, preserving 75%+ engineering time for    \\u2502\\n        \\u2502 high-value tasks.                                                       \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Workflow\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502  Documents \\u2192 Files \\u2192 Downloads                                          \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502  1. Fetch document metadata                                             \\u2502\\n        \\u2502  2. Select relevant documents                                           \\u2502\\n        \\u2502  3. Fetch file metadata for selected docs                               \\u2502\\n        \\u2502  4. Select files to download                                            \\u2502\\n        \\u2502  5. Download and organize files                                         \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Features\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Three-stage workflow with user checkpoints                            \\u2502\\n        \\u2502 \\u2022 Interactive menu for flexible execution                               \\u2502\\n        \\u2502 \\u2022 Markdown interfaces for document/file review                          \\u2502\\n        \\u2502 \\u2022 Configurable filter chains                                            \\u2502\\n        \\u2502 \\u2022 Smart file organization with subfolder support based on naming        \\u2502\\n        \\u2502   patterns                                                              \\u2502\\n        \\u2502 \\u2022 Metadata-driven file naming                                           \\u2502\\n        \\u2502 \\u2022 Error handling and deduplication                                      \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Setup\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 1. Run `py_venv_init.bat` to create Python environment                  \\u2502\\n        \\u2502 2. Run `RigOfficeDownloader-v4.bat` to start the application            \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Usage\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Interactive Menu Options:                                               \\u2502\\n        \\u2502 [0] Change search parameters (rig number, URLs)                         \\u2502\\n        \\u2502 [1] Configure filter chain                                              \\u2502\\n        \\u2502 [2] Fetch docs (scrape initial data)                                    \\u2502\\n        \\u2502 [3] Export docs (to Markdown for editing)                               \\u2502\\n        \\u2502 [4] Import updated doc data                                             \\u2502\\n        \\u2502 [5] Fetch files (prepare files for download)                            \\u2502\\n        \\u2502 [6] Export files (to Markdown for editing)                              \\u2502\\n        \\u2502 [7] Import updated file data                                            \\u2502\\n        \\u2502 [8] Download files                                                      \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 Run Modes:                                                              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --auto` (full automation)                 \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --interactive` (menu-driven)              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --config` (edit filters)                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Directory Structure\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 outputs/                                                                \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500 data/               # Metadata control files                        \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.json                                               \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.md                                                 \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-b-files.json                                              \\u2502\\n        \\u2502 \\u2502   \\u2514\\u2500\\u2500 <rig>-b-files.md                                                \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500 downloads/          # Final organized files                         \\u2502\\n        \\u2502     \\u2514\\u2500\\u2500 <rig>/                                                          \\u2502\\n        \\u2502         \\u251c\\u2500\\u2500 <subfolder>/                                                \\u2502\\n        \\u2502         \\u2502   \\u2514\\u2500\\u2500 <item_generated_name>.<ext>                             \\u2502\\n        \\u2502         \\u2514\\u2500\\u2500 ...                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Benefits\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510  \\u2502\\n        \\u2502 \\u2502                 \\u2502 Manual         \\u2502 Automated                       \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Time per project\\u2502 4-6 hours      \\u2502 <1 hour (75%+ saved)            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Organization    \\u2502 Error-prone    \\u2502 Metadata-driven                 \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 User review     \\u2502 Tedious        \\u2502 Markdown checkpoints            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Auditability    \\u2502 None           \\u2502 JSON/MD traceability            \\u2502  \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Technical Details\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Core Technologies: Python, Selenium, BeautifulSoup, JSON, Markdown     \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Data Flow: Web scraping \\u2192 JSON storage \\u2192 Markdown interface \\u2192         \\u2502\\n        \\u2502   User selection \\u2192 Automated downloads                                  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Filter System: Pattern-based matching (inclusion/exclusion) on fields \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 File Organization: Subfolder creation from '/' in item_generated_name \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Version History\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 v1: Basic document retrieval and download                             \\u2502\\n        \\u2502 \\u2022 v2: JSON/Markdown conversion and user selection                       \\u2502\\n        \\u2502 \\u2022 v3: Improved error handling and field organization                    \\u2502\\n        \\u2502 \\u2022 v4: Subfolder support, filter chains, field ordering                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        > \\\"Complexity is the enemy of execution \\u2014 Optimized for engineers, by engineers.\\\"'\",\n  \"minimal_signal_core\": [\n    \"Engineer-focused design ensures simple setup and reliable adoption.\",\n    \"Modular workflow with user checkpoints enables targeted intervention and robust recovery.\",\n    \"Metadata-driven automation eliminates manual retrieval and saves engineering time.\",\n    \"Pattern-based file organization and naming deliver consistent, reliable processing.\",\n    \"Structured audit trails via metadata and JSON/Markdown provide complete traceability.\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"universal_core_logic\": [\n    \"Design processes and systems to be easy to set up and use, facilitating dependable adoption by end users.\",\n    \"Construct workflows as modular sequences with defined user intervention points to enable focused action and resilient recovery from errors.\",\n    \"Leverage data attribute-based automation to replace manual tasks, resulting in significant time savings.\",\n    \"Employ pattern-based methods for item categorization and labeling, ensuring uniform and dependable processing.\",\n    \"Maintain comprehensive traceability through structured audit records utilizing standardized, readable data formats.\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Linguistic Potency Injection\n\nReforge the `universal_core_logic` using high-potency, action-driven, maximally precise imperatives and descriptors; eliminate passive or neutral language, amplifying structural clarity, actionability, and conceptual force without increasing length. Execute as\n\n`{role=linguistic_intensifier; input=universal_core_logic:any; process=[select_high_potency_verbs_descriptors(), eliminate_passive_neutral_ambiguous_language(), maximize_precision_impact_per_word(), amplify_actionability_via_phrasing()]; output={potent_core:any}}`", "step": "i", "title": "Linguistic Potency Injection", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '        # RigOfficeDownloader\\n\\n        > Automate document retrieval from NOV's RigDoc system, saving engineering time and streamlining 3D modeling preparation.\\n\\n        ## Purpose\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Eliminate tedious manual document retrieval from NOV's RigDoc system    \\u2502\\n        \\u2502 through intelligent automation, preserving 75%+ engineering time for    \\u2502\\n        \\u2502 high-value tasks.                                                       \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Workflow\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502  Documents \\u2192 Files \\u2192 Downloads                                          \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502  1. Fetch document metadata                                             \\u2502\\n        \\u2502  2. Select relevant documents                                           \\u2502\\n        \\u2502  3. Fetch file metadata for selected docs                               \\u2502\\n        \\u2502  4. Select files to download                                            \\u2502\\n        \\u2502  5. Download and organize files                                         \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Features\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Three-stage workflow with user checkpoints                            \\u2502\\n        \\u2502 \\u2022 Interactive menu for flexible execution                               \\u2502\\n        \\u2502 \\u2022 Markdown interfaces for document/file review                          \\u2502\\n        \\u2502 \\u2022 Configurable filter chains                                            \\u2502\\n        \\u2502 \\u2022 Smart file organization with subfolder support based on naming        \\u2502\\n        \\u2502   patterns                                                              \\u2502\\n        \\u2502 \\u2022 Metadata-driven file naming                                           \\u2502\\n        \\u2502 \\u2022 Error handling and deduplication                                      \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Setup\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 1. Run `py_venv_init.bat` to create Python environment                  \\u2502\\n        \\u2502 2. Run `RigOfficeDownloader-v4.bat` to start the application            \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Usage\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Interactive Menu Options:                                               \\u2502\\n        \\u2502 [0] Change search parameters (rig number, URLs)                         \\u2502\\n        \\u2502 [1] Configure filter chain                                              \\u2502\\n        \\u2502 [2] Fetch docs (scrape initial data)                                    \\u2502\\n        \\u2502 [3] Export docs (to Markdown for editing)                               \\u2502\\n        \\u2502 [4] Import updated doc data                                             \\u2502\\n        \\u2502 [5] Fetch files (prepare files for download)                            \\u2502\\n        \\u2502 [6] Export files (to Markdown for editing)                              \\u2502\\n        \\u2502 [7] Import updated file data                                            \\u2502\\n        \\u2502 [8] Download files                                                      \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 Run Modes:                                                              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --auto` (full automation)                 \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --interactive` (menu-driven)              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --config` (edit filters)                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Directory Structure\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 outputs/                                                                \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500 data/               # Metadata control files                        \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.json                                               \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.md                                                 \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-b-files.json                                              \\u2502\\n        \\u2502 \\u2502   \\u2514\\u2500\\u2500 <rig>-b-files.md                                                \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500 downloads/          # Final organized files                         \\u2502\\n        \\u2502     \\u2514\\u2500\\u2500 <rig>/                                                          \\u2502\\n        \\u2502         \\u251c\\u2500\\u2500 <subfolder>/                                                \\u2502\\n        \\u2502         \\u2502   \\u2514\\u2500\\u2500 <item_generated_name>.<ext>                             \\u2502\\n        \\u2502         \\u2514\\u2500\\u2500 ...                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Benefits\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510  \\u2502\\n        \\u2502 \\u2502                 \\u2502 Manual         \\u2502 Automated                       \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Time per project\\u2502 4-6 hours      \\u2502 <1 hour (75%+ saved)            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Organization    \\u2502 Error-prone    \\u2502 Metadata-driven                 \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 User review     \\u2502 Tedious        \\u2502 Markdown checkpoints            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Auditability    \\u2502 None           \\u2502 JSON/MD traceability            \\u2502  \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Technical Details\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Core Technologies: Python, Selenium, BeautifulSoup, JSON, Markdown     \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Data Flow: Web scraping \\u2192 JSON storage \\u2192 Markdown interface \\u2192         \\u2502\\n        \\u2502   User selection \\u2192 Automated downloads                                  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Filter System: Pattern-based matching (inclusion/exclusion) on fields \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 File Organization: Subfolder creation from '/' in item_generated_name \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Version History\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 v1: Basic document retrieval and download                             \\u2502\\n        \\u2502 \\u2022 v2: JSON/Markdown conversion and user selection                       \\u2502\\n        \\u2502 \\u2022 v3: Improved error handling and field organization                    \\u2502\\n        \\u2502 \\u2022 v4: Subfolder support, filter chains, field ordering                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        > \\\"Complexity is the enemy of execution \\u2014 Optimized for engineers, by engineers.\\\"'\",\n  \"universal_core_logic\": [\n    \"Design processes and systems to be easy to set up and use, facilitating dependable adoption by end users.\",\n    \"Construct workflows as modular sequences with defined user intervention points to enable focused action and resilient recovery from errors.\",\n    \"Leverage data attribute-based automation to replace manual tasks, resulting in significant time savings.\",\n    \"Employ pattern-based methods for item categorization and labeling, ensuring uniform and dependable processing.\",\n    \"Maintain comprehensive traceability through structured audit records utilizing standardized, readable data formats.\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"potent_core\": [\n    \"Engineer rapid setup and frictionless operation to guarantee seamless end-user deployment.\",\n    \"Forge modular workflows with explicit user control points, enabling targeted intervention and bulletproof error recovery.\",\n    \"Automate manual workloads using attribute-driven logic to annihilate inefficiency and slash processing times.\",\n    \"Enforce pattern-driven categorization and deterministic labeling to yield uncompromising, repeatable results.\",\n    \"Embed ironclad audit trails using rigorous, accessible data formats to ensure absolute traceability and oversight.\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Axiomatic Vectorization for One-Line\n\nDetermine the optimal linear sequence for the `potent_core` components based on the `causal_nexus`; select the highest-density, unambiguous connectors (e.g., ';', '->', '|') to vectorize the structure ready for single-line collapse. Execute as\n\n`{role=one_line_vectorizer; input={potent_core:any, causal_nexus:dict}; process=[determine_optimal_axiomatic_linear_sequence(), select_high_density_unambiguous_connectors(), vectorize_components_for_linearity()]; output={linear_vector_plan:dict(sequence:list, connectors:list)}}`", "step": "j", "title": "Axiomatic Vectorization for One-Line", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '        # RigOfficeDownloader\\n\\n        > Automate document retrieval from NOV's RigDoc system, saving engineering time and streamlining 3D modeling preparation.\\n\\n        ## Purpose\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Eliminate tedious manual document retrieval from NOV's RigDoc system    \\u2502\\n        \\u2502 through intelligent automation, preserving 75%+ engineering time for    \\u2502\\n        \\u2502 high-value tasks.                                                       \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Workflow\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502  Documents \\u2192 Files \\u2192 Downloads                                          \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502  1. Fetch document metadata                                             \\u2502\\n        \\u2502  2. Select relevant documents                                           \\u2502\\n        \\u2502  3. Fetch file metadata for selected docs                               \\u2502\\n        \\u2502  4. Select files to download                                            \\u2502\\n        \\u2502  5. Download and organize files                                         \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Features\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Three-stage workflow with user checkpoints                            \\u2502\\n        \\u2502 \\u2022 Interactive menu for flexible execution                               \\u2502\\n        \\u2502 \\u2022 Markdown interfaces for document/file review                          \\u2502\\n        \\u2502 \\u2022 Configurable filter chains                                            \\u2502\\n        \\u2502 \\u2022 Smart file organization with subfolder support based on naming        \\u2502\\n        \\u2502   patterns                                                              \\u2502\\n        \\u2502 \\u2022 Metadata-driven file naming                                           \\u2502\\n        \\u2502 \\u2022 Error handling and deduplication                                      \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Setup\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 1. Run `py_venv_init.bat` to create Python environment                  \\u2502\\n        \\u2502 2. Run `RigOfficeDownloader-v4.bat` to start the application            \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Usage\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Interactive Menu Options:                                               \\u2502\\n        \\u2502 [0] Change search parameters (rig number, URLs)                         \\u2502\\n        \\u2502 [1] Configure filter chain                                              \\u2502\\n        \\u2502 [2] Fetch docs (scrape initial data)                                    \\u2502\\n        \\u2502 [3] Export docs (to Markdown for editing)                               \\u2502\\n        \\u2502 [4] Import updated doc data                                             \\u2502\\n        \\u2502 [5] Fetch files (prepare files for download)                            \\u2502\\n        \\u2502 [6] Export files (to Markdown for editing)                              \\u2502\\n        \\u2502 [7] Import updated file data                                            \\u2502\\n        \\u2502 [8] Download files                                                      \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 Run Modes:                                                              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --auto` (full automation)                 \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --interactive` (menu-driven)              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --config` (edit filters)                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Directory Structure\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 outputs/                                                                \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500 data/               # Metadata control files                        \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.json                                               \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.md                                                 \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-b-files.json                                              \\u2502\\n        \\u2502 \\u2502   \\u2514\\u2500\\u2500 <rig>-b-files.md                                                \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500 downloads/          # Final organized files                         \\u2502\\n        \\u2502     \\u2514\\u2500\\u2500 <rig>/                                                          \\u2502\\n        \\u2502         \\u251c\\u2500\\u2500 <subfolder>/                                                \\u2502\\n        \\u2502         \\u2502   \\u2514\\u2500\\u2500 <item_generated_name>.<ext>                             \\u2502\\n        \\u2502         \\u2514\\u2500\\u2500 ...                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Benefits\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510  \\u2502\\n        \\u2502 \\u2502                 \\u2502 Manual         \\u2502 Automated                       \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Time per project\\u2502 4-6 hours      \\u2502 <1 hour (75%+ saved)            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Organization    \\u2502 Error-prone    \\u2502 Metadata-driven                 \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 User review     \\u2502 Tedious        \\u2502 Markdown checkpoints            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Auditability    \\u2502 None           \\u2502 JSON/MD traceability            \\u2502  \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Technical Details\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Core Technologies: Python, Selenium, BeautifulSoup, JSON, Markdown     \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Data Flow: Web scraping \\u2192 JSON storage \\u2192 Markdown interface \\u2192         \\u2502\\n        \\u2502   User selection \\u2192 Automated downloads                                  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Filter System: Pattern-based matching (inclusion/exclusion) on fields \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 File Organization: Subfolder creation from '/' in item_generated_name \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Version History\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 v1: Basic document retrieval and download                             \\u2502\\n        \\u2502 \\u2022 v2: JSON/Markdown conversion and user selection                       \\u2502\\n        \\u2502 \\u2022 v3: Improved error handling and field organization                    \\u2502\\n        \\u2502 \\u2022 v4: Subfolder support, filter chains, field ordering                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        > \\\"Complexity is the enemy of execution \\u2014 Optimized for engineers, by engineers.\\\"'\",\n  \"potent_core\": [\n    \"Engineer rapid setup and frictionless operation to guarantee seamless end-user deployment.\",\n    \"Forge modular workflows with explicit user control points, enabling targeted intervention and bulletproof error recovery.\",\n    \"Automate manual workloads using attribute-driven logic to annihilate inefficiency and slash processing times.\",\n    \"Enforce pattern-driven categorization and deterministic labeling to yield uncompromising, repeatable results.\",\n    \"Embed ironclad audit trails using rigorous, accessible data formats to ensure absolute traceability and oversight.\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"linear_vector_plan\": {\n    \"sequence\": [\n      \"Engineer rapid setup and frictionless operation to guarantee seamless end-user deployment\",\n      \"Automate manual workloads using attribute-driven logic to annihilate inefficiency and slash processing times\",\n      \"Forge modular workflows with explicit user control points, enabling targeted intervention and bulletproof error recovery\",\n      \"Enforce pattern-driven categorization and deterministic labeling to yield uncompromising, repeatable results\",\n      \"Embed ironclad audit trails using rigorous, accessible data formats to ensure absolute traceability and oversight\"\n    ],\n    \"connectors\": [\n      \";\",\n      \";\",\n      \";\",\n      \";\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Semantic Compression & Symbolization\n\nApply maximal semantic compression to the planned linear vector, substituting ultra-concise symbols, abbreviations, or keywords where possible without ambiguity; encapsulate the full essential meaning within the absolute minimum footprint, ensuring self-contained decodability. Execute as\n\n`{role=semantic_compressor; input=linear_vector_plan:dict; process=[identify_compressible_concepts(), substitute_minimal_unambiguous_symbols(), maximize_meaning_per_unit(), validate_decodability_self_sufficiency()]; output={compressed_vector_elements:list}}`", "step": "k", "title": "Semantic Compression & Symbolization", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '        # RigOfficeDownloader\\n\\n        > Automate document retrieval from NOV's RigDoc system, saving engineering time and streamlining 3D modeling preparation.\\n\\n        ## Purpose\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Eliminate tedious manual document retrieval from NOV's RigDoc system    \\u2502\\n        \\u2502 through intelligent automation, preserving 75%+ engineering time for    \\u2502\\n        \\u2502 high-value tasks.                                                       \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Workflow\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502  Documents \\u2192 Files \\u2192 Downloads                                          \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502  1. Fetch document metadata                                             \\u2502\\n        \\u2502  2. Select relevant documents                                           \\u2502\\n        \\u2502  3. Fetch file metadata for selected docs                               \\u2502\\n        \\u2502  4. Select files to download                                            \\u2502\\n        \\u2502  5. Download and organize files                                         \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Features\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Three-stage workflow with user checkpoints                            \\u2502\\n        \\u2502 \\u2022 Interactive menu for flexible execution                               \\u2502\\n        \\u2502 \\u2022 Markdown interfaces for document/file review                          \\u2502\\n        \\u2502 \\u2022 Configurable filter chains                                            \\u2502\\n        \\u2502 \\u2022 Smart file organization with subfolder support based on naming        \\u2502\\n        \\u2502   patterns                                                              \\u2502\\n        \\u2502 \\u2022 Metadata-driven file naming                                           \\u2502\\n        \\u2502 \\u2022 Error handling and deduplication                                      \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Setup\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 1. Run `py_venv_init.bat` to create Python environment                  \\u2502\\n        \\u2502 2. Run `RigOfficeDownloader-v4.bat` to start the application            \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Usage\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 Interactive Menu Options:                                               \\u2502\\n        \\u2502 [0] Change search parameters (rig number, URLs)                         \\u2502\\n        \\u2502 [1] Configure filter chain                                              \\u2502\\n        \\u2502 [2] Fetch docs (scrape initial data)                                    \\u2502\\n        \\u2502 [3] Export docs (to Markdown for editing)                               \\u2502\\n        \\u2502 [4] Import updated doc data                                             \\u2502\\n        \\u2502 [5] Fetch files (prepare files for download)                            \\u2502\\n        \\u2502 [6] Export files (to Markdown for editing)                              \\u2502\\n        \\u2502 [7] Import updated file data                                            \\u2502\\n        \\u2502 [8] Download files                                                      \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 Run Modes:                                                              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --auto` (full automation)                 \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --interactive` (menu-driven)              \\u2502\\n        \\u2502 \\u2022 `RigOfficeDownloader-v4.bat --config` (edit filters)                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Directory Structure\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 outputs/                                                                \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500 data/               # Metadata control files                        \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.json                                               \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.md                                                 \\u2502\\n        \\u2502 \\u2502   \\u251c\\u2500\\u2500 <rig>-b-files.json                                              \\u2502\\n        \\u2502 \\u2502   \\u2514\\u2500\\u2500 <rig>-b-files.md                                                \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500 downloads/          # Final organized files                         \\u2502\\n        \\u2502     \\u2514\\u2500\\u2500 <rig>/                                                          \\u2502\\n        \\u2502         \\u251c\\u2500\\u2500 <subfolder>/                                                \\u2502\\n        \\u2502         \\u2502   \\u2514\\u2500\\u2500 <item_generated_name>.<ext>                             \\u2502\\n        \\u2502         \\u2514\\u2500\\u2500 ...                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Benefits\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u252c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510  \\u2502\\n        \\u2502 \\u2502                 \\u2502 Manual         \\u2502 Automated                       \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Time per project\\u2502 4-6 hours      \\u2502 <1 hour (75%+ saved)            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Organization    \\u2502 Error-prone    \\u2502 Metadata-driven                 \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 User review     \\u2502 Tedious        \\u2502 Markdown checkpoints            \\u2502  \\u2502\\n        \\u2502 \\u251c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u253c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2524  \\u2502\\n        \\u2502 \\u2502 Auditability    \\u2502 None           \\u2502 JSON/MD traceability            \\u2502  \\u2502\\n        \\u2502 \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2534\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Technical Details\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 Core Technologies: Python, Selenium, BeautifulSoup, JSON, Markdown     \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Data Flow: Web scraping \\u2192 JSON storage \\u2192 Markdown interface \\u2192         \\u2502\\n        \\u2502   User selection \\u2192 Automated downloads                                  \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 Filter System: Pattern-based matching (inclusion/exclusion) on fields \\u2502\\n        \\u2502                                                                         \\u2502\\n        \\u2502 \\u2022 File Organization: Subfolder creation from '/' in item_generated_name \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        ## Version History\\n\\n        ```\\n        \\u250c\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2510\\n        \\u2502 \\u2022 v1: Basic document retrieval and download                             \\u2502\\n        \\u2502 \\u2022 v2: JSON/Markdown conversion and user selection                       \\u2502\\n        \\u2502 \\u2022 v3: Improved error handling and field organization                    \\u2502\\n        \\u2502 \\u2022 v4: Subfolder support, filter chains, field ordering                  \\u2502\\n        \\u2514\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2500\\u2518\\n        ```\\n\\n        > \\\"Complexity is the enemy of execution \\u2014 Optimized for engineers, by engineers.\\\"'\",\n  \"linear_vector_plan\": {\n    \"sequence\": [\n      \"Engineer rapid setup and frictionless operation to guarantee seamless end-user deployment\",\n      \"Automate manual workloads using attribute-driven logic to annihilate inefficiency and slash processing times\",\n      \"Forge modular workflows with explicit user control points, enabling targeted intervention and bulletproof error recovery\",\n      \"Enforce pattern-driven categorization and deterministic labeling to yield uncompromising, repeatable results\",\n      \"Embed ironclad audit trails using rigorous, accessible data formats to ensure absolute traceability and oversight\"\n    ],\n    \"connectors\": [\n      \";\",\n      \";\",\n      \";\",\n      \";\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"compressed_vector_elements\": [\n    \"EZ-setup+run→insta-deploy\",\n    \"AUTO(man→attr-logic)⇨-ineff+time\",\n    \"MOD-WF+USR-cp⇨ctrl+err-rec\",\n    \"PAT-categ+LBL⇨det-results\",\n    \"AUD-trace(JSON/MD)⇨oversight\"\n  ]\n}", "cost": 0.0}}}], "total_cost": 0.0}