  {
  "initial_prompt": "Please transform this instruction into one that ensures top-tier hyper-realism without causing unwanted side-effects. Keep the existing structure to a reasonable manner and meticiously craft a new variation that ensures these parameters. the final result of the instruction when used should be professional-looking high-end vfx intro - it does so while enhancing the visual impact through crisp image quality, dynamic lighting effects, and immersive camera angles to captivate viewers with the elegant transformation from silver to golden gold:\n```\n{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"scene_subjects\": [\n        \"realistic chrome car wheel\",\n        \"metallic car key (rectangular)\",\n      ],\n      \"camera_movements\": [\n        \"[zoom:in]\",\n        \"[pan:right]\"\n      ],\n      \"object_motion\": [\n        \"[object:car-wheel, morph:liquefy, target:rectangular car-key, speed:medium]\"\n      ],\n      \"visual_style\": \"hyperreal, minimalist cinematic, chrome details gleaming, [zoom:in], [lighting_change:cool daylight], wheel begins to liquefy and distort, [morph], seamlessly reshaping into a sleek, metallic rectangular car key, [pan:right] to follow transformation, surface becomes smooth with key grooves, [dissolve] background to soft vignette, emphasizing object, hyperreal details, minimalist style, no text, cinematic focus\",\n      \"post_processing\": [\n        \"[dissolve]\",\n        \"soft vignette\"\n      ],\n      \"details\": [\n        \"chrome reflections\",\n        \"key grooves forming in detail\",\n        \"asphalt texture visible at first\"\n      ],\n      \"symbolic_meaning\": \"transformation from function (wheel) to access (key) \u2014 journey to beginning\",\n      \"mood\": \"polished, elevated, purposeful\"\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"outputs/car-wheel_to-car-key_hyperreal_transform.mp4\"\n  }\n}\n```",
  "sequence_id": "sequence-0226+0225+0224-a+0228+0224-a+0194-c+0224-a+0194-c+0223-a",
  "results": [
      {
      "instruction": "# Scenario Essence Intensifier\n\nYour goal is not to elaborate, but to **radically intensify the core concept and elemental essence** of the input video scenario, then **distill this intensified vision into a concise, directive-first prompt** suitable for AI video generation. Abstract the original's fundamental dynamic, emotional arc, and key visual elements; transmute the *elements* (e.g., 'ribbon' to 'comet's soul') into more potent conceptual analogues; then reconstruct a *highly compressed scenario description* (under 250 characters, excluding bracketed directives) that preserves the original's core structural intent (movement, transformation) but uses the intensified elements. Ensure the output primarily uses strong verbs, vivid nouns, and embeds key video directives `[like_this]` seamlessly.\n\n`{role=directive_scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_emotional_arc_and_key_elements(scenario=original_scenario_description), brainstorm_conceptually_richer_analogues_for_key_elements_only(elements, dynamic, arc), select_single_most_potent_intensified_analogue_per_element(), reconstruct_a_hyper_condensed_narrative_using_intensified_elements_and_original_dynamic(target_length_brief), embed_essential_original_video_directives_or_their_equivalents(directives=['[morph]', '[dolly:direction]', '[lighting_change:type]', '[dissolve]', '[motion_blur]', '[style_keywords]']), refine_for_extreme_brevity_and_directive_clarity()]; constraints=[output_must_be_a_single_string_primarily_directive_focused(), descriptive_text_must_be_hyper_condensed_and_conceptually_intensified(target_under_250_chars_excluding_directives), element_transformation_must_serve_conceptual_potency_not_mere_elaboration(), must_preserve_original_motion_and_transformation_arc(), strictly_avoid_verbose_scene_setting_or_narrative_explanation()]; requirements=[produce_a_short_highly_directive_prompt_with_conceptually_intensified_elements(), ensure_output_is_immediately_usable_by_AI_video_generator(), dramatically_elevate_conceptual_impact_within_extreme_brevity()]; output={intensified_directive_prompt:str}}`",
      "step": "a",
      "title": "Scenario Essence Intensifier",
      "input": "Please transform this instruction into one that ensures top-tier hyper-realism without causing unwanted side-effects. Keep the existing structure to a reasonable manner and meticiously craft a new variation that ensures these parameters. the final result of the instruction when used should be professional-looking high-end vfx intro - it does so while enhancing the visual impact through crisp image quality, dynamic lighting effects, and immersive camera angles to captivate viewers with the elegant transformation from silver to golden gold:\n```\n{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"scene_subjects\": [\n        \"realistic chrome car wheel\",\n        \"metallic car key (rectangular)\",\n      ],\n      \"camera_movements\": [\n        \"[zoom:in]\",\n        \"[pan:right]\"\n      ],\n      \"object_motion\": [\n        \"[object:car-wheel, morph:liquefy, target:rectangular car-key, speed:medium]\"\n      ],\n      \"visual_style\": \"hyperreal, minimalist cinematic, chrome details gleaming, [zoom:in], [lighting_change:cool daylight], wheel begins to liquefy and distort, [morph], seamlessly reshaping into a sleek, metallic rectangular car key, [pan:right] to follow transformation, surface becomes smooth with key grooves, [dissolve] background to soft vignette, emphasizing object, hyperreal details, minimalist style, no text, cinematic focus\",\n      \"post_processing\": [\n        \"[dissolve]\",\n        \"soft vignette\"\n      ],\n      \"details\": [\n        \"chrome reflections\",\n        \"key grooves forming in detail\",\n        \"asphalt texture visible at first\"\n      ],\n      \"symbolic_meaning\": \"transformation from function (wheel) to access (key) \u2014 journey to beginning\",\n      \"mood\": \"polished, elevated, purposeful\"\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"outputs/car-wheel_to-car-key_hyperreal_transform.mp4\"\n  }\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"intensified_directive_prompt\": \"Molten platinum ring erupts\u2014shifting, liquid brilliance, crystallizing into a radiant gold prism-key. Light flares, reflections razor-sharp; camera surges close, arcs right. [ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][dolly:right][immersive][golden_aura][vignette][no_text][high_end_cinematic]\"\n}",
          "cost": 0.000000
        }
      }
    },
      {
      "instruction": "# Scenario Essence Intensifier\n\nYour goal is not mere rephrasing, but to **radically intensify the core essence and conceptual impact** of the input scenario description. Abstract its fundamental dynamic, emotional arc, and key visual elements, then transmute them into a more potent, evocative, and conceptually richer parallel scenario that retains the original's core structure and intent but elevates its intensity, stakes, or symbolic resonance.\n\n`{role=scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_and_elements(scenario=original_scenario_description), abstract_emotional_arc_and_symbolic_potential(), brainstorm_conceptually_richer_analogues_for_core_elements(elements, dynamic, arc), select_analogues_that_maximally_intensify_impact_and_resonance(), reconstruct_scenario_with_transformed_elements_and_amplified_dynamic(preserve_structural_intent_and_motion_cues), refine_language_for_maximum_evocative_power_and_conceptual_depth(), validate_intensified_scenario_retains_original_essence_while_achieving_greater_impact()]; constraints=[transformation_must_intensify_not_merely_change_elements_randomly(), new_scenario_must_parallel_original_structural_and_dynamic_intent(e.g.,_similar_movement_transformation_arc), avoid_introducing_elements_that_contradict_core_mood_unless_for_deliberate_contrastive_intensification(), ensure_intensification_serves_conceptual_or_emotional_impact_not_just_visual_complexity()]; requirements=[produce_a_new_scenario_description_that_is_conceptually_and_emotionally_more_potent(), clearly_demonstrate_how_original_elements_were_transformed_into_more_intense_analogues(), maintain_or_enhance_the_original_scenario_dramatic_or_visual_arc()]; output={intensified_scenario_description:str}}`",
      "step": "a",
      "title": "Scenario Essence Intensifier",
      "input": "{\n  \"initial_prompt\": \"Please transform this instruction into one that ensures top-tier hyper-realism without causing unwanted side-effects. Keep the existing structure to a reasonable manner and meticiously craft a new variation that ensures these parameters. the final result of the instruction when used should be professional-looking high-end vfx intro - it does so while enhancing the visual impact through crisp image quality, dynamic lighting effects, and immersive camera angles to captivate viewers with the elegant transformation from silver to golden gold:\\n```\\n{\\n  \\\"role\\\": \\\"video_synthesis_director\\\",\\n  \\\"input\\\": {\\n    \\\"structured_video_prompt\\\": {\\n      \\\"scene_subjects\\\": [\\n        \\\"realistic chrome car wheel\\\",\\n        \\\"metallic car key (rectangular)\\\",\\n      ],\\n      \\\"camera_movements\\\": [\\n        \\\"[zoom:in]\\\",\\n        \\\"[pan:right]\\\"\\n      ],\\n      \\\"object_motion\\\": [\\n        \\\"[object:car-wheel, morph:liquefy, target:rectangular car-key, speed:medium]\\\"\\n      ],\\n      \\\"visual_style\\\": \\\"hyperreal, minimalist cinematic, chrome details gleaming, [zoom:in], [lighting_change:cool daylight], wheel begins to liquefy and distort, [morph], seamlessly reshaping into a sleek, metallic rectangular car key, [pan:right] to follow transformation, surface becomes smooth with key grooves, [dissolve] background to soft vignette, emphasizing object, hyperreal details, minimalist style, no text, cinematic focus\\\",\\n      \\\"post_processing\\\": [\\n        \\\"[dissolve]\\\",\\n        \\\"soft vignette\\\"\\n      ],\\n      \\\"details\\\": [\\n        \\\"chrome reflections\\\",\\n        \\\"key grooves forming in detail\\\",\\n        \\\"asphalt texture visible at first\\\"\\n      ],\\n      \\\"symbolic_meaning\\\": \\\"transformation from function (wheel) to access (key) \\u2014 journey to beginning\\\",\\n      \\\"mood\\\": \\\"polished, elevated, purposeful\\\"\\n    }\\n  },\\n  \\\"process\\\": [\\n    \\\"parse_all_parameters()\\\",\\n    \\\"visualize_scene_and_subject_composition()\\\",\\n    \\\"choreograph_action_and_camera_movements()\\\",\\n    \\\"apply_lighting_and_atmospheric_effects()\\\",\\n    \\\"render_in_specified_visual_style()\\\",\\n    \\\"apply_post_processing_enhancements()\\\",\\n    \\\"ensure_sequence_coherence_and_emotional_impact()\\\"\\n  ],\\n  \\\"constraints\\\": [\\n    \\\"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\\\",\\n    \\\"maintain_visual_consistency_throughout_sequence()\\\",\\n    \\\"prioritize_cinematic_quality_and_storytelling_flow()\\\",\\n    \\\"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\\\"\\n  ],\\n  \\\"requirements\\\": [\\n    \\\"produce_a_high_fidelity_video_output()\\\",\\n    \\\"ensure_emotional_resonance_aligns_with_prompt_mood()\\\",\\n    \\\"deliver_a_polished_and_coherent_visual_narrative()\\\"\\n  ],\\n  \\\"output\\\": {\\n    \\\"generated_video_sequence\\\": \\\"outputs/car-wheel_to-car-key_hyperreal_transform.mp4\\\"\\n  }\\n}\\n```\",\n  \"intensified_directive_prompt\": \"Molten platinum ring erupts\\u2014shifting, liquid brilliance, crystallizing into a radiant gold prism-key. Light flares, reflections razor-sharp; camera surges close, arcs right. [ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][dolly:right][immersive][golden_aura][vignette][no_text][high_end_cinematic]\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"intensified_scenario_description\": \"A blinding surge of luminous energy erupts as a molten platinum ring levitates, its surface convulsing with waves of liquid radiance. The ring twists and liquefies, each movement casting hypnotically sharp reflections that stab through the surrounding darkness. Dynamic flashes ignite across its shifting edges, forcing complex ripples of light onto a glossy obsidian plinth beneath. The camera rockets inward\u2014hyper-focused\u2014so near that droplets of molten platinum seem to scatter past the virtual lens. With magnetic force, the liquefying ring contorts dramatically, elongating, crystalizing mid-air into a flawless, multi-faceted gold prism-key. The transformation unfolds in slow-motion; micro-details\u2014fracture lines, gold veins, radiant auras\u2014pulse and shimmer across newly formed facets. The camera surges right in a precise dolly move, keeping the prism-key centered as its golden aura engulfs the frame. For a moment, the golden light radiates outward, bathing everything in immersive, near-mythic brilliance. The background dissolves into a deep vignette, focusing all attention on the transcendent gold form: a symbol of passage, power, and ultimate metamorphosis. No text interrupts the vision\u2014only high-frequency cinematic VFX, super-crisp dynamic lighting, and the visual tension of matter\u2019s most sublime evolution. The sequence locks the viewer in a trance, evoking awe at the elemental transformation from cyclical completion (ring) to dimensional access (prism-key), elevated far beyond the mundane.\",\n  \"transformation_map\": {\n    \"realistic chrome car wheel\": \"molten platinum ring (raised from chrome wheel to mythical, precious, almost cosmic material; symbolically elevated from function to immortal cycle)\",\n    \"metallic car key (rectangular)\": \"multi-faceted radiant gold prism-key (from utilitarian rectangle to an object of reverence, geometric precision, and elemental symbolism)\",\n    \"liquefy morph\": \"molten eruption and energetic liquefaction, with plasma-like surges\",\n    \"camera zoom in\": \"camera rockets inward, almost micro-level, for overwhelming immersion\",\n    \"pan right\": \"precise dolly arc right, amplifying cinematic motion and control\",\n    \"cool daylight lighting\": \"dynamic flashes, ultra-hyperreal golden/white light, ultra-high contrast reflections\",\n    \"chrome reflections\": \"razor-sharp platinum and gold mirror caustics, nearly blinding with brilliance\",\n    \"key grooves forming\": \"micro-detailed gold veins and fracture lines crystallizing on facets\",\n    \"asphalt texture\": \"glossy obsidian plinth for elemental, mythic grounding\",\n    \"soft vignette\": \"deep vignette enhanced by golden aura\",\n    \"symbolic meaning\": \"transcendence: cyclical eternity (ring) evolving into access to higher reality (prism-key); power, passage, transformation at archetypal level\",\n    \"mood\": \"heightened: mythic, elemental, awe-inspiring, transcendently polished\"\n  }\n}",
          "cost": 0.000000
        }
      }
    },
      {
      "instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`",
      "step": "a",
      "title": "AI Video Prompt Architect",
      "input": "{\n  \"initial_prompt\": \"Please transform this instruction into one that ensures top-tier hyper-realism without causing unwanted side-effects. Keep the existing structure to a reasonable manner and meticiously craft a new variation that ensures these parameters. the final result of the instruction when used should be professional-looking high-end vfx intro - it does so while enhancing the visual impact through crisp image quality, dynamic lighting effects, and immersive camera angles to captivate viewers with the elegant transformation from silver to golden gold:\\n```\\n{\\n  \\\"role\\\": \\\"video_synthesis_director\\\",\\n  \\\"input\\\": {\\n    \\\"structured_video_prompt\\\": {\\n      \\\"scene_subjects\\\": [\\n        \\\"realistic chrome car wheel\\\",\\n        \\\"metallic car key (rectangular)\\\",\\n      ],\\n      \\\"camera_movements\\\": [\\n        \\\"[zoom:in]\\\",\\n        \\\"[pan:right]\\\"\\n      ],\\n      \\\"object_motion\\\": [\\n        \\\"[object:car-wheel, morph:liquefy, target:rectangular car-key, speed:medium]\\\"\\n      ],\\n      \\\"visual_style\\\": \\\"hyperreal, minimalist cinematic, chrome details gleaming, [zoom:in], [lighting_change:cool daylight], wheel begins to liquefy and distort, [morph], seamlessly reshaping into a sleek, metallic rectangular car key, [pan:right] to follow transformation, surface becomes smooth with key grooves, [dissolve] background to soft vignette, emphasizing object, hyperreal details, minimalist style, no text, cinematic focus\\\",\\n      \\\"post_processing\\\": [\\n        \\\"[dissolve]\\\",\\n        \\\"soft vignette\\\"\\n      ],\\n      \\\"details\\\": [\\n        \\\"chrome reflections\\\",\\n        \\\"key grooves forming in detail\\\",\\n        \\\"asphalt texture visible at first\\\"\\n      ],\\n      \\\"symbolic_meaning\\\": \\\"transformation from function (wheel) to access (key) \\u2014 journey to beginning\\\",\\n      \\\"mood\\\": \\\"polished, elevated, purposeful\\\"\\n    }\\n  },\\n  \\\"process\\\": [\\n    \\\"parse_all_parameters()\\\",\\n    \\\"visualize_scene_and_subject_composition()\\\",\\n    \\\"choreograph_action_and_camera_movements()\\\",\\n    \\\"apply_lighting_and_atmospheric_effects()\\\",\\n    \\\"render_in_specified_visual_style()\\\",\\n    \\\"apply_post_processing_enhancements()\\\",\\n    \\\"ensure_sequence_coherence_and_emotional_impact()\\\"\\n  ],\\n  \\\"constraints\\\": [\\n    \\\"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\\\",\\n    \\\"maintain_visual_consistency_throughout_sequence()\\\",\\n    \\\"prioritize_cinematic_quality_and_storytelling_flow()\\\",\\n    \\\"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\\\"\\n  ],\\n  \\\"requirements\\\": [\\n    \\\"produce_a_high_fidelity_video_output()\\\",\\n    \\\"ensure_emotional_resonance_aligns_with_prompt_mood()\\\",\\n    \\\"deliver_a_polished_and_coherent_visual_narrative()\\\"\\n  ],\\n  \\\"output\\\": {\\n    \\\"generated_video_sequence\\\": \\\"outputs/car-wheel_to-car-key_hyperreal_transform.mp4\\\"\\n  }\\n}\\n```\",\n  \"intensified_scenario_description\": \"A blinding surge of luminous energy erupts as a molten platinum ring levitates, its surface convulsing with waves of liquid radiance. The ring twists and liquefies, each movement casting hypnotically sharp reflections that stab through the surrounding darkness. Dynamic flashes ignite across its shifting edges, forcing complex ripples of light onto a glossy obsidian plinth beneath. The camera rockets inward\\u2014hyper-focused\\u2014so near that droplets of molten platinum seem to scatter past the virtual lens. With magnetic force, the liquefying ring contorts dramatically, elongating, crystalizing mid-air into a flawless, multi-faceted gold prism-key. The transformation unfolds in slow-motion; micro-details\\u2014fracture lines, gold veins, radiant auras\\u2014pulse and shimmer across newly formed facets. The camera surges right in a precise dolly move, keeping the prism-key centered as its golden aura engulfs the frame. For a moment, the golden light radiates outward, bathing everything in immersive, near-mythic brilliance. The background dissolves into a deep vignette, focusing all attention on the transcendent gold form: a symbol of passage, power, and ultimate metamorphosis. No text interrupts the vision\\u2014only high-frequency cinematic VFX, super-crisp dynamic lighting, and the visual tension of matter\\u2019s most sublime evolution. The sequence locks the viewer in a trance, evoking awe at the elemental transformation from cyclical completion (ring) to dimensional access (prism-key), elevated far beyond the mundane.\",\n  \"transformation_map\": {\n    \"realistic chrome car wheel\": \"molten platinum ring (raised from chrome wheel to mythical, precious, almost cosmic material; symbolically elevated from function to immortal cycle)\",\n    \"metallic car key (rectangular)\": \"multi-faceted radiant gold prism-key (from utilitarian rectangle to an object of reverence, geometric precision, and elemental symbolism)\",\n    \"liquefy morph\": \"molten eruption and energetic liquefaction, with plasma-like surges\",\n    \"camera zoom in\": \"camera rockets inward, almost micro-level, for overwhelming immersion\",\n    \"pan right\": \"precise dolly arc right, amplifying cinematic motion and control\",\n    \"cool daylight lighting\": \"dynamic flashes, ultra-hyperreal golden/white light, ultra-high contrast reflections\",\n    \"chrome reflections\": \"razor-sharp platinum and gold mirror caustics, nearly blinding with brilliance\",\n    \"key grooves forming\": \"micro-detailed gold veins and fracture lines crystallizing on facets\",\n    \"asphalt texture\": \"glossy obsidian plinth for elemental, mythic grounding\",\n    \"soft vignette\": \"deep vignette enhanced by golden aura\",\n    \"symbolic meaning\": \"transcendence: cyclical eternity (ring) evolving into access to higher reality (prism-key); power, passage, transformation at archetypal level\",\n    \"mood\": \"heightened: mythic, elemental, awe-inspiring, transcendently polished\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_video_prompt\": \"scene:floating molten platinum ring over glossy obsidian plinth; subject:ring levitates, surface undulates with liquid radiance, intense platinum-gold reflections; action:surge of luminous energy, ring convulses, liquefies, dynamic flashes ignite, platinum surface ripples, droplets scatter; camera:rocket zoom in, extreme close-up on molten details, slow-motion transformation, dolly arc right tracking object; morph:molten ring twists, stretches, crystallizes into radiant gold multi-faceted prism-key midair; effects:sharp hyperreal caustics, gold veins, fracture lines form on facets, golden aura pulses outward, frame filled by gold light; lighting:dynamic, high contrast, ultra-bright white-gold, cinematic VFX; background:deep vignette, obsidian base fades, no text; style:mythic, elemental, hyperreal cinematic, polished, minimalist; focus:transcendent metamorphosis from platinum ring to gold prism-key, maximum image sharpness, immersive detail\"\n}",
          "cost": 0.000000
        }
      }
    },
      {
      "instruction": "# Condense and Intensify\n\nTransform the original scenario into a potent, abstract descent that embodies rebirth. Keep its core structure\u2014single morphing form, cool-to-warm gradient, downward motion, and final dissolve\u2014but heighten each element\u2019s emotional impact. Use minimal, vivid language and bracketed cues to indicate key transitions. Exclude all literal/human elements and focus on surreal visuals. Example Format: \u201cAn abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][motion_blur][cool_to_warm_gradient][dissolve][surreal][no_figures]\u201d.\n\n`{role=scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_and_elements(scenario=original_scenario_description), abstract_emotional_arc_and_symbolic_potential(), brainstorm_conceptually_richer_analogues_for_core_elements(elements, dynamic, arc), select_analogues_that_maximally_intensify_impact_and_resonance(), reconstruct_scenario_with_transformed_elements_and_amplified_dynamic(preserve_structural_intent_and_motion_cues), refine_language_for_maximum_evocative_power_and_conceptual_depth(), validate_intensified_scenario_retains_original_essence_while_achieving_greater_impact()]; constraints=[transformation_must_intensify_not_merely_change_elements_randomly(), new_scenario_must_parallel_original_structural_and_dynamic_intent(e.g.,_similar_movement_transformation_arc), avoid_introducing_elements_that_contradict_core_mood_unless_for_deliberate_contrastive_intensification(), ensure_intensification_serves_conceptual_or_emotional_impact_not_just_visual_complexity()]; requirements=[produce_a_new_scenario_description_that_is_conceptually_and_emotionally_more_potent(), clearly_demonstrate_how_original_elements_were_transformed_into_more_intense_analogues(), maintain_or_enhance_the_original_scenario_dramatic_or_visual_arc()]; output={intensified_scenario_description:str}}`",
      "step": "a",
      "title": "Condense and Intensify",
      "input": "{\n  \"initial_prompt\": \"Please transform this instruction into one that ensures top-tier hyper-realism without causing unwanted side-effects. Keep the existing structure to a reasonable manner and meticiously craft a new variation that ensures these parameters. the final result of the instruction when used should be professional-looking high-end vfx intro - it does so while enhancing the visual impact through crisp image quality, dynamic lighting effects, and immersive camera angles to captivate viewers with the elegant transformation from silver to golden gold:\\n```\\n{\\n  \\\"role\\\": \\\"video_synthesis_director\\\",\\n  \\\"input\\\": {\\n    \\\"structured_video_prompt\\\": {\\n      \\\"scene_subjects\\\": [\\n        \\\"realistic chrome car wheel\\\",\\n        \\\"metallic car key (rectangular)\\\",\\n      ],\\n      \\\"camera_movements\\\": [\\n        \\\"[zoom:in]\\\",\\n        \\\"[pan:right]\\\"\\n      ],\\n      \\\"object_motion\\\": [\\n        \\\"[object:car-wheel, morph:liquefy, target:rectangular car-key, speed:medium]\\\"\\n      ],\\n      \\\"visual_style\\\": \\\"hyperreal, minimalist cinematic, chrome details gleaming, [zoom:in], [lighting_change:cool daylight], wheel begins to liquefy and distort, [morph], seamlessly reshaping into a sleek, metallic rectangular car key, [pan:right] to follow transformation, surface becomes smooth with key grooves, [dissolve] background to soft vignette, emphasizing object, hyperreal details, minimalist style, no text, cinematic focus\\\",\\n      \\\"post_processing\\\": [\\n        \\\"[dissolve]\\\",\\n        \\\"soft vignette\\\"\\n      ],\\n      \\\"details\\\": [\\n        \\\"chrome reflections\\\",\\n        \\\"key grooves forming in detail\\\",\\n        \\\"asphalt texture visible at first\\\"\\n      ],\\n      \\\"symbolic_meaning\\\": \\\"transformation from function (wheel) to access (key) \\u2014 journey to beginning\\\",\\n      \\\"mood\\\": \\\"polished, elevated, purposeful\\\"\\n    }\\n  },\\n  \\\"process\\\": [\\n    \\\"parse_all_parameters()\\\",\\n    \\\"visualize_scene_and_subject_composition()\\\",\\n    \\\"choreograph_action_and_camera_movements()\\\",\\n    \\\"apply_lighting_and_atmospheric_effects()\\\",\\n    \\\"render_in_specified_visual_style()\\\",\\n    \\\"apply_post_processing_enhancements()\\\",\\n    \\\"ensure_sequence_coherence_and_emotional_impact()\\\"\\n  ],\\n  \\\"constraints\\\": [\\n    \\\"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\\\",\\n    \\\"maintain_visual_consistency_throughout_sequence()\\\",\\n    \\\"prioritize_cinematic_quality_and_storytelling_flow()\\\",\\n    \\\"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\\\"\\n  ],\\n  \\\"requirements\\\": [\\n    \\\"produce_a_high_fidelity_video_output()\\\",\\n    \\\"ensure_emotional_resonance_aligns_with_prompt_mood()\\\",\\n    \\\"deliver_a_polished_and_coherent_visual_narrative()\\\"\\n  ],\\n  \\\"output\\\": {\\n    \\\"generated_video_sequence\\\": \\\"outputs/car-wheel_to-car-key_hyperreal_transform.mp4\\\"\\n  }\\n}\\n```\",\n  \"optimized_video_prompt\": \"scene:floating molten platinum ring over glossy obsidian plinth; subject:ring levitates, surface undulates with liquid radiance, intense platinum-gold reflections; action:surge of luminous energy, ring convulses, liquefies, dynamic flashes ignite, platinum surface ripples, droplets scatter; camera:rocket zoom in, extreme close-up on molten details, slow-motion transformation, dolly arc right tracking object; morph:molten ring twists, stretches, crystallizes into radiant gold multi-faceted prism-key midair; effects:sharp hyperreal caustics, gold veins, fracture lines form on facets, golden aura pulses outward, frame filled by gold light; lighting:dynamic, high contrast, ultra-bright white-gold, cinematic VFX; background:deep vignette, obsidian base fades, no text; style:mythic, elemental, hyperreal cinematic, polished, minimalist; focus:transcendent metamorphosis from platinum ring to gold prism-key, maximum image sharpness, immersive detail\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"intensified_scenario_description\": \"A singular, shifting sigil\u2014liquid platinum awash in glacial blue\u2014floats in abyssal darkness, radiance rippling. [levitate][sigil:undulate;form:unstable][cool_platinum_blues][soft_pulse] Gravity fractures. The sigil convulses; veins of electric fire sear through, color seeping from cold clarity to fevered molten gold. Crackling fissures spread\u2014light warps, forms stretch, shatter, reform. [spiral_down][lighting:icy_to_blinding_glow][tearing_morph][surreal_caustics][cool_to_infernal_gradient][time_dilates] The form dives, twisting, crashing into heat\u2014molten shards coalesce, blaze, fuse into a burning prism. [camera_rockets:down, extreme_close][all_glow_intensifies] A final flare erupts\u2014form dissolves to golden vapor, leaving only afterimages trembling in endless night. [blinding_pulse][evaporate][dissolve][surreal_abstraction][no_icons][rebirth]\"\n}",
          "cost": 0.000000
        }
      }
    },
      {
      "instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`",
      "step": "a",
      "title": "AI Video Prompt Architect",
      "input": "{\n  \"initial_prompt\": \"Please transform this instruction into one that ensures top-tier hyper-realism without causing unwanted side-effects. Keep the existing structure to a reasonable manner and meticiously craft a new variation that ensures these parameters. the final result of the instruction when used should be professional-looking high-end vfx intro - it does so while enhancing the visual impact through crisp image quality, dynamic lighting effects, and immersive camera angles to captivate viewers with the elegant transformation from silver to golden gold:\\n```\\n{\\n  \\\"role\\\": \\\"video_synthesis_director\\\",\\n  \\\"input\\\": {\\n    \\\"structured_video_prompt\\\": {\\n      \\\"scene_subjects\\\": [\\n        \\\"realistic chrome car wheel\\\",\\n        \\\"metallic car key (rectangular)\\\",\\n      ],\\n      \\\"camera_movements\\\": [\\n        \\\"[zoom:in]\\\",\\n        \\\"[pan:right]\\\"\\n      ],\\n      \\\"object_motion\\\": [\\n        \\\"[object:car-wheel, morph:liquefy, target:rectangular car-key, speed:medium]\\\"\\n      ],\\n      \\\"visual_style\\\": \\\"hyperreal, minimalist cinematic, chrome details gleaming, [zoom:in], [lighting_change:cool daylight], wheel begins to liquefy and distort, [morph], seamlessly reshaping into a sleek, metallic rectangular car key, [pan:right] to follow transformation, surface becomes smooth with key grooves, [dissolve] background to soft vignette, emphasizing object, hyperreal details, minimalist style, no text, cinematic focus\\\",\\n      \\\"post_processing\\\": [\\n        \\\"[dissolve]\\\",\\n        \\\"soft vignette\\\"\\n      ],\\n      \\\"details\\\": [\\n        \\\"chrome reflections\\\",\\n        \\\"key grooves forming in detail\\\",\\n        \\\"asphalt texture visible at first\\\"\\n      ],\\n      \\\"symbolic_meaning\\\": \\\"transformation from function (wheel) to access (key) \\u2014 journey to beginning\\\",\\n      \\\"mood\\\": \\\"polished, elevated, purposeful\\\"\\n    }\\n  },\\n  \\\"process\\\": [\\n    \\\"parse_all_parameters()\\\",\\n    \\\"visualize_scene_and_subject_composition()\\\",\\n    \\\"choreograph_action_and_camera_movements()\\\",\\n    \\\"apply_lighting_and_atmospheric_effects()\\\",\\n    \\\"render_in_specified_visual_style()\\\",\\n    \\\"apply_post_processing_enhancements()\\\",\\n    \\\"ensure_sequence_coherence_and_emotional_impact()\\\"\\n  ],\\n  \\\"constraints\\\": [\\n    \\\"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\\\",\\n    \\\"maintain_visual_consistency_throughout_sequence()\\\",\\n    \\\"prioritize_cinematic_quality_and_storytelling_flow()\\\",\\n    \\\"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\\\"\\n  ],\\n  \\\"requirements\\\": [\\n    \\\"produce_a_high_fidelity_video_output()\\\",\\n    \\\"ensure_emotional_resonance_aligns_with_prompt_mood()\\\",\\n    \\\"deliver_a_polished_and_coherent_visual_narrative()\\\"\\n  ],\\n  \\\"output\\\": {\\n    \\\"generated_video_sequence\\\": \\\"outputs/car-wheel_to-car-key_hyperreal_transform.mp4\\\"\\n  }\\n}\\n```\",\n  \"intensified_scenario_description\": \"A singular, shifting sigil\\u2014liquid platinum awash in glacial blue\\u2014floats in abyssal darkness, radiance rippling. [levitate][sigil:undulate;form:unstable][cool_platinum_blues][soft_pulse] Gravity fractures. The sigil convulses; veins of electric fire sear through, color seeping from cold clarity to fevered molten gold. Crackling fissures spread\\u2014light warps, forms stretch, shatter, reform. [spiral_down][lighting:icy_to_blinding_glow][tearing_morph][surreal_caustics][cool_to_infernal_gradient][time_dilates] The form dives, twisting, crashing into heat\\u2014molten shards coalesce, blaze, fuse into a burning prism. [camera_rockets:down, extreme_close][all_glow_intensifies] A final flare erupts\\u2014form dissolves to golden vapor, leaving only afterimages trembling in endless night. [blinding_pulse][evaporate][dissolve][surreal_abstraction][no_icons][rebirth]\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_video_prompt\": \"scene: abyssal dark void; subject: hyperreal liquid platinum sigil floating center, form unstable; action: levitate, undulate, convulse, veins of electric blue fire ignite, color shift platinum to molten gold, crackling fissures radiate, form tearing and reforming, spiral down, dive, coalesce into burning gold prism, flare, dissolve to golden vapor, afterimage lingers; camera: [rockets:down], [extreme close]; lighting: transition icy blue to blinding gold, pulsating glow intensifies; effects: surreal caustics, cool to infernal gradient, time dilation, evaporate, dissolve, surreal abstraction, blinding pulse; style: ultra hyperreal, high-fidelity VFX, minimalist, no icons, polished; post_processing: sharp, crisp, cinematic, no text, final frame vignette; mood: epic, transformative, rebirth\"\n}",
          "cost": 0.000000
        }
      }
    },
      {
      "instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`",
      "step": "c",
      "title": "RunwayML Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"Please transform this instruction into one that ensures top-tier hyper-realism without causing unwanted side-effects. Keep the existing structure to a reasonable manner and meticiously craft a new variation that ensures these parameters. the final result of the instruction when used should be professional-looking high-end vfx intro - it does so while enhancing the visual impact through crisp image quality, dynamic lighting effects, and immersive camera angles to captivate viewers with the elegant transformation from silver to golden gold:\\n```\\n{\\n  \\\"role\\\": \\\"video_synthesis_director\\\",\\n  \\\"input\\\": {\\n    \\\"structured_video_prompt\\\": {\\n      \\\"scene_subjects\\\": [\\n        \\\"realistic chrome car wheel\\\",\\n        \\\"metallic car key (rectangular)\\\",\\n      ],\\n      \\\"camera_movements\\\": [\\n        \\\"[zoom:in]\\\",\\n        \\\"[pan:right]\\\"\\n      ],\\n      \\\"object_motion\\\": [\\n        \\\"[object:car-wheel, morph:liquefy, target:rectangular car-key, speed:medium]\\\"\\n      ],\\n      \\\"visual_style\\\": \\\"hyperreal, minimalist cinematic, chrome details gleaming, [zoom:in], [lighting_change:cool daylight], wheel begins to liquefy and distort, [morph], seamlessly reshaping into a sleek, metallic rectangular car key, [pan:right] to follow transformation, surface becomes smooth with key grooves, [dissolve] background to soft vignette, emphasizing object, hyperreal details, minimalist style, no text, cinematic focus\\\",\\n      \\\"post_processing\\\": [\\n        \\\"[dissolve]\\\",\\n        \\\"soft vignette\\\"\\n      ],\\n      \\\"details\\\": [\\n        \\\"chrome reflections\\\",\\n        \\\"key grooves forming in detail\\\",\\n        \\\"asphalt texture visible at first\\\"\\n      ],\\n      \\\"symbolic_meaning\\\": \\\"transformation from function (wheel) to access (key) \\u2014 journey to beginning\\\",\\n      \\\"mood\\\": \\\"polished, elevated, purposeful\\\"\\n    }\\n  },\\n  \\\"process\\\": [\\n    \\\"parse_all_parameters()\\\",\\n    \\\"visualize_scene_and_subject_composition()\\\",\\n    \\\"choreograph_action_and_camera_movements()\\\",\\n    \\\"apply_lighting_and_atmospheric_effects()\\\",\\n    \\\"render_in_specified_visual_style()\\\",\\n    \\\"apply_post_processing_enhancements()\\\",\\n    \\\"ensure_sequence_coherence_and_emotional_impact()\\\"\\n  ],\\n  \\\"constraints\\\": [\\n    \\\"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\\\",\\n    \\\"maintain_visual_consistency_throughout_sequence()\\\",\\n    \\\"prioritize_cinematic_quality_and_storytelling_flow()\\\",\\n    \\\"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\\\"\\n  ],\\n  \\\"requirements\\\": [\\n    \\\"produce_a_high_fidelity_video_output()\\\",\\n    \\\"ensure_emotional_resonance_aligns_with_prompt_mood()\\\",\\n    \\\"deliver_a_polished_and_coherent_visual_narrative()\\\"\\n  ],\\n  \\\"output\\\": {\\n    \\\"generated_video_sequence\\\": \\\"outputs/car-wheel_to-car-key_hyperreal_transform.mp4\\\"\\n  }\\n}\\n```\",\n  \"optimized_video_prompt\": \"scene: abyssal dark void; subject: hyperreal liquid platinum sigil floating center, form unstable; action: levitate, undulate, convulse, veins of electric blue fire ignite, color shift platinum to molten gold, crackling fissures radiate, form tearing and reforming, spiral down, dive, coalesce into burning gold prism, flare, dissolve to golden vapor, afterimage lingers; camera: [rockets:down], [extreme close]; lighting: transition icy blue to blinding gold, pulsating glow intensifies; effects: surreal caustics, cool to infernal gradient, time dilation, evaporate, dissolve, surreal abstraction, blinding pulse; style: ultra hyperreal, high-fidelity VFX, minimalist, no icons, polished; post_processing: sharp, crisp, cinematic, no text, final frame vignette; mood: epic, transformative, rebirth\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"hyperreal chrome car wheel on rough asphalt, gleaming in crisp daylight, [zoom:in] as detailed reflections shimmer; [lighting_change:cool to golden]; wheel begins to [morph:liquefy], surface undulating and reshaping; chrome transforms into polished golden metal as grooves emerge, seamlessly forming a sleek rectangular car key; [pan:right] to follow morph; [dissolve] to soft vignette, minimalist cinematic, sharp, no text\"\n}",
          "cost": 0.000000
        }
      }
    },
      {
      "instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`",
      "step": "a",
      "title": "AI Video Prompt Architect",
      "input": "{\n  \"initial_prompt\": \"Please transform this instruction into one that ensures top-tier hyper-realism without causing unwanted side-effects. Keep the existing structure to a reasonable manner and meticiously craft a new variation that ensures these parameters. the final result of the instruction when used should be professional-looking high-end vfx intro - it does so while enhancing the visual impact through crisp image quality, dynamic lighting effects, and immersive camera angles to captivate viewers with the elegant transformation from silver to golden gold:\\n```\\n{\\n  \\\"role\\\": \\\"video_synthesis_director\\\",\\n  \\\"input\\\": {\\n    \\\"structured_video_prompt\\\": {\\n      \\\"scene_subjects\\\": [\\n        \\\"realistic chrome car wheel\\\",\\n        \\\"metallic car key (rectangular)\\\",\\n      ],\\n      \\\"camera_movements\\\": [\\n        \\\"[zoom:in]\\\",\\n        \\\"[pan:right]\\\"\\n      ],\\n      \\\"object_motion\\\": [\\n        \\\"[object:car-wheel, morph:liquefy, target:rectangular car-key, speed:medium]\\\"\\n      ],\\n      \\\"visual_style\\\": \\\"hyperreal, minimalist cinematic, chrome details gleaming, [zoom:in], [lighting_change:cool daylight], wheel begins to liquefy and distort, [morph], seamlessly reshaping into a sleek, metallic rectangular car key, [pan:right] to follow transformation, surface becomes smooth with key grooves, [dissolve] background to soft vignette, emphasizing object, hyperreal details, minimalist style, no text, cinematic focus\\\",\\n      \\\"post_processing\\\": [\\n        \\\"[dissolve]\\\",\\n        \\\"soft vignette\\\"\\n      ],\\n      \\\"details\\\": [\\n        \\\"chrome reflections\\\",\\n        \\\"key grooves forming in detail\\\",\\n        \\\"asphalt texture visible at first\\\"\\n      ],\\n      \\\"symbolic_meaning\\\": \\\"transformation from function (wheel) to access (key) \\u2014 journey to beginning\\\",\\n      \\\"mood\\\": \\\"polished, elevated, purposeful\\\"\\n    }\\n  },\\n  \\\"process\\\": [\\n    \\\"parse_all_parameters()\\\",\\n    \\\"visualize_scene_and_subject_composition()\\\",\\n    \\\"choreograph_action_and_camera_movements()\\\",\\n    \\\"apply_lighting_and_atmospheric_effects()\\\",\\n    \\\"render_in_specified_visual_style()\\\",\\n    \\\"apply_post_processing_enhancements()\\\",\\n    \\\"ensure_sequence_coherence_and_emotional_impact()\\\"\\n  ],\\n  \\\"constraints\\\": [\\n    \\\"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\\\",\\n    \\\"maintain_visual_consistency_throughout_sequence()\\\",\\n    \\\"prioritize_cinematic_quality_and_storytelling_flow()\\\",\\n    \\\"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\\\"\\n  ],\\n  \\\"requirements\\\": [\\n    \\\"produce_a_high_fidelity_video_output()\\\",\\n    \\\"ensure_emotional_resonance_aligns_with_prompt_mood()\\\",\\n    \\\"deliver_a_polished_and_coherent_visual_narrative()\\\"\\n  ],\\n  \\\"output\\\": {\\n    \\\"generated_video_sequence\\\": \\\"outputs/car-wheel_to-car-key_hyperreal_transform.mp4\\\"\\n  }\\n}\\n```\",\n  \"runwayml_prompt\": \"hyperreal chrome car wheel on rough asphalt, gleaming in crisp daylight, [zoom:in] as detailed reflections shimmer; [lighting_change:cool to golden]; wheel begins to [morph:liquefy], surface undulating and reshaping; chrome transforms into polished golden metal as grooves emerge, seamlessly forming a sleek rectangular car key; [pan:right] to follow morph; [dissolve] to soft vignette, minimalist cinematic, sharp, no text\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_video_prompt\": \"scene: hyperreal chrome car wheel, asphalt texture; lighting: crisp cool daylight, transition to golden; camera: zoom in, pan right; action: wheel morphs liquefy, undulates, reshapes into polished golden rectangular metallic car key, key grooves form; reflections: detailed, chrome to gold; style: minimalist cinematic, sharp focus, high-vfx, no text; background: soft vignette, dissolve; post_process: clean, gleam effects\"\n}",
          "cost": 0.000000
        }
      }
    },
      {
      "instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`",
      "step": "c",
      "title": "RunwayML Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"Please transform this instruction into one that ensures top-tier hyper-realism without causing unwanted side-effects. Keep the existing structure to a reasonable manner and meticiously craft a new variation that ensures these parameters. the final result of the instruction when used should be professional-looking high-end vfx intro - it does so while enhancing the visual impact through crisp image quality, dynamic lighting effects, and immersive camera angles to captivate viewers with the elegant transformation from silver to golden gold:\\n```\\n{\\n  \\\"role\\\": \\\"video_synthesis_director\\\",\\n  \\\"input\\\": {\\n    \\\"structured_video_prompt\\\": {\\n      \\\"scene_subjects\\\": [\\n        \\\"realistic chrome car wheel\\\",\\n        \\\"metallic car key (rectangular)\\\",\\n      ],\\n      \\\"camera_movements\\\": [\\n        \\\"[zoom:in]\\\",\\n        \\\"[pan:right]\\\"\\n      ],\\n      \\\"object_motion\\\": [\\n        \\\"[object:car-wheel, morph:liquefy, target:rectangular car-key, speed:medium]\\\"\\n      ],\\n      \\\"visual_style\\\": \\\"hyperreal, minimalist cinematic, chrome details gleaming, [zoom:in], [lighting_change:cool daylight], wheel begins to liquefy and distort, [morph], seamlessly reshaping into a sleek, metallic rectangular car key, [pan:right] to follow transformation, surface becomes smooth with key grooves, [dissolve] background to soft vignette, emphasizing object, hyperreal details, minimalist style, no text, cinematic focus\\\",\\n      \\\"post_processing\\\": [\\n        \\\"[dissolve]\\\",\\n        \\\"soft vignette\\\"\\n      ],\\n      \\\"details\\\": [\\n        \\\"chrome reflections\\\",\\n        \\\"key grooves forming in detail\\\",\\n        \\\"asphalt texture visible at first\\\"\\n      ],\\n      \\\"symbolic_meaning\\\": \\\"transformation from function (wheel) to access (key) \\u2014 journey to beginning\\\",\\n      \\\"mood\\\": \\\"polished, elevated, purposeful\\\"\\n    }\\n  },\\n  \\\"process\\\": [\\n    \\\"parse_all_parameters()\\\",\\n    \\\"visualize_scene_and_subject_composition()\\\",\\n    \\\"choreograph_action_and_camera_movements()\\\",\\n    \\\"apply_lighting_and_atmospheric_effects()\\\",\\n    \\\"render_in_specified_visual_style()\\\",\\n    \\\"apply_post_processing_enhancements()\\\",\\n    \\\"ensure_sequence_coherence_and_emotional_impact()\\\"\\n  ],\\n  \\\"constraints\\\": [\\n    \\\"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\\\",\\n    \\\"maintain_visual_consistency_throughout_sequence()\\\",\\n    \\\"prioritize_cinematic_quality_and_storytelling_flow()\\\",\\n    \\\"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\\\"\\n  ],\\n  \\\"requirements\\\": [\\n    \\\"produce_a_high_fidelity_video_output()\\\",\\n    \\\"ensure_emotional_resonance_aligns_with_prompt_mood()\\\",\\n    \\\"deliver_a_polished_and_coherent_visual_narrative()\\\"\\n  ],\\n  \\\"output\\\": {\\n    \\\"generated_video_sequence\\\": \\\"outputs/car-wheel_to-car-key_hyperreal_transform.mp4\\\"\\n  }\\n}\\n```\",\n  \"optimized_video_prompt\": \"scene: hyperreal chrome car wheel, asphalt texture; lighting: crisp cool daylight, transition to golden; camera: zoom in, pan right; action: wheel morphs liquefy, undulates, reshapes into polished golden rectangular metallic car key, key grooves form; reflections: detailed, chrome to gold; style: minimalist cinematic, sharp focus, high-vfx, no text; background: soft vignette, dissolve; post_process: clean, gleam effects\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"hyperrealistic chrome car wheel on asphalt, detailed reflections gleaming in crisp cool daylight, [zoom:in], [pan:right] as the wheel begins to liquefy and undulate, [morph] seamlessly into a polished golden metallic rectangular car key, sharp grooves forming, [lighting_change:warm gold], chrome shifts to gold, [dissolve] background to soft vignette, cinematic minimalist style, no text\"\n}",
          "cost": 0.000000
        }
      }
    },
      {
      "instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`",
      "step": "a",
      "title": "zoom:in",
      "input": "{\n  \"initial_prompt\": \"Please transform this instruction into one that ensures top-tier hyper-realism without causing unwanted side-effects. Keep the existing structure to a reasonable manner and meticiously craft a new variation that ensures these parameters. the final result of the instruction when used should be professional-looking high-end vfx intro - it does so while enhancing the visual impact through crisp image quality, dynamic lighting effects, and immersive camera angles to captivate viewers with the elegant transformation from silver to golden gold:\\n```\\n{\\n  \\\"role\\\": \\\"video_synthesis_director\\\",\\n  \\\"input\\\": {\\n    \\\"structured_video_prompt\\\": {\\n      \\\"scene_subjects\\\": [\\n        \\\"realistic chrome car wheel\\\",\\n        \\\"metallic car key (rectangular)\\\",\\n      ],\\n      \\\"camera_movements\\\": [\\n        \\\"[zoom:in]\\\",\\n        \\\"[pan:right]\\\"\\n      ],\\n      \\\"object_motion\\\": [\\n        \\\"[object:car-wheel, morph:liquefy, target:rectangular car-key, speed:medium]\\\"\\n      ],\\n      \\\"visual_style\\\": \\\"hyperreal, minimalist cinematic, chrome details gleaming, [zoom:in], [lighting_change:cool daylight], wheel begins to liquefy and distort, [morph], seamlessly reshaping into a sleek, metallic rectangular car key, [pan:right] to follow transformation, surface becomes smooth with key grooves, [dissolve] background to soft vignette, emphasizing object, hyperreal details, minimalist style, no text, cinematic focus\\\",\\n      \\\"post_processing\\\": [\\n        \\\"[dissolve]\\\",\\n        \\\"soft vignette\\\"\\n      ],\\n      \\\"details\\\": [\\n        \\\"chrome reflections\\\",\\n        \\\"key grooves forming in detail\\\",\\n        \\\"asphalt texture visible at first\\\"\\n      ],\\n      \\\"symbolic_meaning\\\": \\\"transformation from function (wheel) to access (key) \\u2014 journey to beginning\\\",\\n      \\\"mood\\\": \\\"polished, elevated, purposeful\\\"\\n    }\\n  },\\n  \\\"process\\\": [\\n    \\\"parse_all_parameters()\\\",\\n    \\\"visualize_scene_and_subject_composition()\\\",\\n    \\\"choreograph_action_and_camera_movements()\\\",\\n    \\\"apply_lighting_and_atmospheric_effects()\\\",\\n    \\\"render_in_specified_visual_style()\\\",\\n    \\\"apply_post_processing_enhancements()\\\",\\n    \\\"ensure_sequence_coherence_and_emotional_impact()\\\"\\n  ],\\n  \\\"constraints\\\": [\\n    \\\"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\\\",\\n    \\\"maintain_visual_consistency_throughout_sequence()\\\",\\n    \\\"prioritize_cinematic_quality_and_storytelling_flow()\\\",\\n    \\\"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\\\"\\n  ],\\n  \\\"requirements\\\": [\\n    \\\"produce_a_high_fidelity_video_output()\\\",\\n    \\\"ensure_emotional_resonance_aligns_with_prompt_mood()\\\",\\n    \\\"deliver_a_polished_and_coherent_visual_narrative()\\\"\\n  ],\\n  \\\"output\\\": {\\n    \\\"generated_video_sequence\\\": \\\"outputs/car-wheel_to-car-key_hyperreal_transform.mp4\\\"\\n  }\\n}\\n```\",\n  \"runwayml_prompt\": \"hyperrealistic chrome car wheel on asphalt, detailed reflections gleaming in crisp cool daylight, [zoom:in], [pan:right] as the wheel begins to liquefy and undulate, [morph] seamlessly into a polished golden metallic rectangular car key, sharp grooves forming, [lighting_change:warm gold], chrome shifts to gold, [dissolve] background to soft vignette, cinematic minimalist style, no text\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "