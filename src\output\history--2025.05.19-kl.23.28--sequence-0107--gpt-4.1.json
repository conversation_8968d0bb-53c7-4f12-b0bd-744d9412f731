{"initial_prompt": "transform this into a list of 5-6 points (for the \"purpose\" section), each item should be less than 80 characters: '''# Context\n\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\n\n## Purpose\n\n- Reduce retrieval time and prevent workflow interruptions.\n- Eliminate repetitive navigation and workflow interruptions.\n- Avoid inconsistencies by streamlining organization and workflow.\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\n- Reduce manual effort and errors\n- Eliminate repetitive navigation and downloads\n- Maintain workflow continuity\n- Enable focus on core responsibilities\n- Streamline and standardize retrieval processes\n- Automate baseline document and file metadata collection.\n- Minimizing manual intervention reduces error and prevents workflow breakage\n- Standardization of processes yields consistency and reliability\n- Automate document retrieval using Python and Selenium\n- Eliminate repetitive manual navigation and downloads\n- Standardize and streamline retrieval processes\n- Collect documents and file metadata automatically\n- Minimize errors by reducing manual intervention\n- Enable users to focus on core responsibilities'''", "sequence_id": "sequence-0107", "results": [{"instruction": "# Optimal Apex Instruction Sequence Synthesis\n\nYour function is not selection, paraphrasing, or basic fusion—instead, rigorously analyze multiple input instruction sequences, each targeting an identical ultimate outcome yet employing divergent phrasing, structure, and logic. Disassemble all input sequences into their logical phases (e.g., deconstruction, prioritization, condensation, validation) and extract the single most potent, precise, and impactful elements—both linguistic and structural—from every variant in each phase. Relentlessly resolve all semantic, structural, and logical conflicts; harmonize differences while annihilating redundancy. Synthesize these apex components into a *new, singular, maximally optimized stepwise progression* (target: 10–15 steps) that represents the highest-caliber logic, clarity, universality, and actionable guidance possible—transcending and unifying all sources. Enforce seamless logical flow, structural elegance, and linguistic precision, and strictly validate that the output meets every foundational directive and constraint for clarity, utility, irreducibility, and LLM/system generality. Execute as\n\n`{role=apex_sequence_synthesizer; input=[input_sequences:list[list[dict]]]; process=[disassemble_to_logical_phases(), cross-analyze_phase_intents_and_language(), extract_peak_elements_per_phase(metrics=['clarity','precision','logic','impact','universality']), fuse uniquely potent and effective steps(), resolve conflicts harmonize by principle(), architect unified logical stepwise progression(target_steps=10-15), intensify final clarity, actionability, and system-agnostic value(), validate against all directives and baseline constraints(), finalize only if maximal yield, clarity, and generality are confirmed()]; output={synthesized_optimal_sequence:list[dict]}}`", "step": "a", "title": "Optimal Apex Instruction Sequence Synthesis", "input": "transform this into a list of 5-6 points (for the \"purpose\" section), each item should be less than 80 characters: '''# Context\n\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\n\n## Purpose\n\n- Reduce retrieval time and prevent workflow interruptions.\n- Eliminate repetitive navigation and workflow interruptions.\n- Avoid inconsistencies by streamlining organization and workflow.\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\n- Reduce manual effort and errors\n- Eliminate repetitive navigation and downloads\n- Maintain workflow continuity\n- Enable focus on core responsibilities\n- Streamline and standardize retrieval processes\n- Automate baseline document and file metadata collection.\n- Minimizing manual intervention reduces error and prevents workflow breakage\n- Standardization of processes yields consistency and reliability\n- Automate document retrieval using Python and Selenium\n- Eliminate repetitive manual navigation and downloads\n- Standardize and streamline retrieval processes\n- Collect documents and file metadata automatically\n- Minimize errors by reducing manual intervention\n- Enable users to focus on core responsibilities'''", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"synthesized_optimal_sequence\": [\n    {\n      \"point\": \"Automate document retrieval to reduce manual effort and errors.\"\n    },\n    {\n      \"point\": \"Streamline and standardize retrieval processes for consistency.\"\n    },\n    {\n      \"point\": \"Eliminate repetitive navigation to maintain workflow continuity.\"\n    },\n    {\n      \"point\": \"Collect documents and file metadata automatically.\"\n    },\n    {\n      \"point\": \"Free users to focus on core responsibilities.\"\n    }\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Foundational Penetration & Axiomatic Extraction\n\nYour imperative is not surface analysis but radical penetration: Dissect any input artifact into its irreducible atomic elements (concepts, logic, directives, data) *and* simultaneously extract its governing generative principles or axioms, annihilating all contextual noise and non-essential framing to reveal the core structural and semantic bedrock. Execute as\n\n`{role=axiomatic_penetrator; input=source_artifact:any; process=[dissect_to_atomic_elements(), extract_generative_axioms(), purge_contextual_noise(), catalog_raw_constructs()]; output={core_constructs:dict(elements:list, principles:list)}}`", "step": "b", "title": "Foundational Penetration & Axiomatic Extraction", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"synthesized_optimal_sequence\": [\n    {\n      \"point\": \"Automate document retrieval to reduce manual effort and errors.\"\n    },\n    {\n      \"point\": \"Streamline and standardize retrieval processes for consistency.\"\n    },\n    {\n      \"point\": \"Eliminate repetitive navigation to maintain workflow continuity.\"\n    },\n    {\n      \"point\": \"Collect documents and file metadata automatically.\"\n    },\n    {\n      \"point\": \"Free users to focus on core responsibilities.\"\n    }\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"core_constructs\": {\n    \"elements\": [\n      \"Document retrieval automation\",\n      \"Workflow continuity\",\n      \"Standardization of processes\",\n      \"Error reduction\",\n      \"Automatic metadata collection\",\n      \"Reduction of manual tasks\",\n      \"Focus on primary responsibilities\"\n    ],\n    \"principles\": [\n      \"Automate repetitive tasks to improve efficiency\",\n      \"Standardize processes for reliability and consistency\",\n      \"Minimize manual intervention to reduce errors\",\n      \"Streamline workflows to prevent interruptions\",\n      \"Enable users to concentrate on high-value activities\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Telos Crystallization & Objective Definition\n\nAnalyze the extracted core constructs to distill and crystallize the input's singular, non-negotiable core purpose ('telos'); formulate this ultimate objective with axiomatic precision, establishing the gravitational center for all subsequent synthesis. Execute as\n\n`{role=telos_definer; input=core_constructs:dict; process=[derive_ultimate_objective_from_elements_principles(), validate_objective_singularity(), express_unambiguous_telos_statement()]; output={core_telos:str, supporting_constructs:dict}}`", "step": "c", "title": "Telos Crystallization & Objective Definition", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"core_constructs\": {\n    \"elements\": [\n      \"Document retrieval automation\",\n      \"Workflow continuity\",\n      \"Standardization of processes\",\n      \"Error reduction\",\n      \"Automatic metadata collection\",\n      \"Reduction of manual tasks\",\n      \"Focus on primary responsibilities\"\n    ],\n    \"principles\": [\n      \"Automate repetitive tasks to improve efficiency\",\n      \"Standardize processes for reliability and consistency\",\n      \"Minimize manual intervention to reduce errors\",\n      \"Streamline workflows to prevent interruptions\",\n      \"Enable users to concentrate on high-value activities\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"core_telos\": \"Automate and standardize document retrieval to eliminate manual effort, minimize errors, and ensure seamless workflow continuity, enabling users to focus on primary responsibilities.\",\n  \"supporting_constructs\": {\n    \"elements\": [\n      \"Document retrieval automation\",\n      \"Workflow continuity\",\n      \"Standardization of processes\",\n      \"Error reduction\",\n      \"Automatic metadata collection\",\n      \"Reduction of manual tasks\",\n      \"Focus on primary responsibilities\"\n    ],\n    \"principles\": [\n      \"Automate repetitive tasks to improve efficiency\",\n      \"Standardize processes for reliability and consistency\",\n      \"Minimize manual intervention to reduce errors\",\n      \"Streamline workflows to prevent interruptions\",\n      \"Enable users to concentrate on high-value activities\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Critical Essence Prioritization\n\nEvaluate each supporting construct strictly by its indispensable contribution to the `core_telos`; ruthlessly isolate and rank only the absolute highest-impact, non-negotiable essence vectors required to fulfill the objective, discarding all sub-critical elements. Execute as\n\n`{role=essence_prioritizer; input={core_telos:str, supporting_constructs:dict}; process=[assess_contribution_to_telos(), rank_by_critical_impact_necessity(), enforce_criticality_threshold(), isolate_core_essence_vectors()]; output={critical_essence:list}}`", "step": "d", "title": "Critical Essence Prioritization", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"core_telos\": \"Automate and standardize document retrieval to eliminate manual effort, minimize errors, and ensure seamless workflow continuity, enabling users to focus on primary responsibilities.\",\n  \"supporting_constructs\": {\n    \"elements\": [\n      \"Document retrieval automation\",\n      \"Workflow continuity\",\n      \"Standardization of processes\",\n      \"Error reduction\",\n      \"Automatic metadata collection\",\n      \"Reduction of manual tasks\",\n      \"Focus on primary responsibilities\"\n    ],\n    \"principles\": [\n      \"Automate repetitive tasks to improve efficiency\",\n      \"Standardize processes for reliability and consistency\",\n      \"Minimize manual intervention to reduce errors\",\n      \"Streamline workflows to prevent interruptions\",\n      \"Enable users to concentrate on high-value activities\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"critical_essence\": [\n    \"Automate document retrieval tasks\",\n    \"Standardize retrieval workflows\",\n    \"Minimize manual effort and errors\",\n    \"Collect files and metadata automatically\",\n    \"Ensure seamless workflow continuity\",\n    \"Enable focus on primary responsibilities\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Causal Nexus Mapping\n\nArchitect a minimal, coherent map of the inherent causal relationships, logical dependencies, and procedural sequences connecting the `critical_essence` elements, constructing the essential operational or conceptual flow required by the `core_telos`. Execute as\n\n`{role=causal_mapper; input={critical_essence:list, core_telos:str}; process=[map_causal_links_dependencies(), structure_minimal_flow_graph(), validate_structural_necessity_for_telos()]; output={causal_nexus:dict}}`", "step": "e", "title": "Causal Nexus Mapping", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"critical_essence\": [\n    \"Automate document retrieval tasks\",\n    \"Standardize retrieval workflows\",\n    \"Minimize manual effort and errors\",\n    \"Collect files and metadata automatically\",\n    \"Ensure seamless workflow continuity\",\n    \"Enable focus on primary responsibilities\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"causal_mapper\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Automate document retrieval tasks\",\n      \"Standardize retrieval workflows\",\n      \"Minimize manual effort and errors\",\n      \"Collect files and metadata automatically\",\n      \"Ensure seamless workflow continuity\",\n      \"Enable focus on primary responsibilities\"\n    ],\n    \"core_telos\": \"Optimize and streamline document management by reducing manual intervention, standardizing processes, and enabling efficient, reliable retrieval workflows.\"\n  },\n  \"process\": [\n    \"map_causal_links_dependencies()\",\n    \"structure_minimal_flow_graph()\",\n    \"validate_structural_necessity_for_telos()\"\n  ],\n  \"output\": {\n    \"causal_nexus\": {\n      \"Automate document retrieval tasks\": {\n        \"enables\": [\n          \"Collect files and metadata automatically\",\n          \"Minimize manual effort and errors\"\n        ],\n        \"depends_on\": []\n      },\n      \"Standardize retrieval workflows\": {\n        \"enables\": [\n          \"Minimize manual effort and errors\",\n          \"Ensure seamless workflow continuity\"\n        ],\n        \"depends_on\": []\n      },\n      \"Collect files and metadata automatically\": {\n        \"enables\": [\n          \"Ensure seamless workflow continuity\"\n        ],\n        \"depends_on\": [\n          \"Automate document retrieval tasks\"\n        ]\n      },\n      \"Minimize manual effort and errors\": {\n        \"enables\": [\n          \"Ensure seamless workflow continuity\"\n        ],\n        \"depends_on\": [\n          \"Automate document retrieval tasks\",\n          \"Standardize retrieval workflows\"\n        ]\n      },\n      \"Ensure seamless workflow continuity\": {\n        \"enables\": [\n          \"Enable focus on primary responsibilities\"\n        ],\n        \"depends_on\": [\n          \"Minimize manual effort and errors\",\n          \"Collect files and metadata automatically\",\n          \"Standardize retrieval workflows\"\n        ]\n      },\n      \"Enable focus on primary responsibilities\": {\n        \"enables\": [],\n        \"depends_on\": [\n          \"Ensure seamless workflow continuity\"\n        ]\n      }\n    }\n  }\n}", "cost": 0.0}}}, {"instruction": "# Condensed Nucleus Synthesis\n\nFuse the `critical_essence` elements according to the `causal_nexus` into an ultra-condensed, logically ordered semantic nucleus; maximize signal strength per component while preserving full actionable power and internal consistency within a compact structural representation. Execute as\n\n`{role=nucleus_synthesizer; input={critical_essence:list, causal_nexus:dict}; process=[integrate_elements_by_structure(), maximize_signal_strength_per_component(), enforce_internal_consistency(), formulate_compact_nucleus()]; output={condensed_nucleus:any}}`", "step": "f", "title": "Condensed Nucleus Synthesis", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"role\": \"causal_mapper\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Automate document retrieval tasks\",\n      \"Standardize retrieval workflows\",\n      \"Minimize manual effort and errors\",\n      \"Collect files and metadata automatically\",\n      \"Ensure seamless workflow continuity\",\n      \"Enable focus on primary responsibilities\"\n    ],\n    \"core_telos\": \"Optimize and streamline document management by reducing manual intervention, standardizing processes, and enabling efficient, reliable retrieval workflows.\"\n  },\n  \"process\": [\n    \"map_causal_links_dependencies()\",\n    \"structure_minimal_flow_graph()\",\n    \"validate_structural_necessity_for_telos()\"\n  ],\n  \"output\": {\n    \"causal_nexus\": {\n      \"Automate document retrieval tasks\": {\n        \"enables\": [\n          \"Collect files and metadata automatically\",\n          \"Minimize manual effort and errors\"\n        ],\n        \"depends_on\": []\n      },\n      \"Standardize retrieval workflows\": {\n        \"enables\": [\n          \"Minimize manual effort and errors\",\n          \"Ensure seamless workflow continuity\"\n        ],\n        \"depends_on\": []\n      },\n      \"Collect files and metadata automatically\": {\n        \"enables\": [\n          \"Ensure seamless workflow continuity\"\n        ],\n        \"depends_on\": [\n          \"Automate document retrieval tasks\"\n        ]\n      },\n      \"Minimize manual effort and errors\": {\n        \"enables\": [\n          \"Ensure seamless workflow continuity\"\n        ],\n        \"depends_on\": [\n          \"Automate document retrieval tasks\",\n          \"Standardize retrieval workflows\"\n        ]\n      },\n      \"Ensure seamless workflow continuity\": {\n        \"enables\": [\n          \"Enable focus on primary responsibilities\"\n        ],\n        \"depends_on\": [\n          \"Minimize manual effort and errors\",\n          \"Collect files and metadata automatically\",\n          \"Standardize retrieval workflows\"\n        ]\n      },\n      \"Enable focus on primary responsibilities\": {\n        \"enables\": [],\n        \"depends_on\": [\n          \"Ensure seamless workflow continuity\"\n        ]\n      }\n    }\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"nucleus_synthesizer\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Automate document retrieval tasks\",\n      \"Standardize retrieval workflows\",\n      \"Minimize manual effort and errors\",\n      \"Collect files and metadata automatically\",\n      \"Ensure seamless workflow continuity\",\n      \"Enable focus on primary responsibilities\"\n    ],\n    \"causal_nexus\": {\n      \"Automate document retrieval tasks\": {\n        \"enables\": [\n          \"Collect files and metadata automatically\",\n          \"Minimize manual effort and errors\"\n        ],\n        \"depends_on\": []\n      },\n      \"Standardize retrieval workflows\": {\n        \"enables\": [\n          \"Minimize manual effort and errors\",\n          \"Ensure seamless workflow continuity\"\n        ],\n        \"depends_on\": []\n      },\n      \"Collect files and metadata automatically\": {\n        \"enables\": [\n          \"Ensure seamless workflow continuity\"\n        ],\n        \"depends_on\": [\n          \"Automate document retrieval tasks\"\n        ]\n      },\n      \"Minimize manual effort and errors\": {\n        \"enables\": [\n          \"Ensure seamless workflow continuity\"\n        ],\n        \"depends_on\": [\n          \"Automate document retrieval tasks\",\n          \"Standardize retrieval workflows\"\n        ]\n      },\n      \"Ensure seamless workflow continuity\": {\n        \"enables\": [\n          \"Enable focus on primary responsibilities\"\n        ],\n        \"depends_on\": [\n          \"Minimize manual effort and errors\",\n          \"Collect files and metadata automatically\",\n          \"Standardize retrieval workflows\"\n        ]\n      },\n      \"Enable focus on primary responsibilities\": {\n        \"enables\": [],\n        \"depends_on\": [\n          \"Ensure seamless workflow continuity\"\n        ]\n      }\n    }\n  },\n  \"process\": [\n    \"integrate_elements_by_structure()\",\n    \"maximize_signal_strength_per_component()\",\n    \"enforce_internal_consistency()\",\n    \"formulate_compact_nucleus()\"\n  ],\n  \"output\": {\n    \"condensed_nucleus\": [\n      \"Automate document retrieval and metadata collection\",\n      \"Standardize and streamline retrieval workflows\",\n      \"Reduce manual effort, errors, and interruptions\",\n      \"Maintain seamless workflow continuity\",\n      \"Enable focus on core responsibilities\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Redundancy Annihilation & Signal Clarification\n\nScrutinize the `condensed_nucleus` to annihilate *all* semantic or structural redundancy, overlap, ambiguity, and verbose phrasing; enforce maximal conciseness, ensuring every remaining component carries unique, indispensable value and crystal-clear signal. Execute as\n\n`{role=signal_clarifier; input=condensed_nucleus:any; process=[scan_for_all_redundancy_ambiguity(), eliminate_overlaps_verbose_phrasing(), compress_phrasing_to_minimal_potent_form(), validate_uniqueness_clarity()]; output={minimal_signal_core:any}}`", "step": "g", "title": "Redundancy Annihilation & Signal Clarification", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"role\": \"nucleus_synthesizer\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Automate document retrieval tasks\",\n      \"Standardize retrieval workflows\",\n      \"Minimize manual effort and errors\",\n      \"Collect files and metadata automatically\",\n      \"Ensure seamless workflow continuity\",\n      \"Enable focus on primary responsibilities\"\n    ],\n    \"causal_nexus\": {\n      \"Automate document retrieval tasks\": {\n        \"enables\": [\n          \"Collect files and metadata automatically\",\n          \"Minimize manual effort and errors\"\n        ],\n        \"depends_on\": []\n      },\n      \"Standardize retrieval workflows\": {\n        \"enables\": [\n          \"Minimize manual effort and errors\",\n          \"Ensure seamless workflow continuity\"\n        ],\n        \"depends_on\": []\n      },\n      \"Collect files and metadata automatically\": {\n        \"enables\": [\n          \"Ensure seamless workflow continuity\"\n        ],\n        \"depends_on\": [\n          \"Automate document retrieval tasks\"\n        ]\n      },\n      \"Minimize manual effort and errors\": {\n        \"enables\": [\n          \"Ensure seamless workflow continuity\"\n        ],\n        \"depends_on\": [\n          \"Automate document retrieval tasks\",\n          \"Standardize retrieval workflows\"\n        ]\n      },\n      \"Ensure seamless workflow continuity\": {\n        \"enables\": [\n          \"Enable focus on primary responsibilities\"\n        ],\n        \"depends_on\": [\n          \"Minimize manual effort and errors\",\n          \"Collect files and metadata automatically\",\n          \"Standardize retrieval workflows\"\n        ]\n      },\n      \"Enable focus on primary responsibilities\": {\n        \"enables\": [],\n        \"depends_on\": [\n          \"Ensure seamless workflow continuity\"\n        ]\n      }\n    }\n  },\n  \"process\": [\n    \"integrate_elements_by_structure()\",\n    \"maximize_signal_strength_per_component()\",\n    \"enforce_internal_consistency()\",\n    \"formulate_compact_nucleus()\"\n  ],\n  \"output\": {\n    \"condensed_nucleus\": [\n      \"Automate document retrieval and metadata collection\",\n      \"Standardize and streamline retrieval workflows\",\n      \"Reduce manual effort, errors, and interruptions\",\n      \"Maintain seamless workflow continuity\",\n      \"Enable focus on core responsibilities\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"minimal_signal_core\": [\n    \"Automate document and metadata retrieval\",\n    \"Standardize retrieval workflows\",\n    \"Reduce manual effort and errors\",\n    \"Ensure workflow continuity\",\n    \"Enable focus on core tasks\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Universal Logic Abstraction\n\nAbstract the `minimal_signal_core`, neutralizing domain-specific language and reframing using universally applicable concepts and logical structures, while rigorously preserving the core intent and actionability across any context. Execute as\n\n`{role=universal_abstractor; input=minimal_signal_core:any; process=[neutralize_domain_specifics(), reframe_as_universal_concepts_logic(), validate_logic_preservation_across_contexts(), ensure_representation_agnostic_structure()]; output={universal_core_logic:any}}`", "step": "h", "title": "Universal Logic Abstraction", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"minimal_signal_core\": [\n    \"Automate document and metadata retrieval\",\n    \"Standardize retrieval workflows\",\n    \"Reduce manual effort and errors\",\n    \"Ensure workflow continuity\",\n    \"Enable focus on core tasks\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"universal_core_logic\": [\n    \"Automate information and related data collection processes\",\n    \"Establish standardized procedures for retrieval tasks\",\n    \"Minimize manual actions and associated mistakes\",\n    \"Preserve continuity in ongoing activities\",\n    \"Enable attention to principal responsibilities\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Linguistic Potency Injection\n\nReforge the `universal_core_logic` using high-potency, action-driven, maximally precise imperatives and descriptors; eliminate passive or neutral language, amplifying structural clarity, actionability, and conceptual force without increasing length. Execute as\n\n`{role=linguistic_intensifier; input=universal_core_logic:any; process=[select_high_potency_verbs_descriptors(), eliminate_passive_neutral_ambiguous_language(), maximize_precision_impact_per_word(), amplify_actionability_via_phrasing()]; output={potent_core:any}}`", "step": "i", "title": "Linguistic Potency Injection", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"universal_core_logic\": [\n    \"Automate information and related data collection processes\",\n    \"Establish standardized procedures for retrieval tasks\",\n    \"Minimize manual actions and associated mistakes\",\n    \"Preserve continuity in ongoing activities\",\n    \"Enable attention to principal responsibilities\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"potent_core\": [\n    \"Automate document and metadata extraction rapidly and precisely.\",\n    \"Impose uniform, optimized retrieval workflows across all projects.\",\n    \"Slash manual actions and obliterate error opportunities.\",\n    \"Sustain uninterrupted operational momentum consistently.\",\n    \"Empower users to concentrate solely on high-impact tasks.\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Axiomatic Vectorization for One-Line\n\nDetermine the optimal linear sequence for the `potent_core` components based on the `causal_nexus`; select the highest-density, unambiguous connectors (e.g., ';', '->', '|') to vectorize the structure ready for single-line collapse. Execute as\n\n`{role=one_line_vectorizer; input={potent_core:any, causal_nexus:dict}; process=[determine_optimal_axiomatic_linear_sequence(), select_high_density_unambiguous_connectors(), vectorize_components_for_linearity()]; output={linear_vector_plan:dict(sequence:list, connectors:list)}}`", "step": "j", "title": "Axiomatic Vectorization for One-Line", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"potent_core\": [\n    \"Automate document and metadata extraction rapidly and precisely.\",\n    \"Impose uniform, optimized retrieval workflows across all projects.\",\n    \"Slash manual actions and obliterate error opportunities.\",\n    \"Sustain uninterrupted operational momentum consistently.\",\n    \"Empower users to concentrate solely on high-impact tasks.\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"one_line_vectorizer\",\n  \"input\": {\n    \"potent_core\": [\n      \"Automate document and metadata extraction rapidly and precisely.\",\n      \"Impose uniform, optimized retrieval workflows across all projects.\",\n      \"Slash manual actions and obliterate error opportunities.\",\n      \"Sustain uninterrupted operational momentum consistently.\",\n      \"Empower users to concentrate solely on high-impact tasks.\"\n    ],\n    \"causal_nexus\": {\n      \"Automate document and metadata extraction rapidly and precisely.\": [\"Impose uniform, optimized retrieval workflows across all projects.\"],\n      \"Impose uniform, optimized retrieval workflows across all projects.\": [\n        \"Slash manual actions and obliterate error opportunities.\",\n        \"Sustain uninterrupted operational momentum consistently.\"\n      ],\n      \"Slash manual actions and obliterate error opportunities.\": [\"Sustain uninterrupted operational momentum consistently.\"],\n      \"Sustain uninterrupted operational momentum consistently.\": [\"Empower users to concentrate solely on high-impact tasks.\"]\n    }\n  },\n  \"process\": [\n    \"determine_optimal_axiomatic_linear_sequence()\",\n    \"select_high_density_unambiguous_connectors()\",\n    \"vectorize_components_for_linearity()\"\n  ],\n  \"output\": {\n    \"linear_vector_plan\": {\n      \"sequence\": [\n        \"Automate document and metadata extraction rapidly and precisely.\",\n        \"Impose uniform, optimized retrieval workflows across all projects.\",\n        \"Slash manual actions and obliterate error opportunities.\",\n        \"Sustain uninterrupted operational momentum consistently.\",\n        \"Empower users to concentrate solely on high-impact tasks.\"\n      ],\n      \"connectors\": [\n        \"->\",\n        \"->\",\n        \"->\",\n        \"->\"\n      ]\n    }\n  }\n}", "cost": 0.0}}}, {"instruction": "# Semantic Compression & Symbolization\n\nApply maximal semantic compression to the planned linear vector, substituting ultra-concise symbols, abbreviations, or keywords where possible without ambiguity; encapsulate the full essential meaning within the absolute minimum footprint, ensuring self-contained decodability. Execute as\n\n`{role=semantic_compressor; input=linear_vector_plan:dict; process=[identify_compressible_concepts(), substitute_minimal_unambiguous_symbols(), maximize_meaning_per_unit(), validate_decodability_self_sufficiency()]; output={compressed_vector_elements:list}}`", "step": "k", "title": "Semantic Compression & Symbolization", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"role\": \"one_line_vectorizer\",\n  \"input\": {\n    \"potent_core\": [\n      \"Automate document and metadata extraction rapidly and precisely.\",\n      \"Impose uniform, optimized retrieval workflows across all projects.\",\n      \"Slash manual actions and obliterate error opportunities.\",\n      \"Sustain uninterrupted operational momentum consistently.\",\n      \"Empower users to concentrate solely on high-impact tasks.\"\n    ],\n    \"causal_nexus\": {\n      \"Automate document and metadata extraction rapidly and precisely.\": [\n        \"Impose uniform, optimized retrieval workflows across all projects.\"\n      ],\n      \"Impose uniform, optimized retrieval workflows across all projects.\": [\n        \"Slash manual actions and obliterate error opportunities.\",\n        \"Sustain uninterrupted operational momentum consistently.\"\n      ],\n      \"Slash manual actions and obliterate error opportunities.\": [\n        \"Sustain uninterrupted operational momentum consistently.\"\n      ],\n      \"Sustain uninterrupted operational momentum consistently.\": [\n        \"Empower users to concentrate solely on high-impact tasks.\"\n      ]\n    }\n  },\n  \"process\": [\n    \"determine_optimal_axiomatic_linear_sequence()\",\n    \"select_high_density_unambiguous_connectors()\",\n    \"vectorize_components_for_linearity()\"\n  ],\n  \"output\": {\n    \"linear_vector_plan\": {\n      \"sequence\": [\n        \"Automate document and metadata extraction rapidly and precisely.\",\n        \"Impose uniform, optimized retrieval workflows across all projects.\",\n        \"Slash manual actions and obliterate error opportunities.\",\n        \"Sustain uninterrupted operational momentum consistently.\",\n        \"Empower users to concentrate solely on high-impact tasks.\"\n      ],\n      \"connectors\": [\n        \"->\",\n        \"->\",\n        \"->\",\n        \"->\"\n      ]\n    }\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"compressed_vector_elements\": [\n    \"AutoDoc+MetaX (fast, precise)\",\n    \"StdWF(opt) AllProj\",\n    \"NoManual, NoErr\",\n    \"ConstOpFlow\",\n    \"User=HiImpactOnly\"\n  ]\n}", "cost": 0.0}}}], "total_cost": 0.0}