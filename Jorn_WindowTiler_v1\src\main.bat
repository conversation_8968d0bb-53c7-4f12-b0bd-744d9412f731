@ECHO OFF
SETLOCAL ENABLEDELAYEDEXPANSION


:: =============================================================================
:: Main Script Logic
:: =============================================================================
CALL :InitBatchScript
CALL :SpecifyOptions
CALL :InitEnvironment
CALL :ExecutePyScript
EXIT /B 0


:: =============================================================================
:: Subroutines
:: =============================================================================

:: Initialize Variables.
:InitBatchScript
    :: Switch to user-provided directory or stay in the current directory.
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    :: Stash away information.
    SET "CmdStartDirectory=%CD%"
    SET "CmdFullPath=%~dp0%~nx0"
    SET "CmdDirectory=%~dp0"
    SET "CmdFileName=%~nx0"
    SET "CmdBaseName=%~n0"
    SET "CmdExtension=%~x0"
GOTO :EOF

:: Specify Options.
:SpecifyOptions
    :: Echo
    SET "EchoOff=true"
    :: Arguments
    SET "PromptForPyInputArgs=false"
    SET "PyFileName=%CmdBaseName%.py"
    SET "PyFilePath=%CmdStartDirectory%\%PyFileName%"
    SET "PyInputArgs="
    :: Execution
    SET "ContinualPrompt=true"     :: #1: Will re-execute whenever you press a key
    SET "RepeatAfterSeconds=false" :: #2: Will re-execute after X seconds (indefinitely)
    SET "ExitAfterSeconds=false"   :: #3: Will automatically close after X seconds
    SET "SecondsUntilRepeat=3"
    SET "SecondsUntilExit=10"
    :: Information
    SET "ShowInfo=true" :: Must be true for any info to be displayed
    SET "ShowProjectInfo=true" :: Will display basic project info
    SET "ShowExecuteInfo=true" :: Will display execution options
GOTO :EOF

:: Locate and retrieve the project and it's venv.
:InitEnvironment
    :: Locate the project folder.
    SET "ProjectCacheIdentifier=.cache"
    :SearchProjectCache
        IF EXIST "%CD%\%ProjectCacheIdentifier%" (GOTO ReadProjectCache)
        SET "TempDir=%CD%" & CD ..
        IF "%CD%"=="%TempDir%" (
            CD /D "%CmdStartDirectory%"
            ECHO Could not find a project directory, make sure you've initialized it.
            GOTO :ExitScript
        )
    GOTO SearchProjectCache
    :: Read the project cache files.
    :ReadProjectCache
        SET "ProjectNameCache=%CD%\.cache\.PROJECT-NAME.%COMPUTERNAME%"
        SET "ProjectPathCache=%CD%\.cache\.PROJECT-PATH.%COMPUTERNAME%"
        SET "VenvDirPathCache=%CD%\.cache\.VENV-PATH.%COMPUTERNAME%"
        SET "VenvPyVersionCache=%CD%\.cache\.PYTHON-VERSION.%COMPUTERNAME%"
        IF EXIST "%ProjectNameCache%" (SET /P ProjectName=<"%ProjectNameCache%")
        IF EXIST "%ProjectPathCache%" (SET /P ProjectPath=<"%ProjectPathCache%")
        IF EXIST "%VenvDirPathCache%" (SET /P VenvDirPath=<"%VenvDirPathCache%")
        IF EXIST "%VenvPyVersionCache%" (SET /P VenvPyVersion=<"%VenvPyVersionCache%")
        CD /D "%CmdStartDirectory%"
        :: Initialize environment variables (or exit with information)
        IF EXIST "%ProjectPath%" (
            IF EXIST "%ProjectPath%" (
                SET "VenvRequirementsTxt=%ProjectPath%\requirements.txt"
                SET "VenvPyExe=%VenvDirPath%\Scripts\python.exe"
                SET "VenvPipExe=%VenvDirPath%\Scripts\pip.exe"
                SET "VenvActivate=%VenvDirPath%\Scripts\activate"
            )
        ) ELSE (
            IF NOT EXIST "%ProjectPath%" (SET "ProjectPathStatus=[ NOT FOUND! ] ->")
            IF NOT EXIST "%VenvDirPath%" (SET "VenvDirPathStatus=[ NOT FOUND! ] ->")
            ECHO Could not read valid paths from cache files.
            ECHO.
            ECHO - ProjectName      : !ProjectPathStatus! %ProjectName%
            ECHO - ProjectPath      : !ProjectPathStatus! %ProjectPath%
            ECHO - VenvDirPath      : !VenvDirPathStatus! %VenvDirPath%
            ECHO - VenvPyVersion    : !VenvDirPathStatus! %VenvPyVersion%
            GOTO :ExitScript
        )
GOTO :EOF

:: Display information.
:DisplayExecuteInfo
    TITLE Execute: "%CmdFileName%"
    IF "%ShowInfo%" == "true" (
        ECHO -----------------------------------------------------------------------
        ECHO Information:
        IF "%ShowProjectInfo%" == "true" (
            ECHO   - Project Name : %ProjectName%
            ECHO   - Project Path : %ProjectPath%
            ECHO   - Venv Path    : %VenvDirPath%
            ECHO   - Py Version   : %VenvPyVersion%
            ECHO.
        )
        IF "%ShowExecuteInfo%" == "true" (
            ECHO   ^> python "%PyFileName%" %PyInputArgs%
        )
        ECHO.
        ECHO -----------------------------------------------------------------------
    )
GOTO :EOF


:: Prompt for input arguments.
:InitPyInputArgs
    IF "%PromptForPyInputArgs%" == "true" (
        ECHO.
        ECHO Enter arguments to pass into the script:
        SET /P PyInputArgs="Args: "
        ECHO !PyInputArgs!
        ECHO -----------------------------------------------------------------------
    )
GOTO :EOF

:: Execute script.
:ExecutePyScript
    TITLE %PyFileName%
    SET "EchoOff=" & IF "%EchoOff%"=="true" SET "EchoOff=& @ECHO OFF"
    CLS
    CALL :InitPyInputArgs
    CALL :DisplayExecuteInfo

    :: Activate environment before executing script
    :: SET "VenvActivateCmd="%VenvActivate%" & CD /D %CD% & TITLE ^(%PyFileName%^) %CD%"
    :: CMD /K "%VenvActivateCmd% & python "%PyFilePath%""

    :: Execute script directly
    "%VenvPyExe%" "%PyFilePath%" "%PyInputArgs%"

    :: -> {1} Exit with message
    IF "%ExitAfterSeconds%" == "true" (
        GOTO :ExitScript
    )
    :: -> {2} Re-execute indefinitely
    IF "%ContinualPrompt%" == "true" (
        ECHO.
        ECHO.
        ECHO.
        ECHO Press a key to re-execute script ...
        PAUSE > NUL
        CLS
        GOTO :ExecutePyScript
    )
    :: -> {3} Continual execution (be vary when e.g. writing to files)
    :ContinualExecution
    CLS
    "%VenvPyExe%" "%PyFilePath%"
    IF "%RepeatAfterSeconds%" == "true" (
        ECHO -----------------------------------------------------------------------
        ECHO.
        ECHO.
        ECHO.
        SET "SecondsUntilRepeat=3"
        ECHO Execution repeat rate: %SecondsUntilRepeat% seconds ...
        PING 127.0.0.1 -n %SecondsUntilRepeat% > NUL
        GOTO :ContinualExecution
    )
GOTO :EOF


:: Exit with message (auto-close after specified amount of seconds)
:ExitScript
    ECHO.
    ECHO.
    ECHO Window will automatically close in %SecondsUntilExit% seconds ...
    PING 127.0.0.1 -n %SecondsUntilExit% > NUL & EXIT
GOTO :EOF

