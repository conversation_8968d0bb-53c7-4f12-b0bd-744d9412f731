    └── src
        ├── __init__.py
        ├── cli.py
        ├── log.py
        ├── main.py
        ├── utils.py
        │
        ├── submodule1
        │   └── ....py
        │
        ├── submodule2
        │   └── ....py
        │
        └── etc




└── BookmarksPrj
    ├── .gitignore
    ├── requirements.txt
    │
    └── src
        ├── __init__.py
        ├── app.log
        ├── cfg_rules.py
        ├── cli.py
        ├── log.py
        ├── main.BAT
        ├── main.py
        ├── metadata_extractor.py
        ├── run.log
        ├── shortcut_processing.py
        ├── utils.py
        ├── tests
        │   ├── urls_testing_reddit.zip
        │   ├── urls_testing_youtube.zip
        │   └── youtu.be.iddfzef4h6e.si.ro3omg0spvpfnk6o.url
        │
        └── url_processing
            ├── _OPEN_PROMPT_HERE.bat
            ├── _url_extractor.py
            ├── url_parser.py
            └── url_utils.py
