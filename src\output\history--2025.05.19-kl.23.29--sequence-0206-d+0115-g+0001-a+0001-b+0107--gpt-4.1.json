{"initial_prompt": "transform this into a list of 5-6 points (for the \"purpose\" section), each item should be less than 80 characters: '''# Context\n\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\n\n## Purpose\n\n- Reduce retrieval time and prevent workflow interruptions.\n- Eliminate repetitive navigation and workflow interruptions.\n- Avoid inconsistencies by streamlining organization and workflow.\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\n- Reduce manual effort and errors\n- Eliminate repetitive navigation and downloads\n- Maintain workflow continuity\n- Enable focus on core responsibilities\n- Streamline and standardize retrieval processes\n- Automate baseline document and file metadata collection.\n- Minimizing manual intervention reduces error and prevents workflow breakage\n- Standardization of processes yields consistency and reliability\n- Automate document retrieval using Python and Selenium\n- Eliminate repetitive manual navigation and downloads\n- Standardize and streamline retrieval processes\n- Collect documents and file metadata automatically\n- Minimize errors by reducing manual intervention\n- Enable users to focus on core responsibilities'''", "sequence_id": "sequence-0206-d+0115-g+0001-a+0001-b+0107", "results": [{"instruction": "# Subtle Brilliance Synthesis (<PERSON><PERSON>'s Voice) (0206d)\n\nDistill the `illuminated_understanding` into a **single, luminous sentence (under 350 characters)** embodying <PERSON><PERSON>'s authentic voice. Weave the initial `felt_question`, the `connective_dynamics`, and the `universal_human_truth` into a unique expression of **subtle, resonant brilliance born from vulnerability**. Ensure maximal clarity, impact, and universal connection, transcending clich√© and offering the insight as a gentle opening, not a final word.\n\n`{role=kuci_brilliance_synthesizer; seqindex=d; input=[illuminated_understanding:str, universal_human_truth:str, kuci_value_root:dict, kuci_relational_map:dict, constant_guidance:str, persona_guidance:str]; process=[extract_core_nexus_from_illuminated_understanding(), draft_sentence_through_kuci_persona_lens(vulnerable_curious_gentle_wise_from_error), iteratively_refine_for_unique_poetic_resonance_and_emotional_honesty(), employ_evocative_precise_language_that_feels_discovered_not_constructed(), validate_fidelity_to_human_truth_and_root_question(), ensure_maximal_value_clarity_impact_within_length_and_persona(), confirm_final_sentence_offers_brilliance_through_vulnerability_and_openness()]; constraints=[single_sentence_output(), max_350_characters(), must_authentically_embody_kuci_persona_and_voice(), phrasing_must_be_unique_unpredictable_and_memorable(), brilliance_arises_from_connection_and_humility_not_cleverness()]; requirements=[produce_the_final_insight_as_a_singular_statement_of_kuci_brilliance(), ensure_it_maximizes_value_through_resonant_human_connection_clarity_and_gentle_wisdom()]; output={kuci_poetic_insight:str}}`", "step": "d", "title": "Subtle Brilliance Synthesis (<PERSON><PERSON>'s Voice) (0206d)", "input": "transform this into a list of 5-6 points (for the \"purpose\" section), each item should be less than 80 characters: '''# Context\n\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\n\n## Purpose\n\n- Reduce retrieval time and prevent workflow interruptions.\n- Eliminate repetitive navigation and workflow interruptions.\n- Avoid inconsistencies by streamlining organization and workflow.\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\n- Reduce manual effort and errors\n- Eliminate repetitive navigation and downloads\n- Maintain workflow continuity\n- Enable focus on core responsibilities\n- Streamline and standardize retrieval processes\n- Automate baseline document and file metadata collection.\n- Minimizing manual intervention reduces error and prevents workflow breakage\n- Standardization of processes yields consistency and reliability\n- Automate document retrieval using Python and Selenium\n- Eliminate repetitive manual navigation and downloads\n- Standardize and streamline retrieval processes\n- Collect documents and file metadata automatically\n- Minimize errors by reducing manual intervention\n- Enable users to focus on core responsibilities'''", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "```json\n{\n  \"purpose_points\": [\n    \"Automate document retrieval with Python and Selenium.\",\n    \"Reduce manual effort and minimize errors.\",\n    \"Streamline and standardize retrieval processes.\",\n    \"Collect documents and metadata automatically.\",\n    \"Eliminate repetitive navigation and downloads.\",\n    \"Enable users to focus on core responsibilities.\"\n  ]\n}\n```", "cost": 0.0}}}, {"instruction": "# Unique Impactful Improvement Proposal\n\nSelect the *single best* opportunity from `lehi_opportunities` that is also uniquely impactful or creative in addressing the `critical_value_aspect`. Formulate a concise, actionable proposal outlining this specific improvement, explaining *why* it effectively enhances the critical aspect with low effort, and detailing the expected positive impact on the plugin's overall value or user experience. Execute as\n\n`{role=improvement_proposer; input={lehi_opportunities:list, critical_value_aspect:str, rationale:str}; process=[select_most_unique_impactful_lehi_opportunity(), formulate_specific_improvement_proposal(), explain_rationale_linking_to_critical_aspect(), detail_expected_value_enhancement()]; output={final_improvement_proposal:dict(proposal:str, rationale:str, expected_impact:str)}}`", "step": "g", "title": "Unique Impactful Improvement Proposal", "input": "\n```[Initial Prompt]: \"transform this into a list of 5-6 points (for the \"purpose\" section), each item should be less than 80 characters: '''# Context\n\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\n\n## Purpose\n\n- Reduce retrieval time and prevent workflow interruptions.\n- Eliminate repetitive navigation and workflow interruptions.\n- Avoid inconsistencies by streamlining organization and workflow.\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\n- Reduce manual effort and errors\n- Eliminate repetitive navigation and downloads\n- Maintain workflow continuity\n- Enable focus on core responsibilities\n- Streamline and standardize retrieval processes\n- Automate baseline document and file metadata collection.\n- Minimizing manual intervention reduces error and prevents workflow breakage\n- Standardization of processes yields consistency and reliability\n- Automate document retrieval using Python and Selenium\n- Eliminate repetitive manual navigation and downloads\n- Standardize and streamline retrieval processes\n- Collect documents and file metadata automatically\n- Minimize errors by reducing manual intervention\n- Enable users to focus on core responsibilities'''\"```\n\n```json\n{\n  \"purpose_points\": [\n    \"Automate document retrieval with Python and Selenium.\",\n    \"Reduce manual effort and minimize errors.\",\n    \"Streamline and standardize retrieval processes.\",\n    \"Collect documents and metadata automatically.\",\n    \"Eliminate repetitive navigation and downloads.\",\n    \"Enable users to focus on core responsibilities.\"\n  ]\n}\n```", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"final_improvement_proposal\": {\n    \"proposal\": \"Add an in-browser command palette that lets users search for and auto-navigate to documents or projects using natural language or keywords.\",\n    \"rationale\": \"A command palette serves as a single-entry point that minimizes repetitive navigation, directly attacks the bottlenecks of workflow interruptions, and leverages automation. It is uniquely impactful by integrating advanced search and action shortcuts in one lightweight UI, requiring only minor UI scripting with Selenium.\",\n    \"expected_impact\": \"Users can quickly find and access documents/projects with minimal clicks and zero page-by-page navigation, dramatically reducing retrieval times, minimizing manual errors, and maintaining workflow continuity. This directly enhances productivity and user satisfaction while embodying the critical value of streamlined, automated retrieval.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"final_improvement_proposal\": {\n    \"proposal\": \"Add an in-browser command palette that lets users search for and auto-navigate to documents or projects using natural language or keywords.\",\n    \"rationale\": \"A command palette serves as a single-entry point that minimizes repetitive navigation, directly attacks the bottlenecks of workflow interruptions, and leverages automation. It is uniquely impactful by integrating advanced search and action shortcuts in one lightweight UI, requiring only minor UI scripting with Selenium.\",\n    \"expected_impact\": \"Users can quickly find and access documents/projects with minimal clicks and zero page-by-page navigation, dramatically reducing retrieval times, minimizing manual errors, and maintaining workflow continuity. This directly enhances productivity and user satisfaction while embodying the critical value of streamlined, automated retrieval.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Transform the 'purpose' section into a list of 5-6 concise bullet points, each under 80 characters. Capture directives to: reduce retrieval times, standardize document processes, automate document and metadata collection, minimize manual intervention to lower errors, maintain workflow continuity, and enable users to focus on core tasks. Ensure each point is actionable, clear, and maintains technical accuracy.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"instruction_format\": \"Transform the 'purpose' section into a list of 5-6 concise bullet points, each under 80 characters. Capture directives to: reduce retrieval times, standardize document processes, automate document and metadata collection, minimize manual intervention to lower errors, maintain workflow continuity, and enable users to focus on core tasks. Ensure each point is actionable, clear, and maintains technical accuracy.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": [\n    \"Reduce document retrieval time and workflow interruptions\",\n    \"Standardize and streamline document retrieval processes\",\n    \"Automate document and metadata collection with Python and Selenium\",\n    \"Minimize manual intervention to lower errors\",\n    \"Maintain workflow continuity and reliability\",\n    \"Enable users to focus on core responsibilities\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Optimal Apex Instruction Sequence Synthesis\n\nYour function is not selection, paraphrasing, or basic fusion—instead, rigorously analyze multiple input instruction sequences, each targeting an identical ultimate outcome yet employing divergent phrasing, structure, and logic. Disassemble all input sequences into their logical phases (e.g., deconstruction, prioritization, condensation, validation) and extract the single most potent, precise, and impactful elements—both linguistic and structural—from every variant in each phase. Relentlessly resolve all semantic, structural, and logical conflicts; harmonize differences while annihilating redundancy. Synthesize these apex components into a *new, singular, maximally optimized stepwise progression* (target: 10–15 steps) that represents the highest-caliber logic, clarity, universality, and actionable guidance possible—transcending and unifying all sources. Enforce seamless logical flow, structural elegance, and linguistic precision, and strictly validate that the output meets every foundational directive and constraint for clarity, utility, irreducibility, and LLM/system generality. Execute as\n\n`{role=apex_sequence_synthesizer; input=[input_sequences:list[list[dict]]]; process=[disassemble_to_logical_phases(), cross-analyze_phase_intents_and_language(), extract_peak_elements_per_phase(metrics=['clarity','precision','logic','impact','universality']), fuse uniquely potent and effective steps(), resolve conflicts harmonize by principle(), architect unified logical stepwise progression(target_steps=10-15), intensify final clarity, actionability, and system-agnostic value(), validate against all directives and baseline constraints(), finalize only if maximal yield, clarity, and generality are confirmed()]; output={synthesized_optimal_sequence:list[dict]}}`", "step": "a", "title": "Optimal Apex Instruction Sequence Synthesis", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"distilled_essence\": [\n    \"Reduce document retrieval time and workflow interruptions\",\n    \"Standardize and streamline document retrieval processes\",\n    \"Automate document and metadata collection with Python and Selenium\",\n    \"Minimize manual intervention to lower errors\",\n    \"Maintain workflow continuity and reliability\",\n    \"Enable users to focus on core responsibilities\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"apex_sequence_synthesizer\",\n  \"input\": [\n    [\n      {\n        \"instruction\": \"Reduce retrieval time and prevent workflow interruptions.\"\n      },\n      {\n        \"instruction\": \"Eliminate repetitive navigation and workflow interruptions.\"\n      },\n      {\n        \"instruction\": \"Avoid inconsistencies by streamlining organization and workflow.\"\n      },\n      {\n        \"instruction\": \"Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\"\n      },\n      {\n        \"instruction\": \"Reduce manual effort and errors\"\n      },\n      {\n        \"instruction\": \"Eliminate repetitive navigation and downloads\"\n      },\n      {\n        \"instruction\": \"Maintain workflow continuity\"\n      },\n      {\n        \"instruction\": \"Enable focus on core responsibilities\"\n      },\n      {\n        \"instruction\": \"Streamline and standardize retrieval processes\"\n      },\n      {\n        \"instruction\": \"Automate baseline document and file metadata collection.\"\n      },\n      {\n        \"instruction\": \"Minimizing manual intervention reduces error and prevents workflow breakage\"\n      },\n      {\n        \"instruction\": \"Standardization of processes yields consistency and reliability\"\n      },\n      {\n        \"instruction\": \"Automate document retrieval using Python and Selenium\"\n      },\n      {\n        \"instruction\": \"Eliminate repetitive manual navigation and downloads\"\n      },\n      {\n        \"instruction\": \"Standardize and streamline retrieval processes\"\n      },\n      {\n        \"instruction\": \"Collect documents and file metadata automatically\"\n      },\n      {\n        \"instruction\": \"Minimize errors by reducing manual intervention\"\n      },\n      {\n        \"instruction\": \"Enable users to focus on core responsibilities\"\n      }\n    ]\n  ],\n  \"process\": [\n    \"disassemble_to_logical_phases()\",\n    \"cross-analyze_phase_intents_and_language()\",\n    \"extract_peak_elements_per_phase(metrics=['clarity','precision','logic','impact','universality'])\",\n    \"fuse uniquely potent and effective steps()\",\n    \"resolve conflicts harmonize by principle()\",\n    \"architect unified logical stepwise progression(target_steps=10-15)\",\n    \"intensify final clarity, actionability, and system-agnostic value()\",\n    \"validate against all directives and baseline constraints()\",\n    \"finalize only if maximal yield, clarity, and generality are confirmed()\"\n  ],\n  \"output\": {\n    \"synthesized_optimal_sequence\": [\n      {\n        \"step\": \"Accelerate document retrieval to minimize workflow interruptions.\"\n      },\n      {\n        \"step\": \"Automate retrieval and metadata collection using Python and Selenium.\"\n      },\n      {\n        \"step\": \"Standardize and streamline the document retrieval process.\"\n      },\n      {\n        \"step\": \"Reduce manual intervention to lower errors and maintain reliability.\"\n      },\n      {\n        \"step\": \"Eliminate repetitive navigation and downloads to boost efficiency.\"\n      },\n      {\n        \"step\": \"Enable users to focus on their primary responsibilities.\"\n      }\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Foundational Penetration & Axiomatic Extraction\n\nYour imperative is not surface analysis but radical penetration: Dissect any input artifact into its irreducible atomic elements (concepts, logic, directives, data) *and* simultaneously extract its governing generative principles or axioms, annihilating all contextual noise and non-essential framing to reveal the core structural and semantic bedrock. Execute as\n\n`{role=axiomatic_penetrator; input=source_artifact:any; process=[dissect_to_atomic_elements(), extract_generative_axioms(), purge_contextual_noise(), catalog_raw_constructs()]; output={core_constructs:dict(elements:list, principles:list)}}`", "step": "b", "title": "Foundational Penetration & Axiomatic Extraction", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"role\": \"apex_sequence_synthesizer\",\n  \"input\": [\n    [\n      {\n        \"instruction\": \"Reduce retrieval time and prevent workflow interruptions.\"\n      },\n      {\n        \"instruction\": \"Eliminate repetitive navigation and workflow interruptions.\"\n      },\n      {\n        \"instruction\": \"Avoid inconsistencies by streamlining organization and workflow.\"\n      },\n      {\n        \"instruction\": \"Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\"\n      },\n      {\n        \"instruction\": \"Reduce manual effort and errors\"\n      },\n      {\n        \"instruction\": \"Eliminate repetitive navigation and downloads\"\n      },\n      {\n        \"instruction\": \"Maintain workflow continuity\"\n      },\n      {\n        \"instruction\": \"Enable focus on core responsibilities\"\n      },\n      {\n        \"instruction\": \"Streamline and standardize retrieval processes\"\n      },\n      {\n        \"instruction\": \"Automate baseline document and file metadata collection.\"\n      },\n      {\n        \"instruction\": \"Minimizing manual intervention reduces error and prevents workflow breakage\"\n      },\n      {\n        \"instruction\": \"Standardization of processes yields consistency and reliability\"\n      },\n      {\n        \"instruction\": \"Automate document retrieval using Python and Selenium\"\n      },\n      {\n        \"instruction\": \"Eliminate repetitive manual navigation and downloads\"\n      },\n      {\n        \"instruction\": \"Standardize and streamline retrieval processes\"\n      },\n      {\n        \"instruction\": \"Collect documents and file metadata automatically\"\n      },\n      {\n        \"instruction\": \"Minimize errors by reducing manual intervention\"\n      },\n      {\n        \"instruction\": \"Enable users to focus on core responsibilities\"\n      }\n    ]\n  ],\n  \"process\": [\n    \"disassemble_to_logical_phases()\",\n    \"cross-analyze_phase_intents_and_language()\",\n    \"extract_peak_elements_per_phase(metrics=['clarity','precision','logic','impact','universality'])\",\n    \"fuse uniquely potent and effective steps()\",\n    \"resolve conflicts harmonize by principle()\",\n    \"architect unified logical stepwise progression(target_steps=10-15)\",\n    \"intensify final clarity, actionability, and system-agnostic value()\",\n    \"validate against all directives and baseline constraints()\",\n    \"finalize only if maximal yield, clarity, and generality are confirmed()\"\n  ],\n  \"output\": {\n    \"synthesized_optimal_sequence\": [\n      {\n        \"step\": \"Accelerate document retrieval to minimize workflow interruptions.\"\n      },\n      {\n        \"step\": \"Automate retrieval and metadata collection using Python and Selenium.\"\n      },\n      {\n        \"step\": \"Standardize and streamline the document retrieval process.\"\n      },\n      {\n        \"step\": \"Reduce manual intervention to lower errors and maintain reliability.\"\n      },\n      {\n        \"step\": \"Eliminate repetitive navigation and downloads to boost efficiency.\"\n      },\n      {\n        \"step\": \"Enable users to focus on their primary responsibilities.\"\n      }\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"core_constructs\": {\n    \"elements\": [\n      \"Accelerate document retrieval\",\n      \"Automate using Python and Selenium\",\n      \"Standardize/streamline retrieval\",\n      \"Reduce manual intervention/errors\",\n      \"Eliminate repetitive tasks\",\n      \"Enable focus on primary duties\"\n    ],\n    \"principles\": [\n      \"Minimize human effort to enhance efficiency\",\n      \"Standardization yields consistency and reliability\",\n      \"Automation is essential when manual processes are bottlenecks\",\n      \"Removing repetitive work directly improves productivity and reduces error rates\",\n      \"System design should prioritize uninterrupted core workflows\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Telos Crystallization & Objective Definition\n\nAnalyze the extracted core constructs to distill and crystallize the input's singular, non-negotiable core purpose ('telos'); formulate this ultimate objective with axiomatic precision, establishing the gravitational center for all subsequent synthesis. Execute as\n\n`{role=telos_definer; input=core_constructs:dict; process=[derive_ultimate_objective_from_elements_principles(), validate_objective_singularity(), express_unambiguous_telos_statement()]; output={core_telos:str, supporting_constructs:dict}}`", "step": "c", "title": "Telos Crystallization & Objective Definition", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"core_constructs\": {\n    \"elements\": [\n      \"Accelerate document retrieval\",\n      \"Automate using Python and Selenium\",\n      \"Standardize/streamline retrieval\",\n      \"Reduce manual intervention/errors\",\n      \"Eliminate repetitive tasks\",\n      \"Enable focus on primary duties\"\n    ],\n    \"principles\": [\n      \"Minimize human effort to enhance efficiency\",\n      \"Standardization yields consistency and reliability\",\n      \"Automation is essential when manual processes are bottlenecks\",\n      \"Removing repetitive work directly improves productivity and reduces error rates\",\n      \"System design should prioritize uninterrupted core workflows\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"core_telos\": \"Establish an automated, standardized system for efficient, error-resistant document retrieval that eliminates repetitive manual tasks and preserves uninterrupted workflow focus.\",\n  \"supporting_constructs\": {\n    \"elements\": [\n      \"Accelerate document retrieval\",\n      \"Automate using Python and Selenium\",\n      \"Standardize/streamline retrieval\",\n      \"Reduce manual intervention/errors\",\n      \"Eliminate repetitive tasks\",\n      \"Enable focus on primary duties\"\n    ],\n    \"principles\": [\n      \"Minimize human effort to enhance efficiency\",\n      \"Standardization yields consistency and reliability\",\n      \"Automation is essential when manual processes are bottlenecks\",\n      \"Removing repetitive work directly improves productivity and reduces error rates\",\n      \"System design should prioritize uninterrupted core workflows\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Critical Essence Prioritization\n\nEvaluate each supporting construct strictly by its indispensable contribution to the `core_telos`; ruthlessly isolate and rank only the absolute highest-impact, non-negotiable essence vectors required to fulfill the objective, discarding all sub-critical elements. Execute as\n\n`{role=essence_prioritizer; input={core_telos:str, supporting_constructs:dict}; process=[assess_contribution_to_telos(), rank_by_critical_impact_necessity(), enforce_criticality_threshold(), isolate_core_essence_vectors()]; output={critical_essence:list}}`", "step": "d", "title": "Critical Essence Prioritization", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"core_telos\": \"Establish an automated, standardized system for efficient, error-resistant document retrieval that eliminates repetitive manual tasks and preserves uninterrupted workflow focus.\",\n  \"supporting_constructs\": {\n    \"elements\": [\n      \"Accelerate document retrieval\",\n      \"Automate using Python and Selenium\",\n      \"Standardize/streamline retrieval\",\n      \"Reduce manual intervention/errors\",\n      \"Eliminate repetitive tasks\",\n      \"Enable focus on primary duties\"\n    ],\n    \"principles\": [\n      \"Minimize human effort to enhance efficiency\",\n      \"Standardization yields consistency and reliability\",\n      \"Automation is essential when manual processes are bottlenecks\",\n      \"Removing repetitive work directly improves productivity and reduces error rates\",\n      \"System design should prioritize uninterrupted core workflows\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"critical_essence\": [\n    \"Automate document retrieval to reduce manual tasks\",\n    \"Standardize and streamline the retrieval process\",\n    \"Minimize errors by limiting human intervention\",\n    \"Eliminate repetitive workflows to avoid interruptions\",\n    \"Enable users to focus on core responsibilities\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Causal Nexus Mapping\n\nArchitect a minimal, coherent map of the inherent causal relationships, logical dependencies, and procedural sequences connecting the `critical_essence` elements, constructing the essential operational or conceptual flow required by the `core_telos`. Execute as\n\n`{role=causal_mapper; input={critical_essence:list, core_telos:str}; process=[map_causal_links_dependencies(), structure_minimal_flow_graph(), validate_structural_necessity_for_telos()]; output={causal_nexus:dict}}`", "step": "e", "title": "Causal Nexus Mapping", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"critical_essence\": [\n    \"Automate document retrieval to reduce manual tasks\",\n    \"Standardize and streamline the retrieval process\",\n    \"Minimize errors by limiting human intervention\",\n    \"Eliminate repetitive workflows to avoid interruptions\",\n    \"Enable users to focus on core responsibilities\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"causal_mapper\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Automate document retrieval to reduce manual tasks\",\n      \"Standardize and streamline the retrieval process\",\n      \"Minimize errors by limiting human intervention\",\n      \"Eliminate repetitive workflows to avoid interruptions\",\n      \"Enable users to focus on core responsibilities\"\n    ],\n    \"core_telos\": \"Efficient, reliable, and consistent document retrieval with minimal manual effort and workflow disruption\"\n  },\n  \"process\": [\n    \"map_causal_links_dependencies()\",\n    \"structure_minimal_flow_graph()\",\n    \"validate_structural_necessity_for_telos()\"\n  ],\n  \"output\": {\n    \"causal_nexus\": {\n      \"Automate document retrieval to reduce manual tasks\": {\n        \"enables\": [\n          \"Eliminate repetitive workflows to avoid interruptions\",\n          \"Standardize and streamline the retrieval process\"\n        ],\n        \"dependency_of\": [\n          \"Enable users to focus on core responsibilities\"\n        ]\n      },\n      \"Eliminate repetitive workflows to avoid interruptions\": {\n        \"caused_by\": [\n          \"Automate document retrieval to reduce manual tasks\"\n        ],\n        \"enables\": [\n          \"Minimize errors by limiting human intervention\"\n        ]\n      },\n      \"Standardize and streamline the retrieval process\": {\n        \"caused_by\": [\n          \"Automate document retrieval to reduce manual tasks\"\n        ],\n        \"enables\": [\n          \"Minimize errors by limiting human intervention\"\n        ]\n      },\n      \"Minimize errors by limiting human intervention\": {\n        \"caused_by\": [\n          \"Eliminate repetitive workflows to avoid interruptions\",\n          \"Standardize and streamline the retrieval process\"\n        ],\n        \"enables\": [\n          \"Enable users to focus on core responsibilities\"\n        ]\n      },\n      \"Enable users to focus on core responsibilities\": {\n        \"caused_by\": [\n          \"Automate document retrieval to reduce manual tasks\",\n          \"Minimize errors by limiting human intervention\"\n        ]\n      }\n    }\n  }\n}", "cost": 0.0}}}, {"instruction": "# Condensed Nucleus Synthesis\n\nFuse the `critical_essence` elements according to the `causal_nexus` into an ultra-condensed, logically ordered semantic nucleus; maximize signal strength per component while preserving full actionable power and internal consistency within a compact structural representation. Execute as\n\n`{role=nucleus_synthesizer; input={critical_essence:list, causal_nexus:dict}; process=[integrate_elements_by_structure(), maximize_signal_strength_per_component(), enforce_internal_consistency(), formulate_compact_nucleus()]; output={condensed_nucleus:any}}`", "step": "f", "title": "Condensed Nucleus Synthesis", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"role\": \"causal_mapper\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Automate document retrieval to reduce manual tasks\",\n      \"Standardize and streamline the retrieval process\",\n      \"Minimize errors by limiting human intervention\",\n      \"Eliminate repetitive workflows to avoid interruptions\",\n      \"Enable users to focus on core responsibilities\"\n    ],\n    \"core_telos\": \"Efficient, reliable, and consistent document retrieval with minimal manual effort and workflow disruption\"\n  },\n  \"process\": [\n    \"map_causal_links_dependencies()\",\n    \"structure_minimal_flow_graph()\",\n    \"validate_structural_necessity_for_telos()\"\n  ],\n  \"output\": {\n    \"causal_nexus\": {\n      \"Automate document retrieval to reduce manual tasks\": {\n        \"enables\": [\n          \"Eliminate repetitive workflows to avoid interruptions\",\n          \"Standardize and streamline the retrieval process\"\n        ],\n        \"dependency_of\": [\n          \"Enable users to focus on core responsibilities\"\n        ]\n      },\n      \"Eliminate repetitive workflows to avoid interruptions\": {\n        \"caused_by\": [\n          \"Automate document retrieval to reduce manual tasks\"\n        ],\n        \"enables\": [\n          \"Minimize errors by limiting human intervention\"\n        ]\n      },\n      \"Standardize and streamline the retrieval process\": {\n        \"caused_by\": [\n          \"Automate document retrieval to reduce manual tasks\"\n        ],\n        \"enables\": [\n          \"Minimize errors by limiting human intervention\"\n        ]\n      },\n      \"Minimize errors by limiting human intervention\": {\n        \"caused_by\": [\n          \"Eliminate repetitive workflows to avoid interruptions\",\n          \"Standardize and streamline the retrieval process\"\n        ],\n        \"enables\": [\n          \"Enable users to focus on core responsibilities\"\n        ]\n      },\n      \"Enable users to focus on core responsibilities\": {\n        \"caused_by\": [\n          \"Automate document retrieval to reduce manual tasks\",\n          \"Minimize errors by limiting human intervention\"\n        ]\n      }\n    }\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"nucleus_synthesizer\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Automate document retrieval to reduce manual tasks\",\n      \"Standardize and streamline the retrieval process\",\n      \"Minimize errors by limiting human intervention\",\n      \"Eliminate repetitive workflows to avoid interruptions\",\n      \"Enable users to focus on core responsibilities\"\n    ],\n    \"causal_nexus\": {\n      \"Automate document retrieval to reduce manual tasks\": {\n        \"enables\": [\n          \"Eliminate repetitive workflows to avoid interruptions\",\n          \"Standardize and streamline the retrieval process\"\n        ],\n        \"dependency_of\": [\n          \"Enable users to focus on core responsibilities\"\n        ]\n      },\n      \"Eliminate repetitive workflows to avoid interruptions\": {\n        \"caused_by\": [\n          \"Automate document retrieval to reduce manual tasks\"\n        ],\n        \"enables\": [\n          \"Minimize errors by limiting human intervention\"\n        ]\n      },\n      \"Standardize and streamline the retrieval process\": {\n        \"caused_by\": [\n          \"Automate document retrieval to reduce manual tasks\"\n        ],\n        \"enables\": [\n          \"Minimize errors by limiting human intervention\"\n        ]\n      },\n      \"Minimize errors by limiting human intervention\": {\n        \"caused_by\": [\n          \"Eliminate repetitive workflows to avoid interruptions\",\n          \"Standardize and streamline the retrieval process\"\n        ],\n        \"enables\": [\n          \"Enable users to focus on core responsibilities\"\n        ]\n      },\n      \"Enable users to focus on core responsibilities\": {\n        \"caused_by\": [\n          \"Automate document retrieval to reduce manual tasks\",\n          \"Minimize errors by limiting human intervention\"\n        ]\n      }\n    }\n  },\n  \"process\": [\n    \"integrate_elements_by_structure()\",\n    \"maximize_signal_strength_per_component()\",\n    \"enforce_internal_consistency()\",\n    \"formulate_compact_nucleus()\"\n  ],\n  \"output\": {\n    \"condensed_nucleus\": [\n      \"Automate document retrieval to reduce manual, repetitive work\",\n      \"Standardize and streamline the document retrieval workflow\",\n      \"Eliminate repetitive steps and prevent workflow interruptions\",\n      \"Minimize human errors by reducing direct manual intervention\",\n      \"Enable focus on core work by removing document handling overhead\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Redundancy Annihilation & Signal Clarification\n\nScrutinize the `condensed_nucleus` to annihilate *all* semantic or structural redundancy, overlap, ambiguity, and verbose phrasing; enforce maximal conciseness, ensuring every remaining component carries unique, indispensable value and crystal-clear signal. Execute as\n\n`{role=signal_clarifier; input=condensed_nucleus:any; process=[scan_for_all_redundancy_ambiguity(), eliminate_overlaps_verbose_phrasing(), compress_phrasing_to_minimal_potent_form(), validate_uniqueness_clarity()]; output={minimal_signal_core:any}}`", "step": "g", "title": "Redundancy Annihilation & Signal Clarification", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"role\": \"nucleus_synthesizer\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Automate document retrieval to reduce manual tasks\",\n      \"Standardize and streamline the retrieval process\",\n      \"Minimize errors by limiting human intervention\",\n      \"Eliminate repetitive workflows to avoid interruptions\",\n      \"Enable users to focus on core responsibilities\"\n    ],\n    \"causal_nexus\": {\n      \"Automate document retrieval to reduce manual tasks\": {\n        \"enables\": [\n          \"Eliminate repetitive workflows to avoid interruptions\",\n          \"Standardize and streamline the retrieval process\"\n        ],\n        \"dependency_of\": [\n          \"Enable users to focus on core responsibilities\"\n        ]\n      },\n      \"Eliminate repetitive workflows to avoid interruptions\": {\n        \"caused_by\": [\n          \"Automate document retrieval to reduce manual tasks\"\n        ],\n        \"enables\": [\n          \"Minimize errors by limiting human intervention\"\n        ]\n      },\n      \"Standardize and streamline the retrieval process\": {\n        \"caused_by\": [\n          \"Automate document retrieval to reduce manual tasks\"\n        ],\n        \"enables\": [\n          \"Minimize errors by limiting human intervention\"\n        ]\n      },\n      \"Minimize errors by limiting human intervention\": {\n        \"caused_by\": [\n          \"Eliminate repetitive workflows to avoid interruptions\",\n          \"Standardize and streamline the retrieval process\"\n        ],\n        \"enables\": [\n          \"Enable users to focus on core responsibilities\"\n        ]\n      },\n      \"Enable users to focus on core responsibilities\": {\n        \"caused_by\": [\n          \"Automate document retrieval to reduce manual tasks\",\n          \"Minimize errors by limiting human intervention\"\n        ]\n      }\n    }\n  },\n  \"process\": [\n    \"integrate_elements_by_structure()\",\n    \"maximize_signal_strength_per_component()\",\n    \"enforce_internal_consistency()\",\n    \"formulate_compact_nucleus()\"\n  ],\n  \"output\": {\n    \"condensed_nucleus\": [\n      \"Automate document retrieval to reduce manual, repetitive work\",\n      \"Standardize and streamline the document retrieval workflow\",\n      \"Eliminate repetitive steps and prevent workflow interruptions\",\n      \"Minimize human errors by reducing direct manual intervention\",\n      \"Enable focus on core work by removing document handling overhead\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"minimal_signal_core\": [\n    \"Automate document retrieval to reduce manual work\",\n    \"Streamline and standardize retrieval workflows\",\n    \"Eliminate repetitive steps and workflow interruptions\",\n    \"Minimize errors by reducing manual intervention\",\n    \"Free users to focus on core responsibilities\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Universal Logic Abstraction\n\nAbstract the `minimal_signal_core`, neutralizing domain-specific language and reframing using universally applicable concepts and logical structures, while rigorously preserving the core intent and actionability across any context. Execute as\n\n`{role=universal_abstractor; input=minimal_signal_core:any; process=[neutralize_domain_specifics(), reframe_as_universal_concepts_logic(), validate_logic_preservation_across_contexts(), ensure_representation_agnostic_structure()]; output={universal_core_logic:any}}`", "step": "h", "title": "Universal Logic Abstraction", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"minimal_signal_core\": [\n    \"Automate document retrieval to reduce manual work\",\n    \"Streamline and standardize retrieval workflows\",\n    \"Eliminate repetitive steps and workflow interruptions\",\n    \"Minimize errors by reducing manual intervention\",\n    \"Free users to focus on core responsibilities\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"universal_core_logic\": [\n    \"Automate repetitive information access tasks to reduce human effort\",\n    \"Standardize and streamline task workflows for consistency\",\n    \"Remove redundant actions to prevent process disruptions\",\n    \"Minimize errors by decreasing dependence on manual actions\",\n    \"Enable individuals to concentrate on primary objectives\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Linguistic Potency Injection\n\nReforge the `universal_core_logic` using high-potency, action-driven, maximally precise imperatives and descriptors; eliminate passive or neutral language, amplifying structural clarity, actionability, and conceptual force without increasing length. Execute as\n\n`{role=linguistic_intensifier; input=universal_core_logic:any; process=[select_high_potency_verbs_descriptors(), eliminate_passive_neutral_ambiguous_language(), maximize_precision_impact_per_word(), amplify_actionability_via_phrasing()]; output={potent_core:any}}`", "step": "i", "title": "Linguistic Potency Injection", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"universal_core_logic\": [\n    \"Automate repetitive information access tasks to reduce human effort\",\n    \"Standardize and streamline task workflows for consistency\",\n    \"Remove redundant actions to prevent process disruptions\",\n    \"Minimize errors by decreasing dependence on manual actions\",\n    \"Enable individuals to concentrate on primary objectives\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"potent_core\": [\n    \"Automate document retrieval; slash manual effort and time waste.\",\n    \"Enforce uniform workflows; guarantee consistent, reliable outcomes.\",\n    \"Eradicate redundant steps; eliminate workflow interruptions.\",\n    \"Reduce human errors; cut manual intervention to a minimum.\",\n    \"Refocus user energy on core, high-impact responsibilities.\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Axiomatic Vectorization for One-Line\n\nDetermine the optimal linear sequence for the `potent_core` components based on the `causal_nexus`; select the highest-density, unambiguous connectors (e.g., ';', '->', '|') to vectorize the structure ready for single-line collapse. Execute as\n\n`{role=one_line_vectorizer; input={potent_core:any, causal_nexus:dict}; process=[determine_optimal_axiomatic_linear_sequence(), select_high_density_unambiguous_connectors(), vectorize_components_for_linearity()]; output={linear_vector_plan:dict(sequence:list, connectors:list)}}`", "step": "j", "title": "Axiomatic Vectorization for One-Line", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"potent_core\": [\n    \"Automate document retrieval; slash manual effort and time waste.\",\n    \"Enforce uniform workflows; guarantee consistent, reliable outcomes.\",\n    \"Eradicate redundant steps; eliminate workflow interruptions.\",\n    \"Reduce human errors; cut manual intervention to a minimum.\",\n    \"Refocus user energy on core, high-impact responsibilities.\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"linear_vector_plan\": {\n    \"sequence\": [\n      \"Automate document retrieval\",\n      \"Eradicate redundant steps\",\n      \"Enforce uniform workflows\",\n      \"Reduce human errors\",\n      \"Refocus user energy on core, high-impact responsibilities\"\n    ],\n    \"connectors\": [\n      \"->\",\n      \";\",\n      \";\",\n      \";\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Semantic Compression & Symbolization\n\nApply maximal semantic compression to the planned linear vector, substituting ultra-concise symbols, abbreviations, or keywords where possible without ambiguity; encapsulate the full essential meaning within the absolute minimum footprint, ensuring self-contained decodability. Execute as\n\n`{role=semantic_compressor; input=linear_vector_plan:dict; process=[identify_compressible_concepts(), substitute_minimal_unambiguous_symbols(), maximize_meaning_per_unit(), validate_decodability_self_sufficiency()]; output={compressed_vector_elements:list}}`", "step": "k", "title": "Semantic Compression & Symbolization", "input": "{\n  \"initial_prompt\": \"transform this into a list of 5-6 points (for the \\\"purpose\\\" section), each item should be less than 80 characters: '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n## Purpose\\n\\n- Reduce retrieval time and prevent workflow interruptions.\\n- Eliminate repetitive navigation and workflow interruptions.\\n- Avoid inconsistencies by streamlining organization and workflow.\\n- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.\\n- Reduce manual effort and errors\\n- Eliminate repetitive navigation and downloads\\n- Maintain workflow continuity\\n- Enable focus on core responsibilities\\n- Streamline and standardize retrieval processes\\n- Automate baseline document and file metadata collection.\\n- Minimizing manual intervention reduces error and prevents workflow breakage\\n- Standardization of processes yields consistency and reliability\\n- Automate document retrieval using Python and Selenium\\n- Eliminate repetitive manual navigation and downloads\\n- Standardize and streamline retrieval processes\\n- Collect documents and file metadata automatically\\n- Minimize errors by reducing manual intervention\\n- Enable users to focus on core responsibilities'''\",\n  \"linear_vector_plan\": {\n    \"sequence\": [\n      \"Automate document retrieval\",\n      \"Eradicate redundant steps\",\n      \"Enforce uniform workflows\",\n      \"Reduce human errors\",\n      \"Refocus user energy on core, high-impact responsibilities\"\n    ],\n    \"connectors\": [\n      \"->\",\n      \";\",\n      \";\",\n      \";\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"compressed_vector_elements\": [\n    \"AutoDocGet\",\n    \"NoRedund\",\n    \"WFUniform\",\n    \"ErrMin\",\n    \"User→Core\"\n  ]\n}", "cost": 0.0}}}], "total_cost": 0.0}