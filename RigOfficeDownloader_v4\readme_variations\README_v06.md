# RigOfficeDownloader

Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.

## Key Features
- **Three-Stage Workflow**: Document selection → File selection → Download
- **Metadata Preservation**: Structured naming with revision/case numbers
- **Interactive Review**: Markdown tables for manual inclusion flags
- **Smart Organization**: Automatic subfolder creation via naming patterns
- **Filter System**: Pattern-based inclusion/exclusion rules
- **Browser Automation**: Smart waiting strategies and session management

## Workflow Process
```python
1. Fetch Documents       # Scrape metadata → <rig>-a-docs.json
2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true
3. Import Selections     # Update JSON with user choices
4. Fetch Files           # Get file listings → <rig>-b-files.json
5. Export Files MD       # <rig>-b-files.md - Set item_download=true
6. Import File Choices   # Update file selections
7. Download Files        # Auto-organized with subfolder support
```

## Configuration (CONFIG Section)
```python
{
    "rig_number": "R0000.020",  # Target rig ID
    "search_urls": [            # Predefined search templates
        "https://rigdoc.nov.com/search/rigsearch?q=...",
        "https://rigdoc.nov.com/search/rigsearch?q=..."
    ],
    "filters": [                # Sequential processing rules
        {
            "type": "docs",     # Apply to documents/files
            "pattern": "*DRILL*FLOOR*",  # Glob-style matching
            "field": "item_include",     # Field to modify
            "value": True       # Set True/False based on match
        }
    ]
}
```

## Setup & Usage
```bash
# Initialize environment
py_venv_init.bat
py_venv_pip_install.bat

# Run modes
RigOfficeDownloader-v4.py [rig_number] [mode]

Modes:
--auto         # Full automation
--interactive  # Step-by-step control
--config       # Modify search templates/filters
```

## Key Configuration Patterns
- **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)
- **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)
- **File Types**: Auto-prioritize PDFs with `*.pdf` pattern

## Requirements
- Chrome Browser + ChromeDriver
- Active RigDoc credentials
- Python 3.8+ with dependencies from requirements.txt
- Network access to rigdoc.nov.com

## Advanced Features
- **Path Sanitization**: Auto-clean special chars while preserving /
- **Deduplication**: Hash-based conflict resolution
- **Field Ordering**: Customizable JSON/Markdown columns
- **Smart Scrolling**: Progressive page loading detection

> Reduces documentation prep time by 60-75% compared to manual retrieval
> Version 4.0 | Active development with subfolder support
