:: =============================================================================
:: cmd: initialize
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
SET "__init_path__=%CD%"
SET "__base_name__=%~n0"


:: =============================================================================
:: default: settings
:: =============================================================================
SET "__output_file__=git_history_graph.md"
SET "__max_commits__=100"
SET "__include_files__=true"


:: =============================================================================
:: args: process command line arguments
:: =============================================================================
:ProcessArgs
    IF "%~1"=="" GOTO VerifyGitRepo
    IF "%~1"=="-h" GOTO ShowHelp
    IF "%~1"=="--help" GOTO ShowHelp

    IF "%~1"=="-o" (
        IF NOT "%~2"=="" SET "__output_file__=%~2"
        SHIFT
        SHIFT
        GOTO ProcessArgs
    )

    IF "%~1"=="--output" (
        IF NOT "%~2"=="" SET "__output_file__=%~2"
        SHIFT
        SHIFT
        GOTO ProcessArgs
    )

    IF "%~1"=="-n" (
        IF NOT "%~2"=="" SET "__max_commits__=%~2"
        SHIFT
        SHIFT
        GOTO ProcessArgs
    )

    IF "%~1"=="--max-commits" (
        IF NOT "%~2"=="" SET "__max_commits__=%~2"
        SHIFT
        SHIFT
        GOTO ProcessArgs
    )

    IF "%~1"=="--no-files" (
        SET "__include_files__=false"
        SHIFT
        GOTO ProcessArgs
    )

    SHIFT
    GOTO ProcessArgs


:: =============================================================================
:: help: show usage information
:: =============================================================================
:ShowHelp
    ECHO Git Commit History Graph Generator
    ECHO.
    ECHO Usage: %__base_name__%.bat [options]
    ECHO.
    ECHO Options:
    ECHO   -h, --help             Show this help message
    ECHO   -o, --output FILE      Specify output file (default: git_history_graph.md)
    ECHO   -n, --max-commits NUM  Limit to NUM commits (default: 100)
    ECHO   --no-files             Exclude file changes from the output
    GOTO ExitScript


:: =============================================================================
:: git: verify repository
:: =============================================================================
:VerifyGitRepo
    :: Get current date/time
    FOR /f "tokens=2 delims==" %%a IN ('wmic OS Get localdatetime /value') DO SET "__datetime__=%%a"
    SET "__year__=!__datetime__:~0,4!"
    SET "__month__=!__datetime__:~4,2!"
    SET "__day__=!__datetime__:~6,2!"
    SET "__hour__=!__datetime__:~8,2!"
    SET "__minute__=!__datetime__:~10,2!"
    SET "__second__=!__datetime__:~12,2!"
    SET "__current_date__=!__year__!-!__month__!-!__day__! !__hour__!:!__minute__!:!__second__!"

    :: Check if we're in a git repository
    git rev-parse --is-inside-work-tree > nul 2>&1
    IF ERRORLEVEL 1 (
        ECHO Error: Not a git repository
        GOTO ExitScript
    )
    GOTO CreateHistoryFile


:: =============================================================================
:: file: create history file
:: =============================================================================
:CreateHistoryFile
    :: Start with header
    ECHO # Git Commit History Graph > "!__output_file__!"
    ECHO. >> "!__output_file__!"
    ECHO Generated on: !__current_date__! >> "!__output_file__!"
    ECHO. >> "!__output_file__!"

    SET "__commit_count__=0"
    GOTO ProcessCommits


:: =============================================================================
:: git: process commits
:: =============================================================================
:ProcessCommits
    :: Get all commits (limited by max commits)
    FOR /f "tokens=*" %%c IN ('git log --max-count=!__max_commits__! --pretty^=format:"%%H"') DO (
        SET /a __commit_count__+=1

        :: Get commit info
        FOR /f "tokens=*" %%i IN ('git show -s --format^="%%h - (%%ar) %%s - %%an%%d" "%%c"') DO (
            SET "__info__=%%i"
        )

        :: Add commit info to file
        ECHO ## Commit: !__info__! >> "!__output_file__!"
        ECHO. >> "!__output_file__!"

        IF "!__include_files__!"=="true" (
            ECHO ### Changed Files: >> "!__output_file__!"
            ECHO. >> "!__output_file__!"
            ECHO ```diff >> "!__output_file__!"

            :: Get and process file changes for this commit
            git show --name-status --format="" "%%c" > temp_files.txt

            :: Process each changed file
            FOR /f "tokens=1,*" %%a IN (temp_files.txt) DO (
                SET "__status__=%%a"
                SET "__filename__=%%b"

                :: Skip empty lines
                IF NOT "!__status__!"=="" (
                    :: Convert status code to appropriate diff format
                    IF "!__status__!"=="A" (
                        ECHO + ^(Added^) !__filename__! >> "!__output_file__!"
                    ) ELSE IF "!__status__!"=="M" (
                        ECHO ~ ^(Modified^) !__filename__! >> "!__output_file__!"
                    ) ELSE IF "!__status__!"=="D" (
                        ECHO - ^(Deleted^) !__filename__! >> "!__output_file__!"
                    ) ELSE IF "!__status__:~0,1!"=="R" (
                        ECHO - ^(Renamed from^) !__filename__! >> "!__output_file__!"
                    ) ELSE IF "!__status__:~0,1!"=="C" (
                        ECHO + ^(Copied^) !__filename__! >> "!__output_file__!"
                    ) ELSE (
                        ECHO ? ^(Unknown: !__status__!^) !__filename__! >> "!__output_file__!"
                    )
                )
            )

            ECHO ``` >> "!__output_file__!"

            DEL temp_files.txt 2>nul
        )

        :: Add visual separator for branch graphing
        ECHO. >> "!__output_file__!"
        ECHO ^| >> "!__output_file__!"
        ECHO ^| >> "!__output_file__!"
        ECHO. >> "!__output_file__!"
    )
    GOTO ReportCompletion


:: =============================================================================
:: report: show completion message
:: =============================================================================
:ReportCompletion
    ECHO Git history graph with !__commit_count__! commits has been saved to !__output_file__!
    ECHO.
    ECHO Usage:
    ECHO   Open !__output_file__! in any Markdown viewer to see the commit history


:: =============================================================================
:: cmd: exit
:: =============================================================================
:ExitScript
    ENDLOCAL
    EXIT /B