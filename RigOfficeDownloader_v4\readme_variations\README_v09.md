## Intent
To automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.

## Key Project Aspects

### Primary Problem
Engineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck—it's the manual preparation process.

### Solution Approach
A Python-based utility that:
1. Automatically scrapes document metadata from RigOffice
2. Extracts file information from those documents
3. Downloads and organizes selected files based on user criteria

### Current State
Functional working prototype that:
- Uses a 3-step workflow (document metadata → file metadata → download)
- Stores intermediate results in JSON format
- Allows user intervention between steps
- Provides progress feedback

### Critical Next Steps
1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches
2. **Implement file hash checking** to prevent redundant downloads
3. **Improve progress visibility** during lengthy scraping operations

### Core Technical Pattern
A single-file, modular approach using:
- Selenium for browser automation
- JSON for data storage
- Three-stage processing with user control points
- Incremental updates to avoid redundant work

### Key Success Metrics
- Reduce documentation gathering time by 75%+
- Ensure reliable retrieval of required documentation
- Organize files in a way that streamlines workflow
- Support both broad searches (by rig number) and targeted searches (by specific documents)
