{"initial_prompt": "An abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][dissolve][motion_blur][surreal][cool_to_warm_gradient][no human figures]\n\nA single abstract, morphing geometric shape (such as a smooth, flowing ribbon) is centered against an undefined, subtly gradient background. The camera glides directly downward alongside the shape throughout the entire 5 seconds. The shape continuously morphs as it descends, symbolizing transformation. At the top, colors are exclusively cool blues and cyans with soft, diffuse lighting. As the descent progresses, both the shape and background gradually shift toward intense warm reds and oranges, with lighting increasing in saturation and contrast. Dynamic motion blur trails accentuate the movement, intensifying as the downward motion speeds up in the latter half. The animation ends as the shape reaches maximum vividness and warmth at the bottom.\n\nA single morphing geometric ribbon floats centered on a soft gradient background, [dolly:down] alongside the ribbon as it transforms and descends. Gradually shift colors from cool blues and cyans to vivid reds and oranges, [lighting_change] increases saturation and contrast, [morph] enhances ribbon transformation, dynamic motion blur trails intensify as speed increases toward the bottom.\n\nAs the ribbon and invisible camera move downward, both ribbon and background colors transition gradually: cool blue-cyan at the top to vivid red-orange at the bottom. Lighting grows noticeably more saturated and higher in contrast by the final second. A subtle motion blur trail appears behind the ribbon, low at first, increasing in intensity as downward speed increases, peaking during the final second. Video ends with the ribbon and background at maximum color warmth, saturation, and contrast.", "sequence_id": "sequence-0224-a+0194-c+0224-a+0194-c", "results": [{"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "An abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][dissolve][motion_blur][surreal][cool_to_warm_gradient][no human figures]\n\nA single abstract, morphing geometric shape (such as a smooth, flowing ribbon) is centered against an undefined, subtly gradient background. The camera glides directly downward alongside the shape throughout the entire 5 seconds. The shape continuously morphs as it descends, symbolizing transformation. At the top, colors are exclusively cool blues and cyans with soft, diffuse lighting. As the descent progresses, both the shape and background gradually shift toward intense warm reds and oranges, with lighting increasing in saturation and contrast. Dynamic motion blur trails accentuate the movement, intensifying as the downward motion speeds up in the latter half. The animation ends as the shape reaches maximum vividness and warmth at the bottom.\n\nA single morphing geometric ribbon floats centered on a soft gradient background, [dolly:down] alongside the ribbon as it transforms and descends. Gradually shift colors from cool blues and cyans to vivid reds and oranges, [lighting_change] increases saturation and contrast, [morph] enhances ribbon transformation, dynamic motion blur trails intensify as speed increases toward the bottom.\n\nAs the ribbon and invisible camera move downward, both ribbon and background colors transition gradually: cool blue-cyan at the top to vivid red-orange at the bottom. Lighting grows noticeably more saturated and higher in contrast by the final second. A subtle motion blur trail appears behind the ribbon, low at first, increasing in intensity as downward speed increases, peaking during the final second. Video ends with the ribbon and background at maximum color warmth, saturation, and contrast.", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: abstract_gradient_bg, subject: single morphing geometric ribbon, color: cool_blue_cyan_to_vivid_red_orange_gradient, action: continuous_morph_and_descent, camera: dolly_down_locked_on_ribbon, lighting: soft_diffuse_to_high_saturation_high_contrast, effect: gradual_motion_blur_trail_intensifies_with_speed, time: 5s, transitions: seamless_cool_to_warm, style: surreal_no_humans, post_processing: dissolve, no extraneous elements\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"An abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][dissolve][motion_blur][surreal][cool_to_warm_gradient][no human figures]\\n\\nA single abstract, morphing geometric shape (such as a smooth, flowing ribbon) is centered against an undefined, subtly gradient background. The camera glides directly downward alongside the shape throughout the entire 5 seconds. The shape continuously morphs as it descends, symbolizing transformation. At the top, colors are exclusively cool blues and cyans with soft, diffuse lighting. As the descent progresses, both the shape and background gradually shift toward intense warm reds and oranges, with lighting increasing in saturation and contrast. Dynamic motion blur trails accentuate the movement, intensifying as the downward motion speeds up in the latter half. The animation ends as the shape reaches maximum vividness and warmth at the bottom.\\n\\nA single morphing geometric ribbon floats centered on a soft gradient background, [dolly:down] alongside the ribbon as it transforms and descends. Gradually shift colors from cool blues and cyans to vivid reds and oranges, [lighting_change] increases saturation and contrast, [morph] enhances ribbon transformation, dynamic motion blur trails intensify as speed increases toward the bottom.\\n\\nAs the ribbon and invisible camera move downward, both ribbon and background colors transition gradually: cool blue-cyan at the top to vivid red-orange at the bottom. Lighting grows noticeably more saturated and higher in contrast by the final second. A subtle motion blur trail appears behind the ribbon, low at first, increasing in intensity as downward speed increases, peaking during the final second. Video ends with the ribbon and background at maximum color warmth, saturation, and contrast.\",\n  \"optimized_video_prompt\": \"scene: abstract_gradient_bg, subject: single morphing geometric ribbon, color: cool_blue_cyan_to_vivid_red_orange_gradient, action: continuous_morph_and_descent, camera: dolly_down_locked_on_ribbon, lighting: soft_diffuse_to_high_saturation_high_contrast, effect: gradual_motion_blur_trail_intensifies_with_speed, time: 5s, transitions: seamless_cool_to_warm, style: surreal_no_humans, post_processing: dissolve, no extraneous elements\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"A single morphing geometric ribbon floats centered on a smooth abstract gradient background, [dolly:down] tracking its descent as it transforms; [cool blue/cyan to vivid red/orange gradient], [lighting_change:soft_to_vivid], [morph], [motion_blur] intensifies as speed increases, ending in maximum warmth and saturation; surreal, no human figures, [dissolve].\"\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"An abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][dissolve][motion_blur][surreal][cool_to_warm_gradient][no human figures]\\n\\nA single abstract, morphing geometric shape (such as a smooth, flowing ribbon) is centered against an undefined, subtly gradient background. The camera glides directly downward alongside the shape throughout the entire 5 seconds. The shape continuously morphs as it descends, symbolizing transformation. At the top, colors are exclusively cool blues and cyans with soft, diffuse lighting. As the descent progresses, both the shape and background gradually shift toward intense warm reds and oranges, with lighting increasing in saturation and contrast. Dynamic motion blur trails accentuate the movement, intensifying as the downward motion speeds up in the latter half. The animation ends as the shape reaches maximum vividness and warmth at the bottom.\\n\\nA single morphing geometric ribbon floats centered on a soft gradient background, [dolly:down] alongside the ribbon as it transforms and descends. Gradually shift colors from cool blues and cyans to vivid reds and oranges, [lighting_change] increases saturation and contrast, [morph] enhances ribbon transformation, dynamic motion blur trails intensify as speed increases toward the bottom.\\n\\nAs the ribbon and invisible camera move downward, both ribbon and background colors transition gradually: cool blue-cyan at the top to vivid red-orange at the bottom. Lighting grows noticeably more saturated and higher in contrast by the final second. A subtle motion blur trail appears behind the ribbon, low at first, increasing in intensity as downward speed increases, peaking during the final second. Video ends with the ribbon and background at maximum color warmth, saturation, and contrast.\",\n  \"runwayml_prompt\": \"A single morphing geometric ribbon floats centered on a smooth abstract gradient background, [dolly:down] tracking its descent as it transforms; [cool blue/cyan to vivid red/orange gradient], [lighting_change:soft_to_vivid], [morph], [motion_blur] intensifies as speed increases, ending in maximum warmth and saturation; surreal, no human figures, [dissolve].\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: abstract_gradient_bg, subject: single morphing geometric ribbon(centered), action: continuous morph, camera: dolly_down tracking, color: gradient_cool_blue_cyan_to_vivid_red_orange, lighting: transition_soft_diffuse_to_vivid_high_saturation_high_contrast, effect: motion_blur_trail_intensifies_with_speed, effect: dissolve, style: surreal, constraint: no_humans, duration: 5s, end_state: max_warmth_saturation_contrast\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"An abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][dissolve][motion_blur][surreal][cool_to_warm_gradient][no human figures]\\n\\nA single abstract, morphing geometric shape (such as a smooth, flowing ribbon) is centered against an undefined, subtly gradient background. The camera glides directly downward alongside the shape throughout the entire 5 seconds. The shape continuously morphs as it descends, symbolizing transformation. At the top, colors are exclusively cool blues and cyans with soft, diffuse lighting. As the descent progresses, both the shape and background gradually shift toward intense warm reds and oranges, with lighting increasing in saturation and contrast. Dynamic motion blur trails accentuate the movement, intensifying as the downward motion speeds up in the latter half. The animation ends as the shape reaches maximum vividness and warmth at the bottom.\\n\\nA single morphing geometric ribbon floats centered on a soft gradient background, [dolly:down] alongside the ribbon as it transforms and descends. Gradually shift colors from cool blues and cyans to vivid reds and oranges, [lighting_change] increases saturation and contrast, [morph] enhances ribbon transformation, dynamic motion blur trails intensify as speed increases toward the bottom.\\n\\nAs the ribbon and invisible camera move downward, both ribbon and background colors transition gradually: cool blue-cyan at the top to vivid red-orange at the bottom. Lighting grows noticeably more saturated and higher in contrast by the final second. A subtle motion blur trail appears behind the ribbon, low at first, increasing in intensity as downward speed increases, peaking during the final second. Video ends with the ribbon and background at maximum color warmth, saturation, and contrast.\",\n  \"optimized_video_prompt\": \"scene: abstract_gradient_bg, subject: single morphing geometric ribbon(centered), action: continuous morph, camera: dolly_down tracking, color: gradient_cool_blue_cyan_to_vivid_red_orange, lighting: transition_soft_diffuse_to_vivid_high_saturation_high_contrast, effect: motion_blur_trail_intensifies_with_speed, effect: dissolve, style: surreal, constraint: no_humans, duration: 5s, end_state: max_warmth_saturation_contrast\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"A single morphing geometric ribbon floats centered on a soft gradient background, [dolly:down] tracking the ribbon's descent as it continuously morphs. Colors and lighting shift from cool blue-cyan and soft diffuse to vivid red-orange and high contrast. [morph][lighting_change][motion_blur] intensifies as speed increases, [dissolve] effect, surreal style, no humans.\"\n}", "cost": 0.0}}}], "total_cost": 0.0}