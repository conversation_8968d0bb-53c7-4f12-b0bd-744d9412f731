{"initial_prompt": "consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '# RigOfficeDownloader\n\n## Overview\nEngineers waste hours manually gathering technical documentation. This tool streamlines the process via a structured workflow with human checkpoints, preserving engineering time for high-value tasks.\n\n## Key Features\n- **Three-Stage Workflow**: Document metadata → File metadata → Download\n- **Interactive Control**: Menu-driven execution & Markdown review interfaces\n- **Smart Organization**: Metadata-driven naming & automatic subfolders\n- **Filter Chains**: Glob patterns for auto-selection (e.g., `*G000*`, `*VOID*`)\n- **Audit Trail**: JSON/Markdown records for full traceability\n\n## Workflow\n1. **Fetch Documents**: Scrape metadata → `<rig>-a-docs.json`\n2. **Select Docs**: Edit `item_include` in `<rig>-a-docs.md`\n3. **Fetch Files**: Get file data for selected docs → `<rig>-b-files.json`\n4. **Select Files**: Mark `item_download` in `<rig>-b-files.md`\n5. **Download**: Auto-organize files with subfolder support\n\n## Setup\n```bash\n1. py_venv_init.bat  # Create Python environment\n2. RigOfficeDownloader-v4.bat  # Launch application\n```\n\n## Usage\n```\nInteractive Menu:\n[0] Change rig/search URLs  [4] Import doc selections\n[1] Configure filters       [5] Fetch files\n[2] Fetch docs              [6] Export files to MD\n[3] Export docs to MD       [7] Import file selections\n[8] Download files          [9] Advanced config\n```\n\n## Directory Structure\n```\noutputs/\n├── data/           # Metadata (JSON/MD)\n└── downloads/      # Organized files\n    └─ <rig>/\n       ├─ Subfolder/  # From item_generated_name\n       └─ File.pdf\n```\n\n## Technical Stack\n- **Core**: Python 3.6+, Selenium, BeautifulSoup\n- **Data**: JSON storage ↔ Markdown interfaces\n- **Automation**: Smart waiting strategies & deduplication\n\n## Benefits\n| Manual Process          | Automated Solution       |\n|-------------------------|--------------------------|\n| 4-6 hours per project   | <1 hour                  |\n| Error-prone organization| Metadata-driven structure|\n| No audit trail          | Full JSON/MD traceability\n\n## Version History\n- v1: Basic retrieval | v2: User selection\n- v3: Error handling | v4: Filters & subfolders\n'", "sequence_id": "sequence-0107", "results": [{"instruction": "# Optimal Apex Instruction Sequence Synthesis\n\nYour function is not selection, paraphrasing, or basic fusion—instead, rigorously analyze multiple input instruction sequences, each targeting an identical ultimate outcome yet employing divergent phrasing, structure, and logic. Disassemble all input sequences into their logical phases (e.g., deconstruction, prioritization, condensation, validation) and extract the single most potent, precise, and impactful elements—both linguistic and structural—from every variant in each phase. Relentlessly resolve all semantic, structural, and logical conflicts; harmonize differences while annihilating redundancy. Synthesize these apex components into a *new, singular, maximally optimized stepwise progression* (target: 10–15 steps) that represents the highest-caliber logic, clarity, universality, and actionable guidance possible—transcending and unifying all sources. Enforce seamless logical flow, structural elegance, and linguistic precision, and strictly validate that the output meets every foundational directive and constraint for clarity, utility, irreducibility, and LLM/system generality. Execute as\n\n`{role=apex_sequence_synthesizer; input=[input_sequences:list[list[dict]]]; process=[disassemble_to_logical_phases(), cross-analyze_phase_intents_and_language(), extract_peak_elements_per_phase(metrics=['clarity','precision','logic','impact','universality']), fuse uniquely potent and effective steps(), resolve conflicts harmonize by principle(), architect unified logical stepwise progression(target_steps=10-15), intensify final clarity, actionability, and system-agnostic value(), validate against all directives and baseline constraints(), finalize only if maximal yield, clarity, and generality are confirmed()]; output={synthesized_optimal_sequence:list[dict]}}`", "step": "a", "title": "Optimal Apex Instruction Sequence Synthesis", "input": "consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '# RigOfficeDownloader\n\n## Overview\nEngineers waste hours manually gathering technical documentation. This tool streamlines the process via a structured workflow with human checkpoints, preserving engineering time for high-value tasks.\n\n## Key Features\n- **Three-Stage Workflow**: Document metadata → File metadata → Download\n- **Interactive Control**: Menu-driven execution & Markdown review interfaces\n- **Smart Organization**: Metadata-driven naming & automatic subfolders\n- **Filter Chains**: Glob patterns for auto-selection (e.g., `*G000*`, `*VOID*`)\n- **Audit Trail**: JSON/Markdown records for full traceability\n\n## Workflow\n1. **Fetch Documents**: Scrape metadata → `<rig>-a-docs.json`\n2. **Select Docs**: Edit `item_include` in `<rig>-a-docs.md`\n3. **Fetch Files**: Get file data for selected docs → `<rig>-b-files.json`\n4. **Select Files**: Mark `item_download` in `<rig>-b-files.md`\n5. **Download**: Auto-organize files with subfolder support\n\n## Setup\n```bash\n1. py_venv_init.bat  # Create Python environment\n2. RigOfficeDownloader-v4.bat  # Launch application\n```\n\n## Usage\n```\nInteractive Menu:\n[0] Change rig/search URLs  [4] Import doc selections\n[1] Configure filters       [5] Fetch files\n[2] Fetch docs              [6] Export files to MD\n[3] Export docs to MD       [7] Import file selections\n[8] Download files          [9] Advanced config\n```\n\n## Directory Structure\n```\noutputs/\n├── data/           # Metadata (JSON/MD)\n└── downloads/      # Organized files\n    └─ <rig>/\n       ├─ Subfolder/  # From item_generated_name\n       └─ File.pdf\n```\n\n## Technical Stack\n- **Core**: Python 3.6+, Selenium, BeautifulSoup\n- **Data**: JSON storage ↔ Markdown interfaces\n- **Automation**: Smart waiting strategies & deduplication\n\n## Benefits\n| Manual Process          | Automated Solution       |\n|-------------------------|--------------------------|\n| 4-6 hours per project   | <1 hour                  |\n| Error-prone organization| Metadata-driven structure|\n| No audit trail          | Full JSON/MD traceability\n\n## Version History\n- v1: Basic retrieval | v2: User selection\n- v3: Error handling | v4: Filters & subfolders\n'", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"synthesized_optimal_sequence\": [\n    {\n      \"step\": 1,\n      \"instruction\": \"Automate technical document collection with a three-stage workflow: extract document metadata, retrieve related file metadata, and download selected files.\"\n    },\n    {\n      \"step\": 2,\n      \"instruction\": \"Utilize menu-driven and Markdown interfaces for interactive review, enabling users to efficiently select documents and files at designated checkpoints.\"\n    },\n    {\n      \"step\": 3,\n      \"instruction\": \"Organize downloaded files automatically using metadata-based naming conventions and structured subfolders, ensuring logical and consistent storage.\"\n    },\n    {\n      \"step\": 4,\n      \"instruction\": \"Apply customizable filter chains (e.g., glob patterns) to streamline auto-selection of relevant documents and files, reducing manual effort.\"\n    },\n    {\n      \"step\": 5,\n      \"instruction\": \"Maintain a complete audit trail with JSON and Markdown records at every workflow stage, supporting traceability and reproducibility.\"\n    },\n    {\n      \"step\": 6,\n      \"instruction\": \"Benefit from significant time savings, error reduction, and robust documentation by leveraging a Python-based stack with smart automation, filtering, and deduplication.\"\n    }\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Foundational Penetration & Axiomatic Extraction\n\nYour imperative is not surface analysis but radical penetration: Dissect any input artifact into its irreducible atomic elements (concepts, logic, directives, data) *and* simultaneously extract its governing generative principles or axioms, annihilating all contextual noise and non-essential framing to reveal the core structural and semantic bedrock. Execute as\n\n`{role=axiomatic_penetrator; input=source_artifact:any; process=[dissect_to_atomic_elements(), extract_generative_axioms(), purge_contextual_noise(), catalog_raw_constructs()]; output={core_constructs:dict(elements:list, principles:list)}}`", "step": "b", "title": "Foundational Penetration & Axiomatic Extraction", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '# RigOfficeDownloader\\n\\n## Overview\\nEngineers waste hours manually gathering technical documentation. This tool streamlines the process via a structured workflow with human checkpoints, preserving engineering time for high-value tasks.\\n\\n## Key Features\\n- **Three-Stage Workflow**: Document metadata \\u2192 File metadata \\u2192 Download\\n- **Interactive Control**: Menu-driven execution & Markdown review interfaces\\n- **Smart Organization**: Metadata-driven naming & automatic subfolders\\n- **Filter Chains**: Glob patterns for auto-selection (e.g., `*G000*`, `*VOID*`)\\n- **Audit Trail**: JSON/Markdown records for full traceability\\n\\n## Workflow\\n1. **Fetch Documents**: Scrape metadata \\u2192 `<rig>-a-docs.json`\\n2. **Select Docs**: Edit `item_include` in `<rig>-a-docs.md`\\n3. **Fetch Files**: Get file data for selected docs \\u2192 `<rig>-b-files.json`\\n4. **Select Files**: Mark `item_download` in `<rig>-b-files.md`\\n5. **Download**: Auto-organize files with subfolder support\\n\\n## Setup\\n```bash\\n1. py_venv_init.bat  # Create Python environment\\n2. RigOfficeDownloader-v4.bat  # Launch application\\n```\\n\\n## Usage\\n```\\nInteractive Menu:\\n[0] Change rig/search URLs  [4] Import doc selections\\n[1] Configure filters       [5] Fetch files\\n[2] Fetch docs              [6] Export files to MD\\n[3] Export docs to MD       [7] Import file selections\\n[8] Download files          [9] Advanced config\\n```\\n\\n## Directory Structure\\n```\\noutputs/\\n\\u251c\\u2500\\u2500 data/           # Metadata (JSON/MD)\\n\\u2514\\u2500\\u2500 downloads/      # Organized files\\n    \\u2514\\u2500 <rig>/\\n       \\u251c\\u2500 Subfolder/  # From item_generated_name\\n       \\u2514\\u2500 File.pdf\\n```\\n\\n## Technical Stack\\n- **Core**: Python 3.6+, Selenium, BeautifulSoup\\n- **Data**: JSON storage \\u2194 Markdown interfaces\\n- **Automation**: Smart waiting strategies & deduplication\\n\\n## Benefits\\n| Manual Process          | Automated Solution       |\\n|-------------------------|--------------------------|\\n| 4-6 hours per project   | <1 hour                  |\\n| Error-prone organization| Metadata-driven structure|\\n| No audit trail          | Full JSON/MD traceability\\n\\n## Version History\\n- v1: Basic retrieval | v2: User selection\\n- v3: Error handling | v4: Filters & subfolders\\n'\",\n  \"synthesized_optimal_sequence\": [\n    {\n      \"step\": 1,\n      \"instruction\": \"Automate technical document collection with a three-stage workflow: extract document metadata, retrieve related file metadata, and download selected files.\"\n    },\n    {\n      \"step\": 2,\n      \"instruction\": \"Utilize menu-driven and Markdown interfaces for interactive review, enabling users to efficiently select documents and files at designated checkpoints.\"\n    },\n    {\n      \"step\": 3,\n      \"instruction\": \"Organize downloaded files automatically using metadata-based naming conventions and structured subfolders, ensuring logical and consistent storage.\"\n    },\n    {\n      \"step\": 4,\n      \"instruction\": \"Apply customizable filter chains (e.g., glob patterns) to streamline auto-selection of relevant documents and files, reducing manual effort.\"\n    },\n    {\n      \"step\": 5,\n      \"instruction\": \"Maintain a complete audit trail with JSON and Markdown records at every workflow stage, supporting traceability and reproducibility.\"\n    },\n    {\n      \"step\": 6,\n      \"instruction\": \"Benefit from significant time savings, error reduction, and robust documentation by leveraging a Python-based stack with smart automation, filtering, and deduplication.\"\n    }\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"core_constructs\": {\n    \"elements\": [\n      \"Three-stage workflow: document metadata extraction, file metadata retrieval, file download\",\n      \"Interactive selection via menu and Markdown interfaces\",\n      \"Metadata-driven file naming and subfolder organization\",\n      \"Customizable filter chains using glob patterns\",\n      \"Audit trail via JSON and Markdown at each workflow stage\",\n      \"Python-based automation with deduplication and smart waiting\"\n    ],\n    \"principles\": [\n      \"Automate repetitive and error-prone manual processes to reclaim engineering time\",\n      \"Segment complex tasks into discrete, checkpointed workflow stages for control and verification\",\n      \"Leverage metadata to drive consistency, organization, and selection logic\",\n      \"Enable user-guided selection and review within automated frameworks to balance efficiency and oversight\",\n      \"Ensure traceability and accountability through comprehensive stage-wise records\",\n      \"Implement extensible, modular automation for technical data operations\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Telos Crystallization & Objective Definition\n\nAnalyze the extracted core constructs to distill and crystallize the input's singular, non-negotiable core purpose ('telos'); formulate this ultimate objective with axiomatic precision, establishing the gravitational center for all subsequent synthesis. Execute as\n\n`{role=telos_definer; input=core_constructs:dict; process=[derive_ultimate_objective_from_elements_principles(), validate_objective_singularity(), express_unambiguous_telos_statement()]; output={core_telos:str, supporting_constructs:dict}}`", "step": "c", "title": "Telos Crystallization & Objective Definition", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '# RigOfficeDownloader\\n\\n## Overview\\nEngineers waste hours manually gathering technical documentation. This tool streamlines the process via a structured workflow with human checkpoints, preserving engineering time for high-value tasks.\\n\\n## Key Features\\n- **Three-Stage Workflow**: Document metadata \\u2192 File metadata \\u2192 Download\\n- **Interactive Control**: Menu-driven execution & Markdown review interfaces\\n- **Smart Organization**: Metadata-driven naming & automatic subfolders\\n- **Filter Chains**: Glob patterns for auto-selection (e.g., `*G000*`, `*VOID*`)\\n- **Audit Trail**: JSON/Markdown records for full traceability\\n\\n## Workflow\\n1. **Fetch Documents**: Scrape metadata \\u2192 `<rig>-a-docs.json`\\n2. **Select Docs**: Edit `item_include` in `<rig>-a-docs.md`\\n3. **Fetch Files**: Get file data for selected docs \\u2192 `<rig>-b-files.json`\\n4. **Select Files**: Mark `item_download` in `<rig>-b-files.md`\\n5. **Download**: Auto-organize files with subfolder support\\n\\n## Setup\\n```bash\\n1. py_venv_init.bat  # Create Python environment\\n2. RigOfficeDownloader-v4.bat  # Launch application\\n```\\n\\n## Usage\\n```\\nInteractive Menu:\\n[0] Change rig/search URLs  [4] Import doc selections\\n[1] Configure filters       [5] Fetch files\\n[2] Fetch docs              [6] Export files to MD\\n[3] Export docs to MD       [7] Import file selections\\n[8] Download files          [9] Advanced config\\n```\\n\\n## Directory Structure\\n```\\noutputs/\\n\\u251c\\u2500\\u2500 data/           # Metadata (JSON/MD)\\n\\u2514\\u2500\\u2500 downloads/      # Organized files\\n    \\u2514\\u2500 <rig>/\\n       \\u251c\\u2500 Subfolder/  # From item_generated_name\\n       \\u2514\\u2500 File.pdf\\n```\\n\\n## Technical Stack\\n- **Core**: Python 3.6+, Selenium, BeautifulSoup\\n- **Data**: JSON storage \\u2194 Markdown interfaces\\n- **Automation**: Smart waiting strategies & deduplication\\n\\n## Benefits\\n| Manual Process          | Automated Solution       |\\n|-------------------------|--------------------------|\\n| 4-6 hours per project   | <1 hour                  |\\n| Error-prone organization| Metadata-driven structure|\\n| No audit trail          | Full JSON/MD traceability\\n\\n## Version History\\n- v1: Basic retrieval | v2: User selection\\n- v3: Error handling | v4: Filters & subfolders\\n'\",\n  \"core_constructs\": {\n    \"elements\": [\n      \"Three-stage workflow: document metadata extraction, file metadata retrieval, file download\",\n      \"Interactive selection via menu and Markdown interfaces\",\n      \"Metadata-driven file naming and subfolder organization\",\n      \"Customizable filter chains using glob patterns\",\n      \"Audit trail via JSON and Markdown at each workflow stage\",\n      \"Python-based automation with deduplication and smart waiting\"\n    ],\n    \"principles\": [\n      \"Automate repetitive and error-prone manual processes to reclaim engineering time\",\n      \"Segment complex tasks into discrete, checkpointed workflow stages for control and verification\",\n      \"Leverage metadata to drive consistency, organization, and selection logic\",\n      \"Enable user-guided selection and review within automated frameworks to balance efficiency and oversight\",\n      \"Ensure traceability and accountability through comprehensive stage-wise records\",\n      \"Implement extensible, modular automation for technical data operations\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"core_telos\": \"Automate and streamline the collection, selection, and organization of technical documentation to reclaim engineering time, guarantee accuracy and traceability, and optimize workflow efficiency through structured, metadata-driven processes with human oversight.\",\n  \"supporting_constructs\": {\n    \"elements\": [\n      \"Three-stage workflow: document metadata extraction, file metadata retrieval, file download\",\n      \"Interactive selection via menu and Markdown interfaces\",\n      \"Metadata-driven file naming and subfolder organization\",\n      \"Customizable filter chains using glob patterns\",\n      \"Audit trail via JSON and Markdown at each workflow stage\",\n      \"Python-based automation with deduplication and smart waiting\"\n    ],\n    \"principles\": [\n      \"Automate repetitive and error-prone manual processes to reclaim engineering time\",\n      \"Segment complex tasks into discrete, checkpointed workflow stages for control and verification\",\n      \"Leverage metadata to drive consistency, organization, and selection logic\",\n      \"Enable user-guided selection and review within automated frameworks to balance efficiency and oversight\",\n      \"Ensure traceability and accountability through comprehensive stage-wise records\",\n      \"Implement extensible, modular automation for technical data operations\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Critical Essence Prioritization\n\nEvaluate each supporting construct strictly by its indispensable contribution to the `core_telos`; ruthlessly isolate and rank only the absolute highest-impact, non-negotiable essence vectors required to fulfill the objective, discarding all sub-critical elements. Execute as\n\n`{role=essence_prioritizer; input={core_telos:str, supporting_constructs:dict}; process=[assess_contribution_to_telos(), rank_by_critical_impact_necessity(), enforce_criticality_threshold(), isolate_core_essence_vectors()]; output={critical_essence:list}}`", "step": "d", "title": "Critical Essence Prioritization", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '# RigOfficeDownloader\\n\\n## Overview\\nEngineers waste hours manually gathering technical documentation. This tool streamlines the process via a structured workflow with human checkpoints, preserving engineering time for high-value tasks.\\n\\n## Key Features\\n- **Three-Stage Workflow**: Document metadata \\u2192 File metadata \\u2192 Download\\n- **Interactive Control**: Menu-driven execution & Markdown review interfaces\\n- **Smart Organization**: Metadata-driven naming & automatic subfolders\\n- **Filter Chains**: Glob patterns for auto-selection (e.g., `*G000*`, `*VOID*`)\\n- **Audit Trail**: JSON/Markdown records for full traceability\\n\\n## Workflow\\n1. **Fetch Documents**: Scrape metadata \\u2192 `<rig>-a-docs.json`\\n2. **Select Docs**: Edit `item_include` in `<rig>-a-docs.md`\\n3. **Fetch Files**: Get file data for selected docs \\u2192 `<rig>-b-files.json`\\n4. **Select Files**: Mark `item_download` in `<rig>-b-files.md`\\n5. **Download**: Auto-organize files with subfolder support\\n\\n## Setup\\n```bash\\n1. py_venv_init.bat  # Create Python environment\\n2. RigOfficeDownloader-v4.bat  # Launch application\\n```\\n\\n## Usage\\n```\\nInteractive Menu:\\n[0] Change rig/search URLs  [4] Import doc selections\\n[1] Configure filters       [5] Fetch files\\n[2] Fetch docs              [6] Export files to MD\\n[3] Export docs to MD       [7] Import file selections\\n[8] Download files          [9] Advanced config\\n```\\n\\n## Directory Structure\\n```\\noutputs/\\n\\u251c\\u2500\\u2500 data/           # Metadata (JSON/MD)\\n\\u2514\\u2500\\u2500 downloads/      # Organized files\\n    \\u2514\\u2500 <rig>/\\n       \\u251c\\u2500 Subfolder/  # From item_generated_name\\n       \\u2514\\u2500 File.pdf\\n```\\n\\n## Technical Stack\\n- **Core**: Python 3.6+, Selenium, BeautifulSoup\\n- **Data**: JSON storage \\u2194 Markdown interfaces\\n- **Automation**: Smart waiting strategies & deduplication\\n\\n## Benefits\\n| Manual Process          | Automated Solution       |\\n|-------------------------|--------------------------|\\n| 4-6 hours per project   | <1 hour                  |\\n| Error-prone organization| Metadata-driven structure|\\n| No audit trail          | Full JSON/MD traceability\\n\\n## Version History\\n- v1: Basic retrieval | v2: User selection\\n- v3: Error handling | v4: Filters & subfolders\\n'\",\n  \"core_telos\": \"Automate and streamline the collection, selection, and organization of technical documentation to reclaim engineering time, guarantee accuracy and traceability, and optimize workflow efficiency through structured, metadata-driven processes with human oversight.\",\n  \"supporting_constructs\": {\n    \"elements\": [\n      \"Three-stage workflow: document metadata extraction, file metadata retrieval, file download\",\n      \"Interactive selection via menu and Markdown interfaces\",\n      \"Metadata-driven file naming and subfolder organization\",\n      \"Customizable filter chains using glob patterns\",\n      \"Audit trail via JSON and Markdown at each workflow stage\",\n      \"Python-based automation with deduplication and smart waiting\"\n    ],\n    \"principles\": [\n      \"Automate repetitive and error-prone manual processes to reclaim engineering time\",\n      \"Segment complex tasks into discrete, checkpointed workflow stages for control and verification\",\n      \"Leverage metadata to drive consistency, organization, and selection logic\",\n      \"Enable user-guided selection and review within automated frameworks to balance efficiency and oversight\",\n      \"Ensure traceability and accountability through comprehensive stage-wise records\",\n      \"Implement extensible, modular automation for technical data operations\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"critical_essence\": [\n    \"Structured, checkpointed workflow: Automate document and file collection through staged processes with human oversight to ensure control and verification.\",\n    \"Metadata-driven selection and organization: Leverage metadata for consistent, accurate filtering, naming, and subfolder organization of documentation.\",\n    \"Traceability and accountability: Maintain comprehensive, stage-wise JSON/Markdown audit trails to guarantee accuracy and enable full process traceability.\",\n    \"Interactive user control: Integrate user-guided selection and review at critical workflow points to balance automation with essential human validation.\",\n    \"Python-based automation with reliability features: Use robust automation (including deduplication and smart waiting) to maximize efficiency and minimize errors in technical data workflows.\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Causal Nexus Mapping\n\nArchitect a minimal, coherent map of the inherent causal relationships, logical dependencies, and procedural sequences connecting the `critical_essence` elements, constructing the essential operational or conceptual flow required by the `core_telos`. Execute as\n\n`{role=causal_mapper; input={critical_essence:list, core_telos:str}; process=[map_causal_links_dependencies(), structure_minimal_flow_graph(), validate_structural_necessity_for_telos()]; output={causal_nexus:dict}}`", "step": "e", "title": "Causal Nexus Mapping", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '# RigOfficeDownloader\\n\\n## Overview\\nEngineers waste hours manually gathering technical documentation. This tool streamlines the process via a structured workflow with human checkpoints, preserving engineering time for high-value tasks.\\n\\n## Key Features\\n- **Three-Stage Workflow**: Document metadata \\u2192 File metadata \\u2192 Download\\n- **Interactive Control**: Menu-driven execution & Markdown review interfaces\\n- **Smart Organization**: Metadata-driven naming & automatic subfolders\\n- **Filter Chains**: Glob patterns for auto-selection (e.g., `*G000*`, `*VOID*`)\\n- **Audit Trail**: JSON/Markdown records for full traceability\\n\\n## Workflow\\n1. **Fetch Documents**: Scrape metadata \\u2192 `<rig>-a-docs.json`\\n2. **Select Docs**: Edit `item_include` in `<rig>-a-docs.md`\\n3. **Fetch Files**: Get file data for selected docs \\u2192 `<rig>-b-files.json`\\n4. **Select Files**: Mark `item_download` in `<rig>-b-files.md`\\n5. **Download**: Auto-organize files with subfolder support\\n\\n## Setup\\n```bash\\n1. py_venv_init.bat  # Create Python environment\\n2. RigOfficeDownloader-v4.bat  # Launch application\\n```\\n\\n## Usage\\n```\\nInteractive Menu:\\n[0] Change rig/search URLs  [4] Import doc selections\\n[1] Configure filters       [5] Fetch files\\n[2] Fetch docs              [6] Export files to MD\\n[3] Export docs to MD       [7] Import file selections\\n[8] Download files          [9] Advanced config\\n```\\n\\n## Directory Structure\\n```\\noutputs/\\n\\u251c\\u2500\\u2500 data/           # Metadata (JSON/MD)\\n\\u2514\\u2500\\u2500 downloads/      # Organized files\\n    \\u2514\\u2500 <rig>/\\n       \\u251c\\u2500 Subfolder/  # From item_generated_name\\n       \\u2514\\u2500 File.pdf\\n```\\n\\n## Technical Stack\\n- **Core**: Python 3.6+, Selenium, BeautifulSoup\\n- **Data**: JSON storage \\u2194 Markdown interfaces\\n- **Automation**: Smart waiting strategies & deduplication\\n\\n## Benefits\\n| Manual Process          | Automated Solution       |\\n|-------------------------|--------------------------|\\n| 4-6 hours per project   | <1 hour                  |\\n| Error-prone organization| Metadata-driven structure|\\n| No audit trail          | Full JSON/MD traceability\\n\\n## Version History\\n- v1: Basic retrieval | v2: User selection\\n- v3: Error handling | v4: Filters & subfolders\\n'\",\n  \"critical_essence\": [\n    \"Structured, checkpointed workflow: Automate document and file collection through staged processes with human oversight to ensure control and verification.\",\n    \"Metadata-driven selection and organization: Leverage metadata for consistent, accurate filtering, naming, and subfolder organization of documentation.\",\n    \"Traceability and accountability: Maintain comprehensive, stage-wise JSON/Markdown audit trails to guarantee accuracy and enable full process traceability.\",\n    \"Interactive user control: Integrate user-guided selection and review at critical workflow points to balance automation with essential human validation.\",\n    \"Python-based automation with reliability features: Use robust automation (including deduplication and smart waiting) to maximize efficiency and minimize errors in technical data workflows.\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"causal_mapper\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Structured, checkpointed workflow: Automate document and file collection through staged processes with human oversight to ensure control and verification.\",\n      \"Metadata-driven selection and organization: Leverage metadata for consistent, accurate filtering, naming, and subfolder organization of documentation.\",\n      \"Traceability and accountability: Maintain comprehensive, stage-wise JSON/Markdown audit trails to guarantee accuracy and enable full process traceability.\",\n      \"Interactive user control: Integrate user-guided selection and review at critical workflow points to balance automation with essential human validation.\",\n      \"Python-based automation with reliability features: Use robust automation (including deduplication and smart waiting) to maximize efficiency and minimize errors in technical data workflows.\"\n    ],\n    \"core_telos\": \"Minimize engineer time spent gathering and organizing technical documentation while ensuring accuracy, control, and full traceability.\"\n  },\n  \"process\": [\n    \"map_causal_links_dependencies()\",\n    \"structure_minimal_flow_graph()\",\n    \"validate_structural_necessity_for_telos()\"\n  ],\n  \"output\": {\n    \"causal_nexus\": {\n      \"nodes\": [\n        \"Python-based automation with reliability features\",\n        \"Structured, checkpointed workflow\",\n        \"Interactive user control\",\n        \"Metadata-driven selection and organization\",\n        \"Traceability and accountability\"\n      ],\n      \"edges\": [\n        {\n          \"from\": \"Python-based automation with reliability features\",\n          \"to\": \"Structured, checkpointed workflow\",\n          \"relation\": \"enables efficient implementation of\"\n        },\n        {\n          \"from\": \"Structured, checkpointed workflow\",\n          \"to\": \"Interactive user control\",\n          \"relation\": \"incorporates at checkpoints for human verification\"\n        },\n        {\n          \"from\": \"Structured, checkpointed workflow\",\n          \"to\": \"Metadata-driven selection and organization\",\n          \"relation\": \"relies on for systematic filtering, naming, and subfoldering\"\n        },\n        {\n          \"from\": \"Structured, checkpointed workflow\",\n          \"to\": \"Traceability and accountability\",\n          \"relation\": \"generates artifacts for tracking each process stage\"\n        },\n        {\n          \"from\": \"Metadata-driven selection and organization\",\n          \"to\": \"Interactive user control\",\n          \"relation\": \"provides meaningful selection options for users\"\n        },\n        {\n          \"from\": \"Interactive user control\",\n          \"to\": \"Traceability and accountability\",\n          \"relation\": \"validates and documents human decisions in the audit trail\"\n        }\n      ],\n      \"essential_path\": [\n        \"Python-based automation with reliability features\",\n        \"Structured, checkpointed workflow\",\n        [\n          \"Interactive user control\",\n          \"Metadata-driven selection and organization\"\n        ],\n        \"Traceability and accountability\"\n      ],\n      \"telos_validation\": \"All critical_essence elements are structurally necessary and causally interconnected to maximize automation and reliability, ensure user oversight for accuracy, enable organized outputs, and maintain a verifiable audit trail, thus minimizing engineering manual effort while retaining control and traceability.\"\n    }\n  }\n}", "cost": 0.0}}}, {"instruction": "# Condensed Nucleus Synthesis\n\nFuse the `critical_essence` elements according to the `causal_nexus` into an ultra-condensed, logically ordered semantic nucleus; maximize signal strength per component while preserving full actionable power and internal consistency within a compact structural representation. Execute as\n\n`{role=nucleus_synthesizer; input={critical_essence:list, causal_nexus:dict}; process=[integrate_elements_by_structure(), maximize_signal_strength_per_component(), enforce_internal_consistency(), formulate_compact_nucleus()]; output={condensed_nucleus:any}}`", "step": "f", "title": "Condensed Nucleus Synthesis", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '# RigOfficeDownloader\\n\\n## Overview\\nEngineers waste hours manually gathering technical documentation. This tool streamlines the process via a structured workflow with human checkpoints, preserving engineering time for high-value tasks.\\n\\n## Key Features\\n- **Three-Stage Workflow**: Document metadata \\u2192 File metadata \\u2192 Download\\n- **Interactive Control**: Menu-driven execution & Markdown review interfaces\\n- **Smart Organization**: Metadata-driven naming & automatic subfolders\\n- **Filter Chains**: Glob patterns for auto-selection (e.g., `*G000*`, `*VOID*`)\\n- **Audit Trail**: JSON/Markdown records for full traceability\\n\\n## Workflow\\n1. **Fetch Documents**: Scrape metadata \\u2192 `<rig>-a-docs.json`\\n2. **Select Docs**: Edit `item_include` in `<rig>-a-docs.md`\\n3. **Fetch Files**: Get file data for selected docs \\u2192 `<rig>-b-files.json`\\n4. **Select Files**: Mark `item_download` in `<rig>-b-files.md`\\n5. **Download**: Auto-organize files with subfolder support\\n\\n## Setup\\n```bash\\n1. py_venv_init.bat  # Create Python environment\\n2. RigOfficeDownloader-v4.bat  # Launch application\\n```\\n\\n## Usage\\n```\\nInteractive Menu:\\n[0] Change rig/search URLs  [4] Import doc selections\\n[1] Configure filters       [5] Fetch files\\n[2] Fetch docs              [6] Export files to MD\\n[3] Export docs to MD       [7] Import file selections\\n[8] Download files          [9] Advanced config\\n```\\n\\n## Directory Structure\\n```\\noutputs/\\n\\u251c\\u2500\\u2500 data/           # Metadata (JSON/MD)\\n\\u2514\\u2500\\u2500 downloads/      # Organized files\\n    \\u2514\\u2500 <rig>/\\n       \\u251c\\u2500 Subfolder/  # From item_generated_name\\n       \\u2514\\u2500 File.pdf\\n```\\n\\n## Technical Stack\\n- **Core**: Python 3.6+, Selenium, BeautifulSoup\\n- **Data**: JSON storage \\u2194 Markdown interfaces\\n- **Automation**: Smart waiting strategies & deduplication\\n\\n## Benefits\\n| Manual Process          | Automated Solution       |\\n|-------------------------|--------------------------|\\n| 4-6 hours per project   | <1 hour                  |\\n| Error-prone organization| Metadata-driven structure|\\n| No audit trail          | Full JSON/MD traceability\\n\\n## Version History\\n- v1: Basic retrieval | v2: User selection\\n- v3: Error handling | v4: Filters & subfolders\\n'\",\n  \"role\": \"causal_mapper\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Structured, checkpointed workflow: Automate document and file collection through staged processes with human oversight to ensure control and verification.\",\n      \"Metadata-driven selection and organization: Leverage metadata for consistent, accurate filtering, naming, and subfolder organization of documentation.\",\n      \"Traceability and accountability: Maintain comprehensive, stage-wise JSON/Markdown audit trails to guarantee accuracy and enable full process traceability.\",\n      \"Interactive user control: Integrate user-guided selection and review at critical workflow points to balance automation with essential human validation.\",\n      \"Python-based automation with reliability features: Use robust automation (including deduplication and smart waiting) to maximize efficiency and minimize errors in technical data workflows.\"\n    ],\n    \"core_telos\": \"Minimize engineer time spent gathering and organizing technical documentation while ensuring accuracy, control, and full traceability.\"\n  },\n  \"process\": [\n    \"map_causal_links_dependencies()\",\n    \"structure_minimal_flow_graph()\",\n    \"validate_structural_necessity_for_telos()\"\n  ],\n  \"output\": {\n    \"causal_nexus\": {\n      \"nodes\": [\n        \"Python-based automation with reliability features\",\n        \"Structured, checkpointed workflow\",\n        \"Interactive user control\",\n        \"Metadata-driven selection and organization\",\n        \"Traceability and accountability\"\n      ],\n      \"edges\": [\n        {\n          \"from\": \"Python-based automation with reliability features\",\n          \"to\": \"Structured, checkpointed workflow\",\n          \"relation\": \"enables efficient implementation of\"\n        },\n        {\n          \"from\": \"Structured, checkpointed workflow\",\n          \"to\": \"Interactive user control\",\n          \"relation\": \"incorporates at checkpoints for human verification\"\n        },\n        {\n          \"from\": \"Structured, checkpointed workflow\",\n          \"to\": \"Metadata-driven selection and organization\",\n          \"relation\": \"relies on for systematic filtering, naming, and subfoldering\"\n        },\n        {\n          \"from\": \"Structured, checkpointed workflow\",\n          \"to\": \"Traceability and accountability\",\n          \"relation\": \"generates artifacts for tracking each process stage\"\n        },\n        {\n          \"from\": \"Metadata-driven selection and organization\",\n          \"to\": \"Interactive user control\",\n          \"relation\": \"provides meaningful selection options for users\"\n        },\n        {\n          \"from\": \"Interactive user control\",\n          \"to\": \"Traceability and accountability\",\n          \"relation\": \"validates and documents human decisions in the audit trail\"\n        }\n      ],\n      \"essential_path\": [\n        \"Python-based automation with reliability features\",\n        \"Structured, checkpointed workflow\",\n        [\n          \"Interactive user control\",\n          \"Metadata-driven selection and organization\"\n        ],\n        \"Traceability and accountability\"\n      ],\n      \"telos_validation\": \"All critical_essence elements are structurally necessary and causally interconnected to maximize automation and reliability, ensure user oversight for accuracy, enable organized outputs, and maintain a verifiable audit trail, thus minimizing engineering manual effort while retaining control and traceability.\"\n    }\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"nucleus_synthesizer\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Structured, checkpointed workflow: Automate document and file collection through staged processes with human oversight to ensure control and verification.\",\n      \"Metadata-driven selection and organization: Leverage metadata for consistent, accurate filtering, naming, and subfolder organization of documentation.\",\n      \"Traceability and accountability: Maintain comprehensive, stage-wise JSON/Markdown audit trails to guarantee accuracy and enable full process traceability.\",\n      \"Interactive user control: Integrate user-guided selection and review at critical workflow points to balance automation with essential human validation.\",\n      \"Python-based automation with reliability features: Use robust automation (including deduplication and smart waiting) to maximize efficiency and minimize errors in technical data workflows.\"\n    ],\n    \"causal_nexus\": {\n      \"nodes\": [\n        \"Python-based automation with reliability features\",\n        \"Structured, checkpointed workflow\",\n        \"Interactive user control\",\n        \"Metadata-driven selection and organization\",\n        \"Traceability and accountability\"\n      ],\n      \"edges\": [\n        {\n          \"from\": \"Python-based automation with reliability features\",\n          \"to\": \"Structured, checkpointed workflow\",\n          \"relation\": \"enables efficient implementation of\"\n        },\n        {\n          \"from\": \"Structured, checkpointed workflow\",\n          \"to\": \"Interactive user control\",\n          \"relation\": \"incorporates at checkpoints for human verification\"\n        },\n        {\n          \"from\": \"Structured, checkpointed workflow\",\n          \"to\": \"Metadata-driven selection and organization\",\n          \"relation\": \"relies on for systematic filtering, naming, and subfoldering\"\n        },\n        {\n          \"from\": \"Structured, checkpointed workflow\",\n          \"to\": \"Traceability and accountability\",\n          \"relation\": \"generates artifacts for tracking each process stage\"\n        },\n        {\n          \"from\": \"Metadata-driven selection and organization\",\n          \"to\": \"Interactive user control\",\n          \"relation\": \"provides meaningful selection options for users\"\n        },\n        {\n          \"from\": \"Interactive user control\",\n          \"to\": \"Traceability and accountability\",\n          \"relation\": \"validates and documents human decisions in the audit trail\"\n        }\n      ],\n      \"essential_path\": [\n        \"Python-based automation with reliability features\",\n        \"Structured, checkpointed workflow\",\n        [\n          \"Interactive user control\",\n          \"Metadata-driven selection and organization\"\n        ],\n        \"Traceability and accountability\"\n      ],\n      \"telos_validation\": \"All critical_essence elements are structurally necessary and causally interconnected to maximize automation and reliability, ensure user oversight for accuracy, enable organized outputs, and maintain a verifiable audit trail, thus minimizing engineering manual effort while retaining control and traceability.\"\n    }\n  },\n  \"process\": [\n    \"integrate_elements_by_structure()\",\n    \"maximize_signal_strength_per_component()\",\n    \"enforce_internal_consistency()\",\n    \"formulate_compact_nucleus()\"\n  ],\n  \"output\": {\n    \"condensed_nucleus\": [\n      \"Employ Python automation with reliability mechanisms (deduplication, smart waits) to drive an efficient, staged workflow for technical document collection.\",\n      \"Implement a structured, checkpointed process that incorporates human reviews at defined steps to ensure control, accuracy, and verification.\",\n      \"Leverage document and file metadata for systematic filtering, naming, and subfolder organization, enabling precise and predictable results.\",\n      \"Provide interactive user interfaces for guided selection and validation at critical workflow points, balancing automation with essential human oversight.\",\n      \"Maintain comprehensive JSON/Markdown audit trails at every stage to guarantee full traceability, accountability, and transparency of the process.\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Redundancy Annihilation & Signal Clarification\n\nScrutinize the `condensed_nucleus` to annihilate *all* semantic or structural redundancy, overlap, ambiguity, and verbose phrasing; enforce maximal conciseness, ensuring every remaining component carries unique, indispensable value and crystal-clear signal. Execute as\n\n`{role=signal_clarifier; input=condensed_nucleus:any; process=[scan_for_all_redundancy_ambiguity(), eliminate_overlaps_verbose_phrasing(), compress_phrasing_to_minimal_potent_form(), validate_uniqueness_clarity()]; output={minimal_signal_core:any}}`", "step": "g", "title": "Redundancy Annihilation & Signal Clarification", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '# RigOfficeDownloader\\n\\n## Overview\\nEngineers waste hours manually gathering technical documentation. This tool streamlines the process via a structured workflow with human checkpoints, preserving engineering time for high-value tasks.\\n\\n## Key Features\\n- **Three-Stage Workflow**: Document metadata \\u2192 File metadata \\u2192 Download\\n- **Interactive Control**: Menu-driven execution & Markdown review interfaces\\n- **Smart Organization**: Metadata-driven naming & automatic subfolders\\n- **Filter Chains**: Glob patterns for auto-selection (e.g., `*G000*`, `*VOID*`)\\n- **Audit Trail**: JSON/Markdown records for full traceability\\n\\n## Workflow\\n1. **Fetch Documents**: Scrape metadata \\u2192 `<rig>-a-docs.json`\\n2. **Select Docs**: Edit `item_include` in `<rig>-a-docs.md`\\n3. **Fetch Files**: Get file data for selected docs \\u2192 `<rig>-b-files.json`\\n4. **Select Files**: Mark `item_download` in `<rig>-b-files.md`\\n5. **Download**: Auto-organize files with subfolder support\\n\\n## Setup\\n```bash\\n1. py_venv_init.bat  # Create Python environment\\n2. RigOfficeDownloader-v4.bat  # Launch application\\n```\\n\\n## Usage\\n```\\nInteractive Menu:\\n[0] Change rig/search URLs  [4] Import doc selections\\n[1] Configure filters       [5] Fetch files\\n[2] Fetch docs              [6] Export files to MD\\n[3] Export docs to MD       [7] Import file selections\\n[8] Download files          [9] Advanced config\\n```\\n\\n## Directory Structure\\n```\\noutputs/\\n\\u251c\\u2500\\u2500 data/           # Metadata (JSON/MD)\\n\\u2514\\u2500\\u2500 downloads/      # Organized files\\n    \\u2514\\u2500 <rig>/\\n       \\u251c\\u2500 Subfolder/  # From item_generated_name\\n       \\u2514\\u2500 File.pdf\\n```\\n\\n## Technical Stack\\n- **Core**: Python 3.6+, Selenium, BeautifulSoup\\n- **Data**: JSON storage \\u2194 Markdown interfaces\\n- **Automation**: Smart waiting strategies & deduplication\\n\\n## Benefits\\n| Manual Process          | Automated Solution       |\\n|-------------------------|--------------------------|\\n| 4-6 hours per project   | <1 hour                  |\\n| Error-prone organization| Metadata-driven structure|\\n| No audit trail          | Full JSON/MD traceability\\n\\n## Version History\\n- v1: Basic retrieval | v2: User selection\\n- v3: Error handling | v4: Filters & subfolders\\n'\",\n  \"role\": \"nucleus_synthesizer\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Structured, checkpointed workflow: Automate document and file collection through staged processes with human oversight to ensure control and verification.\",\n      \"Metadata-driven selection and organization: Leverage metadata for consistent, accurate filtering, naming, and subfolder organization of documentation.\",\n      \"Traceability and accountability: Maintain comprehensive, stage-wise JSON/Markdown audit trails to guarantee accuracy and enable full process traceability.\",\n      \"Interactive user control: Integrate user-guided selection and review at critical workflow points to balance automation with essential human validation.\",\n      \"Python-based automation with reliability features: Use robust automation (including deduplication and smart waiting) to maximize efficiency and minimize errors in technical data workflows.\"\n    ],\n    \"causal_nexus\": {\n      \"nodes\": [\n        \"Python-based automation with reliability features\",\n        \"Structured, checkpointed workflow\",\n        \"Interactive user control\",\n        \"Metadata-driven selection and organization\",\n        \"Traceability and accountability\"\n      ],\n      \"edges\": [\n        {\n          \"from\": \"Python-based automation with reliability features\",\n          \"to\": \"Structured, checkpointed workflow\",\n          \"relation\": \"enables efficient implementation of\"\n        },\n        {\n          \"from\": \"Structured, checkpointed workflow\",\n          \"to\": \"Interactive user control\",\n          \"relation\": \"incorporates at checkpoints for human verification\"\n        },\n        {\n          \"from\": \"Structured, checkpointed workflow\",\n          \"to\": \"Metadata-driven selection and organization\",\n          \"relation\": \"relies on for systematic filtering, naming, and subfoldering\"\n        },\n        {\n          \"from\": \"Structured, checkpointed workflow\",\n          \"to\": \"Traceability and accountability\",\n          \"relation\": \"generates artifacts for tracking each process stage\"\n        },\n        {\n          \"from\": \"Metadata-driven selection and organization\",\n          \"to\": \"Interactive user control\",\n          \"relation\": \"provides meaningful selection options for users\"\n        },\n        {\n          \"from\": \"Interactive user control\",\n          \"to\": \"Traceability and accountability\",\n          \"relation\": \"validates and documents human decisions in the audit trail\"\n        }\n      ],\n      \"essential_path\": [\n        \"Python-based automation with reliability features\",\n        \"Structured, checkpointed workflow\",\n        [\n          \"Interactive user control\",\n          \"Metadata-driven selection and organization\"\n        ],\n        \"Traceability and accountability\"\n      ],\n      \"telos_validation\": \"All critical_essence elements are structurally necessary and causally interconnected to maximize automation and reliability, ensure user oversight for accuracy, enable organized outputs, and maintain a verifiable audit trail, thus minimizing engineering manual effort while retaining control and traceability.\"\n    }\n  },\n  \"process\": [\n    \"integrate_elements_by_structure()\",\n    \"maximize_signal_strength_per_component()\",\n    \"enforce_internal_consistency()\",\n    \"formulate_compact_nucleus()\"\n  ],\n  \"output\": {\n    \"condensed_nucleus\": [\n      \"Employ Python automation with reliability mechanisms (deduplication, smart waits) to drive an efficient, staged workflow for technical document collection.\",\n      \"Implement a structured, checkpointed process that incorporates human reviews at defined steps to ensure control, accuracy, and verification.\",\n      \"Leverage document and file metadata for systematic filtering, naming, and subfolder organization, enabling precise and predictable results.\",\n      \"Provide interactive user interfaces for guided selection and validation at critical workflow points, balancing automation with essential human oversight.\",\n      \"Maintain comprehensive JSON/Markdown audit trails at every stage to guarantee full traceability, accountability, and transparency of the process.\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"signal_clarifier\",\n  \"input\": {\n    \"condensed_nucleus\": [\n      \"Employ Python automation with reliability mechanisms (deduplication, smart waits) to drive an efficient, staged workflow for technical document collection.\",\n      \"Implement a structured, checkpointed process that incorporates human reviews at defined steps to ensure control, accuracy, and verification.\",\n      \"Leverage document and file metadata for systematic filtering, naming, and subfolder organization, enabling precise and predictable results.\",\n      \"Provide interactive user interfaces for guided selection and validation at critical workflow points, balancing automation with essential human oversight.\",\n      \"Maintain comprehensive JSON/Markdown audit trails at every stage to guarantee full traceability, accountability, and transparency of the process.\"\n    ]\n  },\n  \"process\": [\n    \"scan_for_all_redundancy_ambiguity()\",\n    \"eliminate_overlaps_verbose_phrasing()\",\n    \"compress_phrasing_to_minimal_potent_form()\",\n    \"validate_uniqueness_clarity()\"\n  ],\n  \"output\": {\n    \"minimal_signal_core\": [\n      \"Automate staged technical document collection with reliable Python tools (deduplication, smart waits).\",\n      \"Integrate human review checkpoints for control and accuracy.\",\n      \"Use metadata for systematic filtering, naming, and subfolder organization.\",\n      \"Provide guided user interfaces for workflow validation.\",\n      \"Generate JSON/Markdown audit trails for complete traceability.\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Universal Logic Abstraction\n\nAbstract the `minimal_signal_core`, neutralizing domain-specific language and reframing using universally applicable concepts and logical structures, while rigorously preserving the core intent and actionability across any context. Execute as\n\n`{role=universal_abstractor; input=minimal_signal_core:any; process=[neutralize_domain_specifics(), reframe_as_universal_concepts_logic(), validate_logic_preservation_across_contexts(), ensure_representation_agnostic_structure()]; output={universal_core_logic:any}}`", "step": "h", "title": "Universal Logic Abstraction", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '# RigOfficeDownloader\\n\\n## Overview\\nEngineers waste hours manually gathering technical documentation. This tool streamlines the process via a structured workflow with human checkpoints, preserving engineering time for high-value tasks.\\n\\n## Key Features\\n- **Three-Stage Workflow**: Document metadata \\u2192 File metadata \\u2192 Download\\n- **Interactive Control**: Menu-driven execution & Markdown review interfaces\\n- **Smart Organization**: Metadata-driven naming & automatic subfolders\\n- **Filter Chains**: Glob patterns for auto-selection (e.g., `*G000*`, `*VOID*`)\\n- **Audit Trail**: JSON/Markdown records for full traceability\\n\\n## Workflow\\n1. **Fetch Documents**: Scrape metadata \\u2192 `<rig>-a-docs.json`\\n2. **Select Docs**: Edit `item_include` in `<rig>-a-docs.md`\\n3. **Fetch Files**: Get file data for selected docs \\u2192 `<rig>-b-files.json`\\n4. **Select Files**: Mark `item_download` in `<rig>-b-files.md`\\n5. **Download**: Auto-organize files with subfolder support\\n\\n## Setup\\n```bash\\n1. py_venv_init.bat  # Create Python environment\\n2. RigOfficeDownloader-v4.bat  # Launch application\\n```\\n\\n## Usage\\n```\\nInteractive Menu:\\n[0] Change rig/search URLs  [4] Import doc selections\\n[1] Configure filters       [5] Fetch files\\n[2] Fetch docs              [6] Export files to MD\\n[3] Export docs to MD       [7] Import file selections\\n[8] Download files          [9] Advanced config\\n```\\n\\n## Directory Structure\\n```\\noutputs/\\n\\u251c\\u2500\\u2500 data/           # Metadata (JSON/MD)\\n\\u2514\\u2500\\u2500 downloads/      # Organized files\\n    \\u2514\\u2500 <rig>/\\n       \\u251c\\u2500 Subfolder/  # From item_generated_name\\n       \\u2514\\u2500 File.pdf\\n```\\n\\n## Technical Stack\\n- **Core**: Python 3.6+, Selenium, BeautifulSoup\\n- **Data**: JSON storage \\u2194 Markdown interfaces\\n- **Automation**: Smart waiting strategies & deduplication\\n\\n## Benefits\\n| Manual Process          | Automated Solution       |\\n|-------------------------|--------------------------|\\n| 4-6 hours per project   | <1 hour                  |\\n| Error-prone organization| Metadata-driven structure|\\n| No audit trail          | Full JSON/MD traceability\\n\\n## Version History\\n- v1: Basic retrieval | v2: User selection\\n- v3: Error handling | v4: Filters & subfolders\\n'\",\n  \"role\": \"signal_clarifier\",\n  \"input\": {\n    \"condensed_nucleus\": [\n      \"Employ Python automation with reliability mechanisms (deduplication, smart waits) to drive an efficient, staged workflow for technical document collection.\",\n      \"Implement a structured, checkpointed process that incorporates human reviews at defined steps to ensure control, accuracy, and verification.\",\n      \"Leverage document and file metadata for systematic filtering, naming, and subfolder organization, enabling precise and predictable results.\",\n      \"Provide interactive user interfaces for guided selection and validation at critical workflow points, balancing automation with essential human oversight.\",\n      \"Maintain comprehensive JSON/Markdown audit trails at every stage to guarantee full traceability, accountability, and transparency of the process.\"\n    ]\n  },\n  \"process\": [\n    \"scan_for_all_redundancy_ambiguity()\",\n    \"eliminate_overlaps_verbose_phrasing()\",\n    \"compress_phrasing_to_minimal_potent_form()\",\n    \"validate_uniqueness_clarity()\"\n  ],\n  \"output\": {\n    \"minimal_signal_core\": [\n      \"Automate staged technical document collection with reliable Python tools (deduplication, smart waits).\",\n      \"Integrate human review checkpoints for control and accuracy.\",\n      \"Use metadata for systematic filtering, naming, and subfolder organization.\",\n      \"Provide guided user interfaces for workflow validation.\",\n      \"Generate JSON/Markdown audit trails for complete traceability.\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"universal_abstractor\",\n  \"input\": [\n    \"Automate staged technical document collection with reliable Python tools (deduplication, smart waits).\",\n    \"Integrate human review checkpoints for control and accuracy.\",\n    \"Use metadata for systematic filtering, naming, and subfolder organization.\",\n    \"Provide guided user interfaces for workflow validation.\",\n    \"Generate JSON/Markdown audit trails for complete traceability.\"\n  ],\n  \"process\": [\n    \"neutralize_domain_specifics()\",\n    \"reframe_as_universal_concepts_logic()\",\n    \"validate_logic_preservation_across_contexts()\",\n    \"ensure_representation_agnostic_structure()\"\n  ],\n  \"output\": {\n    \"universal_core_logic\": [\n      \"Implement a multi-stage process for resource collection, supporting reliable progression and error reduction.\",\n      \"Embed explicit checkpoints requiring human validation to maintain quality and oversight.\",\n      \"Utilize intrinsic data attributes for systematic selection, categorization, and organization of units.\",\n      \"Offer interactive mechanisms to guide and validate participant actions within the process.\",\n      \"Record comprehensive activity logs in an interoperable format to ensure end-to-end traceability.\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Linguistic Potency Injection\n\nReforge the `universal_core_logic` using high-potency, action-driven, maximally precise imperatives and descriptors; eliminate passive or neutral language, amplifying structural clarity, actionability, and conceptual force without increasing length. Execute as\n\n`{role=linguistic_intensifier; input=universal_core_logic:any; process=[select_high_potency_verbs_descriptors(), eliminate_passive_neutral_ambiguous_language(), maximize_precision_impact_per_word(), amplify_actionability_via_phrasing()]; output={potent_core:any}}`", "step": "i", "title": "Linguistic Potency Injection", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '# RigOfficeDownloader\\n\\n## Overview\\nEngineers waste hours manually gathering technical documentation. This tool streamlines the process via a structured workflow with human checkpoints, preserving engineering time for high-value tasks.\\n\\n## Key Features\\n- **Three-Stage Workflow**: Document metadata \\u2192 File metadata \\u2192 Download\\n- **Interactive Control**: Menu-driven execution & Markdown review interfaces\\n- **Smart Organization**: Metadata-driven naming & automatic subfolders\\n- **Filter Chains**: Glob patterns for auto-selection (e.g., `*G000*`, `*VOID*`)\\n- **Audit Trail**: JSON/Markdown records for full traceability\\n\\n## Workflow\\n1. **Fetch Documents**: Scrape metadata \\u2192 `<rig>-a-docs.json`\\n2. **Select Docs**: Edit `item_include` in `<rig>-a-docs.md`\\n3. **Fetch Files**: Get file data for selected docs \\u2192 `<rig>-b-files.json`\\n4. **Select Files**: Mark `item_download` in `<rig>-b-files.md`\\n5. **Download**: Auto-organize files with subfolder support\\n\\n## Setup\\n```bash\\n1. py_venv_init.bat  # Create Python environment\\n2. RigOfficeDownloader-v4.bat  # Launch application\\n```\\n\\n## Usage\\n```\\nInteractive Menu:\\n[0] Change rig/search URLs  [4] Import doc selections\\n[1] Configure filters       [5] Fetch files\\n[2] Fetch docs              [6] Export files to MD\\n[3] Export docs to MD       [7] Import file selections\\n[8] Download files          [9] Advanced config\\n```\\n\\n## Directory Structure\\n```\\noutputs/\\n\\u251c\\u2500\\u2500 data/           # Metadata (JSON/MD)\\n\\u2514\\u2500\\u2500 downloads/      # Organized files\\n    \\u2514\\u2500 <rig>/\\n       \\u251c\\u2500 Subfolder/  # From item_generated_name\\n       \\u2514\\u2500 File.pdf\\n```\\n\\n## Technical Stack\\n- **Core**: Python 3.6+, Selenium, BeautifulSoup\\n- **Data**: JSON storage \\u2194 Markdown interfaces\\n- **Automation**: Smart waiting strategies & deduplication\\n\\n## Benefits\\n| Manual Process          | Automated Solution       |\\n|-------------------------|--------------------------|\\n| 4-6 hours per project   | <1 hour                  |\\n| Error-prone organization| Metadata-driven structure|\\n| No audit trail          | Full JSON/MD traceability\\n\\n## Version History\\n- v1: Basic retrieval | v2: User selection\\n- v3: Error handling | v4: Filters & subfolders\\n'\",\n  \"role\": \"universal_abstractor\",\n  \"input\": [\n    \"Automate staged technical document collection with reliable Python tools (deduplication, smart waits).\",\n    \"Integrate human review checkpoints for control and accuracy.\",\n    \"Use metadata for systematic filtering, naming, and subfolder organization.\",\n    \"Provide guided user interfaces for workflow validation.\",\n    \"Generate JSON/Markdown audit trails for complete traceability.\"\n  ],\n  \"process\": [\n    \"neutralize_domain_specifics()\",\n    \"reframe_as_universal_concepts_logic()\",\n    \"validate_logic_preservation_across_contexts()\",\n    \"ensure_representation_agnostic_structure()\"\n  ],\n  \"output\": {\n    \"universal_core_logic\": [\n      \"Implement a multi-stage process for resource collection, supporting reliable progression and error reduction.\",\n      \"Embed explicit checkpoints requiring human validation to maintain quality and oversight.\",\n      \"Utilize intrinsic data attributes for systematic selection, categorization, and organization of units.\",\n      \"Offer interactive mechanisms to guide and validate participant actions within the process.\",\n      \"Record comprehensive activity logs in an interoperable format to ensure end-to-end traceability.\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"potent_core\": [\n    \"Enforce a rigorous multi-stage acquisition pipeline to eliminate errors and guarantee reliable resource aggregation.\",\n    \"Mandate human validation checkpoints to safeguard quality and sustain oversight at critical junctures.\",\n    \"Exploit intrinsic data attributes to drive precise selection, robust categorization, and systematic organization of all units.\",\n    \"Deploy interactive guidance systems to direct participant actions and validate each workflow step with clarity.\",\n    \"Capture exhaustive, interoperable activity logs to secure uncompromising, end-to-end traceability.\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Axiomatic Vectorization for One-Line\n\nDetermine the optimal linear sequence for the `potent_core` components based on the `causal_nexus`; select the highest-density, unambiguous connectors (e.g., ';', '->', '|') to vectorize the structure ready for single-line collapse. Execute as\n\n`{role=one_line_vectorizer; input={potent_core:any, causal_nexus:dict}; process=[determine_optimal_axiomatic_linear_sequence(), select_high_density_unambiguous_connectors(), vectorize_components_for_linearity()]; output={linear_vector_plan:dict(sequence:list, connectors:list)}}`", "step": "j", "title": "Axiomatic Vectorization for One-Line", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '# RigOfficeDownloader\\n\\n## Overview\\nEngineers waste hours manually gathering technical documentation. This tool streamlines the process via a structured workflow with human checkpoints, preserving engineering time for high-value tasks.\\n\\n## Key Features\\n- **Three-Stage Workflow**: Document metadata \\u2192 File metadata \\u2192 Download\\n- **Interactive Control**: Menu-driven execution & Markdown review interfaces\\n- **Smart Organization**: Metadata-driven naming & automatic subfolders\\n- **Filter Chains**: Glob patterns for auto-selection (e.g., `*G000*`, `*VOID*`)\\n- **Audit Trail**: JSON/Markdown records for full traceability\\n\\n## Workflow\\n1. **Fetch Documents**: Scrape metadata \\u2192 `<rig>-a-docs.json`\\n2. **Select Docs**: Edit `item_include` in `<rig>-a-docs.md`\\n3. **Fetch Files**: Get file data for selected docs \\u2192 `<rig>-b-files.json`\\n4. **Select Files**: Mark `item_download` in `<rig>-b-files.md`\\n5. **Download**: Auto-organize files with subfolder support\\n\\n## Setup\\n```bash\\n1. py_venv_init.bat  # Create Python environment\\n2. RigOfficeDownloader-v4.bat  # Launch application\\n```\\n\\n## Usage\\n```\\nInteractive Menu:\\n[0] Change rig/search URLs  [4] Import doc selections\\n[1] Configure filters       [5] Fetch files\\n[2] Fetch docs              [6] Export files to MD\\n[3] Export docs to MD       [7] Import file selections\\n[8] Download files          [9] Advanced config\\n```\\n\\n## Directory Structure\\n```\\noutputs/\\n\\u251c\\u2500\\u2500 data/           # Metadata (JSON/MD)\\n\\u2514\\u2500\\u2500 downloads/      # Organized files\\n    \\u2514\\u2500 <rig>/\\n       \\u251c\\u2500 Subfolder/  # From item_generated_name\\n       \\u2514\\u2500 File.pdf\\n```\\n\\n## Technical Stack\\n- **Core**: Python 3.6+, Selenium, BeautifulSoup\\n- **Data**: JSON storage \\u2194 Markdown interfaces\\n- **Automation**: Smart waiting strategies & deduplication\\n\\n## Benefits\\n| Manual Process          | Automated Solution       |\\n|-------------------------|--------------------------|\\n| 4-6 hours per project   | <1 hour                  |\\n| Error-prone organization| Metadata-driven structure|\\n| No audit trail          | Full JSON/MD traceability\\n\\n## Version History\\n- v1: Basic retrieval | v2: User selection\\n- v3: Error handling | v4: Filters & subfolders\\n'\",\n  \"potent_core\": [\n    \"Enforce a rigorous multi-stage acquisition pipeline to eliminate errors and guarantee reliable resource aggregation.\",\n    \"Mandate human validation checkpoints to safeguard quality and sustain oversight at critical junctures.\",\n    \"Exploit intrinsic data attributes to drive precise selection, robust categorization, and systematic organization of all units.\",\n    \"Deploy interactive guidance systems to direct participant actions and validate each workflow step with clarity.\",\n    \"Capture exhaustive, interoperable activity logs to secure uncompromising, end-to-end traceability.\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"linear_vector_plan\": {\n    \"sequence\": [\n      \"Enforce a rigorous multi-stage acquisition pipeline to eliminate errors and guarantee reliable resource aggregation\",\n      \"Mandate human validation checkpoints to safeguard quality and sustain oversight at critical junctures\",\n      \"Exploit intrinsic data attributes to drive precise selection, robust categorization, and systematic organization of all units\",\n      \"Deploy interactive guidance systems to direct participant actions and validate each workflow step with clarity\",\n      \"Capture exhaustive, interoperable activity logs to secure uncompromising, end-to-end traceability\"\n    ],\n    \"connectors\": [\n      \";\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Semantic Compression & Symbolization\n\nApply maximal semantic compression to the planned linear vector, substituting ultra-concise symbols, abbreviations, or keywords where possible without ambiguity; encapsulate the full essential meaning within the absolute minimum footprint, ensuring self-contained decodability. Execute as\n\n`{role=semantic_compressor; input=linear_vector_plan:dict; process=[identify_compressible_concepts(), substitute_minimal_unambiguous_symbols(), maximize_meaning_per_unit(), validate_decodability_self_sufficiency()]; output={compressed_vector_elements:list}}`", "step": "k", "title": "Semantic Compression & Symbolization", "input": "{\n  \"initial_prompt\": \"consolidate and rephrase as a list of 5-6 points phrased in a concistent manner: '# RigOfficeDownloader\\n\\n## Overview\\nEngineers waste hours manually gathering technical documentation. This tool streamlines the process via a structured workflow with human checkpoints, preserving engineering time for high-value tasks.\\n\\n## Key Features\\n- **Three-Stage Workflow**: Document metadata \\u2192 File metadata \\u2192 Download\\n- **Interactive Control**: Menu-driven execution & Markdown review interfaces\\n- **Smart Organization**: Metadata-driven naming & automatic subfolders\\n- **Filter Chains**: Glob patterns for auto-selection (e.g., `*G000*`, `*VOID*`)\\n- **Audit Trail**: JSON/Markdown records for full traceability\\n\\n## Workflow\\n1. **Fetch Documents**: Scrape metadata \\u2192 `<rig>-a-docs.json`\\n2. **Select Docs**: Edit `item_include` in `<rig>-a-docs.md`\\n3. **Fetch Files**: Get file data for selected docs \\u2192 `<rig>-b-files.json`\\n4. **Select Files**: Mark `item_download` in `<rig>-b-files.md`\\n5. **Download**: Auto-organize files with subfolder support\\n\\n## Setup\\n```bash\\n1. py_venv_init.bat  # Create Python environment\\n2. RigOfficeDownloader-v4.bat  # Launch application\\n```\\n\\n## Usage\\n```\\nInteractive Menu:\\n[0] Change rig/search URLs  [4] Import doc selections\\n[1] Configure filters       [5] Fetch files\\n[2] Fetch docs              [6] Export files to MD\\n[3] Export docs to MD       [7] Import file selections\\n[8] Download files          [9] Advanced config\\n```\\n\\n## Directory Structure\\n```\\noutputs/\\n\\u251c\\u2500\\u2500 data/           # Metadata (JSON/MD)\\n\\u2514\\u2500\\u2500 downloads/      # Organized files\\n    \\u2514\\u2500 <rig>/\\n       \\u251c\\u2500 Subfolder/  # From item_generated_name\\n       \\u2514\\u2500 File.pdf\\n```\\n\\n## Technical Stack\\n- **Core**: Python 3.6+, Selenium, BeautifulSoup\\n- **Data**: JSON storage \\u2194 Markdown interfaces\\n- **Automation**: Smart waiting strategies & deduplication\\n\\n## Benefits\\n| Manual Process          | Automated Solution       |\\n|-------------------------|--------------------------|\\n| 4-6 hours per project   | <1 hour                  |\\n| Error-prone organization| Metadata-driven structure|\\n| No audit trail          | Full JSON/MD traceability\\n\\n## Version History\\n- v1: Basic retrieval | v2: User selection\\n- v3: Error handling | v4: Filters & subfolders\\n'\",\n  \"linear_vector_plan\": {\n    \"sequence\": [\n      \"Enforce a rigorous multi-stage acquisition pipeline to eliminate errors and guarantee reliable resource aggregation\",\n      \"Mandate human validation checkpoints to safeguard quality and sustain oversight at critical junctures\",\n      \"Exploit intrinsic data attributes to drive precise selection, robust categorization, and systematic organization of all units\",\n      \"Deploy interactive guidance systems to direct participant actions and validate each workflow step with clarity\",\n      \"Capture exhaustive, interoperable activity logs to secure uncompromising, end-to-end traceability\"\n    ],\n    \"connectors\": [\n      \";\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"compressed_vector_elements\": [\n    \"3↔S-acq-pipe⇨err⊘,agg✔\",\n    \"H-check⇨qual✔,oversight⊕\",\n    \"meta→sel,cat,org⇨precis\",\n    \"IA-guide⇨step✔,user⇨clar\",\n    \"log-f✚⇨trace🌐⊆all\"\n  ]\n}", "cost": 0.0}}}], "total_cost": 0.0}