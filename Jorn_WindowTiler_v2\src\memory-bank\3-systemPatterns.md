# System Patterns: Windows Window Tiler

## System Architecture

The Window Tiler employs a modular object-oriented architecture with clear separation of concerns. The system is organized around three primary components:

```mermaid
graph TD
    M[Monitor] --- W[Window]
    M --- WT[WindowTiler]
    W --- WT
    WT --- TW[Tiled Windows]
```

### Core Components

1. **Monitor Component**
   - Responsible for detecting and representing physical display devices
   - Manages monitor boundaries, work areas, and device information
   - Provides coordinate translation between screen and monitor-relative positions

2. **Window Component**
   - Represents individual application windows in the system
   - Encapsulates window properties (position, size, visibility, etc.)
   - Provides methods for manipulating windows (resize, move, maximize, etc.)

3. **WindowTiler Component**
   - Core algorithmic engine for arranging windows
   - Implements grid-based window layout calculations
   - Handles distribution of windows across monitors

## Key Technical Decisions

### 1. Win32 API Integration
The system uses the Win32 API through Python bindings (win32gui, win32api, win32con) to interact with the Windows operating system. This enables direct manipulation of windows and access to system information without third-party dependencies.

**Rationale**: Direct API access provides more control and reliability than shell-based approaches or higher-level abstractions.

### 2. Object Representation
Windows and monitors are represented as first-class objects with properties and methods, rather than as simple data structures or procedural code.

**Rationale**: Object-oriented design provides cleaner separation of concerns, more maintainable code, and intuitive interfaces for manipulating windows and monitors.

### 3. Proportional Tiling
Window positions are calculated using proportional coordinates (0.0-1.0) relative to monitor dimensions rather than absolute pixel coordinates.

**Rationale**: Proportional positioning enables flexible layouts that adapt to different monitor resolutions and configurations.

### 4. Separation of Detection and Manipulation
The system separates the detection of windows/monitors from their manipulation.

**Rationale**: This separation allows the system to gather information about available resources before making decisions about how to arrange them.

## Design Patterns

### Factory Pattern
Factory functions (`get_all_windows()`, `get_all_monitors()`) encapsulate the complexity of system API calls and return collections of fully initialized Window and Monitor objects.

### Strategy Pattern
The WindowTiler implements a strategy pattern for layout algorithms, allowing different tiling approaches based on configuration parameters.

### Facade Pattern
The main interface provides a simplified facade over the complex underlying Win32 API interactions.

## Component Relationships

### Monitor and Window Relationship
- Windows exist within the coordinate space of a monitor
- Monitor provides the boundaries and constraints for window operations
- Windows reference monitors for coordinate translation and boundary calculations

### WindowTiler Dependencies
- Depends on both Monitor and Window objects
- Uses monitor information to determine layout boundaries
- Manipulates Window objects to achieve desired arrangements

## Critical Implementation Paths

### Window Detection and Filtering
1. Enumerate all top-level windows using Win32 EnumWindows
2. Filter windows based on visibility, title, or process criteria
3. Create Window objects for relevant windows
4. Extract and store window properties for manipulation

### Monitor Detection and Boundary Calculation
1. Enumerate all display monitors using Win32 EnumDisplayMonitors
2. Extract physical dimensions and work areas
3. Identify primary monitor
4. Create Monitor objects with boundary information

### Window Tiling Implementation
1. Determine target monitor and window collection
2. Calculate grid dimensions based on rows and columns
3. Apply optional customization (column/row ratios)
4. For each window, calculate its position in the grid
5. Apply the calculated position to the window
6. Handle overflow gracefully (more windows than grid cells)

## Extensibility Points

### Window Type Detection
The system can be extended to support additional window type detection mechanisms beyond the current process-based approach.

### Tiling Strategies
Additional tiling algorithms can be implemented to support different layout patterns (e.g., cascading, radial, etc.).

### Layout Persistence
The architecture allows for extending the system to save and restore window layouts.
