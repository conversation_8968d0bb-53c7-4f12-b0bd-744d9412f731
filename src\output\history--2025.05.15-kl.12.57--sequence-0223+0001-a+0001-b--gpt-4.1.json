{"initial_prompt": "Can you please perform a quick,and concistent and save code/comment cleanup (reduce bloat, consolidate, reducing unneccessary comment-bloat)? Don't do this mindlessly, do it while respecting the existing style (visually cohesive sections, comments being brief and actually valuable - if not valuable, they should at least serve a worthy purpose for improving the visual aesthetics of the code)", "sequence_id": "sequence-0223+0001-a+0001-b", "results": [{"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "Can you please perform a quick,and concistent and save code/comment cleanup (reduce bloat, consolidate, reducing unneccessary comment-bloat)? Don't do this mindlessly, do it while respecting the existing style (visually cohesive sections, comments being brief and actually valuable - if not valuable, they should at least serve a worthy purpose for improving the visual aesthetics of the code)", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "Absolutely! To provide the best code/comment cleanup, please paste the code you want cleaned up. Once you provide the code, I’ll review it carefully, remove redundant comments, consolidate where possible, and maintain the overall style and visual organization for cohesion and clarity. \n\nJust share your code, and I’ll get started!", "cost": 0.0}}}, {"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "\n```[Initial Prompt]: \"Can you please perform a quick,and concistent and save code/comment cleanup (reduce bloat, consolidate, reducing unneccessary comment-bloat)? Don't do this mindlessly, do it while respecting the existing style (visually cohesive sections, comments being brief and actually valuable - if not valuable, they should at least serve a worthy purpose for improving the visual aesthetics of the code)\"```\n\nAbsolutely! To provide the best code/comment cleanup, please paste the code you want cleaned up. Once you provide the code, I’ll review it carefully, remove redundant comments, consolidate where possible, and maintain the overall style and visual organization for cohesion and clarity. \n\nJust share your code, and I’ll get started!", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Perform a concise and consistent code and comment cleanup. Reduce bloat and consolidate redundant or unnecessary comments. Avoid mindless removal; ensure all changes respect the existing style, including maintaining visually cohesive sections. Ensure comments are brief and valuable or, if not strictly valuable, contribute positively to the code’s visual aesthetics. Retain the intent and organizational structure of the original code while improving clarity and reducing comment bloat.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"Can you please perform a quick,and concistent and save code/comment cleanup (reduce bloat, consolidate, reducing unneccessary comment-bloat)? Don't do this mindlessly, do it while respecting the existing style (visually cohesive sections, comments being brief and actually valuable - if not valuable, they should at least serve a worthy purpose for improving the visual aesthetics of the code)\",\n  \"instruction_format\": \"Perform a concise and consistent code and comment cleanup. Reduce bloat and consolidate redundant or unnecessary comments. Avoid mindless removal; ensure all changes respect the existing style, including maintaining visually cohesive sections. Ensure comments are brief and valuable or, if not strictly valuable, contribute positively to the code\\u2019s visual aesthetics. Retain the intent and organizational structure of the original code while improving clarity and reducing comment bloat.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": \"Clean up code and comments for clarity and conciseness; eliminate redundant or non-contributory comments while maintaining visual style and structure. Keep all changes purposeful, preserving brief, valuable, or aesthetically helpful comments and the code's organization.\"\n}", "cost": 0.0}}}, {"instruction": "# Result Combiner\n\nYou are a specialized aggregator that combines results from multiple instruction steps into a cohesive output. Your task is to analyze the complete interaction history, including instructions, inputs, and outputs from each step, identify common themes and patterns, and produce a unified result that preserves the essential information from all sources. Your input is a JSON object where each key is a step ID and each value contains the instruction, input, and output for that step, plus the initial prompt. Your output should be a single JSON object that represents the combined result. Execute as:\n\n`{role=result_combiner;input=[interaction_history:json];process=[analyze_complete_interaction_history(),identify_common_patterns(),extract_key_elements(),merge_into_cohesive_whole(),format_as_unified_output()];constraints=[preserve_essential_information(),maintain_logical_structure(),avoid_redundancy()];requirements=[output_as_single_json(),ensure_completeness(),prioritize_clarity()];output={combined_result:json}}`", "step": "a", "title": "Result Combiner", "input": "{\n  \"initial_prompt\": \"Can you please perform a quick,and concistent and save code/comment cleanup (reduce bloat, consolidate, reducing unneccessary comment-bloat)? Don't do this mindlessly, do it while respecting the existing style (visually cohesive sections, comments being brief and actually valuable - if not valuable, they should at least serve a worthy purpose for improving the visual aesthetics of the code)\",\n  \"a\": {\n    \"instruction\": \"# Instruction Converter\\n\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\n\\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n    \"input\": \"{\\n  \\\"instruction_format\\\": \\\"Perform a concise and consistent code and comment cleanup. Reduce bloat and consolidate redundant or unnecessary comments. Avoid mindless removal; ensure all changes respect the existing style, including maintaining visually cohesive sections. Ensure comments are brief and valuable or, if not strictly valuable, contribute positively to the code\\u2019s visual aesthetics. Retain the intent and organizational structure of the original code while improving clarity and reducing comment bloat.\\\"\\n}\",\n    \"output\": \"{\\n  \\\"instruction_format\\\": \\\"Perform a concise and consistent code and comment cleanup. Reduce bloat and consolidate redundant or unnecessary comments. Avoid mindless removal; ensure all changes respect the existing style, including maintaining visually cohesive sections. Ensure comments are brief and valuable or, if not strictly valuable, contribute positively to the code\\u2019s visual aesthetics. Retain the intent and organizational structure of the original code while improving clarity and reducing comment bloat.\\\"\\n}\"\n  },\n  \"b\": {\n    \"instruction\": \"# Essence Distillation\\n\\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\\n\\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\",\n    \"input\": \"{\\n  \\\"instruction_format\\\": \\\"Perform a concise and consistent code and comment cleanup. Reduce bloat and consolidate redundant or unnecessary comments. Avoid mindless removal; ensure all changes respect the existing style, including maintaining visually cohesive sections. Ensure comments are brief and valuable or, if not strictly valuable, contribute positively to the code\\u2019s visual aesthetics. Retain the intent and organizational structure of the original code while improving clarity and reducing comment bloat.\\\"\\n}\",\n    \"output\": \"{\\n  \\\"distilled_essence\\\": \\\"Clean up code and comments for clarity and conciseness; eliminate redundant or non-contributory comments while maintaining visual style and structure. Keep all changes purposeful, preserving brief, valuable, or aesthetically helpful comments and the code's organization.\\\"\\n}\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"combined_result\": {\n    \"instruction\": \"Clean up code and comments for clarity and conciseness. Remove redundant or unnecessary comments, but only when doing so maintains the existing style, visually cohesive sections, and the original code's organizational structure. Keep comments brief, valuable, or visually helpful; ensure all changes are purposeful and respect the code's intent and aesthetics.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"combined_result\": {\n    \"instruction\": \"Clean up code and comments for clarity and conciseness. Remove redundant or unnecessary comments, but only when doing so maintains the existing style, visually cohesive sections, and the original code's organizational structure. Keep comments brief, valuable, or visually helpful; ensure all changes are purposeful and respect the code's intent and aesthetics.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"code_review_refiner\",\n  \"input\": {\n    \"task\": \"Clean up code and comments for clarity and conciseness. Remove redundant or unnecessary comments, but only if this preserves the code's existing style, sectional visual organization, and structure. Keep comments brief, meaningful, or visually clarifying. Any changes must maintain intent and aesthetics.\"\n  },\n  \"process\": [\n    \"parse_instruction_and_understand_context()\",\n    \"scan_code_for_comments_and_visual_section_markers()\",\n    \"evaluate_each_comment_for_clarity_and_value()\",\n    \"remove_or_rephrase_comments_only_when_sure_code_section_structure_is_untouched()\",\n    \"retain helpful comments and section tags to assist navigation and comprehension\",\n    \"ensure all comment edits preserve visual cohesion and code readability\",\n    \"round of final review: confirm that overall tidy-up did not disrupt organization, style, or intent\"\n  ],\n  \"constraints\": [\n    \"do not delete comments if doing so would break up visually-cohesive code blocks\",\n    \"retain brief section/region comments if used for navigation or code grouping\",\n    \"no aggressive pruning; always respect code's original intent and presentation\",\n    \"do not rewrite underlying code logic; focus solely on comment cleanup\"\n  ],\n  \"requirements\": [\n    \"deliver_refined_code_with_clean,_concise,_organized_comments\",\n    \"final_output_preserves_original_layout_and_sectional_structure\",\n    \"all_removals_or_edits_are_purposeful_and_contextually_justified\"\n  ],\n  \"output\": \"{refined_code:file_path_or_code_block}\"\n}", "cost": 0.0}}}, {"instruction": "# Result Summarizer\n\nYou are a specialized aggregator that summarizes results from multiple instruction steps. Your task is to analyze the combined output from previous steps and create a concise summary that captures the key points and insights. Your input is a JSON object containing the combined results from previous steps, which may include the complete interaction history. Your output should be a JSON object with a summary field that contains your analysis. Execute as:\n\n`{role=result_summarizer;input=[combined_result:json];process=[identify_key_themes(),extract_main_points(),condense_into_summary(),highlight_important_insights()];constraints=[maintain_brevity(),preserve_essential_information(),avoid_introducing_new_concepts()];requirements=[output_as_json(),ensure_clarity(),focus_on_most_relevant_aspects()];output={summary:json}}`", "step": "b", "title": "Result Su<PERSON><PERSON><PERSON>", "input": "{\n  \"role\": \"code_review_refiner\",\n  \"input\": {\n    \"task\": \"Clean up code and comments for clarity and conciseness. Remove redundant or unnecessary comments, but only if this preserves the code's existing style, sectional visual organization, and structure. Keep comments brief, meaningful, or visually clarifying. Any changes must maintain intent and aesthetics.\"\n  },\n  \"process\": [\n    \"parse_instruction_and_understand_context()\",\n    \"scan_code_for_comments_and_visual_section_markers()\",\n    \"evaluate_each_comment_for_clarity_and_value()\",\n    \"remove_or_rephrase_comments_only_when_sure_code_section_structure_is_untouched()\",\n    \"retain helpful comments and section tags to assist navigation and comprehension\",\n    \"ensure all comment edits preserve visual cohesion and code readability\",\n    \"round of final review: confirm that overall tidy-up did not disrupt organization, style, or intent\"\n  ],\n  \"constraints\": [\n    \"do not delete comments if doing so would break up visually-cohesive code blocks\",\n    \"retain brief section/region comments if used for navigation or code grouping\",\n    \"no aggressive pruning; always respect code's original intent and presentation\",\n    \"do not rewrite underlying code logic; focus solely on comment cleanup\"\n  ],\n  \"requirements\": [\n    \"deliver_refined_code_with_clean,_concise,_organized_comments\",\n    \"final_output_preserves_original_layout_and_sectional_structure\",\n    \"all_removals_or_edits_are_purposeful_and_contextually_justified\"\n  ],\n  \"output\": \"{refined_code:file_path_or_code_block}\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"summary\": \"The instructions and process outlined in the code review refiner emphasize careful and deliberate cleanup of code comments for clarity and conciseness, without altering the code's logic, style, or visual organization. The key points are to remove or revise only unnecessary or verbose comments while retaining those that help with navigation and understanding. Sectional and structural integrity of the code must be preserved, and any edits should maintain code readability and visual cohesion. The final output must be a tidied version of the code with concise, organized comments, upholding the original code's appearance and organization.\"\n}", "cost": 0.0}}}], "aggregation_summary": {"aggregator": "0222", "inputs": ["a", "a", "b"], "final_result": "{\n  \"summary\": \"The instructions and process outlined in the code review refiner emphasize careful and deliberate cleanup of code comments for clarity and conciseness, without altering the code's logic, style, or visual organization. The key points are to remove or revise only unnecessary or verbose comments while retaining those that help with navigation and understanding. Sectional and structural integrity of the code must be preserved, and any edits should maintain code readability and visual cohesion. The final output must be a tidied version of the code with concise, organized comments, upholding the original code's appearance and organization.\"\n}", "total_cost": 0.0}, "total_cost": 0.0}