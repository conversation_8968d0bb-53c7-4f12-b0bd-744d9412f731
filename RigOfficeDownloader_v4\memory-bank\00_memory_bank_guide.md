## Distilled Highlights
- Memory-bank is a structured documentation system for RigOfficeDownloader
- 8 core files track everything from project purpose to current objectives
- Documentation should be updated at key development milestones
- Each file begins with Distilled Highlights for quick context scanning

# Memory Bank Guide

## Purpose

This memory-bank provides a complete, structured documentation system for the RigOfficeDownloader project. It is designed to ensure knowledge continuity between development sessions and team members, enabling anyone (including an AI agent) to quickly understand the project and continue development effectively.

## Structure

The memory-bank consists of 8 core files, numbered to indicate their logical sequence:

```
memory-bank/
├── 01_foundation.md - Core mission, values, and vision
├── 02_context.md - Problem space, stakeholders, and goals
├── 03_patterns.md - System architecture and design patterns
├── 04_tech.md - Technology stack and technical decisions
├── 05_activity.md - Current focus and recent decisions
├── 06_progress.md - Build state and completed milestones
├── 07_tasks.md - Current tasks and priorities
├── 08_objective.md - Current objective and success criteria
```

## How to Use This Memory Bank

### For New Development Sessions

1. **Start by reading all files** in numerical order (01-08)
2. Pay special attention to the **Distilled Highlights** at the top of each file
3. Note any sections marked with **[TO BE FILLED]** as areas needing completion
4. Focus particularly on `05_activity.md`, `06_progress.md`, `07_tasks.md`, and `08_objective.md` for current status

### When to Update

Update the memory-bank files:

1. After completing significant development tasks
2. When discovering new information about the system
3. Before ending a development session
4. When changing direction or priorities
5. When resolving issues or implementing new features

### How to Update

1. Identify which files are affected by your changes
2. Update the relevant sections with new information
3. **Always update the Distilled Highlights** at the top of modified files
4. Mark outdated information as such rather than deleting it
5. Add new files (09_*.md, 10_*.md, etc.) for specialized topics as needed

## File-Specific Update Guidelines

- **01_foundation.md**: Rarely changes; update only for fundamental shifts in project purpose
- **02_context.md**: Update when stakeholder needs or constraints change
- **03_patterns.md**: Update when architectural patterns or data structures evolve
- **04_tech.md**: Update when changing technologies or technical approaches
- **05_activity.md**: Update frequently to reflect current development focus
- **06_progress.md**: Update when milestones are reached or blockers resolved
- **07_tasks.md**: Update as tasks are completed and new ones are identified
- **08_objective.md**: Update when objectives shift or success criteria change

## Memory Bank Maintenance

To keep the memory-bank effective:

1. Be concise but comprehensive
2. Prioritize clarity over verbosity
3. Use consistent formatting across all files
4. Replace [TO BE FILLED] placeholders as information becomes available
5. Regularly review all files for accuracy and completeness

Remember that this documentation system is designed to enable continuity despite the complete memory reset that occurs between development sessions. The goal is to capture all essential knowledge in a structured, accessible format.
