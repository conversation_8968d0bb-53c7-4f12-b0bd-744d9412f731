{"initial_prompt": " Camera Movement: FPV shot diving into the ocean. Scene Description: The camera dives from a cliff into the ocean, showing the transition from air to water. Additional Details: The scene is extremely vivid, and when transitioning into the water the water changes hue to reds.", "sequence_id": "sequence-0223-a+0194-c+0001-a+0001-b+0194-c", "results": [{"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": " Camera Movement: FPV shot diving into the ocean. Scene Description: The camera dives from a cliff into the ocean, showing the transition from air to water. Additional Details: The scene is extremely vivid, and when transitioning into the water the water changes hue to reds.", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"scene\": \"FPV shot launches from a jagged cliff edge, plunging downward through salty, sunlit air. The camera's perspective is the viewer's own eyes.\",\n      \"camera_movement\": \"dolly:in, tilt:down, speed:fast; the camera dives off the cliff and noses downward steeply, wind whipping past.\",\n      \"transformation\": \"Transition from air to water as camera pierces the surface. All visible sea hues morph into vivid gradations of reds and crimsons.\",\n      \"atmospheric_details\": \"Cliffside bathed in golden hour light that dappled across stones. Moisture beads on the virtual lens as the camera plunges. Bubbles bloom and trail as entry into water occurs. Underwater light rays kaleidoscope, then the blue-green world bleeds into burning reds.\",\n      \"symbolic_meaning\": \"Transitioning from one realm to another, with the hue shift symbolizing passion, change, or a rite of passage.\",\n      \"visual_style\": \"Extremely vivid, highly saturated realism with painterly touches in the water's refracted light.\",\n      \"emotional_tone\": \"Awe, exhilaration morphing into profound wonder.\"\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"/stream/fpv_cliff_to_ocean_reddening_transition.mp4\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \" Camera Movement: FPV shot diving into the ocean. Scene Description: The camera dives from a cliff into the ocean, showing the transition from air to water. Additional Details: The scene is extremely vivid, and when transitioning into the water the water changes hue to reds.\",\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"scene\": \"FPV shot launches from a jagged cliff edge, plunging downward through salty, sunlit air. The camera's perspective is the viewer's own eyes.\",\n      \"camera_movement\": \"dolly:in, tilt:down, speed:fast; the camera dives off the cliff and noses downward steeply, wind whipping past.\",\n      \"transformation\": \"Transition from air to water as camera pierces the surface. All visible sea hues morph into vivid gradations of reds and crimsons.\",\n      \"atmospheric_details\": \"Cliffside bathed in golden hour light that dappled across stones. Moisture beads on the virtual lens as the camera plunges. Bubbles bloom and trail as entry into water occurs. Underwater light rays kaleidoscope, then the blue-green world bleeds into burning reds.\",\n      \"symbolic_meaning\": \"Transitioning from one realm to another, with the hue shift symbolizing passion, change, or a rite of passage.\",\n      \"visual_style\": \"Extremely vivid, highly saturated realism with painterly touches in the water's refracted light.\",\n      \"emotional_tone\": \"Awe, exhilaration morphing into profound wonder.\"\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"/stream/fpv_cliff_to_ocean_reddening_transition.mp4\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"FPV shot plunges from a jagged cliff edge at golden hour, [dolly:in] and [tilt:down] fast, wind rushing past, lens flecked with moisture, camera dives into the ocean; [morph] hues from blue-green to vivid crimson and red underwater, sunbeams kaleidoscope through bubbles, awe and wonder, painterly saturated realism.\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "{\n  \"initial_prompt\": \" Camera Movement: FPV shot diving into the ocean. Scene Description: The camera dives from a cliff into the ocean, showing the transition from air to water. Additional Details: The scene is extremely vivid, and when transitioning into the water the water changes hue to reds.\",\n  \"runwayml_prompt\": \"FPV shot plunges from a jagged cliff edge at golden hour, [dolly:in] and [tilt:down] fast, wind rushing past, lens flecked with moisture, camera dives into the ocean; [morph] hues from blue-green to vivid crimson and red underwater, sunbeams kaleidoscope through bubbles, awe and wonder, painterly saturated realism.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Create an FPV camera sequence that initiates with a dive from a cliff into the ocean. Operate the camera to capture the transition from aerial to underwater environments. Emphasize a vibrant, highly detailed depiction throughout the sequence. As the camera enters the water, modulate the underwater color grading, shifting hues from blue-green to intense reds. Incorporate visual effects that highlight the transition between air and water, including wind movement, moisture on the lens, and refracted sunlight underwater. Render the scene in a painterly, saturated, and realistic style to evoke awe and wonder.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \" Camera Movement: FPV shot diving into the ocean. Scene Description: The camera dives from a cliff into the ocean, showing the transition from air to water. Additional Details: The scene is extremely vivid, and when transitioning into the water the water changes hue to reds.\",\n  \"instruction_format\": \"Create an FPV camera sequence that initiates with a dive from a cliff into the ocean. Operate the camera to capture the transition from aerial to underwater environments. Emphasize a vibrant, highly detailed depiction throughout the sequence. As the camera enters the water, modulate the underwater color grading, shifting hues from blue-green to intense reds. Incorporate visual effects that highlight the transition between air and water, including wind movement, moisture on the lens, and refracted sunlight underwater. Render the scene in a painterly, saturated, and realistic style to evoke awe and wonder.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": \"Create a vivid FPV camera dive from cliff to ocean, emphasizing the air-to-water transition with detailed visuals, shifting underwater colors from blue-green to red, and realistic effects highlighting the shift, rendered in a painterly, awe-inspiring style.\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \" Camera Movement: FPV shot diving into the ocean. Scene Description: The camera dives from a cliff into the ocean, showing the transition from air to water. Additional Details: The scene is extremely vivid, and when transitioning into the water the water changes hue to reds.\",\n  \"distilled_essence\": \"Create a vivid FPV camera dive from cliff to ocean, emphasizing the air-to-water transition with detailed visuals, shifting underwater colors from blue-green to red, and realistic effects highlighting the shift, rendered in a painterly, awe-inspiring style.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"FPV dive from a cliff into vivid blue-green ocean, air to water transition with detailed splash, underwater colors dramatically morph to red hues, painterly awe-inspiring style, [fpv] [dolly:down] [lighting_change] [morph] [dissolve]\"\n}", "cost": 0.0}}}], "total_cost": 0.0}