@ECHO OFF
:: --------------------------------------------------------------------------
:: SET CURRENT DIRECTORY TO THE LOCATION OF THIS BATCH FILE OR 'INPUT-ARG-1'
:: --------------------------------------------------------------------------
IF "%~1" == "" (CD /D "%~dp0") ELSE (CD /D "%~1")
SET CURRENT_SCRIPT_PATH=%CD%
:: --------------------------------------------------------------------------
:: LOCATE THE CURRENT VENV'S ROOT-DIRECTORY (IN ORDER TO USE RELATIVE PATHS)
:: --------------------------------------------------------------------------
:LoopThroughParentDirs
IF EXIST "%CD%\venv\Scripts\python.exe" (GOTO FoundVenvDir)
SET TMP=%CD% && CD..
IF %CD% == %TMP% (ECHO Error: "venv\Scripts\python.exe" was not found! & PAUSE > NUL & EXIT)
GOTO LoopThroughParentDirs
:: --------------------------------------------------------------------------
:: CODE BELOW IS EXECUTED FROM THE VENV ROOT DIRECTORY
:: --------------------------------------------------------------------------
:: Create static definitions for the current virtual environment.
:FoundVenvDir
TITLE %CD%
SET "VENV_ROOT_DIR=%CD%"
SET "VENV_PYTHON_PATH=%CD%\venv\Scripts\python.exe"
SET "VENV_PIP_PATH=%CD%\venv\Scripts\pip.exe"

:: --------------------------------------------------------------------------
:: CUSTOM
:: --------------------------------------------------------------------------
:: Script name:
SET "SCRIPT_NAME=grow_windows_01.py"
TITLE %SCRIPT_NAME%

:: Execute:
"%VENV_PYTHON_PATH%" "%CURRENT_SCRIPT_PATH%\%SCRIPT_NAME%"

PAUSE