### RigOfficeDownloader Utility Workflow

This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow is clearly defined in the docstring at the beginning of the main script and follows these steps:

1. Fetch Documents
- The utility starts by scraping document metadata from predefined search URLs
- Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory
- Each document entry includes metadata like title, document number, revision, etc.
- All documents are initially marked with item_include=False
- Each document gets an item_generated_name for better identification

2. Export Documents to Markdown
- The JSON data is exported to a Markdown table: <rig>-a-docs.md
- This allows the user to easily review and edit which documents to include
- The user is expected to edit the Markdown file and set item_include=true for desired documents

3. Import Updated Document Data
- After the user edits the Markdown file, the utility imports the changes back to the JSON file
- This updates which documents are marked for file retrieval

4. Fetch Files for Selected Documents
- For each document with item_include=true, the utility scrapes file metadata
- File data is saved to <rig>-b-files.json
- Each file is initially marked with item_download=False
- Files inherit the document's item_generated_name with additional identifiers

5. Export Files to Markdown
- The file data is exported to a Markdown table: <rig>-b-files.md
- The user reviews and edits which files to download by setting item_download=true

6. Import Updated File Data
- After editing, the utility imports the changes back to the JSON file
- This updates which files are marked for download

7. Download Selected Files
- Files with item_download=true are downloaded
- Files are named according to their item_generated_name + extension
- The utility supports creating subfolders based on '/' in the item_generated_name
- Files are saved to the outputs/downloads/<rig> directory

Interactive Menu
- The utility provides an interactive menu where the user can choose which steps to execute
- This allows for flexibility in the workflow, enabling the user to run specific steps as needed
- The user can also update the rig number and search URLs through this menu

Key Features
- Automatic document and file metadata scraping
- User-friendly Markdown editing interface
- Customizable file naming with item_generated_name
- Support for subfolder organization in downloads
- Deduplication of documents and files
- Configurable field ordering for JSON and Markdown exports

Technical Implementation
- Uses Selenium with Chrome WebDriver for web scraping
- Implements smart waiting strategies for page loading
- Handles browser sessions with proper cleanup
- Provides progress feedback during operations
- Sanitizes filenames for valid paths
