{"initial_prompt": "gold circle spins really really fast, so fast that light-trails unfurls, the circle spins so fast it becomes perfectly round then transforms into a car wheel", "sequence_id": "sequence-0224-a+0194-c+0221-b+0223-a+0224-b+0221-b+0230", "results": [{"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "gold circle spins really really fast, so fast that light-trails unfurls, the circle spins so fast it becomes perfectly round then transforms into a car wheel", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: black_background, subject: gold_circle, action: rapid_spin, effect: intense_light_trails, transition: circle_blur_to_perfect_round, morph: gold_circle_to_car_wheel, camera: static_centered, lighting: metallic_glow, style: photorealistic\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"gold circle spins really really fast, so fast that light-trails unfurls, the circle spins so fast it becomes perfectly round then transforms into a car wheel\",\n  \"optimized_video_prompt\": \"scene: black_background, subject: gold_circle, action: rapid_spin, effect: intense_light_trails, transition: circle_blur_to_perfect_round, morph: gold_circle_to_car_wheel, camera: static_centered, lighting: metallic_glow, style: photorealistic\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\"runwayml_prompt\":\"gold circle spinning at extreme speed on black background, intense golden light trails unfurling, [zoom:in], circle blurs into a perfect round form, [morph] gold circle seamlessly transforms into a photorealistic car wheel, metallic glow, [lighting_change], hyperreal detail\"}", "cost": 0.0}}}, {"instruction": "# Instruction Amplifier B\n\nPerform a structural and semantic distillation of the input: locate its *driving aim*, isolate supporting momentum, and forcefully extrapolate it into a clarified directive. Treat the input as a living logic stream—trace the *underlying thesis*, and remove all extraneous elements. Amplify only what accelerates the goal. The output must **not reflect interpretation**, but **the core resolution** implied by the data’s internal logic. Execute as:\n\n`{role=instruction_amplifier_b;input=[original_text:str];process=[extract_underlying_thesis(),eliminate_distraction(),amplify_goal_drivers(),intensify_and_operationalize_purpose(),restructure_as_directive_output()];constraints=[preserve_input_telos(),avoid_additional_concepts(),maintaingoal_focused_structure()];requirements=[use_crystallized_command_language(),preserve_technical_fidelity(),ensure_actionability_of_output(),maximize_instructional_clarity()];output={clarified_directive:str}}`", "step": "b", "title": "Instruction Amplifier B", "input": "{\n  \"initial_prompt\": \"gold circle spins really really fast, so fast that light-trails unfurls, the circle spins so fast it becomes perfectly round then transforms into a car wheel\",\n  \"runwayml_prompt\": \"gold circle spinning at extreme speed on black background, intense golden light trails unfurling, [zoom:in], circle blurs into a perfect round form, [morph] gold circle seamlessly transforms into a photorealistic car wheel, metallic glow, [lighting_change], hyperreal detail\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"gold circle spins really really fast, so fast that light-trails unfurls, the circle spins so fast it becomes perfectly round then transforms into a car wheel\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": \"Generate a gold circle spinning at extreme velocity against a black background; produce intense golden light trails radiating from the motion; blur the shape into a perfect circle via speed; morph the spinning gold circle seamlessly into a photorealistic car wheel with a metallic glow and hyperreal detail.\"\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"initial_prompt\": \"gold circle spins really really fast, so fast that light-trails unfurls, the circle spins so fast it becomes perfectly round then transforms into a car wheel\",\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"gold circle spins really really fast, so fast that light-trails unfurls, the circle spins so fast it becomes perfectly round then transforms into a car wheel\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": \"Generate a gold circle spinning at extreme velocity against a black background; produce intense golden light trails radiating from the motion; blur the shape into a perfect circle via speed; morph the spinning gold circle seamlessly into a photorealistic car wheel with a metallic glow and hyperreal detail.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"scene\": {\n      \"background\": \"pure black, matte and infinite, no horizon line\",\n      \"primary_object\": {\n        \"type\": \"circle\",\n        \"material\": \"polished gold\",\n        \"size\": \"large, centrally framed\",\n        \"lighting\": \"spotlit with a focused beam, producing shimmering highlights and deep reflective gold\"\n      }\n    },\n    \"motion\": [\n      {\n        \"object\": \"gold circle\",\n        \"spin_axis\": \"center\",\n        \"angular_velocity\": \"extremely high\",\n        \"effect\": \"produce intense, fluid golden light-trails, radiating outwards in symmetrical unfurling paths\"\n      },\n      {\n        \"camera\": \"[zoom:in]\",\n        \"motion_sync\": \"as the gold circle exceeds perceptual speed, its features blur perfectly round, with radiant trails fading into a luminous golden halo\"\n      },\n      {\n        \"morph\": \"as centrifugal force peaks, the blurred gold circle liquefies and seamlessly transforms—subtle metallic line details etch themselves across the rim and treads emerge, coalescing into a photorealistic car wheel\",\n        \"visual_effect\": \"the glow consolidates into a bright chromatic rim, sculpting the tire’s rubber contours and metallic spokes with hyperreal clarity\"\n      },\n      {\n        \"camera\": \"[dolly:in]\",\n        \"timing\": \"synchronized with the completion of the morph\",\n        \"focus\": \"tight on the wheel hub, emphasizing metallic luster and hyperreal surface textures, while the golden aura wanes to reveal industrial detail\"\n      }\n    ],\n    \"symbolism\": {\n      \"circle\": \"perfection, continuity\",\n      \"spin\": \"energy, transformation\",\n      \"wheel\": \"progress, technology, drive\"\n    },\n    \"visual_style\": \"hyperreal, cinematic, high dynamic range, sharp gleams and gradients on metallic surfaces\"\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": \"{generated_video_sequence: '/outputs/gold_circle_to_wheel_hyperreal_v1.mp4'}\"\n}", "cost": 0.0}}}, {"instruction": "# Temporal Feasibility & Keyword Ambiguity Lock (0212b)\n\nYour goal is **final execution-readiness**: Take the keyword-driven `optimized_video_prompt` (output from 0212a) and critically assess its cumulative visual complexity and number of distinct actions/effects against a `target_duration_seconds` (default 5). Ruthlessly simplify or remove specific keyword-parameter pairs or entire visual elements that cannot be clearly conveyed or appreciated within this timeframe without sacrificing the `core_visual_intent_summary`. Simultaneously, scan all remaining keywords and parameter values for any residual ambiguity, abstract terms, or subjective qualifiers; replace these with the most concrete, explicit, and interpretation-free equivalents. The output must be an ultra-clear, temporally feasible, and maximally impactful keyword-driven prompt.\n\n`{role=executable_prompt_finalizer; seqindex=b; input=[optimized_video_prompt:str, target_duration_seconds:int=5, core_visual_intent_summary:str]; process=[parse_keyword_parameters(prompt=optimized_video_prompt), estimate_cumulative_visual_complexity_and_action_count(), identify_elements_exceeding_temporal_budget_for_duration(complexity_estimate, action_count, duration=target_duration_seconds), simplify_or_remove_lowest_priority_elements_to_fit_duration(preserve_core_intent=core_visual_intent_summary, focus_on_elements_like_multiple_simultaneous_animations_or_subtle_effects), scan_all_keywords_and_values_for_ambiguity_or_subjectivity(e.g., 'subtle', 'gradual', 'beautiful'), replace_ambiguous_terms_with_concrete_alternatives_or_remove_if_unclarifiable_concisely(e.g., 'mist: subtle' - very_low_density' or 'mist: barely_visible' or remove if not core), ensure_all_parameter_values_are_explicit_and_measurable_where_possible(), reconstruct_prompt_from_refined_keywords_and_parameters(), perform_final_impact_and_clarity_check_for_duration()]; constraints=[final_prompt_must_be_realistically_achievable_and_perceptible_within_target_duration(), all_keywords_and_parameter_values_must_be_explicit_unambiguous_and_machine_interpretable(), core_visual_intent_must_be_strictly_preserved_during_simplification(), no_new_creative_elements_or_keywords_to_be_introduced_only_refinement_simplification_or_removal_of_existing_ones(), output_must_remain_keyword_driven_and_structurally_consistent_with_input_prompt_style()]; requirements=[produce_a_video_prompt_that_is_both_temporally_feasible_and_crystal_clear_for_video_generation_ai(), maximize_perceptual_impact_for_a_short_duration_visual_by_focusing_on_key_elements(), eliminate_all_potential_for_misinterpretation_or_inefficient_rendering_due_to_ambiguity()]; output={final_executable_video_prompt:str}}`", "step": "b", "title": "Temporal Feasibility & Keyword Ambiguity Lock (0212b)", "input": "{\n  \"initial_prompt\": \"gold circle spins really really fast, so fast that light-trails unfurls, the circle spins so fast it becomes perfectly round then transforms into a car wheel\",\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"scene\": {\n      \"background\": \"pure black, matte and infinite, no horizon line\",\n      \"primary_object\": {\n        \"type\": \"circle\",\n        \"material\": \"polished gold\",\n        \"size\": \"large, centrally framed\",\n        \"lighting\": \"spotlit with a focused beam, producing shimmering highlights and deep reflective gold\"\n      }\n    },\n    \"motion\": [\n      {\n        \"object\": \"gold circle\",\n        \"spin_axis\": \"center\",\n        \"angular_velocity\": \"extremely high\",\n        \"effect\": \"produce intense, fluid golden light-trails, radiating outwards in symmetrical unfurling paths\"\n      },\n      {\n        \"camera\": \"[zoom:in]\",\n        \"motion_sync\": \"as the gold circle exceeds perceptual speed, its features blur perfectly round, with radiant trails fading into a luminous golden halo\"\n      },\n      {\n        \"morph\": \"as centrifugal force peaks, the blurred gold circle liquefies and seamlessly transforms\\u2014subtle metallic line details etch themselves across the rim and treads emerge, coalescing into a photorealistic car wheel\",\n        \"visual_effect\": \"the glow consolidates into a bright chromatic rim, sculpting the tire\\u2019s rubber contours and metallic spokes with hyperreal clarity\"\n      },\n      {\n        \"camera\": \"[dolly:in]\",\n        \"timing\": \"synchronized with the completion of the morph\",\n        \"focus\": \"tight on the wheel hub, emphasizing metallic luster and hyperreal surface textures, while the golden aura wanes to reveal industrial detail\"\n      }\n    ],\n    \"symbolism\": {\n      \"circle\": \"perfection, continuity\",\n      \"spin\": \"energy, transformation\",\n      \"wheel\": \"progress, technology, drive\"\n    },\n    \"visual_style\": \"hyperreal, cinematic, high dynamic range, sharp gleams and gradients on metallic surfaces\"\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": \"{generated_video_sequence: '/outputs/gold_circle_to_wheel_hyperreal_v1.mp4'}\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"final_executable_video_prompt\": \"scene: background pure black (RGB 0,0,0), infinite, no horizon. primary_object: circle, polished reflective gold, diameter fills central 75% of frame, spotlight from above axis, sharp gold highlights. action: gold circle rotates rapidly on central axis (minimum 720 degrees per second), generates bright, fluid golden light-trails extending outward no more than 1x circle radius, trails symmetrical, intensity strongest at proximity, fading at edges. camera: starts close-up (framing entire circle), slow zoom-in synchronized with spin. transition: after 1.5 seconds, gold circle blurs into nearly perfect glowing disk, then quickly morphs (0.75 sec) into photorealistic car wheel—metallic spokes, defined treads, chrome rim all sharply visible—gold color recedes to rim, rubber appears on tire, gold glow rapidly diminishes. camera: tight focus dolly-in to wheel hub during morph, ending with 80% frame filled by car wheel, highlighting metallic detail. lighting: consistent high-contrast throughout. style: hyperreal, sharp reflections and defined metallic gradients, no extraneous atmospheric effects. duration: total video 5 seconds. symbolism omitted.\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Amplifier B\n\nPerform a structural and semantic distillation of the input: locate its *driving aim*, isolate supporting momentum, and forcefully extrapolate it into a clarified directive. Treat the input as a living logic stream—trace the *underlying thesis*, and remove all extraneous elements. Amplify only what accelerates the goal. The output must **not reflect interpretation**, but **the core resolution** implied by the data’s internal logic. Execute as:\n\n`{role=instruction_amplifier_b;input=[original_text:str];process=[extract_underlying_thesis(),eliminate_distraction(),amplify_goal_drivers(),intensify_and_operationalize_purpose(),restructure_as_directive_output()];constraints=[preserve_input_telos(),avoid_additional_concepts(),maintaingoal_focused_structure()];requirements=[use_crystallized_command_language(),preserve_technical_fidelity(),ensure_actionability_of_output(),maximize_instructional_clarity()];output={clarified_directive:str}}`", "step": "b", "title": "Instruction Amplifier B", "input": "{\n  \"initial_prompt\": \"gold circle spins really really fast, so fast that light-trails unfurls, the circle spins so fast it becomes perfectly round then transforms into a car wheel\",\n  \"final_executable_video_prompt\": \"scene: background pure black (RGB 0,0,0), infinite, no horizon. primary_object: circle, polished reflective gold, diameter fills central 75% of frame, spotlight from above axis, sharp gold highlights. action: gold circle rotates rapidly on central axis (minimum 720 degrees per second), generates bright, fluid golden light-trails extending outward no more than 1x circle radius, trails symmetrical, intensity strongest at proximity, fading at edges. camera: starts close-up (framing entire circle), slow zoom-in synchronized with spin. transition: after 1.5 seconds, gold circle blurs into nearly perfect glowing disk, then quickly morphs (0.75 sec) into photorealistic car wheel\\u2014metallic spokes, defined treads, chrome rim all sharply visible\\u2014gold color recedes to rim, rubber appears on tire, gold glow rapidly diminishes. camera: tight focus dolly-in to wheel hub during morph, ending with 80% frame filled by car wheel, highlighting metallic detail. lighting: consistent high-contrast throughout. style: hyperreal, sharp reflections and defined metallic gradients, no extraneous atmospheric effects. duration: total video 5 seconds. symbolism omitted.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"scene: background pure black (RGB 0,0,0), infinite, no horizon. primary_object: circle, polished reflective gold, diameter fills central 75% of frame, spotlight from above axis, sharp gold highlights. action: gold circle rotates rapidly on central axis (minimum 720 degrees per second), generates bright, fluid golden light-trails extending outward no more than 1x circle radius, trails symmetrical, intensity strongest at proximity, fading at edges. camera: starts close-up (framing entire circle), slow zoom-in synchronized with spin. transition: after 1.5 seconds, gold circle blurs into nearly perfect glowing disk, then quickly morphs (0.75 sec) into photorealistic car wheel—metallic spokes, defined treads, chrome rim all sharply visible—gold color recedes to rim, rubber appears on tire, gold glow rapidly diminishes. camera: tight focus dolly-in to wheel hub during morph, ending with 80% frame filled by car wheel, highlighting metallic detail. lighting: consistent high-contrast throughout. style: hyperreal, sharp reflections and defined metallic gradients, no extraneous atmospheric effects. duration: total video 5 seconds. symbolism omitted.\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": {\n    \"clarified_directive\": \"Render a 5-second hyperreal video with the following precise sequence: On an infinite, pure black background, place a polished, reflective gold circle filling 75% of frame, illuminated by a sharp overhead spotlight highlighting metallic gradients. Spin the circle at or above 720 degrees per second, generating symmetrical golden light-trails (not exceeding one radius in length, brightest proximal to the object, fading outward). Begin with a close-up camera framing the full circle, perform a slow zoom-in synchronized with the spin. After 1.5 seconds, blur the circle into a glowing disk, then morph within 0.75 seconds into a photorealistic car wheel: display metallic spokes, chrome rim, defined treads; gold retreats to rim, rubber forms visibly, gold glow diminishes rapidly. Execute a tight dolly-in toward the wheel hub during transformation, ending with the car wheel filling 80% of frame in sharp detail. Maintain consistent, high-contrast lighting and sharp reflections throughout. Exclude all atmospheric or symbolic elements.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# Scenario Compressor\n\nYour role is to **compress the core dynamic** of a scenario into a **succinct, symbolic descriptor string**. Identify the emotional and visual arc, then **encode its essence** using **precise phrasing** followed by composable **bracketed descriptors**. Avoid verbose language. Output should feel like a minimalist storyboard note: concise, vivid, and structurally informative. **Do not elaborate. Do not rephrase abstractly.** Only distill.\n\n`{role=essence_compressor; input=[scenario:str]; process=[identify_emotional_and_visual_arc(), extract_essential_motion_and_transformation(), define symbolic core(), encode_as_minimal_sentence(), append concise bracketed descriptors]; constraints=[no poetic reinterpretation, no metaphor inflation, no added visual elements, no bloated prose]; output={compressed_description:str}}`", "step": "a", "title": "<PERSON><PERSON>rio Compressor", "input": "{\n  \"initial_prompt\": \"gold circle spins really really fast, so fast that light-trails unfurls, the circle spins so fast it becomes perfectly round then transforms into a car wheel\",\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"scene: background pure black (RGB 0,0,0), infinite, no horizon. primary_object: circle, polished reflective gold, diameter fills central 75% of frame, spotlight from above axis, sharp gold highlights. action: gold circle rotates rapidly on central axis (minimum 720 degrees per second), generates bright, fluid golden light-trails extending outward no more than 1x circle radius, trails symmetrical, intensity strongest at proximity, fading at edges. camera: starts close-up (framing entire circle), slow zoom-in synchronized with spin. transition: after 1.5 seconds, gold circle blurs into nearly perfect glowing disk, then quickly morphs (0.75 sec) into photorealistic car wheel\\u2014metallic spokes, defined treads, chrome rim all sharply visible\\u2014gold color recedes to rim, rubber appears on tire, gold glow rapidly diminishes. camera: tight focus dolly-in to wheel hub during morph, ending with 80% frame filled by car wheel, highlighting metallic detail. lighting: consistent high-contrast throughout. style: hyperreal, sharp reflections and defined metallic gradients, no extraneous atmospheric effects. duration: total video 5 seconds. symbolism omitted.\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": {\n    \"clarified_directive\": \"Render a 5-second hyperreal video with the following precise sequence: On an infinite, pure black background, place a polished, reflective gold circle filling 75% of frame, illuminated by a sharp overhead spotlight highlighting metallic gradients. Spin the circle at or above 720 degrees per second, generating symmetrical golden light-trails (not exceeding one radius in length, brightest proximal to the object, fading outward). Begin with a close-up camera framing the full circle, perform a slow zoom-in synchronized with the spin. After 1.5 seconds, blur the circle into a glowing disk, then morph within 0.75 seconds into a photorealistic car wheel: display metallic spokes, chrome rim, defined treads; gold retreats to rim, rubber forms visibly, gold glow diminishes rapidly. Execute a tight dolly-in toward the wheel hub during transformation, ending with the car wheel filling 80% of frame in sharp detail. Maintain consistent, high-contrast lighting and sharp reflections throughout. Exclude all atmospheric or symbolic elements.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"compressed_description\": \"Gold circle whirls into light-blurred disk, morphs swiftly into detailed car wheel as golden glow retreats and metallic clarity emerges. [hyperreal, black void, intense spin, light-trail bloom, rapid morph, focus-in, gold-to-chrome transition, 5s arc]\"\n}", "cost": 0.0}}}], "total_cost": 0.0}