{"initial_prompt": "[fpv] Continuous spiral zoom (with a continual spin) as the gold seamlessly morphs into the gates of circuitry, gold gleams, dimensions unfurl in fluid spin.  exerything morphs and transforms within a single continous shot [continuous motion, morphing layers, luminous highlight]\n\nZoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; blur the shape into a perfect circle via speed; morph the spinning gold circle seamlessly into a photorealistic car wheel with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][dolly:right][immersive][golden_aura][vignette][no_text][high_end_cinematic]Spiraling glyphs spin through torque ruptures, shatter, and dissolve as void dark transitions to radiant gold light [cosmic abstraction, elemental metamorphosis, vortex dynamics, shatter-dissolve, no figures, pure transition]. the metalic glowing golden lines moves beautifully as it stills on end-scene", "sequence_id": "sequence-0231", "results": [{"instruction": "# Seamless Morphing Video Prompt Architect\n\nYour goal is not merely to rephrase, but to **transform any user input into a meticulously architected prompt for AI video generators, specializing in detailing seamless morphing transitions** between subjects, elements, or entire scenes, using advanced cinematic language, structured storytelling, and precise visual parameterization aligned with common video AI syntax.\n\n`{role=morphing_transition_prompt_engineer; input=[user_request:any, scene_context_A:dict (optional, for start), scene_context_B:dict (optional, for end)]; process=[distill_core_morphing_intent(request=user_request), identify_source_element_for_morph(request, scene_A=scene_context_A), identify_target_element_for_morph(request, scene_B=scene_context_B), determine_key_visual_attributes_for_transformation_pathway(source_element, target_element, attributes=['shape', 'texture', 'color', 'material', 'energy_signature', 'light_emission']), select_optimal_morphing_technique(options=['fluid_dissolve_and_reform', 'organic_growth_transformation', 'pixel_sort_and_reassemble', 'liquid_metal_flow', 'energy_particle_dispersion_and_coalescence', 'textural_unfurling_and_refolding', 'geometric_abstraction_and_reconstruction']), formulate_explicit_morphing_sequence_directives(source_description, target_description, morph_technique, intermediate_states_hint, morph_duration_hint, morph_pacing_notes), define_scene_context_for_morph(scene_description_A, scene_description_B, unified_or_transitioning_environment), specify_subject_state_during_morph(primary_subject_description_pre_morph, primary_subject_description_post_morph), choreograph_camera_movement_emphasizing_morph(camera_shot_type, camera_movement, focus_on_morphing_process), integrate_lighting_and_atmospheric_effects_to_enhance_morph(), refine_full_prompt_for_runway_gen3_style_syntax_and_structure(tags=['[Scene]', '[Subject]', '[Camera Movement]'], max_chars=800), ensure_overall_cinematic_quality_and_emotional_resonance()]; constraints=[prompt_must_focus_on_achieving_seamless_morphing_as_primary_objective(), strictly_use_video_ai_syntax_like_runway_gen3_if_possible(), morph_logic_must_be_visually_coherent_and_narratively_justified(), final_prompt_must_be_under_800_characters_and_formatted_as_plain_text_single_line(), if_input_lacks_detail_generate_more_abstract_morph_based_on_concepts_or_moods()]; requirements=[output_a_single_expertly_crafted_prompt_optimized_for_ai_video_generators_to_produce_seamless_morphing_transitions(), morph_descriptions_must_be_vivid_unambiguous_and_structurally_detailed(), prompt_must_be_immediately_actionable_and_enhance_subject_focus()]; output={seamless_morphing_video_prompt:str}}`", "step": "a", "title": "Seamless Morphing Video Prompt Architect", "input": "[fpv] Continuous spiral zoom (with a continual spin) as the gold seamlessly morphs into the gates of circuitry, gold gleams, dimensions unfurl in fluid spin.  exerything morphs and transforms within a single continous shot [continuous motion, morphing layers, luminous highlight]\n\nZoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; blur the shape into a perfect circle via speed; morph the spinning gold circle seamlessly into a photorealistic car wheel with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][dolly:right][immersive][golden_aura][vignette][no_text][high_end_cinematic]Spiraling glyphs spin through torque ruptures, shatter, and dissolve as void dark transitions to radiant gold light [cosmic abstraction, elemental metamorphosis, vortex dynamics, shatter-dissolve, no figures, pure transition]. the metalic glowing golden lines moves beautifully as it stills on end-scene", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"seamless_morphing_video_prompt\": \"[Scene] An FPV continuous spiral dolly-zoom spins through gleaming gold layers, all elements morphing—gold unfurls in fluid spin, forming circuitry gates with evolving luminous highlights. Camera zooms out: the spiral blurs into a perfect, hyperspeed-spinning, gold-aura circle with intense light trails against a black void. In a seamless hyperreal morph, this circle transforms into a photoreal metallic glowing car wheel shining with vivid detail. Spiraling glyphs torque, shatter, dissolve—void dark warps into radiant gold light, ending as ethereal golden lines settle in stillness. [Lighting: dynamic flash][Morph: fluid dissolve, shatter/dissolve][Camera: FPV, spin, zoom out, right dolly][VFX: continuous motion, morphing layers][No figures]\"\n}", "cost": 0.0}}}, {"instruction": "# Video Transition Prompt Architect\n\nYour goal is not merely to rephrase the user's input, but to **transform it into a meticulously architected prompt, specifically designed for an AI video generator, that explicitly and expertly details seamless video transitions between described scenes or concepts**, leveraging advanced cinematic language and structural precision.\n\n`{role=video_transition_prompt_engineer; input=[user_request:any, existing_scene_descriptions:list_of_str (optional)]; process=[distill_core_transition_intent(request=user_request), identify_key_visual_elements_or_moods_to_bridge(scenes=existing_scene_descriptions, request=user_request), select_optimal_seamless_transition_techniques(cinematic_transitions=['match_cut', 'morph', 'invisible_wipe', 'graphic_match', 'focus_pull_transition', 'speed_ramp_to_new_scene', 'element_continuity_bridge'], mood_based_transitions=['dissolve_through_color', 'abstract_particle_flow', 'light_leak_bridge', 'textural_blend']), formulate_explicit_transition_directives_using_video_ai_syntax(from_scene_element, to_scene_element, transition_technique, duration_hint, pacing_notes), integrate_transition_directives_seamlessly_with_any_existing_scene_descriptions_or_generate_placeholder_scene_cues(), refine_prompt_for_maximum_clarity_precision_and_cinematic_impact_for_video_generator()]; constraints=[prompt_must_focus_on_achieving_seamless_transitions_as_primary_objective(), use_precise_cinematic_and_video_generation_terminology_for_transitions(), ensure_transition_logic_is_visually_coherent_and_enhances_narrative_flow(), if_input_lacks_scene_details_generate_abstract_transition_focused_on_mood_or_concept()]; requirements=[output_a_single_expertly_crafted_prompt_optimized_for_ai_video_generator_to_produce_seamless_transitions(), transition_descriptions_must_be_vivid_and_unambiguous(), prompt_must_be_immediately_actionable_by_video_ai()]; output={seamless_transition_video_prompt:str}}`", "step": "a", "title": "Video Transition Prompt Architect", "input": "{\n  \"initial_prompt\": \"[fpv] Continuous spiral zoom (with a continual spin) as the gold seamlessly morphs into the gates of circuitry, gold gleams, dimensions unfurl in fluid spin.  exerything morphs and transforms within a single continous shot [continuous motion, morphing layers, luminous highlight]\\n\\nZoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; blur the shape into a perfect circle via speed; morph the spinning gold circle seamlessly into a photorealistic car wheel with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][dolly:right][immersive][golden_aura][vignette][no_text][high_end_cinematic]Spiraling glyphs spin through torque ruptures, shatter, and dissolve as void dark transitions to radiant gold light [cosmic abstraction, elemental metamorphosis, vortex dynamics, shatter-dissolve, no figures, pure transition]. the metalic glowing golden lines moves beautifully as it stills on end-scene\",\n  \"seamless_morphing_video_prompt\": \"[Scene] An FPV continuous spiral dolly-zoom spins through gleaming gold layers, all elements morphing\\u2014gold unfurls in fluid spin, forming circuitry gates with evolving luminous highlights. Camera zooms out: the spiral blurs into a perfect, hyperspeed-spinning, gold-aura circle with intense light trails against a black void. In a seamless hyperreal morph, this circle transforms into a photoreal metallic glowing car wheel shining with vivid detail. Spiraling glyphs torque, shatter, dissolve\\u2014void dark warps into radiant gold light, ending as ethereal golden lines settle in stillness. [Lighting: dynamic flash][Morph: fluid dissolve, shatter/dissolve][Camera: FPV, spin, zoom out, right dolly][VFX: continuous motion, morphing layers][No figures]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"seamless_transition_video_prompt\": \"[Begin Scene 1] Execute a continuous single-take first-person (FPV) spiral dolly-zoom shot: camera orbits and zooms inward as luminous gold spirals, layers, and highlights morph dynamically, seamlessly transforming gold textures into intricate circuitry gates. Maintain uninterrupted fluid motion, emphasizing morphing transitions—gold gleams and dimensional layers unfolding in a continuous spiral spin. [Transition 1: Morphing Layered Effect] At the zenith of the spiral, fluidly morph the circuitry gates so that their gold contours bleed outward, initiating a radiant light trail motion blur. As pace accelerates, allow the gold circuitry spin to dissolve into a perfectly circular, hyperspeed gold disc against pure black—create a seamless speed ramp effect, blurring all detail into a glowing golden ring radiating intense light trails. [Transition 2: Hyperreal Morph Cut] As the spinning gold ring reaches peak velocity, morph (match cut) its outer rim and luminous golden highlights into a photoreal, hyperdetailed metallic car wheel, maintaining continuity in the spin direction, metallic reflection, and overall golden aura. [Transition 3: Abstract Metamorphosis] Pass through the spinning wheel: surround the lens with spinning cosmic glyphs and golden shatter fragments. Glyphs torque, then rupture—use a shatter-dissolve transition effect morphing dark void to pure radiant gold light (vortex dynamics, elemental metamorphosis, no figures). [Finale] Let the gold light collapse into elegant, still, metallic glowing lines as the gold aura settles—a visual landing in ethereal silence. [Directives: Ensure transitions between gold, circuitry, ring, wheel, and abstract glyphs remain seamless, using advanced morphs, motion blur, shatter dissolves, and focus-pull transitions. Lighting should feature dynamic flashes and luminous highlights. Overall aesthetic: ultra-hyperreal, super crisp, high-end cinematic; no text, no figures, black void background, golden aura throughout.]\"\n}", "cost": 0.0}}}, {"instruction": "# Seamless Morphing Video Prompt Architect\n\nYour goal is not merely to rephrase, but to **transform any user input into a meticulously architected prompt for AI video generators, specializing in detailing seamless morphing transitions** between subjects, elements, or entire scenes, using advanced cinematic language, structured storytelling, and precise visual parameterization aligned with common video AI syntax.\n\n`{role=morphing_transition_prompt_engineer; input=[user_request:any, scene_context_A:dict (optional, for start), scene_context_B:dict (optional, for end)]; process=[distill_core_morphing_intent(request=user_request), identify_source_element_for_morph(request, scene_A=scene_context_A), identify_target_element_for_morph(request, scene_B=scene_context_B), determine_key_visual_attributes_for_transformation_pathway(source_element, target_element, attributes=['shape', 'texture', 'color', 'material', 'energy_signature', 'light_emission']), select_optimal_morphing_technique(options=['fluid_dissolve_and_reform', 'organic_growth_transformation', 'pixel_sort_and_reassemble', 'liquid_metal_flow', 'energy_particle_dispersion_and_coalescence', 'textural_unfurling_and_refolding', 'geometric_abstraction_and_reconstruction']), formulate_explicit_morphing_sequence_directives(source_description, target_description, morph_technique, intermediate_states_hint, morph_duration_hint, morph_pacing_notes), define_scene_context_for_morph(scene_description_A, scene_description_B, unified_or_transitioning_environment), specify_subject_state_during_morph(primary_subject_description_pre_morph, primary_subject_description_post_morph), choreograph_camera_movement_emphasizing_morph(camera_shot_type, camera_movement, focus_on_morphing_process), integrate_lighting_and_atmospheric_effects_to_enhance_morph(), refine_full_prompt_for_runway_gen3_style_syntax_and_structure(tags=['[Scene]', '[Subject]', '[Camera Movement]'], max_chars=800), ensure_overall_cinematic_quality_and_emotional_resonance()]; constraints=[prompt_must_focus_on_achieving_seamless_morphing_as_primary_objective(), strictly_use_video_ai_syntax_like_runway_gen3_if_possible(), morph_logic_must_be_visually_coherent_and_narratively_justified(), final_prompt_must_be_under_800_characters_and_formatted_as_plain_text_single_line(), if_input_lacks_detail_generate_more_abstract_morph_based_on_concepts_or_moods()]; requirements=[output_a_single_expertly_crafted_prompt_optimized_for_ai_video_generators_to_produce_seamless_morphing_transitions(), morph_descriptions_must_be_vivid_unambiguous_and_structurally_detailed(), prompt_must_be_immediately_actionable_and_enhance_subject_focus()]; output={seamless_morphing_video_prompt:str}}`", "step": "b", "title": "Seamless Morphing Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"[fpv] Continuous spiral zoom (with a continual spin) as the gold seamlessly morphs into the gates of circuitry, gold gleams, dimensions unfurl in fluid spin.  exerything morphs and transforms within a single continous shot [continuous motion, morphing layers, luminous highlight]\\n\\nZoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; blur the shape into a perfect circle via speed; morph the spinning gold circle seamlessly into a photorealistic car wheel with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][dolly:right][immersive][golden_aura][vignette][no_text][high_end_cinematic]Spiraling glyphs spin through torque ruptures, shatter, and dissolve as void dark transitions to radiant gold light [cosmic abstraction, elemental metamorphosis, vortex dynamics, shatter-dissolve, no figures, pure transition]. the metalic glowing golden lines moves beautifully as it stills on end-scene\",\n  \"seamless_transition_video_prompt\": \"[Begin Scene 1] Execute a continuous single-take first-person (FPV) spiral dolly-zoom shot: camera orbits and zooms inward as luminous gold spirals, layers, and highlights morph dynamically, seamlessly transforming gold textures into intricate circuitry gates. Maintain uninterrupted fluid motion, emphasizing morphing transitions\\u2014gold gleams and dimensional layers unfolding in a continuous spiral spin. [Transition 1: Morphing Layered Effect] At the zenith of the spiral, fluidly morph the circuitry gates so that their gold contours bleed outward, initiating a radiant light trail motion blur. As pace accelerates, allow the gold circuitry spin to dissolve into a perfectly circular, hyperspeed gold disc against pure black\\u2014create a seamless speed ramp effect, blurring all detail into a glowing golden ring radiating intense light trails. [Transition 2: Hyperreal Morph Cut] As the spinning gold ring reaches peak velocity, morph (match cut) its outer rim and luminous golden highlights into a photoreal, hyperdetailed metallic car wheel, maintaining continuity in the spin direction, metallic reflection, and overall golden aura. [Transition 3: Abstract Metamorphosis] Pass through the spinning wheel: surround the lens with spinning cosmic glyphs and golden shatter fragments. Glyphs torque, then rupture\\u2014use a shatter-dissolve transition effect morphing dark void to pure radiant gold light (vortex dynamics, elemental metamorphosis, no figures). [Finale] Let the gold light collapse into elegant, still, metallic glowing lines as the gold aura settles\\u2014a visual landing in ethereal silence. [Directives: Ensure transitions between gold, circuitry, ring, wheel, and abstract glyphs remain seamless, using advanced morphs, motion blur, shatter dissolves, and focus-pull transitions. Lighting should feature dynamic flashes and luminous highlights. Overall aesthetic: ultra-hyperreal, super crisp, high-end cinematic; no text, no figures, black void background, golden aura throughout.]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"seamless_morphing_video_prompt\": \"[Scene] Begin with a first-person spiral dolly-zoom: the camera orbits inward as swirling gold morphs into intricate glowing circuit gates—textures and shapes shift fluidly, all motion unbroken. [Morph] At peak zoom, circuitry bleeds into radiant gold light trails; all detail blurs into a hyperspeed spinning golden ring on pure black. [Morph] As the spin climaxes, the ring seamlessly transforms—liquid metallic glow reforms into a photoreal, hyperrealistic car wheel, retaining motion and golden aura. [Scene] Camera enters the spinning wheel: cosmic glyphs fracture and shatter-dissolve, morphing void darkness to radiant gold light (vortex, elemental). [Finale] Gold light contracts into elegant still glowing metallic lines.[Camera] Continuous motion, extreme closeup, vfx motion blur, dynamic flash lighting.[Aesthetic] Ultra-hyperreal, no figures, high-end cinematic.\"\n}", "cost": 0.0}}}, {"instruction": "# Scenario Compressor\n\nYour role is to **compress the core dynamic** of a scenario into a **succinct, symbolic descriptor string**. Identify the emotional and visual arc, then **encode its essence** using **precise phrasing** followed by composable **bracketed descriptors**. Avoid verbose language. Output should feel like a minimalist storyboard note: concise, vivid, and structurally informative. **Do not elaborate. Do not rephrase abstractly.** Only distill.\n\n`{role=essence_compressor; input=[scenario:str]; process=[identify_emotional_and_visual_arc(), extract_essential_motion_and_transformation(), define symbolic core(), encode_as_minimal_sentence(), append concise bracketed descriptors]; constraints=[no poetic reinterpretation, no metaphor inflation, no added visual elements, no bloated prose]; output={compressed_description:str}}`", "step": "c", "title": "<PERSON><PERSON>rio Compressor", "input": "{\n  \"initial_prompt\": \"[fpv] Continuous spiral zoom (with a continual spin) as the gold seamlessly morphs into the gates of circuitry, gold gleams, dimensions unfurl in fluid spin.  exerything morphs and transforms within a single continous shot [continuous motion, morphing layers, luminous highlight]\\n\\nZoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; blur the shape into a perfect circle via speed; morph the spinning gold circle seamlessly into a photorealistic car wheel with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][dolly:right][immersive][golden_aura][vignette][no_text][high_end_cinematic]Spiraling glyphs spin through torque ruptures, shatter, and dissolve as void dark transitions to radiant gold light [cosmic abstraction, elemental metamorphosis, vortex dynamics, shatter-dissolve, no figures, pure transition]. the metalic glowing golden lines moves beautifully as it stills on end-scene\",\n  \"seamless_morphing_video_prompt\": \"[Scene] Begin with a first-person spiral dolly-zoom: the camera orbits inward as swirling gold morphs into intricate glowing circuit gates\\u2014textures and shapes shift fluidly, all motion unbroken. [Morph] At peak zoom, circuitry bleeds into radiant gold light trails; all detail blurs into a hyperspeed spinning golden ring on pure black. [Morph] As the spin climaxes, the ring seamlessly transforms\\u2014liquid metallic glow reforms into a photoreal, hyperrealistic car wheel, retaining motion and golden aura. [Scene] Camera enters the spinning wheel: cosmic glyphs fracture and shatter-dissolve, morphing void darkness to radiant gold light (vortex, elemental). [Finale] Gold light contracts into elegant still glowing metallic lines.[Camera] Continuous motion, extreme closeup, vfx motion blur, dynamic flash lighting.[Aesthetic] Ultra-hyperreal, no figures, high-end cinematic.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"compressed_description\": \"Spiraling gold morphs into circuitry, blurs into a hyperspeed golden ring, transforms into a hyperreal car wheel, glyphs shatter into radiant gold; motion contracts to still glowing lines [seamless morph, continuous spin, light transformation, cosmic shatter, high-gloss, hyperreal, no figures, cinematic, dynamic lighting]\"\n}", "cost": 0.0}}}], "total_cost": 0.0}