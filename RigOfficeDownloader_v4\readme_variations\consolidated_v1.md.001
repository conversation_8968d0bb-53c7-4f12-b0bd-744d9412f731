# Context

RigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).

---

# RigOfficeDownloader


## Purpose

Automate document retrieval from NOV's RigDoc (https://rigdoc.nov.com) via Python and Selenium.

- Reduce retrieval time and prevent workflow interruptions.
- Eliminate repetitive navigation and workflow interruptions.
- Avoid inconsistencies by streamlining organization and workflow.
- Reduce manual document retrieval time and eliminate repetitive navigation and downloading tasks.
- Reduce manual effort and errors
- Eliminate repetitive navigation and downloads
- Maintain workflow continuity
- Enable focus on core responsibilities
- Streamline and standardize retrieval processes
- Automate baseline document and file metadata collection.
- Minimizing manual intervention reduces error and prevents workflow breakage
- Standardization of processes yields consistency and reliability
- Automate document retrieval using Python and Selenium
- Eliminate repetitive manual navigation and downloads
- Standardize and streamline retrieval processes
- Collect documents and file metadata automatically
- Minimize errors by reducing manual intervention
- Enable users to focus on core responsibilities

Minimize inconsistencies by improving organization and processes.
Enable employees to focus on core tasks with efficient systems.


Automate document retrieval from NOV's RigDoc system (https://rigdoc.nov.com) using Python and Selenium via the web interface to eliminate the need for manual, repetitive, and error-prone processes in RigOffice. Reduce time spent on manual retrieval, minimize errors from inconsistent organization, eliminate repetitive navigation and downloading tasks, prevent workflow interruptions, and enable employees to focus on primary responsibilities. Ensure the solution increases efficiency and streamlines retrieval processes despite the absence of an official API.



 to replace repetitive, manual processes, increasing efficiency and freeing employees for core tasks despite lacking an official API.
- Reduce time spent on manual document retrieval
- Minimize errors caused by inconsistent manual organization
- Eliminate repetitive navigation and downloading tasks
- Prevent workflow interruptions and maintain process continuity
- Refocus attention on high-value, core responsibilities
- Enhance overall productivity by streamlining retrieval processes

---

## Problem

Manual document retrieval in RigOffice is slow and requires time investments for each project.
- Manual document retrieval is slow, tedious, and error-prone.
- Repetitive navigation and downloading make retrieval tedious.
- Workflow interruptions divert attention from other tasks and hinder focus and productivity.
- Repetitive actions and workflow interruptions hinder focus and productivity.

- Manual processes introduce inefficiency and error
- Repetitive tasks consume time and distract from primary objectives
- Lack of automation fosters inconsistencies and reduces reliability
- Workflow interruptions diminish productivity

- Manual document retrieval
- Slow, tedious, error-prone process
- Repetitive navigation and downloading
- Workflow interruptions
- Reduced focus and productivity
- Inefficiency and error introduction
- Time consumption and distraction from core tasks
- Lack of automation
- Inconsistencies and reduced reliability
- Operational inefficiency
- Negative impact on project timelines


- Automated scraping of document and file metadata, ensuring all required technical information is efficiently collected for downstream selection.
- Explicit, user-driven selection process via editable exports (e.g., Markdown), guaranteeing only relevant documents and files progress through the workflow.
- Stepwise, modular pipeline that separates data retrieval, human review, and execution, empowering user control, oversight, and intervention at each critical phase.
- Reliable and organized download orchestration, with file naming and structure linked to persistent metadata for traceability and streamlined access.
- Integrated mechanisms to prevent redundant work (e.g., file hash checking, idempotent updates), maximizing efficiency and ensuring accurate results.
- Continuous feedback and interactive controls that provide transparency, problem traceability, and the flexibility to adapt parameters or re-run steps as required.

Problem: Manual document retrieval in RigOffice is slow, repetitive, error-prone, and disrupts productivity; no API is available. Solution: Use Python and Selenium to automate retrieval via web interface (RigOfficeDownloader), reducing manual effort, errors, and workflow interruptions, thereby increasing efficiency and enabling employees to focus on core tasks.




It involves repetitive and tedious navigation and download actions.
Frequent interruptions disrupt workflow and focus.
Attention is diverted away from core, high-value tasks.
Cumulatively, these factors undermine overall efficiency and productivity.

Manual document retrieval in RigOffice is slow, tedious, and error-prone

hindering efficiency and undermines productivity.

This process increases time investment, fosters inconsistencies, interrupts workflows, diverts attention from core tasks, and undermines overall productivity.

- Time-consuming: Requiring manual time investments for each project
- Error-prone: Manual organization leads to inconsistencies
- Tedious: Involves repetitive navigation and download actions
- Distracting: Takes focus away and diverts attention from high-value tasks


Automation with Python and Selenium eliminates repetitive tasks and errors.
Efficiency increases, letting employees focus on core, high-value work.


---

Manual document retrieval in RigOffice is inefficient and repetitive, significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time.

Engineers working with NOV's equipment need to retrieve numerous technical documents and drawings from RigDoc, the company's document management system. This process is:


The utility streamlines document retrieval in RigOffice, eliminating repetitive manual searches and saving time before projects begin.


Manual document collection for projects wastes time due to inefficient processes (manually navigating poorly functioning applications to gather documents before work can begin).

 before they can begin their actual 3D modeling work. This manual process is:

- Tedious and repetitive
- Error-prone
- A poor use of skilled engineering time




Automates retrieval and download of files from the NOV RigDoc system (https://rigdoc.nov.com).


Automates project document retrieval from RigOffice to eliminate manual, error-prone tasks and save engineering time.



  "distilled_essence": "Automates document retrieval from NOV's RigDoc to save time and streamline engineering tasks."


## Overview

RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.

RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.

> Enable automation of document retrieval from NOV's RigDoc system (https://rigdoc.nov.com)

RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.

Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.

- Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.
- Use case: curate, review, and batch-download rig-related documents and technical files.

> **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.

RigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents → Files → Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.

To automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.

This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).

---


1. Fetch Documents
- The utility starts by scraping document metadata from predefined search URLs
- Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory
- Each document entry includes metadata like title, document number, revision, etc.
- All documents are initially marked with item_include=False
- Each document gets an item_generated_name for better identification

2. Export Documents to Markdown
- The JSON data is exported to a Markdown table: <rig>-a-docs.md
- This allows the user to easily review and edit which documents to include
- The user is expected to edit the Markdown file and set item_include=true for desired documents

3. Import Updated Document Data
- After the user edits the Markdown file, the utility imports the changes back to the JSON file
- This updates which documents are marked for file retrieval

4. Fetch Files for Selected Documents
- For each document with item_include=true, the utility scrapes file metadata
- File data is saved to <rig>-b-files.json
- Each file is initially marked with item_download=False
- Files inherit the document's item_generated_name with additional identifiers

5. Export Files to Markdown
- The file data is exported to a Markdown table: <rig>-b-files.md
- The user reviews and edits which files to download by setting item_download=true

6. Import Updated File Data
- After editing, the utility imports the changes back to the JSON file
- This updates which files are marked for download

7. Download Selected Files
- Files with item_download=true are downloaded
- Files are named according to their item_generated_name + extension
- The utility supports creating subfolders based on '/' in the item_generated_name
- Files are saved to the outputs/downloads/<rig> directory

Interactive Menu
- The utility provides an interactive menu where the user can choose which steps to execute
- This allows for flexibility in the workflow, enabling the user to run specific steps as needed
- The user can also update the rig number and search URLs through this menu



---

## Key Project Aspects

### Primary Problem
Engineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck—it's the manual preparation process.

### Solution Approach
A Python-based utility that:
1. Automatically scrapes document metadata from RigOffice
2. Extracts file information from those documents
3. Downloads and organizes selected files based on user criteria

### Current State
Functional working prototype that:
- Uses a 3-step workflow (document metadata → file metadata → download)
- Stores intermediate results in JSON format
- Allows user intervention between steps
- Provides progress feedback

### Critical Next Steps
1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches
2. **Implement file hash checking** to prevent redundant downloads
3. **Improve progress visibility** during lengthy scraping operations

### Core Technical Pattern
A single-file, modular approach using:
- Selenium for browser automation
- JSON for data storage
- Three-stage processing with user control points
- Incremental updates to avoid redundant work

### Key Success Metrics
- Reduce documentation gathering time by 75%+
- Ensure reliable retrieval of required documentation
- Organize files in a way that streamlines workflow
- Support both broad searches (by rig number) and targeted searches (by specific documents)

---


## The Problem
Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:
- Tedious and repetitive
- Error-prone
- A poor use of skilled engineering time

## The Solution
RigOfficeDownloader automates the document retrieval process through a three-stage workflow:
1. **Document Retrieval**: Automatically scrapes document metadata
2. **File Metadata**: Fetches file information for selected documents
3. **Smart Downloads**: Downloads files with intelligent naming and organization

## Workflow Process
```python
1. Fetch Documents       # Scrape metadata → <rig>-a-docs.json
2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true
3. Import Selections     # Update JSON with user choices
4. Fetch Files           # Get file listings → <rig>-b-files.json
5. Export Files MD       # <rig>-b-files.md - Set item_download=true
6. Import File Choices   # Update file selections
7. Download Files        # Auto-organized with subfolder support
```


## Directory Structure

* `outputs/` (BASE\_OUTPUT): All results stored here
* `outputs/data/` (DATA\_DIR): Document and file metadata (JSON/MD)
* `outputs/downloads/` (DL\_DIR): Downloaded PDF and file outputs

## Pipeline Overview

1. Change search parameters (rig number, URLs)
2. Configure filter chain (add, edit, delete, toggle, reorder filters)
3. Fetch docs (scrape data from rigdoc.nov.com)
4. Export docs to Markdown (for selection/editing)
5. Import docs from Markdown (sync edited selection)
6. Fetch candidate files linked to selected docs
7. Export file list to Markdown (for editing/selecting files for download)
8. Import updated file list from Markdown
9. Download marked files (PDFs only, via Chrome)

## Manual Editing

* Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing
* Set `item_include` (docs) and `item_download` (files) fields

## Running the Tool

```bash
python rigdocscraper.py
```

* Interactive menu enables step selection (numbers/comma/space-separated)
* Supports adjusting parameters, filter configuration, and reviewing batch steps
* Prompts will guide through editing, import/export, and download procedures

## Troubleshooting

* Requires functioning Chrome installation; verify webdriver-manager compatibility
* Common issues: browser launch failures, login/captcha requirements, file permissions
* Output logs and warnings shown in terminal; inspect `outputs/data/` for progress
