# =======================================================
# [2025.04.15 20:51]
# 'https://aistudio.google.com/prompts/1AhinWk-71xlpkRbJv--CN_YBbWMwPbPM'

#!/usr/bin/env python3

# =============================================================================
# SECTION 1: Core Imports & Environment Setup
# =============================================================================
import asyncio
import json
import os
import sys
import argparse
from datetime import datetime
from typing import Dict, List, Any, Optional

# External Dependencies
from pydantic import BaseModel, Field  # Data validation and settings management
import litellm                     # LLM provider abstraction layer

# Internal Dependencies
# Import functions for loading and managing instruction templates/sequences
from templates.lvl1.templates_lvl1_md_catalog_generator import (
    load_catalog,               # Loads the template catalog structure
    get_sequence,               # Retrieves a specific sequence of templates
    get_all_sequences,          # Lists all available sequence IDs
    get_system_instruction,     # Extracts the system instruction from a template part
    regenerate_catalog          # Regenerates the catalog if needed
)


# =============================================================================
# SECTION 2: Centralized Configuration Management (`Config`)
# =============================================================================
class Config:
    """
    Provides a centralized and self-documenting configuration system.
    Manages LLM providers, models, parameters, and LiteLLM settings.
    Designed for clarity and extensibility.
    """
    # --- Environment Setup ---
    @staticmethod
    def setup_encoding():
        """Ensures UTF-8 output encoding for terminals to prevent character errors."""
        for stream in (sys.stdout, sys.stderr):
            if hasattr(stream, "reconfigure"):
                try:
                    stream.reconfigure(encoding="utf-8", errors="replace")
                except Exception as e:
                    # Silently ignore if reconfiguring fails (e.g., in certain environments)
                    # print(f"Warning: Could not reconfigure {stream.name} encoding: {e}", file=sys.stderr)
                    pass

    # --- Provider Identifiers ---
    # Define constants for provider names to avoid typos
    PROVIDER_ANTHROPIC = "anthropic"
    PROVIDER_DEEPSEEK  = "deepseek"
    PROVIDER_GOOGLE    = "google"
    PROVIDER_OPENAI    = "openai"

    # --- Default Provider Selection ---
    # Set the application-wide default provider. The *last assignment* is the active default.
    # This allows easy switching during development or testing by uncommenting/reordering.
    DEFAULT_PROVIDER = PROVIDER_DEEPSEEK
    DEFAULT_PROVIDER = PROVIDER_OPENAI
    DEFAULT_PROVIDER = PROVIDER_ANTHROPIC
    DEFAULT_PROVIDER = PROVIDER_GOOGLE  # Currently active default

    # --- Provider-Specific Model Configuration & Default Selection ---
    # Defines available models *per provider* and sets the *default model* for each provider.
    # The *last "model_name"* entry within each provider's dictionary determines its default model.
    # Additional provider-specific default parameters (e.g., temperature) can be added here.
    PROVIDER_MODELS = {
        PROVIDER_ANTHROPIC: {
            # Available Anthropic Models (Add or comment out as needed)
            "model_name": "claude-3-opus-20240229",
            "model_name": "claude-3-sonnet-20240229",
            "model_name": "claude-3-haiku-20240307",
            "model_name": "openrouter/anthropic/claude-3.7-sonnet:beta", # Active default for Anthropic
            # "temperature": 0.2, # Example provider-specific default
            # "max_tokens": 4000, # Example provider-specific default
        },
        PROVIDER_DEEPSEEK: {
            # Available DeepSeek Models
            "model_name": "deepseek/deepseek-reasoner",
            "model_name": "deepseek/deepseek-coder",
            "model_name": "deepseek/deepseek-chat", # Active default for DeepSeek
            # "temperature": 0.1, # Example provider-specific default
        },
        PROVIDER_GOOGLE: {
            # Available Google Models
            "model_name": "gemini/gemini-1.5-flash-latest",
            "model_name": "gemini/gemini-1.5-pro", # Temporary switch to Pro during Gemini 2 issues
            # "model_name": "gemini/gemini-2.0-flash", # Potential future model
            # "model_name": "gemini/gemini-2.5-pro-preview-03-25", # Potential future model
            # "model_name": "gemini/gemini-1.5-flash-latest", # Active default for Google
            # "temperature": 0.3, # Example provider-specific default
        },
        PROVIDER_OPENAI: {
            # Available OpenAI Models
            "model_name": "gpt-4o",
            "model_name": "gpt-4o-mini",
            "model_name": "gpt-3.5-turbo",
            "model_name": "gpt-3.5-turbo-instruct",
            "model_name": "gpt-3.5-turbo-1106",
            "model_name": "o3-mini", # Active default for OpenAI
            # "temperature": 0.1, # Example provider-specific default
        },
    }

    # --- User-Friendly Name to LiteLLM Model ID Mapping ---
    # Maps concise, user-facing names (used in CLI/scripts) to the actual identifiers
    # required by LiteLLM, often including the provider prefix.
    MODEL_REGISTRY = {
        # OpenAI Models
        "gpt-4o": "gpt-4o",
        "gpt-4o-mini": "gpt-4o-mini",
        "gpt-4-turbo": "gpt-4-turbo",
        "gpt-4": "gpt-4",
        "gpt-3.5-turbo": "gpt-3.5-turbo",
        "gpt-3.5-turbo-instruct": "gpt-3.5-turbo-instruct",
        "gpt-3.5-turbo-1106": "gpt-3.5-turbo-1106", # Example specific version
        "o3-mini": "o3-mini", # Example alias or alternative name

        # Anthropic Models (Note: LiteLLM often requires provider prefix)
        "claude-3-opus": "anthropic/claude-3-opus-20240229",
        "claude-3-sonnet": "anthropic/claude-3-sonnet-20240229",
        "claude-3-haiku": "anthropic/claude-3-haiku-20240307",
        "claude-3.7-sonnet": "openrouter/anthropic/claude-3.7-sonnet:beta", # Using OpenRouter ID

        # Google Models (Note: LiteLLM requires provider prefix)
        "gemini-pro": "gemini/gemini-1.5-pro",
        "gemini-flash": "gemini/gemini-1.5-flash-latest",
        # "gemini-2-flash": "gemini/gemini-2.0-flash", # Example future model mapping
        # "gemini-2.5-pro": "gemini/gemini-2.5-pro-preview-03-25", # Example future model mapping

        # Deepseek Models (Note: LiteLLM requires provider prefix)
        "deepseek-reasoner": "deepseek/deepseek-reasoner",
        "deepseek-coder": "deepseek/deepseek-coder",
        "deepseek-chat": "deepseek/deepseek-chat",
    }

    # --- Model Resolution Logic ---
    @classmethod
    def get_default_model(cls, provider: Optional[str] = None) -> str:
        """
        Retrieves the default model name for the specified provider.
        Uses the application default provider if none is specified.
        Relies on the 'last assignment wins' logic in PROVIDER_MODELS.
        Includes fallbacks if a provider block lacks a 'model_name'.

        Args:
            provider: The provider identifier (e.g., Config.PROVIDER_OPENAI). Defaults to cls.DEFAULT_PROVIDER.

        Returns:
            The default model name (user-facing name or LiteLLM ID) for the provider.
        """
        provider = provider or cls.DEFAULT_PROVIDER
        provider_config = cls.PROVIDER_MODELS.get(provider, {})

        # Return the last assigned 'model_name' in the provider's config block
        if "model_name" in provider_config:
            return provider_config["model_name"]

        # Define hardcoded fallbacks if 'model_name' is missing (should ideally not happen)
        fallbacks = {
            cls.PROVIDER_OPENAI: "gpt-3.5-turbo",
            cls.PROVIDER_ANTHROPIC: "claude-3-haiku-20240307", # Using the LiteLLM ID directly here
            cls.PROVIDER_GOOGLE: "gemini/gemini-1.5-flash-latest", # Using the LiteLLM ID
            cls.PROVIDER_DEEPSEEK: "deepseek/deepseek-chat" # Using the LiteLLM ID
        }
        fallback_model = fallbacks.get(provider, "gpt-3.5-turbo") # Ultimate fallback
        # print(f"Warning: No 'model_name' found for provider '{provider}'. Using fallback: '{fallback_model}'", file=sys.stderr)
        return fallback_model

    @classmethod
    def get_model_params(cls, model_name: Optional[str] = None, provider: Optional[str] = None) -> Dict[str, Any]:
        """
        Constructs the parameter dictionary for a LiteLLM API call.
        Resolves the user-friendly model name to its LiteLLM ID via MODEL_REGISTRY.
        Merges provider-specific defaults (if any) with the resolved model ID.

        Args:
            model_name: The user-friendly model name (e.g., "gpt-4o", "claude-3-haiku").
                        If None, uses the default model for the determined provider.
            provider: The provider identifier. If None, uses the application default provider.

        Returns:
            A dictionary containing the 'model' key (with the LiteLLM ID) and any
            other provider-specific default parameters.
        """
        provider = provider or cls.DEFAULT_PROVIDER
        # If no model name is given, determine the default for the provider
        model_name = model_name or cls.get_default_model(provider)

        # Retrieve provider-specific defaults from PROVIDER_MODELS
        provider_defaults = cls.PROVIDER_MODELS.get(provider, {}).copy()

        # Filter out the 'model_name' key itself, keeping only actual parameters
        params = {k: v for k, v in provider_defaults.items() if k != "model_name"}

        # Resolve the user-facing name to the actual LiteLLM model ID
        # If the name is not in the registry, assume it's already a valid LiteLLM ID
        actual_model_id = cls.MODEL_REGISTRY.get(model_name, model_name)

        # Return the final parameter dictionary, ensuring 'model' is set correctly
        return {
            "model": actual_model_id,
            **params  # Merge resolved model ID with other default parameters
        }

    # --- Informational Methods ---
    @classmethod
    def get_available_models(cls) -> Dict[str, List[Dict[str, Any]]]:
        """
        Generates a structured overview of all registered models, grouped by provider.
        Indicates which model is currently configured as the default for each provider.

        Returns:
            A dictionary where keys are provider names and values are lists of
            model dictionaries, each containing 'name', 'model_id', and 'is_default'.
        """
        available_models_structure = {}

        # Iterate through each configured provider
        for provider, config_block in cls.PROVIDER_MODELS.items():
            # Determine the currently selected default model for this provider
            current_default_model = cls.get_default_model(provider)

            provider_model_list = []
            # Iterate through the MODEL_REGISTRY to find models associated with this provider
            for user_name, model_id in cls.MODEL_REGISTRY.items():
                # Check if the model ID belongs to the current provider
                # (Handles OpenAI's lack of prefix and others' use of prefixes)
                is_openai_model = (provider == cls.PROVIDER_OPENAI and '/' not in model_id)
                is_other_provider_model = (provider != cls.PROVIDER_OPENAI and model_id.startswith(f"{provider}/"))
                # Also handle cases where model_id might be from OpenRouter etc. but conceptually linked
                # For simplicity, we check if the model_id matches any `model_name` in the provider block
                is_in_provider_block = model_id in config_block.values() or user_name in config_block.values()

                if is_openai_model or is_other_provider_model or is_in_provider_block:
                     # Check if this model is the active default for the provider
                    is_default = (user_name == current_default_model or model_id == current_default_model)
                    provider_model_list.append({
                        "name": user_name,        # User-friendly name
                        "model_id": model_id,     # LiteLLM identifier
                        "is_default": is_default  # Boolean flag
                    })

            # Sort models alphabetically by name for consistent listing
            provider_model_list.sort(key=lambda x: x['name'])
            available_models_structure[provider] = provider_model_list

        return available_models_structure

    # --- LiteLLM Initialization ---
    @classmethod
    def configure_litellm(cls):
        """Configures global LiteLLM settings."""
        # Essential LiteLLM Settings
        litellm.drop_params = True        # Prevent unsupported params from causing errors
        litellm.num_retries = 3           # Retry failed API calls up to 3 times
        litellm.request_timeout = 120     # Set timeout for API requests (seconds)
        litellm.calculate_cost = True     # Enable automatic cost calculation (requires setup)
        litellm.set_verbose = False       # Reduce LiteLLM's own console output

        # Apply UTF-8 encoding fix
        cls.setup_encoding()

        # Log configuration summary
        print(f"\n[Config] LiteLLM Initialized:")
        print(f"  - Default Provider: {cls.DEFAULT_PROVIDER}")
        print(f"  - Default Model   : {cls.get_default_model()}") # Display user-friendly name
        resolved_params = cls.get_model_params()
        print(f"  - Resolved ID   : {resolved_params.get('model')}") # Display resolved LiteLLM ID
        print(f"  - Retries: {litellm.num_retries}, Timeout: {litellm.request_timeout}s, Cost Calculation: {litellm.calculate_cost}")


# =============================================================================
# SECTION 3: Data Structures for Results (`Pydantic Models`)
# =============================================================================
class ModelResponse(BaseModel):
    """Represents the output and metadata from a single model call."""
    model: str = Field(..., description="User-friendly name or ID of the model used.")
    content: str = Field(..., description="The full text content generated by the model.")
    cost: float = Field(..., description="Calculated cost of the API call in USD.")


class InstructionResult(BaseModel):
    """Aggregates results for a single instruction step across all targeted models."""
    instruction: str = Field(..., description="The system instruction provided to the models for this step.")
    step: str = Field(..., description="Identifier for this step within the sequence (e.g., 'a', 'b', '01').")
    title: str = Field(..., description="A descriptive title for the instruction step.")
    responses: Dict[str, ModelResponse] = Field(..., description="Dictionary mapping model names to their respective ModelResponse objects.")


class ExecutionResults(BaseModel):
    """Encapsulates the complete results of executing a sequence for a given user prompt."""
    user_prompt: str = Field(..., description="The initial user prompt that triggered the sequence execution.")
    sequence_id: str = Field(..., description="Identifier of the instruction sequence used (e.g., '0099', 'text-my_sequence').")
    results: List[InstructionResult] = Field(..., description="List of results, one InstructionResult per step in the sequence.")
    total_cost: float = Field(..., description="Aggregated total cost of all LLM API calls for this execution in USD.")


# =============================================================================
# SECTION 4: Cost Tracking Utility
# =============================================================================
class CostTracker:
    """A simple utility class to accumulate the cost of LLM API calls."""
    def __init__(self):
        self._total_cost: float = 0.0

    def add(self, cost: Optional[float]):
        """Adds the cost of a completed API call to the total."""
        if cost is not None:
            self._total_cost += cost

    def total(self) -> float:
        """Returns the current accumulated cost."""
        return self._total_cost

    def reset(self):
        """Resets the accumulated cost to zero."""
        self._total_cost = 0.0

    def get_cost_and_reset_tracker(self, tracker_instance: 'CostTracker') -> float:
        """ Placeholder - Seems related to litellm callback, refine if needed. """
        # This method seems intended for a callback scenario.
        # In the current script structure, cost is tracked *after* the call completes.
        # If using litellm callbacks for cost, this might need adjustment.
        cost = tracker_instance.total()
        # tracker_instance.reset() # Decide if reset should happen here or elsewhere
        return cost

# =============================================================================
# SECTION 5: Legacy Text Sequence Support
# =============================================================================
def load_text_sequence(sequence_name: str) -> List[str]:
    """
    Loads instructions from a legacy plain text file.
    Assumes instructions are separated by "---".

    Args:
        sequence_name: The base name of the text file (e.g., "my_sequence" for "my_sequence.txt").

    Returns:
        A list of instruction strings.

    Raises:
        FileNotFoundError: If the specified sequence file does not exist.
    """
    templates_dir = os.path.join(os.path.dirname(__file__), "templates")
    sequence_path = os.path.join(templates_dir, f"{sequence_name}.txt")

    if not os.path.exists(sequence_path):
        raise FileNotFoundError(f"Legacy text sequence file not found: {sequence_path}")

    try:
        with open(sequence_path, "r", encoding="utf-8") as f:
            content = f.read()
        # Split by '---' and remove leading/trailing whitespace from each part
        instructions = [part.strip() for part in content.split("---") if part.strip()]
        if not instructions:
             print(f"Warning: Text sequence file '{sequence_path}' is empty or contains only separators.", file=sys.stderr)
        return instructions
    except Exception as e:
        print(f"Error reading text sequence file '{sequence_path}': {e}", file=sys.stderr)
        raise


# =============================================================================
# SECTION 6: Core Sequence Execution Engine
# =============================================================================
async def execute_sequence(
    sequence_steps: List[tuple],
    user_prompt: str,
    sequence_id: str,
    models: List[str],
    output_file: str,
    cost_tracker: CostTracker,
    system_instruction_extractor: callable, # Pass the correct extractor function
    **litellm_kwargs: Any # Pass through additional LiteLLM parameters
) -> None:
    """
    Executes a sequence of instruction steps against one or more LLM models.
    Streams results incrementally to a JSON file and tracks costs.

    Args:
        sequence_steps: List of tuples, where each tuple is (step_id, template_data).
                        template_data is expected to be processable by system_instruction_extractor.
        user_prompt: The initial user input to be processed by the sequence.
        sequence_id: Identifier for the sequence being executed (for reporting).
        models: A list of user-friendly model names to run each step against.
        output_file: Path to the file where JSON results will be saved.
        cost_tracker: An instance of CostTracker to accumulate API costs.
        system_instruction_extractor: Function to extract the system message from a template_data item.
        **litellm_kwargs: Additional keyword arguments (e.g., temperature, max_tokens)
                          to be passed directly to `litellm.acompletion`.
    """
    execution_results_list: List[InstructionResult] = [] # Store structured results in memory
    total_cost_start = cost_tracker.total() # Cost before this execution starts

    print(f"\n[Executor] Starting sequence '{sequence_id}' for prompt: '{user_prompt[:100]}...'")
    print(f"[Executor] Models to run: {', '.join(models)}")
    print(f"[Executor] Saving results to: {output_file}")

    # --- Incremental JSON File Writing ---
    # We write the JSON file piece by piece to handle potentially large outputs
    # and provide results even if interrupted.
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            # Write JSON header
            f.write('{\n')
            f.write(f'  "user_prompt": {json.dumps(user_prompt)},\n')
            f.write(f'  "sequence_id": {json.dumps(sequence_id)},\n')
            f.write('  "results": [\n') # Start of the main results array

            # --- Process Each Step in the Sequence ---
            for i, (step_id, template_data) in enumerate(sequence_steps):
                # Extract system instruction and title using the provided function
                system_instruction = system_instruction_extractor(template_data)
                # Use get() for title in case the structure differs (e.g., legacy text)
                title = template_data.get("parts", {}).get("title", f"Step {step_id}") if isinstance(template_data, dict) else f"Step {step_id}"

                print(f"\n--- Step {step_id} ({title}) ---")
                print(f"System Instruction: {system_instruction[:150]}...")

                # Add comma separator between JSON objects in the array
                if i > 0:
                    f.write(",\n")

                # Write the start of the JSON object for this instruction step
                f.write('    {\n')
                f.write(f'      "instruction": {json.dumps(system_instruction)},\n')
                f.write(f'      "step": {json.dumps(step_id)},\n')
                f.write(f'      "title": {json.dumps(title)},\n')
                f.write('      "responses": {\n') # Start of the responses dictionary for this step

                step_model_responses: Dict[str, ModelResponse] = {}

                # --- Run Each Model for the Current Step ---
                for j, model_name in enumerate(models):
                    print(f"  Model: {model_name}")

                    if j > 0:
                        f.write(',\n') # Add comma separator between model response objects

                    # Write the start of the JSON object for this model's response
                    f.write(f'        {json.dumps(model_name)}: {{\n') # Key is the model name
                    f.write(f'          "model": {json.dumps(model_name)},\n')
                    f.write('          "content": "') # Start the content string (will be escaped)
                    f.flush() # Ensure previous parts are written to disk

                    # --- Call LiteLLM API ---
                    step_cost_before = cost_tracker.total()
                    full_response_content = ""
                    response_metadata = None
                    error_occurred = False

                    try:
                        # Prepare model parameters, merging defaults with overrides
                        model_params = Config.get_model_params(model_name)
                        model_params.update(litellm_kwargs) # Apply CLI/caller overrides

                        # Define messages for the LLM
                        # NOTE: Appending 'RESPONSE MUST BE A VALID JSON OBJECT.'
                        # This is a crucial instruction for models when JSON output is expected.
                        # Adapt or remove if the sequence step doesn't require JSON output.
                        messages = [
                            {"role": "system", "content": system_instruction + "\n\nRESPONSE MUST BE A VALID JSON OBJECT."},
                            {"role": "user", "content": user_prompt}
                        ]

                        # Initiate asynchronous streaming completion call
                        response_stream = await litellm.acompletion(
                            messages=messages,
                            stream=True,
                            **model_params # Pass combined parameters
                        )

                        # Process the stream chunks
                        print("    Response: ", end="") # Indicate stream start
                        async for chunk in response_stream:
                            # Extract text content from the chunk
                            text_piece = chunk.choices[0].delta.content or ""
                            print(text_piece, end="", flush=True) # Print stream to console in real-time
                            full_response_content += text_piece

                            # Capture response metadata (like cost) from the first chunk or as available
                            if response_metadata is None and hasattr(chunk, 'cost'):
                                response_metadata = chunk # Store the chunk containing metadata

                        print() # Newline after the full response stream for this model

                    except Exception as e:
                        error_message = f"LLM API Error ({model_name}): {e}"
                        print(f"\n    ERROR: {error_message}", file=sys.stderr)
                        full_response_content = f"Error: {e}" # Store error in content
                        error_occurred = True
                        # Attempt to continue with other models/steps

                    # --- Finalize Model Response & Cost ---
                    step_cost = 0.0
                    # Try extracting cost from LiteLLM's response metadata or callback mechanism
                    if not error_occurred and response_metadata and hasattr(response_metadata, 'cost') and response_metadata.cost:
                         step_cost = response_metadata.cost.get('total_cost', 0.0) # Access nested cost
                         cost_tracker.add(step_cost) # Add cost if found in metadata
                    elif not error_occurred and hasattr(litellm.completion_cost, '__defaults__'): # Fallback via global tracker? (Less reliable with async)
                        # This fallback might be less reliable with concurrent async calls.
                        # Explicit cost tracking via callbacks or post-call calculation is safer.
                        current_total = cost_tracker.total()
                        step_cost = current_total - step_cost_before
                        # cost_tracker.add(step_cost) # Already added potentially? Be careful not to double add.

                    # Escape the collected content for safe JSON embedding
                    escaped_content = json.dumps(full_response_content)[1:-1] # Use json.dumps for robust escaping, then strip outer quotes

                    # Write the rest of the model's response object to the file
                    f.write(escaped_content) # Write the escaped content
                    f.write('",\n')          # Close the content string
                    f.write(f'          "cost": {step_cost:.6f}\n') # Write the calculated cost
                    f.write('        }') # Close the model response object
                    f.flush() # Ensure write to disk

                    # Store the result in memory as well
                    step_model_responses[model_name] = ModelResponse(
                        model=model_name,
                        content=full_response_content, # Store raw content
                        cost=step_cost
                    )

                # --- Finalize Step Results ---
                f.write('\n      }\n') # Close the "responses" dictionary for the step
                f.write('    }') # Close the JSON object for the instruction step
                f.flush()

                # Append the structured result for this step to the in-memory list
                instruction_result_obj = InstructionResult(
                    instruction=system_instruction,
                    step=step_id,
                    title=title,
                    responses=step_model_responses
                )
                execution_results_list.append(instruction_result_obj)

            # --- Finalize JSON File ---
            f.write('\n  ],\n') # Close the main "results" array
            total_execution_cost = cost_tracker.total() - total_cost_start
            f.write(f'  "total_cost": {total_execution_cost:.6f}\n') # Write the total cost for this run
            f.write('}\n') # Close the root JSON object

    except IOError as e:
        print(f"\n[Executor] FATAL ERROR: Could not write to output file '{output_file}': {e}", file=sys.stderr)
        # Optionally re-raise or handle differently
        raise
    except Exception as e:
        print(f"\n[Executor] UNEXPECTED ERROR during execution: {e}", file=sys.stderr)
        # Optionally write partial results or error info to the file if possible
        # Re-raise the exception to indicate failure
        raise

    # --- Print Execution Summary ---
    print("\n=== EXECUTION SUMMARY ===")
    print(f"Sequence ID : {sequence_id}")
    print(f"Models Used : {', '.join(models)}")
    print(f"Total Cost  : ${total_execution_cost:.6f}")
    print(f"Results File: {output_file}")

    # Optionally print snippets of results (can be verbose)
    # for idx, result_step in enumerate(execution_results_list):
    #     print(f"\nStep {result_step.step} ({result_step.title}):")
    #     for model, resp in result_step.responses.items():
    #         snippet = resp.content[:80].replace('\n', ' ') + "..."
    #         print(f"  - {model}: Cost ${resp.cost:.4f} -> \"{snippet}\"")

    print("\n[Executor] Sequence execution completed.")


# =============================================================================
# SECTION 7: Command-Line Interface (CLI) & Main Application Logic
# =============================================================================
def print_available_models():
    """Prints a formatted list of models available via the Config class."""
    models_by_provider = Config.get_available_models()

    print("\n=== Available Models (via Config) ===")
    if not models_by_provider:
        print("No models configured or found in MODEL_REGISTRY.")
        return

    for provider, models in models_by_provider.items():
        print(f"\nProvider: {provider.upper()}")
        if not models:
            print("  (No models registered for this provider)")
            continue
        for model_info in models:
            default_marker = " (*Default*)" if model_info["is_default"] else ""
            print(f"  - Name: {model_info['name']}{default_marker}")
            # Only show the ID if it's different from the name for clarity
            if model_info['name'] != model_info['model_id']:
                print(f"    LiteLLM ID: {model_info['model_id']}")


async def main():
    """Parses CLI arguments, orchestrates configuration, and runs the sequence execution."""
    parser = argparse.ArgumentParser(
        description="Execute multi-step instruction sequences across various LLM providers using LiteLLM.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter # Show defaults in help
    )

    # --- Core Execution Arguments ---
    parser.add_argument("--sequence", type=str, default="0099",
                        help="Sequence ID from catalog (e.g., '0002') or base name of a legacy .txt file (requires --use-text). Default is '0099' (essence extraction).")
    parser.add_argument("--prompt", type=str,
                        help="User prompt text to process through the sequence. If omitted, a default prompt is used.")
    parser.add_argument("--output", type=str,
                        help="Path for the output JSON results file. If omitted, a timestamped filename is generated.")

    # --- Model Selection Arguments ---
    model_group = parser.add_mutually_exclusive_group()
    model_group.add_argument("--models", type=str,
                             help="Comma-separated list of user-friendly model names (from Config.MODEL_REGISTRY) to execute.")
    model_group.add_argument("--provider", type=str, choices=[Config.PROVIDER_OPENAI, Config.PROVIDER_ANTHROPIC, Config.PROVIDER_GOOGLE, Config.PROVIDER_DEEPSEEK],
                             help=f"Specify a provider; uses the default model configured for that provider in Config. Overrides the global default ({Config.DEFAULT_PROVIDER}).")

    # --- Execution Mode Arguments ---
    parser.add_argument("--use-text", action="store_true",
                        help="Load sequence from a legacy '.txt' file (named '<sequence>.txt') instead of the structured catalog.")
    parser.add_argument("--force-regenerate", action="store_true",
                        help="Force regeneration of the template catalog before execution.")

    # --- Informational Arguments ---
    parser.add_argument("--list-sequences", action="store_true",
                        help="List available sequences from the catalog and exit.")
    parser.add_argument("--list-models", action="store_true",
                        help="List available models defined in the Config class and exit.")

    # --- LLM Parameter Overrides ---
    parser.add_argument("--temperature", type=float, default=None,
                        help="Override model temperature (0.0-2.0). If unset, uses provider/model default.")
    parser.add_argument("--max-tokens", type=int, default=None,
                        help="Override maximum tokens for generation. If unset, uses provider/model default.")

    args = parser.parse_args()

    # --- Initial Setup ---
    Config.configure_litellm() # Configure LiteLLM and environment first
    cost_tracker = CostTracker() # Initialize cost tracking

    # --- Handle Informational Flags ---
    if args.list_models:
        print_available_models()
        sys.exit(0)

    # Load or regenerate the template catalog (unless using text mode)
    catalog = None
    if not args.use_text or args.list_sequences or args.force_regenerate:
        print("[Main] Loading/Regenerating template catalog...")
        try:
            catalog = regenerate_catalog(force=args.force_regenerate)
            if not catalog:
                 print("[Main] Error: Failed to load or regenerate catalog.", file=sys.stderr)
                 sys.exit(1)
            print("[Main] Catalog ready.")
        except Exception as e:
            print(f"[Main] Error loading/regenerating catalog: {e}", file=sys.stderr)
            sys.exit(1)


    if args.list_sequences:
        if not catalog:
             print("[Main] Cannot list sequences, catalog not loaded.", file=sys.stderr)
             sys.exit(1)
        print("\n=== Available Sequences (from Catalog) ===")
        all_seq_ids = get_all_sequences(catalog)
        if not all_seq_ids:
             print("No sequences found in the catalog.")
        else:
            for seq_id in sorted(all_seq_ids): # Sort for consistent listing
                sequence = get_sequence(catalog, seq_id)
                if sequence:
                    # Attempt to get title from the first step for context
                    first_step_template = sequence[0][1]
                    title = first_step_template.get("parts", {}).get("title", "N/A")
                    print(f"  - ID: {seq_id:<6} | Title: {title:<40} | Steps: {len(sequence)}")
                else:
                    print(f"  - ID: {seq_id:<6} | (Error retrieving sequence details)")
        sys.exit(0)

    # --- Determine Execution Parameters ---

    # 1. Select Models
    selected_models: List[str]
    if args.models:
        selected_models = [model.strip() for model in args.models.split(",") if model.strip()]
        print(f"[Main] Using models specified via --models: {selected_models}")
    elif args.provider:
        selected_models = [Config.get_default_model(args.provider)]
        print(f"[Main] Using default model for specified provider ({args.provider}): {selected_models}")
    else:
        # Default: Use the default model of the application's default provider
        selected_models = [Config.get_default_model()] # Default provider already set in Config
        print(f"[Main] Using default model for default provider ({Config.DEFAULT_PROVIDER}): {selected_models}")

    if not selected_models:
        print("[Main] Error: No valid models selected for execution.", file=sys.stderr)
        sys.exit(1)

    # 2. Define User Prompt
    # Provide a default prompt if none is given via CLI arguments
    default_prompt = """Analyze the following requirement and generate a concise summary of key objectives and potential challenges: "Implement a real-time dashboard displaying key performance indicators (KPIs) for website traffic, user engagement, and conversion rates, sourcing data from Google Analytics and our internal sales database. The dashboard must be accessible on desktop and mobile devices." """
    user_prompt = args.prompt or default_prompt
    # print(f"[Main] Using prompt: {user_prompt}") # Can be very long, print truncated?

    # 3. Determine Sequence ID and Source
    sequence_id = args.sequence
    source_type = "text" if args.use_text else "catalog"
    display_sequence_id = f"{source_type}-{sequence_id}" # For logging/output filename

    # 4. Prepare Output File Path
    if args.output:
        output_path = args.output
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_tag = "_".join(selected_models).replace('/','-') # Create a model tag for filename
        output_path = f"output_{timestamp}_{display_sequence_id}_{model_tag[:30]}.json" # Truncate model tag if too long
    # Ensure output directory exists if specified as part of the path
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"[Main] Created output directory: {output_dir}")

    # 5. Load Sequence Steps and Determine Instruction Extractor
    sequence_steps: List[tuple]
    system_instruction_extractor: callable

    if args.use_text:
        print(f"[Main] Loading sequence '{sequence_id}' from legacy text file...")
        try:
            instructions = load_text_sequence(sequence_id)
            # Adapt legacy format to the expected (step_id, template_data) structure
            sequence_steps = [
                (chr(97 + i), {"raw": instr}) # step_id='a', 'b', ..., template_data={'raw': instruction_string}
                for i, instr in enumerate(instructions)
            ]
            # Define a simple extractor for the raw text format
            def get_text_instruction(template_data):
                return template_data.get("raw", "")
            system_instruction_extractor = get_text_instruction
            print(f"[Main] Loaded {len(sequence_steps)} steps from text file.")
        except FileNotFoundError:
            print(f"[Main] Error: Text sequence file '{sequence_id}.txt' not found in templates directory.", file=sys.stderr)
            sys.exit(1)
        except Exception as e:
             print(f"[Main] Error processing text sequence file '{sequence_id}.txt': {e}", file=sys.stderr)
             sys.exit(1)
    else:
        if not catalog:
             print("[Main] Error: Catalog not loaded, cannot retrieve catalog sequence.", file=sys.stderr)
             sys.exit(1)
        print(f"[Main] Loading sequence '{sequence_id}' from catalog...")
        sequence_steps = get_sequence(catalog, sequence_id)
        if not sequence_steps:
            print(f"[Main] Error: Sequence ID '{sequence_id}' not found in the catalog.", file=sys.stderr)
            print("Use --list-sequences to see available IDs.")
            sys.exit(1)
        # Use the standard instruction extractor from the imported module
        system_instruction_extractor = get_system_instruction
        print(f"[Main] Loaded {len(sequence_steps)} steps from catalog.")

    if not sequence_steps:
        print("[Main] Error: No sequence steps loaded. Cannot proceed.", file=sys.stderr)
        sys.exit(1)


    # 6. Collect LiteLLM Parameter Overrides
    litellm_overrides = {}
    if args.temperature is not None:
        litellm_overrides["temperature"] = args.temperature
        print(f"[Main] Overriding temperature: {args.temperature}")
    if args.max_tokens is not None:
        litellm_overrides["max_tokens"] = args.max_tokens
        print(f"[Main] Overriding max_tokens: {args.max_tokens}")

    # --- Execute the Sequence ---
    try:
        await execute_sequence(
            sequence_steps=sequence_steps,
            user_prompt=user_prompt,
            sequence_id=display_sequence_id, # Use the display ID
            models=selected_models,
            output_file=output_path,
            cost_tracker=cost_tracker,
            system_instruction_extractor=system_instruction_extractor, # Pass the correct function
            **litellm_overrides # Pass any CLI overrides
        )
    except Exception as e:
        # Catch errors from execute_sequence (already printed internally)
        print(f"[Main] Execution failed: {e}", file=sys.stderr)
        sys.exit(1)

    print("\n[Main] Process finished.")


# =============================================================================
# SECTION 8: Script Entry Point
# =============================================================================
if __name__ == "__main__":
    # Ensure the script runs within an asyncio event loop
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n[Main] Execution interrupted by user.", file=sys.stderr)
        sys.exit(1)
