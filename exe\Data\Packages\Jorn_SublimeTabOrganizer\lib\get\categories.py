import os
import time
import sublime
from .tabs import is_in_project, view_details

TAB_CATEGORIES = [
    "empty_and_deleted",
    "empty_and_unsaved",
    "deleted_not_empty",
    "unsaved_not_empty",
    "clean_and_external",
    "clean_and_project",
    "dirty_and_external",
    "dirty_and_project",
    "inactive_and_project",
    "inactive_and_external",
    "short_lived_and_project",
    "short_lived_and_external",
    "uncategorized",
]

def determine_category(view):
    if not view:
        return "uncategorized"
    
    content_stripped = view.substr(sublime.Region(0, view.size())).strip()
    content_length = len(content_stripped)
    file_name = view.file_name()
    
    if not file_name:
        if content_length <= 1:
            return "empty_and_unsaved"
        return "unsaved_not_empty"
    
    if not os.path.exists(file_name):
        if content_length <= 1:
            return "empty_and_deleted"
        return "deleted_not_empty"
    
    in_project = is_in_project(view)
    is_dirty = view.is_dirty()
    
    last_accessed = view.settings().get('jorn_last_accessed_time')
    if last_accessed and (time.time() - last_accessed > 3600):
        return "inactive_and_project" if in_project else "inactive_and_external"
    
    if file_name and os.path.exists(file_name):
        creation_time = os.path.getctime(file_name)
        if (time.time() - creation_time < 300):
            return "short_lived_and_project" if in_project else "short_lived_and_external"
    
    if in_project:
        return "dirty_and_project" if is_dirty else "clean_and_project"
    else:
        return "dirty_and_external" if is_dirty else "clean_and_external"

def categorize_views(views, include_details=False):
    result = {category: [] for category in TAB_CATEGORIES}
    
    for view in views:
        category = determine_category(view)
        
        if include_details:
            details = view_details(view)
            result[category].append((view, details))
        else:
            result[category].append(view)
    
    return result

def categorize_window_views(window, include_details=False):
    return categorize_views(window.views(), include_details)

def views_by_category(window, category):
    categorized = categorize_window_views(window)
    return categorized.get(category, [])
