{"initial_prompt": "rephrase into a generalized and clarified sentence that describes the problem the utility is made to adress (in a single unbroken line of less than 200 characters): '''# Context\n\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\n\n---\n\n# RigOfficeDownloader\n\n## Purpose\n\n> Enable automation of document retrieval from NOV's RigDoc system (https://rigdoc.nov.com), eliminating tedious manual processes and allowing engineers to focus on their primary work\n\n## Problem\n\n> Manual document retrieval in RigOffice is inefficient and repetitive, requiring automation to save engineering time. Significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time'''", "sequence_id": "sequence-0001-a+0001-b+0001-a+0001-b", "results": [{"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "rephrase into a generalized and clarified sentence that describes the problem the utility is made to adress (in a single unbroken line of less than 200 characters): '''# Context\n\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\n\n---\n\n# RigOfficeDownloader\n\n## Purpose\n\n> Enable automation of document retrieval from NOV's RigDoc system (https://rigdoc.nov.com), eliminating tedious manual processes and allowing engineers to focus on their primary work\n\n## Problem\n\n> Manual document retrieval in RigOffice is inefficient and repetitive, requiring automation to save engineering time. Significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time'''", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Summarize the problem addressed by the utility in a single clear sentence (under 200 characters): Manual document retrieval from RigOffice is inefficient and repetitive, wasting engineering time due to a lack of automation.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"rephrase into a generalized and clarified sentence that describes the problem the utility is made to adress (in a single unbroken line of less than 200 characters): '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Purpose\\n\\n> Enable automation of document retrieval from NOV's RigDoc system (https://rigdoc.nov.com), eliminating tedious manual processes and allowing engineers to focus on their primary work\\n\\n## Problem\\n\\n> Manual document retrieval in RigOffice is inefficient and repetitive, requiring automation to save engineering time. Significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time'''\",\n  \"instruction_format\": \"Summarize the problem addressed by the utility in a single clear sentence (under 200 characters): Manual document retrieval from RigOffice is inefficient and repetitive, wasting engineering time due to a lack of automation.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": \"Manual document retrieval in RigOffice is slow and repetitive, causing unnecessary delays and wasting engineering time due to lack of automation.\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "{\n  \"initial_prompt\": \"rephrase into a generalized and clarified sentence that describes the problem the utility is made to adress (in a single unbroken line of less than 200 characters): '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Purpose\\n\\n> Enable automation of document retrieval from NOV's RigDoc system (https://rigdoc.nov.com), eliminating tedious manual processes and allowing engineers to focus on their primary work\\n\\n## Problem\\n\\n> Manual document retrieval in RigOffice is inefficient and repetitive, requiring automation to save engineering time. Significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time'''\",\n  \"distilled_essence\": \"Manual document retrieval in RigOffice is slow and repetitive, causing unnecessary delays and wasting engineering time due to lack of automation.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Describe the main problem addressed by the utility in a single, clear sentence under 200 characters, highlighting that manual document retrieval in RigOffice is inefficient and wastes skilled engineering time due to lack of automation.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"rephrase into a generalized and clarified sentence that describes the problem the utility is made to adress (in a single unbroken line of less than 200 characters): '''# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Purpose\\n\\n> Enable automation of document retrieval from NOV's RigDoc system (https://rigdoc.nov.com), eliminating tedious manual processes and allowing engineers to focus on their primary work\\n\\n## Problem\\n\\n> Manual document retrieval in RigOffice is inefficient and repetitive, requiring automation to save engineering time. Significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time'''\",\n  \"instruction_format\": \"Describe the main problem addressed by the utility in a single, clear sentence under 200 characters, highlighting that manual document retrieval in RigOffice is inefficient and wastes skilled engineering time due to lack of automation.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": \"Manual document retrieval in RigOffice is repetitive and inefficient, wasting engineers' time due to the absence of automation.\"\n}", "cost": 0.0}}}], "total_cost": 0.0}