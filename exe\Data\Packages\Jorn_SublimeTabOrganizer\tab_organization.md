| Save Count | Depth | Last Saved | Ext | File | Status | Group | Index | Modified | Lines | Size | Last Access | Category | Path | 
| ---------- | ----- | ---------- | --- | ---- | ------ | ----- | ----- | -------- | ----- | ---- | ----------- | -------- | ---- | 
| 0 | 9 | D : unsaved | md | test_layout.md | Dirty,External | 0 | 0 | N/A | 53 | 4.5 KB | N/A | deleted_not_empty | C:\Users\<USER>\Desktop\SCRATCH\2025.05.25-kl.13.45--WindowTiler\WindowTiler\Jorn_WindowTiler_v5 1\src\test_layout.md | 
| 0 | 11 | D : unsaved | py | testtttt.py | Dirty,External | 0 | 3 | N/A | 2 | < 1 KB | N/A | deleted_not_empty | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\testtttt.py | 
| 0 | 0 | D : unsaved | html | Untitled | Dirty,Unsaved | 0 | 4 | N/A | 81 | 4.5 KB | N/A | unsaved_not_empty | N/A | 
| 0 | 0 | D : unsaved | py | Untitled | Dirty,Unsaved | 0 | 5 | N/A | 95 | 4.0 KB | N/A | unsaved_not_empty | N/A | 
| 0 | 8 | D : unsaved | txt | WindowTiler.code-workspace | External | 0 | 1 | 2025.05.25-kl.16.34 | 20 | < 1 KB | N/A | clean_and_external | C:\Users\<USER>\Desktop\SCRATCH\2025.05.25-kl.18.28--aiflow\ai\system\WindowTiler.code-workspace | 
| 0 | 12 | D : unsaved | txt | all-projects.code-workspace | External | 0 | 2 | 2024.09.14-kl.18.47 | 62 | 1.0 KB | N/A | clean_and_external | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_autogpt\exe\AutoGPT-master\.vscode\all-projects.code-workspace | 
| 4 | 13 | 0 : <5 min | md | tab_organization.md | External | 0 | 6 | 2025.05.26-kl.14.17 | 32 | 12.6 KB | N/A | clean_and_external | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_SublimeTabOrganizer\tab_organization.md | 
| 0 | 12 | D : unsaved | md | 2025.05.19-kl.11.11--rigoffice_readme.003.md | Clean | 1 | 0 | 2025.05.19-kl.11.50 | 878 | 38.1 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\__meta__\2025.05.19-kl.11.11--rigoffice_readme.003.md | 
| 0 | 14 | D : unsaved | md | 2025.05.18-kl.18.57-memorybank_elegant_direction.002.md | Clean | 1 | 1 | 2025.05.18-kl.21.20 | 173 | 8.8 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\__meta__\memorybank\prompts\2025.05.18-kl.18.57-memorybank_elegant_direction.002.md | 
| 0 | 14 | D : unsaved | md | 2025.05.18-kl.18.57-memorybank_elegant_direction.003.md | Clean | 1 | 2 | 2025.05.18-kl.21.20 | 173 | 8.8 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\__meta__\memorybank\prompts\2025.05.18-kl.18.57-memorybank_elegant_direction.003.md | 
| 0 | 14 | D : unsaved | md | 2025.05.18-kl.18.57-memorybank_elegant_direction.004.md | Clean | 1 | 3 | 2025.05.18-kl.22.12 | 112 | 5.1 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\__meta__\memorybank\prompts\2025.05.18-kl.18.57-memorybank_elegant_direction.004.md | 
| 0 | 14 | D : unsaved | md | 2025.05.18-kl.18.57-memorybank_elegant_direction.005.md | Clean | 1 | 4 | 2025.05.18-kl.22.49 | 114 | 4.3 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\__meta__\memorybank\prompts\2025.05.18-kl.18.57-memorybank_elegant_direction.005.md | 
| 0 | 14 | D : unsaved | md | 2025.05.18-kl.18.57-memorybank_elegant_direction.006.md | Clean | 1 | 5 | 2025.05.18-kl.23.27 | 769 | 35.1 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\__meta__\memorybank\prompts\2025.05.18-kl.18.57-memorybank_elegant_direction.006.md | 
| 0 | 14 | D : unsaved | md | 2025.05.18-kl.22.24--memorybank_distilledobjective.004.md | Clean | 1 | 6 | 2025.05.19-kl.10.33 | 348 | 13.7 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\__meta__\memorybank\templates\2025.05.18-kl.22.24--memorybank_distilledobjective.004.md | 
| 0 | 11 | D : unsaved | json | ai.sublime-project | Clean | 1 | 7 | 2025.05.19-kl.22.00 | 91 | 3.6 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\ai.sublime-project | 
| 0 | 13 | D : unsaved | py | litellm_instruction_executor.py | Clean | 1 | 8 | 2025.04.22-kl.22.13 | 418 | 330.7 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\src\simple\litellm_instruction_executor.py | 
| 0 | 12 | D : unsaved | md | conversationlog_example.md | Clean | 1 | 9 | 2025.05.25-kl.16.40 | 352 | 21.4 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\system\conversationlog_example.md | 
| 0 | 13 | D : unsaved | py | window_tiler.py | Clean | 1 | 10 | 2025.05.25-kl.19.14 | 304 | 10.1 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\WindowTiler_v6\src\window_tiler.py | 
| 0 | 15 | D : unsaved | md | 2025.04.13_002-chat.md | Clean | 1 | 11 | 2025.04.13-kl.12.19 | 2176 | 147.9 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\src\systems\Py_Ai_Utils.working_systems--g_system_prompting\__meta__\2025.04.13_002-chat.md | 
| 0 | 15 | D : unsaved | md | 2025.04.15_b.001-chat.rcombined.md | Clean | 1 | 12 | 2025.04.15-kl.18.42 | 345 | 34.2 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\src\systems\Py_Ai_Utils.working_systems--g_system_prompting\__meta__\2025.04.15_b.001-chat.rcombined.md | 
| 0 | 15 | D : unsaved | md | 2025.04.23_b.001--sysinstructionstemplate.md | Clean | 1 | 13 | 2025.04.23-kl.17.31 | 607 | 34.2 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\src\systems\Py_Ai_Utils.working_systems--g_system_prompting\__meta__\2025.04.23_b.001--sysinstructionstemplate.md | 
| 0 | 16 | D : unsaved | md | 2025.04.15_conv001.md | Clean | 1 | 14 | 2025.04.15-kl.11.07 | 8648 | 843.6 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\src\systems\Py_Ai_Utils.working_systems--g_system_prompting\__meta__\_conversations\2025.04.15_conv001.md | 
| 0 | 15 | D : unsaved | py | litellm_sequence_executor.py | Clean | 1 | 15 | 2025.05.25-kl.00.26 | 1967 | 114.6 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\src\systems\Py_Ai_Utils.working_systems--g_system_prompting\src\litellm_sequence_executor.py | 
| 0 | 17 | D : unsaved | json | templates_lvl1_md_catalog.json | Clean | 1 | 16 | 2025.05.24-kl.15.29 | 18000 | 4.1 MB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\src\systems\Py_Ai_Utils.working_systems--g_system_prompting\src\templates\lvl1\templates_lvl1_md_catalog.json | 
| 0 | 17 | D : unsaved | py | template_runner.py | Clean | 1 | 17 | 2025.05.19-kl.21.54 | 1038 | 92.2 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\src\systems\Py_Ai_Utils.working_systems--llm_framework_py\src\original\ai\template_runner.py | 
| 0 | 17 | D : unsaved | py | template_runner_sysinstr_a_great.py | Clean | 1 | 18 | 2025.05.19-kl.22.01 | 960 | 46.1 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\src\systems\Py_Ai_Utils.working_systems--llm_framework_py\src\original\ai\template_runner_sysinstr_a_great.py | 
| 0 | 16 | D : unsaved | py | a.003_instructions_runner_test_a_works.py | Clean | 1 | 19 | 2025.05.19-kl.21.56 | 146 | 10.5 KB | N/A | clean_and_project | C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\src\systems\Py_Ai_Utils.working_systems--llm_framework_py\src\system_instructions\a.003_instructions_runner_test_a_works.py | 
