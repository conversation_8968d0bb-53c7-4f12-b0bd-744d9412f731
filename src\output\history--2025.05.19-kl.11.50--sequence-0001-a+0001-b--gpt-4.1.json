{"initial_prompt": "# CONTEXT\n\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\n\nRigOfficeDownloader is a Python-based automation tool designed to streamline and significantly reduce the time required for retrieving, reviewing, and organizing technical documentation from the NOV RigDoc system. Its primary workflow utilizes a sequential pipeline (Documents → Files → Downloads) and allows for either automated or menu-driven interactive processes. User selection checkpoints via editable Markdown tables, filter chains for inclusion/exclusion, and metadata-driven file organization ensure accuracy and efficiency. Outputs include traceable JSON and Markdown records. The utility integrates easily into existing Windows-based environments and offers substantial labor savings (75%+ reduction in manual prep time), scalability, and simplification of thorough auditability.\n\n# SOLUTION\n\ni've given the utility the temporary name `RigOfficeDownloader`, but this might change (as it feels lazy and non-proprietary). it's a utility that'll be used internally in my department (in a large engineering company) to automate (speed up the process) when working on new projects. Although the default mode of the utility is to rely on the predefined search templates and filters to automate the full process, it is also designed (and thoughtfully set up) such that it can either be used manually or interactively (e.g. if i just want to download a list of specific documents) while adhering to the *same* concepts. although this utility springs out from the need of a small subset of the company (just a small department within the company of <20 people) it's important to always keep in mind that since it'll *eventually* be used by other people (in other departmens within the company), ALWAYS ENSURE **inherent and fundamental cohesiveness**; because **simplicity and elegance** is __INTEGRAL__ - *\"Complexity is the enemy of execution\" - Optimized for engineers, by engineers*.\n\n## WORKFLOW\n\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).\n\n# REQUIREMENTS\n\ni want you to focus on the abstract and general concepts related to **functionality**, i don't want it to destroy the \"canvas\" (of `README.md`) with unusefull noise (unneccessary bloat).\n\n# OBJECTIVE\n\nConsolidate and extract the essential functional concepts from all provided README variations for the RigOfficeDownloader utility. Produce a finalized README.md under 100 lines that:\n\n- Defines the application's purpose: automating and streamlining document retrieval from the NOV RigDoc system, eliminating tedious manual effort, and enabling rapid, organized access to technical documentation.\n- Clearly lists key features and workflow architecture:\n  - Three-stage pipeline: Documents → Files → Downloads\n  - Interactive or automated menu-driven operation\n  - User checkpoints and Markdown review/edit interfaces for selection\n  - Configurable pattern-based filter chains for auto-selection/inclusion/exclusion\n  - Metadata-driven naming and subfolder file organization\n  - Smart browser automation, error handling, deduplication, and field ordering\n- Presents the sequential workflow steps as actionable bullet points or numbered steps:\n  1. Configure rig number and search URLs (with filter setup)\n  2. Fetch document metadata (JSON export)\n  3. Export documents to Markdown for user editing (set item_include)\n  4. Import edited selections (Markdown → JSON)\n  5. Fetch related file metadata for selected documents\n  6. Export file list to Markdown for review (set item_download)\n  7. Import updated file selections\n  8. Download marked files, organized via smart naming/subfolders\n- Summarize directory structure for outputs (data, downloads) and illustrate how subfolders reflect metadata in file names\n- Specify setup/prerequisites concisely:\n  - Python 3.11+, Windows OS, Chrome browser, valid RigDoc credentials, requirements.txt\n- Describe available run modes: --auto (automated), --interactive (step-by-step), --config (adjust templates/filters)\n- Emphasize benefits: reduction in document gathering time (75%+), consistent organization, robust error handling, extensibility for broader internal use, and auditability through JSON/Markdown traceability\n- Maintain original intent, technical depth, and context integrity throughout while strictly omitting unnecessary bloat and self-references.\n- Format the README as a single clear markdown file, using concise sections, technical terminology, and actionable language for internal engineering users.\n\n# VARIATIONS\n\n    # Dir `readme_variations`\n\n    ### File Structure\n\n    ```\n    ├── README_v01.md\n    ├── README_v02.md\n    ├── README_v03.md\n    ├── README_v04.md\n    ├── README_v05.md\n    ├── README_v06.md\n    ├── README_v07.md\n    ├── README_v08.md\n    ├── README_v09.md\n    ├── README_v10.md\n    ├── README_v11.md\n    ├── README_v12.md\n    ├── README_v13.md\n    └── README_v14.md\n    ```\n\n    ---\n\n    #### `README_v01.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        ## Overview\n        RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\n\n        ## Key Features\n        - Three-stage workflow: Documents -> Files -> Downloads\n        - Interactive menu for flexible execution\n        - User control points via Markdown interfaces\n        - Smart file organization with subfolder support\n        - Configurable filter chains\n\n        ## Setup & Usage\n        1. Run `py_venv_init.bat` to create the Python environment\n        2. Run `RigOfficeDownloader-v4.bat` to start the application\n        3. Use the interactive menu to configure and execute workflow steps\n\n        ## Benefits\n        - Reduces documentation gathering time by 75%+\n        - Maintains consistent file organization\n        - Provides user control at key decision points\n        - Preserves document context and relationships\n\n        ## Requirements\n        - Windows OS\n        - Python 3.6+\n        - Chrome browser (for Selenium automation)\n    ```\n\n    ---\n\n    #### `README_v02.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        ## The Problem\n        Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:\n        - Tedious and repetitive\n        - Error-prone\n        - A poor use of skilled engineering time\n\n        ## The Solution\n        RigOfficeDownloader automates the document retrieval process through a three-stage workflow:\n        1. **Document Retrieval**: Automatically scrapes document metadata\n        2. **File Metadata**: Fetches file information for selected documents\n        3. **Smart Downloads**: Downloads files with intelligent naming and organization\n\n        ## How It Works\n        - Uses Selenium to automate web interactions with RigDoc\n        - Exports data to Markdown for user review and selection\n        - Applies configurable filters to pre-select relevant documents\n        - Organizes downloads with consistent naming patterns\n\n        ## Getting Started\n        1. Run `py_venv_init.bat` to set up the environment\n        2. Run `RigOfficeDownloader-v4.bat` to launch the application\n        3. Follow the interactive menu prompts\n\n        ## Impact\n        Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.\n    ```\n\n    ---\n\n    #### `README_v03.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        ## Technical Overview\n        RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\n\n        ## Architecture\n        - **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown\n        - **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads\n        - **File Organization**: Hierarchical naming system with metadata embedding and subfolder support\n\n        ## Workflow Steps\n        1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information\n        2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing\n        3. **Selection Import**: `md_table_to_json()` imports user selections\n        4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents\n        5. **File Selection**: Export/import cycle for user selection of files\n        6. **Download Process**: `download_files()` retrieves selected files with smart naming\n\n        ## Filter Chain System\n        Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.\n\n        ## Setup Instructions\n        1. Run `py_venv_init.bat` to initialize the Python environment\n        2. Run `RigOfficeDownloader-v4.bat` to execute the application\n\n        ## Version History\n        - v1: Basic document retrieval and download\n        - v2: JSON/Markdown conversion and user selection\n        - v3: Improved error handling and field organization\n        - v4: Subfolder support, filter chains, field ordering\n    ```\n\n    ---\n\n    #### `README_v04.md`\n\n    ```markdown\n        # RigOfficeDownloader\n        > Automate document retrieval from NOV's RigDoc system\n\n        ## What This Tool Does\n        RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\n\n        ## Quick Start Guide\n        1. **Setup**: Run `py_venv_init.bat` to create the Python environment\n        2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application\n        3. **Configure**: Enter your rig number and search URLs when prompted\n        4. **Run**: Follow the numbered menu to execute each step of the workflow\n\n        ## Workflow Steps Explained\n        0. **Change Parameters**: Update rig number and search URLs\n        1. **Configure Filters**: Set up automatic document/file selection rules\n        2. **Fetch Documents**: Retrieve document metadata from RigDoc\n        3. **Review Documents**: Edit the Markdown file to select which documents to process\n        4. **Import Selections**: Load your document selections\n        5. **Fetch Files**: Get file metadata for selected documents\n        6. **Review Files**: Edit the Markdown file to select which files to download\n        7. **Import File Selections**: Load your file selections\n        8. **Download Files**: Retrieve the selected files\n\n        ## Tips for Success\n        - Use filters to automatically pre-select relevant documents\n        - Review the Markdown files carefully before proceeding to the next step\n        - Files will be organized in subfolders based on '/' in their generated names\n\n        ## Need Help?\n        Check the source code comments for detailed information about each function and workflow step.\n    ```\n\n    ---\n\n    #### `README_v05.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\n\n        ```\n        Documents -> Files -> Downloads\n        ```\n\n        ## Features\n        - Three-stage workflow with user control points\n        - Interactive menu for flexible execution\n        - Configurable filter chains for automatic selection\n        - Smart file organization with subfolder support\n        - Markdown interfaces for document/file review\n\n        ## Quick Start\n        ```\n        1. Run py_venv_init.bat\n        2. Run RigOfficeDownloader-v4.bat\n        3. Follow the interactive menu\n        ```\n\n        ## Benefits\n        - Reduces documentation gathering time by 75%+\n        - Maintains consistent file organization\n        - Provides user control at key decision points\n        - Handles errors gracefully\n\n        ## Requirements\n        - Windows OS\n        - Python 3.6+\n        - Chrome browser\n    ```\n\n    ---\n\n    #### `README_v06.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.\n\n        ## Key Features\n        - **Three-Stage Workflow**: Document selection → File selection → Download\n        - **Metadata Preservation**: Structured naming with revision/case numbers\n        - **Interactive Review**: Markdown tables for manual inclusion flags\n        - **Smart Organization**: Automatic subfolder creation via naming patterns\n        - **Filter System**: Pattern-based inclusion/exclusion rules\n        - **Browser Automation**: Smart waiting strategies and session management\n\n        ## Workflow Process\n        ```python\n        1. Fetch Documents       # Scrape metadata → <rig>-a-docs.json\n        2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true\n        3. Import Selections     # Update JSON with user choices\n        4. Fetch Files           # Get file listings → <rig>-b-files.json\n        5. Export Files MD       # <rig>-b-files.md - Set item_download=true\n        6. Import File Choices   # Update file selections\n        7. Download Files        # Auto-organized with subfolder support\n        ```\n\n        ## Configuration (CONFIG Section)\n        ```python\n        {\n            \"rig_number\": \"R0000.020\",  # Target rig ID\n            \"search_urls\": [            # Predefined search templates\n                \"https://rigdoc.nov.com/search/rigsearch?q=...\",\n                \"https://rigdoc.nov.com/search/rigsearch?q=...\"\n            ],\n            \"filters\": [                # Sequential processing rules\n                {\n                    \"type\": \"docs\",     # Apply to documents/files\n                    \"pattern\": \"*DRILL*FLOOR*\",  # Glob-style matching\n                    \"field\": \"item_include\",     # Field to modify\n                    \"value\": True       # Set True/False based on match\n                }\n            ]\n        }\n        ```\n\n        ## Setup & Usage\n        ```bash\n        # Initialize environment\n        py_venv_init.bat\n        py_venv_pip_install.bat\n\n        # Run modes\n        RigOfficeDownloader-v4.py [rig_number] [mode]\n\n        Modes:\n        --auto         # Full automation\n        --interactive  # Step-by-step control\n        --config       # Modify search templates/filters\n        ```\n\n        ## Key Configuration Patterns\n        - **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)\n        - **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)\n        - **File Types**: Auto-prioritize PDFs with `*.pdf` pattern\n\n        ## Requirements\n        - Chrome Browser + ChromeDriver\n        - Active RigDoc credentials\n        - Python 3.8+ with dependencies from requirements.txt\n        - Network access to rigdoc.nov.com\n\n        ## Advanced Features\n        - **Path Sanitization**: Auto-clean special chars while preserving /\n        - **Deduplication**: Hash-based conflict resolution\n        - **Field Ordering**: Customizable JSON/Markdown columns\n        - **Smart Scrolling**: Progressive page loading detection\n\n        > Reduces documentation prep time by 60-75% compared to manual retrieval\n        > Version 4.0 | Active development with subfolder support\n    ```\n\n    ---\n\n    #### `README_v07.md`\n\n    ```markdown\n\n        ## Overview\n        - Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\n        - Use case: curate, review, and batch-download rig-related documents and technical files.\n\n        ## Directory Structure\n\n        * `outputs/` (BASE\\_OUTPUT): All results stored here\n        * `outputs/data/` (DATA\\_DIR): Document and file metadata (JSON/MD)\n        * `outputs/downloads/` (DL\\_DIR): Downloaded PDF and file outputs\n\n        ## Pipeline Overview\n\n        1. Change search parameters (rig number, URLs)\n        2. Configure filter chain (add, edit, delete, toggle, reorder filters)\n        3. Fetch docs (scrape data from rigdoc.nov.com)\n        4. Export docs to Markdown (for selection/editing)\n        5. Import docs from Markdown (sync edited selection)\n        6. Fetch candidate files linked to selected docs\n        7. Export file list to Markdown (for editing/selecting files for download)\n        8. Import updated file list from Markdown\n        9. Download marked files (PDFs only, via Chrome)\n\n        ## Manual Editing\n\n        * Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing\n        * Set `item_include` (docs) and `item_download` (files) fields\n\n        ## Running the Tool\n\n        ```bash\n        python rigdocscraper.py\n        ```\n\n        * Interactive menu enables step selection (numbers/comma/space-separated)\n        * Supports adjusting parameters, filter configuration, and reviewing batch steps\n        * Prompts will guide through editing, import/export, and download procedures\n\n        ## Troubleshooting\n\n        * Requires functioning Chrome installation; verify webdriver-manager compatibility\n        * Common issues: browser launch failures, login/captcha requirements, file permissions\n        * Output logs and warnings shown in terminal; inspect `outputs/data/` for progress\n    ```\n\n    ---\n\n    #### `README_v08.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        > **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.\n\n        ---\n\n        ## 1. Overview\n\n        RigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents → Files → Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.\n\n        ---\n\n        ## 2. The Problem\n\n        - Engineers lose valuable hours **manually searching** and **downloading** technical documentation.\n        - The repetitive process is **error-prone** and distracts from real engineering work.\n\n        ---\n\n        ## 3. The Solution\n\n        - **Automates** the document search, scraping, and download phases, cutting the time required by **75%+**.\n        - Offers **three main stages**—document metadata retrieval, file metadata retrieval, and file downloads—each controlled by user-edited Markdown tables for fine-tuned selection.\n\n        ---\n\n        ## 4. Key Features\n\n        - **Three-Stage Workflow**\n          1. **Documents**: Gather doc metadata, mark `item_include` in Markdown.\n          2. **Files**: Fetch file info for included docs, mark `item_download` in Markdown.\n          3. **Downloads**: Organize files with subfolder support based on `'/'` in `item_generated_name`.\n\n        - **Interactive Menu**: Allows step-by-step or fully automated runs.\n        - **User Review via Markdown**: You decide which items to include or skip by editing `.md` tables.\n        - **Configurable Filter Chains**: Glob-style pattern matching to auto-select or exclude docs/files.\n        - **Smart Organization**: Hierarchical naming that embeds rig/drawing metadata, plus subfolder creation.\n        - **Error Handling**: Graceful session management, robust page-scrolling logic, and deduplication.\n\n        ---\n\n        ## 5. Workflow Steps\n\n        1. **Change Parameters**: (Optional) Update rig number, search URLs.\n        2. **Configure Filters**: Setup or edit filter rules to auto-include/exclude docs/files.\n        3. **Fetch Docs**: Scrape document metadata into `<rig>-a-docs.json` (all `item_include=false` initially).\n        4. **Export Docs**: Generate `<rig>-a-docs.md`; manually set `item_include=true` for desired items.\n        5. **Import Updated Docs**: Sync changes back from Markdown to JSON.\n        6. **Fetch Files**: Retrieve file metadata for those docs, saved as `<rig>-b-files.json` (`item_download=false`).\n        7. **Export Files**: Create `<rig>-b-files.md`; set `item_download=true` for target files.\n        8. **Import Updated Files**: Sync file selection from Markdown to JSON.\n        9. **Download Files**: Acquire and organize all selected files under `outputs/downloads/<rig>`.\n\n        ---\n\n        ## 6. Architecture & Technology\n\n        - **Core Stack**:\n          - **Python 3.6+**\n          - **Selenium** for browser automation\n          - **BeautifulSoup** for HTML parsing\n          - **JSON/Markdown** for data storage and user edits\n        - **Linear Multi-Stage** design with user checkpoints ensures incremental control and reusability.\n        - **Filter System**: Pattern-based matching (inclusion/exclusion) on fields like `item_generated_name` or `item_case_description`.\n        - **File Organization**: Subfolder creation from `'/'` in `item_generated_name`, plus sanitization of invalid characters.\n\n        ---\n\n        ## 7. Directory Structure\n\n    ```\n\n    ---\n\n    #### `README_v09.md`\n\n    ```markdown\n        ## Intent\n        To automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\n\n        ## Key Project Aspects\n\n        ### Primary Problem\n        Engineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck—it's the manual preparation process.\n\n        ### Solution Approach\n        A Python-based utility that:\n        1. Automatically scrapes document metadata from RigOffice\n        2. Extracts file information from those documents\n        3. Downloads and organizes selected files based on user criteria\n\n        ### Current State\n        Functional working prototype that:\n        - Uses a 3-step workflow (document metadata → file metadata → download)\n        - Stores intermediate results in JSON format\n        - Allows user intervention between steps\n        - Provides progress feedback\n\n        ### Critical Next Steps\n        1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches\n        2. **Implement file hash checking** to prevent redundant downloads\n        3. **Improve progress visibility** during lengthy scraping operations\n\n        ### Core Technical Pattern\n        A single-file, modular approach using:\n        - Selenium for browser automation\n        - JSON for data storage\n        - Three-stage processing with user control points\n        - Incremental updates to avoid redundant work\n\n        ### Key Success Metrics\n        - Reduce documentation gathering time by 75%+\n        - Ensure reliable retrieval of required documentation\n        - Organize files in a way that streamlines workflow\n        - Support both broad searches (by rig number) and targeted searches (by specific documents)\n    ```\n\n    ---\n\n    #### `README_v10.md`\n\n    ```markdown\n        ### RigOfficeDownloader Utility Workflow\n\n        This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow is clearly defined in the docstring at the beginning of the main script and follows these steps:\n\n        1. Fetch Documents\n        - The utility starts by scraping document metadata from predefined search URLs\n        - Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory\n        - Each document entry includes metadata like title, document number, revision, etc.\n        - All documents are initially marked with item_include=False\n        - Each document gets an item_generated_name for better identification\n\n        2. Export Documents to Markdown\n        - The JSON data is exported to a Markdown table: <rig>-a-docs.md\n        - This allows the user to easily review and edit which documents to include\n        - The user is expected to edit the Markdown file and set item_include=true for desired documents\n\n        3. Import Updated Document Data\n        - After the user edits the Markdown file, the utility imports the changes back to the JSON file\n        - This updates which documents are marked for file retrieval\n\n        4. Fetch Files for Selected Documents\n        - For each document with item_include=true, the utility scrapes file metadata\n        - File data is saved to <rig>-b-files.json\n        - Each file is initially marked with item_download=False\n        - Files inherit the document's item_generated_name with additional identifiers\n\n        5. Export Files to Markdown\n        - The file data is exported to a Markdown table: <rig>-b-files.md\n        - The user reviews and edits which files to download by setting item_download=true\n\n        6. Import Updated File Data\n        - After editing, the utility imports the changes back to the JSON file\n        - This updates which files are marked for download\n\n        7. Download Selected Files\n        - Files with item_download=true are downloaded\n        - Files are named according to their item_generated_name + extension\n        - The utility supports creating subfolders based on '/' in the item_generated_name\n        - Files are saved to the outputs/downloads/<rig> directory\n\n        Interactive Menu\n        - The utility provides an interactive menu where the user can choose which steps to execute\n        - This allows for flexibility in the workflow, enabling the user to run specific steps as needed\n        - The user can also update the rig number and search URLs through this menu\n\n        Key Features\n        - Automatic document and file metadata scraping\n        - User-friendly Markdown editing interface\n        - Customizable file naming with item_generated_name\n        - Support for subfolder organization in downloads\n        - Deduplication of documents and files\n        - Configurable field ordering for JSON and Markdown exports\n\n        Technical Implementation\n        - Uses Selenium with Chrome WebDriver for web scraping\n        - Implements smart waiting strategies for page loading\n        - Handles browser sessions with proper cleanup\n        - Provides progress feedback during operations\n        - Sanitizes filenames for valid paths\n    ```\n\n    ---\n\n    #### `README_v11.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        ## Overview\n        RigOfficeDownloader is an automation tool designed to streamline the process of retrieving and downloading technical documentation from NOV's RigDoc system. It eliminates the tedious, time-consuming process of manual document retrieval, allowing engineers to focus on their primary work.\n\n        ## Key Features\n        - **Three-Stage Workflow**: Documents → Files → Downloads\n        - **Interactive Menu**: Choose which steps to execute\n        - **User Control Points**: Review and select documents/files via Markdown interfaces\n        - **Smart File Organization**: Subfolder support based on naming patterns\n        - **Configurable Filters**: Apply filter chains to automatically select relevant documents\n\n        ## Workflow\n        1. **Document Retrieval**: Scrapes document metadata from RigDoc\n        2. **Document Selection**: Exports to Markdown for user review and selection\n        3. **File Metadata**: Fetches file information for selected documents\n        4. **File Selection**: Exports to Markdown for user review and selection\n        5. **Download**: Downloads selected files with intelligent naming and organization\n\n        ## Setup\n        1. Run `py_venv_init.bat` to create and initialize the Python virtual environment\n        2. The script will:\n           - Find available Python installations\n           - Create a virtual environment\n           - Install required packages from requirements.txt\n\n        ## Usage\n        1. Run `RigOfficeDownloader-v4.bat` to start the application\n        2. Use the interactive menu to:\n           - Configure search parameters\n           - Run specific workflow steps\n           - Apply filter chains\n           - Review and select documents/files\n\n        ## Benefits\n        - Reduces documentation gathering time by 75%+\n        - Maintains consistent file organization\n        - Provides user control at key decision points\n        - Handles errors gracefully\n        - Preserves document context and relationships\n\n        ## File Structure\n        ```\n        outputs/\n        ├── data/           # JSON and Markdown files for documents and files\n        └── downloads/      # Downloaded files organized by rig number\n        ```\n\n        ## Requirements\n        - Windows OS\n        - Python 3.6+\n        - Chrome browser (for Selenium automation)\n    ```\n\n    ---\n\n    #### `README_v12.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\n\n        ```\n        Documents -> Files -> Downloads\n        ```\n\n        ## Overview\n        RigOfficeDownloader eliminates the tedious, time-consuming process of manually retrieving technical documentation from NOV's RigDoc system. Engineers can focus on their primary work instead of navigating complex document repositories.\n\n        ## Key Features\n        - Three-stage workflow with user control points\n        - Interactive menu for flexible execution\n        - Configurable filter chains for automatic selection\n        - Smart file organization with subfolder support\n        - Markdown interfaces for document/file review\n\n        ## Workflow\n        1. **Document Retrieval**: Scrapes document metadata from RigDoc\n        2. **Document Selection**: Exports to Markdown for user review and selection\n        3. **File Metadata**: Fetches file information for selected documents\n        4. **File Selection**: Exports to Markdown for user review and selection\n        5. **Download**: Downloads selected files with intelligent naming and organization\n\n        ## Setup\n        1. Run `py_venv_init.bat` to create and initialize the Python virtual environment\n        2. Run `RigOfficeDownloader-v4.bat` to start the application\n\n        ## Usage\n        Choose from the interactive menu:\n        ```\n        [0] Change search parameters\n        [1] Configure filter chain\n        [2] Fetch docs (scrape initial data)\n        [3] Export docs (to Markdown for editing)\n        [4] Import updated doc data\n        [5] Fetch files (prepare files for download)\n        [6] Export files (to Markdown for editing)\n        [7] Import updated file data\n        [8] Download files\n        ```\n\n        ## Benefits\n        - Reduces documentation gathering time by 75%+\n        - Maintains consistent file organization\n        - Provides user control at key decision points\n        - Handles errors gracefully\n\n        ## Requirements\n        - Windows OS\n        - Python 3.6+\n        - Chrome browser (for Selenium automation)\n\n        ## Version History\n        - v1: Basic document retrieval and download\n        - v2: JSON/Markdown conversion and user selection\n        - v3: Improved error handling and field organization\n        - v4: Subfolder support, filter chains, field ordering\n    ```\n\n    ---\n\n    #### `README_v13.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        > **Automate** document retrieval from NOV’s RigDoc system, **minimize** repetitive tasks, and **accelerate** workflow.\n\n        ---\n\n        ## Overview\n\n        **RigOfficeDownloader** is a single-script Python utility that **scrapes**, **filters**, and **downloads** technical documentation from NOV’s RigDoc. Rather than wasting hours searching for and reviewing documents one by one, engineers can leverage an **interactive workflow** that guides them through each phase of document collection.\n\n        **Key Highlights**\n        - **Three-Stage Workflow**: Documents → Files → Downloads\n        - **Interactive Menu**: Run the entire workflow or individual steps on demand\n        - **Markdown Edits**: Quickly select or deselect items by editing `.md` tables\n        - **Subfolder Support**: Organize downloads with nested folder paths\n        - **Filter Chains**: Automate selection based on glob-style patterns\n        - **Single-File Simplicity**: All core logic in one script (~500 lines)\n\n        ---\n\n        ## The Problem\n\n        Engineering projects require diverse technical documents from RigDoc. This **manual** retrieval is:\n        - **Tedious & Repetitive**: Browsing multiple pages, copying metadata, creating folders\n        - **Error-Prone**: Risk of missing crucial docs or misplacing files\n        - **Time Consuming**: Delays the start of high-value engineering tasks\n\n        ---\n\n        ## The Solution\n\n        RigOfficeDownloader streamlines the entire process through an **automated workflow**:\n        1. **Fetch** document metadata (stores in `<rig>-a-docs.json`)\n        2. **Export** docs to Markdown (`<rig>-a-docs.md`): mark `item_include=true`\n        3. **Import** updated doc data back into JSON\n        4. **Fetch** file metadata for included docs\n        5. **Export** files to Markdown (`<rig>-b-files.md`): mark `item_download=true`\n        6. **Import** updated file data\n        7. **Download** files with subfolder paths derived from the generated names\n\n        ---\n\n        ## Workflow Steps in Detail\n\n        1. **Change / Configure**\n           - Input your **rig number** and **search URLs**.\n           - Set up or modify **filters** (e.g., auto-include `*DRILL*FLOOR*` docs, exclude `*VOID*` docs).\n\n        2. **Fetch Documents**\n           - Scrape document metadata from RigDoc URLs.\n           - Data saved to JSON (`<rig>-a-docs.json`), all set to `item_include=false`.\n\n        3. **Export & Review Docs**\n           - Creates `<rig>-a-docs.md`.\n           - Manually set `item_include=true` for relevant docs.\n\n        4. **Import Updated Docs**\n           - Reads user changes back into JSON.\n\n        5. **Fetch File Metadata**\n           - For each included doc, scrapes associated files (saved in `<rig>-b-files.json`).\n           - `item_download=false` by default.\n\n        6. **Export & Review Files**\n           - Creates `<rig>-b-files.md`.\n           - Set `item_download=true` for desired files.\n\n        7. **Import Updated Files**\n           - Sync file selections back into JSON.\n\n        8. **Download Files**\n           - Retrieves selected files, applying `item_generated_name` for naming.\n           - `'/'` in `item_generated_name` spawns subfolders in `outputs/downloads/<rig>`.\n\n        ---\n\n        ## Directory Structure\n\n    ```\n\n    ---\n\n    #### `README_v14.md`\n\n    ```markdown\n        # RigOfficeDownloader\n        *Automated Document Retrieval for NOV RigDoc*\n\n        ## Overview\n        Eliminates tedious manual document retrieval from NOV's RigDoc system through intelligent automation. Preserves 75%+ engineering time by streamlining the 3D modeling preparation workflow.\n\n        ## Key Features\n        ```\n        ◼ Three-Stage Workflow  ◼ Interactive Menu  ◼ Human Checkpoints\n        ◼ Metadata-Powered File Organization  ◼ Pattern-Based Filter Chains\n        ◼ Markdown Review Interface  ◼ Smart Browser Automation\n        ```\n\n        ## Workflow Architecture\n        **1. Document Retrieval**\n        ```python\n        fetch_docs() → <rig>-a-docs.json  # Scrape metadata\n        json_to_md_table() → <rig>-a-docs.md  # Edit item_include=true\n        md_table_to_json()  # Commit selections\n        ```\n        **2. File Processing**\n        ```python\n        fetch_files() → <rig>-b-files.json  # Get file metadata\n        json_to_md_table() → <rig>-b-files.md  # Set item_download=true\n        md_table_to_json()  # Finalize choices\n        ```\n        **3. Smart Download**\n        ```python\n        download_files()  # Auto-organize with:\n        • /subfolders from item_generated_name\n        • Sanitized filenames\n        • Deduplication\n        ```\n\n        ## Getting Started\n        ```bash\n        # 1. Initialize environment\n        py_venv_init.bat\n\n        # 2. Launch utility\n        RigOfficeDownloader-v4.bat [--auto|--interactive|--config]\n        ```\n        **First-Run Configuration**\n        ```python\n        CONFIG = {\n            \"rig_number\": \"R0000.020\",\n            \"search_urls\": PROJECTINFO_GAD,  # Prebuilt equipment templates\n            \"filters\": [{\n                \"type\": \"docs\",\n                \"pattern\": \"*G000*\",\n                \"field\": \"item_include\",\n                \"value\": True\n            }]\n        }\n        ```\n\n        ## File Organization\n        ```\n        outputs/\n        ├── data/               # Metadata control files\n        │   ├── R1234-a-docs.json\n        │   ├── R1234-a-docs.md\n        │   ├── R1234-b-files.json\n        │   └── R1234-b-files.md\n        └── downloads/          # Final organized files\n            └── R1234/\n                ├── Drilling/\n                │   └── G0001_REV03--CASE45-DrillFloor.pdf\n                └── Topdrive/\n                    └── ASM-00_REV12--CASE32-TopDriveAssembly.dwg\n        ```\n\n        ## Interactive Control\n        ```\n        [0] Change rig number/search URLs    [4] Import doc selections\n        [1] Configure filter chain           [5] Fetch file metadata\n        [2] Fetch documents                  [6] Export files to Markdown\n        [3] Export docs to Markdown          [7] Import file selections\n        [8] Download files                   [9] Advanced configuration\n        ```\n\n        ## Why This Tool?\n        | Manual Process               | Automated Solution           |\n        |------------------------------|-------------------------------|\n        | 4-6 hours per project setup  | <1 hour with 75% automation  |\n        | Error-prone file management  | Metadata-driven organization |\n        | No version control           | JSON/Markdown audit trail    |\n        | Context loss between teams   | Standardized naming schema   |\n\n    ```", "sequence_id": "sequence-0001-a+0001-b", "results": [{"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "# CONTEXT\n\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\n\nRigOfficeDownloader is a Python-based automation tool designed to streamline and significantly reduce the time required for retrieving, reviewing, and organizing technical documentation from the NOV RigDoc system. Its primary workflow utilizes a sequential pipeline (Documents → Files → Downloads) and allows for either automated or menu-driven interactive processes. User selection checkpoints via editable Markdown tables, filter chains for inclusion/exclusion, and metadata-driven file organization ensure accuracy and efficiency. Outputs include traceable JSON and Markdown records. The utility integrates easily into existing Windows-based environments and offers substantial labor savings (75%+ reduction in manual prep time), scalability, and simplification of thorough auditability.\n\n# SOLUTION\n\ni've given the utility the temporary name `RigOfficeDownloader`, but this might change (as it feels lazy and non-proprietary). it's a utility that'll be used internally in my department (in a large engineering company) to automate (speed up the process) when working on new projects. Although the default mode of the utility is to rely on the predefined search templates and filters to automate the full process, it is also designed (and thoughtfully set up) such that it can either be used manually or interactively (e.g. if i just want to download a list of specific documents) while adhering to the *same* concepts. although this utility springs out from the need of a small subset of the company (just a small department within the company of <20 people) it's important to always keep in mind that since it'll *eventually* be used by other people (in other departmens within the company), ALWAYS ENSURE **inherent and fundamental cohesiveness**; because **simplicity and elegance** is __INTEGRAL__ - *\"Complexity is the enemy of execution\" - Optimized for engineers, by engineers*.\n\n## WORKFLOW\n\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).\n\n# REQUIREMENTS\n\ni want you to focus on the abstract and general concepts related to **functionality**, i don't want it to destroy the \"canvas\" (of `README.md`) with unusefull noise (unneccessary bloat).\n\n# OBJECTIVE\n\nConsolidate and extract the essential functional concepts from all provided README variations for the RigOfficeDownloader utility. Produce a finalized README.md under 100 lines that:\n\n- Defines the application's purpose: automating and streamlining document retrieval from the NOV RigDoc system, eliminating tedious manual effort, and enabling rapid, organized access to technical documentation.\n- Clearly lists key features and workflow architecture:\n  - Three-stage pipeline: Documents → Files → Downloads\n  - Interactive or automated menu-driven operation\n  - User checkpoints and Markdown review/edit interfaces for selection\n  - Configurable pattern-based filter chains for auto-selection/inclusion/exclusion\n  - Metadata-driven naming and subfolder file organization\n  - Smart browser automation, error handling, deduplication, and field ordering\n- Presents the sequential workflow steps as actionable bullet points or numbered steps:\n  1. Configure rig number and search URLs (with filter setup)\n  2. Fetch document metadata (JSON export)\n  3. Export documents to Markdown for user editing (set item_include)\n  4. Import edited selections (Markdown → JSON)\n  5. Fetch related file metadata for selected documents\n  6. Export file list to Markdown for review (set item_download)\n  7. Import updated file selections\n  8. Download marked files, organized via smart naming/subfolders\n- Summarize directory structure for outputs (data, downloads) and illustrate how subfolders reflect metadata in file names\n- Specify setup/prerequisites concisely:\n  - Python 3.11+, Windows OS, Chrome browser, valid RigDoc credentials, requirements.txt\n- Describe available run modes: --auto (automated), --interactive (step-by-step), --config (adjust templates/filters)\n- Emphasize benefits: reduction in document gathering time (75%+), consistent organization, robust error handling, extensibility for broader internal use, and auditability through JSON/Markdown traceability\n- Maintain original intent, technical depth, and context integrity throughout while strictly omitting unnecessary bloat and self-references.\n- Format the README as a single clear markdown file, using concise sections, technical terminology, and actionable language for internal engineering users.\n\n# VARIATIONS\n\n    # Dir `readme_variations`\n\n    ### File Structure\n\n    ```\n    ├── README_v01.md\n    ├── README_v02.md\n    ├── README_v03.md\n    ├── README_v04.md\n    ├── README_v05.md\n    ├── README_v06.md\n    ├── README_v07.md\n    ├── README_v08.md\n    ├── README_v09.md\n    ├── README_v10.md\n    ├── README_v11.md\n    ├── README_v12.md\n    ├── README_v13.md\n    └── README_v14.md\n    ```\n\n    ---\n\n    #### `README_v01.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        ## Overview\n        RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\n\n        ## Key Features\n        - Three-stage workflow: Documents -> Files -> Downloads\n        - Interactive menu for flexible execution\n        - User control points via Markdown interfaces\n        - Smart file organization with subfolder support\n        - Configurable filter chains\n\n        ## Setup & Usage\n        1. Run `py_venv_init.bat` to create the Python environment\n        2. Run `RigOfficeDownloader-v4.bat` to start the application\n        3. Use the interactive menu to configure and execute workflow steps\n\n        ## Benefits\n        - Reduces documentation gathering time by 75%+\n        - Maintains consistent file organization\n        - Provides user control at key decision points\n        - Preserves document context and relationships\n\n        ## Requirements\n        - Windows OS\n        - Python 3.6+\n        - Chrome browser (for Selenium automation)\n    ```\n\n    ---\n\n    #### `README_v02.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        ## The Problem\n        Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:\n        - Tedious and repetitive\n        - Error-prone\n        - A poor use of skilled engineering time\n\n        ## The Solution\n        RigOfficeDownloader automates the document retrieval process through a three-stage workflow:\n        1. **Document Retrieval**: Automatically scrapes document metadata\n        2. **File Metadata**: Fetches file information for selected documents\n        3. **Smart Downloads**: Downloads files with intelligent naming and organization\n\n        ## How It Works\n        - Uses Selenium to automate web interactions with RigDoc\n        - Exports data to Markdown for user review and selection\n        - Applies configurable filters to pre-select relevant documents\n        - Organizes downloads with consistent naming patterns\n\n        ## Getting Started\n        1. Run `py_venv_init.bat` to set up the environment\n        2. Run `RigOfficeDownloader-v4.bat` to launch the application\n        3. Follow the interactive menu prompts\n\n        ## Impact\n        Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.\n    ```\n\n    ---\n\n    #### `README_v03.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        ## Technical Overview\n        RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\n\n        ## Architecture\n        - **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown\n        - **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads\n        - **File Organization**: Hierarchical naming system with metadata embedding and subfolder support\n\n        ## Workflow Steps\n        1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information\n        2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing\n        3. **Selection Import**: `md_table_to_json()` imports user selections\n        4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents\n        5. **File Selection**: Export/import cycle for user selection of files\n        6. **Download Process**: `download_files()` retrieves selected files with smart naming\n\n        ## Filter Chain System\n        Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.\n\n        ## Setup Instructions\n        1. Run `py_venv_init.bat` to initialize the Python environment\n        2. Run `RigOfficeDownloader-v4.bat` to execute the application\n\n        ## Version History\n        - v1: Basic document retrieval and download\n        - v2: JSON/Markdown conversion and user selection\n        - v3: Improved error handling and field organization\n        - v4: Subfolder support, filter chains, field ordering\n    ```\n\n    ---\n\n    #### `README_v04.md`\n\n    ```markdown\n        # RigOfficeDownloader\n        > Automate document retrieval from NOV's RigDoc system\n\n        ## What This Tool Does\n        RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\n\n        ## Quick Start Guide\n        1. **Setup**: Run `py_venv_init.bat` to create the Python environment\n        2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application\n        3. **Configure**: Enter your rig number and search URLs when prompted\n        4. **Run**: Follow the numbered menu to execute each step of the workflow\n\n        ## Workflow Steps Explained\n        0. **Change Parameters**: Update rig number and search URLs\n        1. **Configure Filters**: Set up automatic document/file selection rules\n        2. **Fetch Documents**: Retrieve document metadata from RigDoc\n        3. **Review Documents**: Edit the Markdown file to select which documents to process\n        4. **Import Selections**: Load your document selections\n        5. **Fetch Files**: Get file metadata for selected documents\n        6. **Review Files**: Edit the Markdown file to select which files to download\n        7. **Import File Selections**: Load your file selections\n        8. **Download Files**: Retrieve the selected files\n\n        ## Tips for Success\n        - Use filters to automatically pre-select relevant documents\n        - Review the Markdown files carefully before proceeding to the next step\n        - Files will be organized in subfolders based on '/' in their generated names\n\n        ## Need Help?\n        Check the source code comments for detailed information about each function and workflow step.\n    ```\n\n    ---\n\n    #### `README_v05.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\n\n        ```\n        Documents -> Files -> Downloads\n        ```\n\n        ## Features\n        - Three-stage workflow with user control points\n        - Interactive menu for flexible execution\n        - Configurable filter chains for automatic selection\n        - Smart file organization with subfolder support\n        - Markdown interfaces for document/file review\n\n        ## Quick Start\n        ```\n        1. Run py_venv_init.bat\n        2. Run RigOfficeDownloader-v4.bat\n        3. Follow the interactive menu\n        ```\n\n        ## Benefits\n        - Reduces documentation gathering time by 75%+\n        - Maintains consistent file organization\n        - Provides user control at key decision points\n        - Handles errors gracefully\n\n        ## Requirements\n        - Windows OS\n        - Python 3.6+\n        - Chrome browser\n    ```\n\n    ---\n\n    #### `README_v06.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.\n\n        ## Key Features\n        - **Three-Stage Workflow**: Document selection → File selection → Download\n        - **Metadata Preservation**: Structured naming with revision/case numbers\n        - **Interactive Review**: Markdown tables for manual inclusion flags\n        - **Smart Organization**: Automatic subfolder creation via naming patterns\n        - **Filter System**: Pattern-based inclusion/exclusion rules\n        - **Browser Automation**: Smart waiting strategies and session management\n\n        ## Workflow Process\n        ```python\n        1. Fetch Documents       # Scrape metadata → <rig>-a-docs.json\n        2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true\n        3. Import Selections     # Update JSON with user choices\n        4. Fetch Files           # Get file listings → <rig>-b-files.json\n        5. Export Files MD       # <rig>-b-files.md - Set item_download=true\n        6. Import File Choices   # Update file selections\n        7. Download Files        # Auto-organized with subfolder support\n        ```\n\n        ## Configuration (CONFIG Section)\n        ```python\n        {\n            \"rig_number\": \"R0000.020\",  # Target rig ID\n            \"search_urls\": [            # Predefined search templates\n                \"https://rigdoc.nov.com/search/rigsearch?q=...\",\n                \"https://rigdoc.nov.com/search/rigsearch?q=...\"\n            ],\n            \"filters\": [                # Sequential processing rules\n                {\n                    \"type\": \"docs\",     # Apply to documents/files\n                    \"pattern\": \"*DRILL*FLOOR*\",  # Glob-style matching\n                    \"field\": \"item_include\",     # Field to modify\n                    \"value\": True       # Set True/False based on match\n                }\n            ]\n        }\n        ```\n\n        ## Setup & Usage\n        ```bash\n        # Initialize environment\n        py_venv_init.bat\n        py_venv_pip_install.bat\n\n        # Run modes\n        RigOfficeDownloader-v4.py [rig_number] [mode]\n\n        Modes:\n        --auto         # Full automation\n        --interactive  # Step-by-step control\n        --config       # Modify search templates/filters\n        ```\n\n        ## Key Configuration Patterns\n        - **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)\n        - **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)\n        - **File Types**: Auto-prioritize PDFs with `*.pdf` pattern\n\n        ## Requirements\n        - Chrome Browser + ChromeDriver\n        - Active RigDoc credentials\n        - Python 3.8+ with dependencies from requirements.txt\n        - Network access to rigdoc.nov.com\n\n        ## Advanced Features\n        - **Path Sanitization**: Auto-clean special chars while preserving /\n        - **Deduplication**: Hash-based conflict resolution\n        - **Field Ordering**: Customizable JSON/Markdown columns\n        - **Smart Scrolling**: Progressive page loading detection\n\n        > Reduces documentation prep time by 60-75% compared to manual retrieval\n        > Version 4.0 | Active development with subfolder support\n    ```\n\n    ---\n\n    #### `README_v07.md`\n\n    ```markdown\n\n        ## Overview\n        - Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\n        - Use case: curate, review, and batch-download rig-related documents and technical files.\n\n        ## Directory Structure\n\n        * `outputs/` (BASE\\_OUTPUT): All results stored here\n        * `outputs/data/` (DATA\\_DIR): Document and file metadata (JSON/MD)\n        * `outputs/downloads/` (DL\\_DIR): Downloaded PDF and file outputs\n\n        ## Pipeline Overview\n\n        1. Change search parameters (rig number, URLs)\n        2. Configure filter chain (add, edit, delete, toggle, reorder filters)\n        3. Fetch docs (scrape data from rigdoc.nov.com)\n        4. Export docs to Markdown (for selection/editing)\n        5. Import docs from Markdown (sync edited selection)\n        6. Fetch candidate files linked to selected docs\n        7. Export file list to Markdown (for editing/selecting files for download)\n        8. Import updated file list from Markdown\n        9. Download marked files (PDFs only, via Chrome)\n\n        ## Manual Editing\n\n        * Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing\n        * Set `item_include` (docs) and `item_download` (files) fields\n\n        ## Running the Tool\n\n        ```bash\n        python rigdocscraper.py\n        ```\n\n        * Interactive menu enables step selection (numbers/comma/space-separated)\n        * Supports adjusting parameters, filter configuration, and reviewing batch steps\n        * Prompts will guide through editing, import/export, and download procedures\n\n        ## Troubleshooting\n\n        * Requires functioning Chrome installation; verify webdriver-manager compatibility\n        * Common issues: browser launch failures, login/captcha requirements, file permissions\n        * Output logs and warnings shown in terminal; inspect `outputs/data/` for progress\n    ```\n\n    ---\n\n    #### `README_v08.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        > **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.\n\n        ---\n\n        ## 1. Overview\n\n        RigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents → Files → Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.\n\n        ---\n\n        ## 2. The Problem\n\n        - Engineers lose valuable hours **manually searching** and **downloading** technical documentation.\n        - The repetitive process is **error-prone** and distracts from real engineering work.\n\n        ---\n\n        ## 3. The Solution\n\n        - **Automates** the document search, scraping, and download phases, cutting the time required by **75%+**.\n        - Offers **three main stages**—document metadata retrieval, file metadata retrieval, and file downloads—each controlled by user-edited Markdown tables for fine-tuned selection.\n\n        ---\n\n        ## 4. Key Features\n\n        - **Three-Stage Workflow**\n          1. **Documents**: Gather doc metadata, mark `item_include` in Markdown.\n          2. **Files**: Fetch file info for included docs, mark `item_download` in Markdown.\n          3. **Downloads**: Organize files with subfolder support based on `'/'` in `item_generated_name`.\n\n        - **Interactive Menu**: Allows step-by-step or fully automated runs.\n        - **User Review via Markdown**: You decide which items to include or skip by editing `.md` tables.\n        - **Configurable Filter Chains**: Glob-style pattern matching to auto-select or exclude docs/files.\n        - **Smart Organization**: Hierarchical naming that embeds rig/drawing metadata, plus subfolder creation.\n        - **Error Handling**: Graceful session management, robust page-scrolling logic, and deduplication.\n\n        ---\n\n        ## 5. Workflow Steps\n\n        1. **Change Parameters**: (Optional) Update rig number, search URLs.\n        2. **Configure Filters**: Setup or edit filter rules to auto-include/exclude docs/files.\n        3. **Fetch Docs**: Scrape document metadata into `<rig>-a-docs.json` (all `item_include=false` initially).\n        4. **Export Docs**: Generate `<rig>-a-docs.md`; manually set `item_include=true` for desired items.\n        5. **Import Updated Docs**: Sync changes back from Markdown to JSON.\n        6. **Fetch Files**: Retrieve file metadata for those docs, saved as `<rig>-b-files.json` (`item_download=false`).\n        7. **Export Files**: Create `<rig>-b-files.md`; set `item_download=true` for target files.\n        8. **Import Updated Files**: Sync file selection from Markdown to JSON.\n        9. **Download Files**: Acquire and organize all selected files under `outputs/downloads/<rig>`.\n\n        ---\n\n        ## 6. Architecture & Technology\n\n        - **Core Stack**:\n          - **Python 3.6+**\n          - **Selenium** for browser automation\n          - **BeautifulSoup** for HTML parsing\n          - **JSON/Markdown** for data storage and user edits\n        - **Linear Multi-Stage** design with user checkpoints ensures incremental control and reusability.\n        - **Filter System**: Pattern-based matching (inclusion/exclusion) on fields like `item_generated_name` or `item_case_description`.\n        - **File Organization**: Subfolder creation from `'/'` in `item_generated_name`, plus sanitization of invalid characters.\n\n        ---\n\n        ## 7. Directory Structure\n\n    ```\n\n    ---\n\n    #### `README_v09.md`\n\n    ```markdown\n        ## Intent\n        To automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\n\n        ## Key Project Aspects\n\n        ### Primary Problem\n        Engineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck—it's the manual preparation process.\n\n        ### Solution Approach\n        A Python-based utility that:\n        1. Automatically scrapes document metadata from RigOffice\n        2. Extracts file information from those documents\n        3. Downloads and organizes selected files based on user criteria\n\n        ### Current State\n        Functional working prototype that:\n        - Uses a 3-step workflow (document metadata → file metadata → download)\n        - Stores intermediate results in JSON format\n        - Allows user intervention between steps\n        - Provides progress feedback\n\n        ### Critical Next Steps\n        1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches\n        2. **Implement file hash checking** to prevent redundant downloads\n        3. **Improve progress visibility** during lengthy scraping operations\n\n        ### Core Technical Pattern\n        A single-file, modular approach using:\n        - Selenium for browser automation\n        - JSON for data storage\n        - Three-stage processing with user control points\n        - Incremental updates to avoid redundant work\n\n        ### Key Success Metrics\n        - Reduce documentation gathering time by 75%+\n        - Ensure reliable retrieval of required documentation\n        - Organize files in a way that streamlines workflow\n        - Support both broad searches (by rig number) and targeted searches (by specific documents)\n    ```\n\n    ---\n\n    #### `README_v10.md`\n\n    ```markdown\n        ### RigOfficeDownloader Utility Workflow\n\n        This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow is clearly defined in the docstring at the beginning of the main script and follows these steps:\n\n        1. Fetch Documents\n        - The utility starts by scraping document metadata from predefined search URLs\n        - Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory\n        - Each document entry includes metadata like title, document number, revision, etc.\n        - All documents are initially marked with item_include=False\n        - Each document gets an item_generated_name for better identification\n\n        2. Export Documents to Markdown\n        - The JSON data is exported to a Markdown table: <rig>-a-docs.md\n        - This allows the user to easily review and edit which documents to include\n        - The user is expected to edit the Markdown file and set item_include=true for desired documents\n\n        3. Import Updated Document Data\n        - After the user edits the Markdown file, the utility imports the changes back to the JSON file\n        - This updates which documents are marked for file retrieval\n\n        4. Fetch Files for Selected Documents\n        - For each document with item_include=true, the utility scrapes file metadata\n        - File data is saved to <rig>-b-files.json\n        - Each file is initially marked with item_download=False\n        - Files inherit the document's item_generated_name with additional identifiers\n\n        5. Export Files to Markdown\n        - The file data is exported to a Markdown table: <rig>-b-files.md\n        - The user reviews and edits which files to download by setting item_download=true\n\n        6. Import Updated File Data\n        - After editing, the utility imports the changes back to the JSON file\n        - This updates which files are marked for download\n\n        7. Download Selected Files\n        - Files with item_download=true are downloaded\n        - Files are named according to their item_generated_name + extension\n        - The utility supports creating subfolders based on '/' in the item_generated_name\n        - Files are saved to the outputs/downloads/<rig> directory\n\n        Interactive Menu\n        - The utility provides an interactive menu where the user can choose which steps to execute\n        - This allows for flexibility in the workflow, enabling the user to run specific steps as needed\n        - The user can also update the rig number and search URLs through this menu\n\n        Key Features\n        - Automatic document and file metadata scraping\n        - User-friendly Markdown editing interface\n        - Customizable file naming with item_generated_name\n        - Support for subfolder organization in downloads\n        - Deduplication of documents and files\n        - Configurable field ordering for JSON and Markdown exports\n\n        Technical Implementation\n        - Uses Selenium with Chrome WebDriver for web scraping\n        - Implements smart waiting strategies for page loading\n        - Handles browser sessions with proper cleanup\n        - Provides progress feedback during operations\n        - Sanitizes filenames for valid paths\n    ```\n\n    ---\n\n    #### `README_v11.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        ## Overview\n        RigOfficeDownloader is an automation tool designed to streamline the process of retrieving and downloading technical documentation from NOV's RigDoc system. It eliminates the tedious, time-consuming process of manual document retrieval, allowing engineers to focus on their primary work.\n\n        ## Key Features\n        - **Three-Stage Workflow**: Documents → Files → Downloads\n        - **Interactive Menu**: Choose which steps to execute\n        - **User Control Points**: Review and select documents/files via Markdown interfaces\n        - **Smart File Organization**: Subfolder support based on naming patterns\n        - **Configurable Filters**: Apply filter chains to automatically select relevant documents\n\n        ## Workflow\n        1. **Document Retrieval**: Scrapes document metadata from RigDoc\n        2. **Document Selection**: Exports to Markdown for user review and selection\n        3. **File Metadata**: Fetches file information for selected documents\n        4. **File Selection**: Exports to Markdown for user review and selection\n        5. **Download**: Downloads selected files with intelligent naming and organization\n\n        ## Setup\n        1. Run `py_venv_init.bat` to create and initialize the Python virtual environment\n        2. The script will:\n           - Find available Python installations\n           - Create a virtual environment\n           - Install required packages from requirements.txt\n\n        ## Usage\n        1. Run `RigOfficeDownloader-v4.bat` to start the application\n        2. Use the interactive menu to:\n           - Configure search parameters\n           - Run specific workflow steps\n           - Apply filter chains\n           - Review and select documents/files\n\n        ## Benefits\n        - Reduces documentation gathering time by 75%+\n        - Maintains consistent file organization\n        - Provides user control at key decision points\n        - Handles errors gracefully\n        - Preserves document context and relationships\n\n        ## File Structure\n        ```\n        outputs/\n        ├── data/           # JSON and Markdown files for documents and files\n        └── downloads/      # Downloaded files organized by rig number\n        ```\n\n        ## Requirements\n        - Windows OS\n        - Python 3.6+\n        - Chrome browser (for Selenium automation)\n    ```\n\n    ---\n\n    #### `README_v12.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\n\n        ```\n        Documents -> Files -> Downloads\n        ```\n\n        ## Overview\n        RigOfficeDownloader eliminates the tedious, time-consuming process of manually retrieving technical documentation from NOV's RigDoc system. Engineers can focus on their primary work instead of navigating complex document repositories.\n\n        ## Key Features\n        - Three-stage workflow with user control points\n        - Interactive menu for flexible execution\n        - Configurable filter chains for automatic selection\n        - Smart file organization with subfolder support\n        - Markdown interfaces for document/file review\n\n        ## Workflow\n        1. **Document Retrieval**: Scrapes document metadata from RigDoc\n        2. **Document Selection**: Exports to Markdown for user review and selection\n        3. **File Metadata**: Fetches file information for selected documents\n        4. **File Selection**: Exports to Markdown for user review and selection\n        5. **Download**: Downloads selected files with intelligent naming and organization\n\n        ## Setup\n        1. Run `py_venv_init.bat` to create and initialize the Python virtual environment\n        2. Run `RigOfficeDownloader-v4.bat` to start the application\n\n        ## Usage\n        Choose from the interactive menu:\n        ```\n        [0] Change search parameters\n        [1] Configure filter chain\n        [2] Fetch docs (scrape initial data)\n        [3] Export docs (to Markdown for editing)\n        [4] Import updated doc data\n        [5] Fetch files (prepare files for download)\n        [6] Export files (to Markdown for editing)\n        [7] Import updated file data\n        [8] Download files\n        ```\n\n        ## Benefits\n        - Reduces documentation gathering time by 75%+\n        - Maintains consistent file organization\n        - Provides user control at key decision points\n        - Handles errors gracefully\n\n        ## Requirements\n        - Windows OS\n        - Python 3.6+\n        - Chrome browser (for Selenium automation)\n\n        ## Version History\n        - v1: Basic document retrieval and download\n        - v2: JSON/Markdown conversion and user selection\n        - v3: Improved error handling and field organization\n        - v4: Subfolder support, filter chains, field ordering\n    ```\n\n    ---\n\n    #### `README_v13.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        > **Automate** document retrieval from NOV’s RigDoc system, **minimize** repetitive tasks, and **accelerate** workflow.\n\n        ---\n\n        ## Overview\n\n        **RigOfficeDownloader** is a single-script Python utility that **scrapes**, **filters**, and **downloads** technical documentation from NOV’s RigDoc. Rather than wasting hours searching for and reviewing documents one by one, engineers can leverage an **interactive workflow** that guides them through each phase of document collection.\n\n        **Key Highlights**\n        - **Three-Stage Workflow**: Documents → Files → Downloads\n        - **Interactive Menu**: Run the entire workflow or individual steps on demand\n        - **Markdown Edits**: Quickly select or deselect items by editing `.md` tables\n        - **Subfolder Support**: Organize downloads with nested folder paths\n        - **Filter Chains**: Automate selection based on glob-style patterns\n        - **Single-File Simplicity**: All core logic in one script (~500 lines)\n\n        ---\n\n        ## The Problem\n\n        Engineering projects require diverse technical documents from RigDoc. This **manual** retrieval is:\n        - **Tedious & Repetitive**: Browsing multiple pages, copying metadata, creating folders\n        - **Error-Prone**: Risk of missing crucial docs or misplacing files\n        - **Time Consuming**: Delays the start of high-value engineering tasks\n\n        ---\n\n        ## The Solution\n\n        RigOfficeDownloader streamlines the entire process through an **automated workflow**:\n        1. **Fetch** document metadata (stores in `<rig>-a-docs.json`)\n        2. **Export** docs to Markdown (`<rig>-a-docs.md`): mark `item_include=true`\n        3. **Import** updated doc data back into JSON\n        4. **Fetch** file metadata for included docs\n        5. **Export** files to Markdown (`<rig>-b-files.md`): mark `item_download=true`\n        6. **Import** updated file data\n        7. **Download** files with subfolder paths derived from the generated names\n\n        ---\n\n        ## Workflow Steps in Detail\n\n        1. **Change / Configure**\n           - Input your **rig number** and **search URLs**.\n           - Set up or modify **filters** (e.g., auto-include `*DRILL*FLOOR*` docs, exclude `*VOID*` docs).\n\n        2. **Fetch Documents**\n           - Scrape document metadata from RigDoc URLs.\n           - Data saved to JSON (`<rig>-a-docs.json`), all set to `item_include=false`.\n\n        3. **Export & Review Docs**\n           - Creates `<rig>-a-docs.md`.\n           - Manually set `item_include=true` for relevant docs.\n\n        4. **Import Updated Docs**\n           - Reads user changes back into JSON.\n\n        5. **Fetch File Metadata**\n           - For each included doc, scrapes associated files (saved in `<rig>-b-files.json`).\n           - `item_download=false` by default.\n\n        6. **Export & Review Files**\n           - Creates `<rig>-b-files.md`.\n           - Set `item_download=true` for desired files.\n\n        7. **Import Updated Files**\n           - Sync file selections back into JSON.\n\n        8. **Download Files**\n           - Retrieves selected files, applying `item_generated_name` for naming.\n           - `'/'` in `item_generated_name` spawns subfolders in `outputs/downloads/<rig>`.\n\n        ---\n\n        ## Directory Structure\n\n    ```\n\n    ---\n\n    #### `README_v14.md`\n\n    ```markdown\n        # RigOfficeDownloader\n        *Automated Document Retrieval for NOV RigDoc*\n\n        ## Overview\n        Eliminates tedious manual document retrieval from NOV's RigDoc system through intelligent automation. Preserves 75%+ engineering time by streamlining the 3D modeling preparation workflow.\n\n        ## Key Features\n        ```\n        ◼ Three-Stage Workflow  ◼ Interactive Menu  ◼ Human Checkpoints\n        ◼ Metadata-Powered File Organization  ◼ Pattern-Based Filter Chains\n        ◼ Markdown Review Interface  ◼ Smart Browser Automation\n        ```\n\n        ## Workflow Architecture\n        **1. Document Retrieval**\n        ```python\n        fetch_docs() → <rig>-a-docs.json  # Scrape metadata\n        json_to_md_table() → <rig>-a-docs.md  # Edit item_include=true\n        md_table_to_json()  # Commit selections\n        ```\n        **2. File Processing**\n        ```python\n        fetch_files() → <rig>-b-files.json  # Get file metadata\n        json_to_md_table() → <rig>-b-files.md  # Set item_download=true\n        md_table_to_json()  # Finalize choices\n        ```\n        **3. Smart Download**\n        ```python\n        download_files()  # Auto-organize with:\n        • /subfolders from item_generated_name\n        • Sanitized filenames\n        • Deduplication\n        ```\n\n        ## Getting Started\n        ```bash\n        # 1. Initialize environment\n        py_venv_init.bat\n\n        # 2. Launch utility\n        RigOfficeDownloader-v4.bat [--auto|--interactive|--config]\n        ```\n        **First-Run Configuration**\n        ```python\n        CONFIG = {\n            \"rig_number\": \"R0000.020\",\n            \"search_urls\": PROJECTINFO_GAD,  # Prebuilt equipment templates\n            \"filters\": [{\n                \"type\": \"docs\",\n                \"pattern\": \"*G000*\",\n                \"field\": \"item_include\",\n                \"value\": True\n            }]\n        }\n        ```\n\n        ## File Organization\n        ```\n        outputs/\n        ├── data/               # Metadata control files\n        │   ├── R1234-a-docs.json\n        │   ├── R1234-a-docs.md\n        │   ├── R1234-b-files.json\n        │   └── R1234-b-files.md\n        └── downloads/          # Final organized files\n            └── R1234/\n                ├── Drilling/\n                │   └── G0001_REV03--CASE45-DrillFloor.pdf\n                └── Topdrive/\n                    └── ASM-00_REV12--CASE32-TopDriveAssembly.dwg\n        ```\n\n        ## Interactive Control\n        ```\n        [0] Change rig number/search URLs    [4] Import doc selections\n        [1] Configure filter chain           [5] Fetch file metadata\n        [2] Fetch documents                  [6] Export files to Markdown\n        [3] Export docs to Markdown          [7] Import file selections\n        [8] Download files                   [9] Advanced configuration\n        ```\n\n        ## Why This Tool?\n        | Manual Process               | Automated Solution           |\n        |------------------------------|-------------------------------|\n        | 4-6 hours per project setup  | <1 hour with 75% automation  |\n        | Error-prone file management  | Metadata-driven organization |\n        | No version control           | JSON/Markdown audit trail    |\n        | Context loss between teams   | Standardized naming schema   |\n\n    ```", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"# RigOfficeDownloader\\n\\nAutomate and streamline technical document retrieval from NOV RigDoc, eliminating tedious manual effort and enabling rapid, organized access to engineering documentation.\\n\\n## Purpose\\nAutomate document retrieval from NOV RigDoc, drastically reducing manual prep time, ensuring reliable, organized, and auditable project documentation access for engineering workflows.\\n\\n## Key Features\\n- Three-stage sequential pipeline: Documents → Files → Downloads\\n- Toggle between fully automated and interactive (menu-driven) operation\\n- Human checkpoints and user review via editable Markdown tables for both document and file selection\\n- Configurable pattern-based filter chains for auto-inclusion/exclusion of items\\n- Metadata-driven file naming, with subfolder/file organization based on metadata fields (e.g., rig number, doc type)\\n- Robust browser automation (Selenium) with smart waiting, session management, deduplication, and error handling\\n- Strict field ordering in JSON and Markdown outputs\\n- Comprehensive traceability and auditability via exported JSON and Markdown records\\n\\n## Workflow: Step-by-Step Process\\n1. Configure rig number and search URLs (define or select search templates and filters)\\n2. Fetch document metadata from RigDoc and export to `<rig>-a-docs.json`\\n3. Export document list to Markdown (`<rig>-a-docs.md`) for manual review/edit (set `item_include=true` as needed)\\n4. Import updated selections from Markdown back to JSON, reflecting included docs\\n5. Fetch file metadata for selected documents and store in `<rig>-b-files.json`\\n6. Export file list to Markdown (`<rig>-b-files.md`) for review (set `item_download=true` on desired files)\\n7. Import updated file selections from Markdown, updating which files will be downloaded\\n8. Download all marked files to `outputs/downloads/<rig>`, auto-organized via generated names/subfolder paths derived from metadata\\n\\n## Directory Structure\\n```\\noutputs/\\n├── data/           # All metadata/selection states (JSON + Markdown)\\n│   ├── <rig>-a-docs.json\\n│   ├── <rig>-a-docs.md\\n│   ├── <rig>-b-files.json\\n│   └── <rig>-b-files.md\\n└── downloads/      # All downloaded documents, hierarchically organized\\n    └── <rig>/\\n        ├── <subfolder>/\\n        │   └── <item_generated_name>.<ext>\\n        └── ...\\n```\\nFile and folder naming reflect document metadata and support subfolders using '/' within the `item_generated_name` field.\\n\\n## Setup / Prerequisites\\n- Windows OS\\n- Python 3.11+ (installer provided or specify in requirements.txt)\\n- Chrome browser (with valid Chromedriver for Selenium automation)\\n- Valid NOV RigDoc credentials\\n- Install Python dependencies: `pip install -r requirements.txt`\\n\\n## Execution / Run Modes\\n- Run interactively or fully automated via batch or script command:\\n    - `RigOfficeDownloader-v4.bat --auto` – full pipeline, minimal user intervention\\n    - `RigOfficeDownloader-v4.bat --interactive` – menu-driven, step-by-step workflow\\n    - `RigOfficeDownloader-v4.bat --config` – edit search templates, update filters\\n\\n## Formulate Filters & Configuration\\n- Edit filter chains to define auto-inclusion/exclusion (glob-style patterns, e.g. `*G000*`, `*VOID*`)\\n- Adjust which document fields control item selection (such as by rig, system, doc type, status)\\n- Update configuration to specify rig number and/or template search URLs\\n\\n## Benefits & Advantages\\n- Achieve a 75%+ reduction in time spent on document gathering for engineering projects\\n- Allow engineers to focus on technical work, not mundane document management\\n- Maintain consistently structured output directories with audit trails (JSON + Markdown)\\n- Ensure robust error handling, smart deduplication, and correct organization of files\\n- Provide a solution that is scalable, cohesive, and ready for broader internal adoption\\n\\n## Summary Table of Features\\n| Capability             | Manual Process         | Automated with RigOfficeDownloader       |\\n|----------------------- |---------------------- |-----------------------------------------|\\n| Time per project setup | 4–6 hours             | <1 hour                                 |\\n| File organization      | Error-prone, ad hoc   | Metadata-driven, standardized           |\\n| User review            | Tedious, repetitive   | Centralized via Markdown checkpoints    |\\n| Auditability           | Weak/none             | Full JSON/Markdown traceability         |\\n| Expandability          | N/A                   | Configurable, reusable templates        |\\n\\n## Version/Extensibility\\n- Built-in support for subfolders, filter chains, field ordering, advanced configuration\\n- Easily extensible for new departments and additional filtering/organizational patterns\\n\\n> \\\"Complexity is the enemy of execution – Optimized for engineers, by engineers.\\\"\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"# CONTEXT\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\nRigOfficeDownloader is a Python-based automation tool designed to streamline and significantly reduce the time required for retrieving, reviewing, and organizing technical documentation from the NOV RigDoc system. Its primary workflow utilizes a sequential pipeline (Documents \\u2192 Files \\u2192 Downloads) and allows for either automated or menu-driven interactive processes. User selection checkpoints via editable Markdown tables, filter chains for inclusion/exclusion, and metadata-driven file organization ensure accuracy and efficiency. Outputs include traceable JSON and Markdown records. The utility integrates easily into existing Windows-based environments and offers substantial labor savings (75%+ reduction in manual prep time), scalability, and simplification of thorough auditability.\\n\\n# SOLUTION\\n\\ni've given the utility the temporary name `RigOfficeDownloader`, but this might change (as it feels lazy and non-proprietary). it's a utility that'll be used internally in my department (in a large engineering company) to automate (speed up the process) when working on new projects. Although the default mode of the utility is to rely on the predefined search templates and filters to automate the full process, it is also designed (and thoughtfully set up) such that it can either be used manually or interactively (e.g. if i just want to download a list of specific documents) while adhering to the *same* concepts. although this utility springs out from the need of a small subset of the company (just a small department within the company of <20 people) it's important to always keep in mind that since it'll *eventually* be used by other people (in other departmens within the company), ALWAYS ENSURE **inherent and fundamental cohesiveness**; because **simplicity and elegance** is __INTEGRAL__ - *\\\"Complexity is the enemy of execution\\\" - Optimized for engineers, by engineers*.\\n\\n## WORKFLOW\\n\\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).\\n\\n# REQUIREMENTS\\n\\ni want you to focus on the abstract and general concepts related to **functionality**, i don't want it to destroy the \\\"canvas\\\" (of `README.md`) with unusefull noise (unneccessary bloat).\\n\\n# OBJECTIVE\\n\\nConsolidate and extract the essential functional concepts from all provided README variations for the RigOfficeDownloader utility. Produce a finalized README.md under 100 lines that:\\n\\n- Defines the application's purpose: automating and streamlining document retrieval from the NOV RigDoc system, eliminating tedious manual effort, and enabling rapid, organized access to technical documentation.\\n- Clearly lists key features and workflow architecture:\\n  - Three-stage pipeline: Documents \\u2192 Files \\u2192 Downloads\\n  - Interactive or automated menu-driven operation\\n  - User checkpoints and Markdown review/edit interfaces for selection\\n  - Configurable pattern-based filter chains for auto-selection/inclusion/exclusion\\n  - Metadata-driven naming and subfolder file organization\\n  - Smart browser automation, error handling, deduplication, and field ordering\\n- Presents the sequential workflow steps as actionable bullet points or numbered steps:\\n  1. Configure rig number and search URLs (with filter setup)\\n  2. Fetch document metadata (JSON export)\\n  3. Export documents to Markdown for user editing (set item_include)\\n  4. Import edited selections (Markdown \\u2192 JSON)\\n  5. Fetch related file metadata for selected documents\\n  6. Export file list to Markdown for review (set item_download)\\n  7. Import updated file selections\\n  8. Download marked files, organized via smart naming/subfolders\\n- Summarize directory structure for outputs (data, downloads) and illustrate how subfolders reflect metadata in file names\\n- Specify setup/prerequisites concisely:\\n  - Python 3.11+, Windows OS, Chrome browser, valid RigDoc credentials, requirements.txt\\n- Describe available run modes: --auto (automated), --interactive (step-by-step), --config (adjust templates/filters)\\n- Emphasize benefits: reduction in document gathering time (75%+), consistent organization, robust error handling, extensibility for broader internal use, and auditability through JSON/Markdown traceability\\n- Maintain original intent, technical depth, and context integrity throughout while strictly omitting unnecessary bloat and self-references.\\n- Format the README as a single clear markdown file, using concise sections, technical terminology, and actionable language for internal engineering users.\\n\\n# VARIATIONS\\n\\n    # Dir `readme_variations`\\n\\n    ### File Structure\\n\\n    ```\\n    \\u251c\\u2500\\u2500 README_v01.md\\n    \\u251c\\u2500\\u2500 README_v02.md\\n    \\u251c\\u2500\\u2500 README_v03.md\\n    \\u251c\\u2500\\u2500 README_v04.md\\n    \\u251c\\u2500\\u2500 README_v05.md\\n    \\u251c\\u2500\\u2500 README_v06.md\\n    \\u251c\\u2500\\u2500 README_v07.md\\n    \\u251c\\u2500\\u2500 README_v08.md\\n    \\u251c\\u2500\\u2500 README_v09.md\\n    \\u251c\\u2500\\u2500 README_v10.md\\n    \\u251c\\u2500\\u2500 README_v11.md\\n    \\u251c\\u2500\\u2500 README_v12.md\\n    \\u251c\\u2500\\u2500 README_v13.md\\n    \\u2514\\u2500\\u2500 README_v14.md\\n    ```\\n\\n    ---\\n\\n    #### `README_v01.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## Overview\\n        RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\\n\\n        ## Key Features\\n        - Three-stage workflow: Documents -> Files -> Downloads\\n        - Interactive menu for flexible execution\\n        - User control points via Markdown interfaces\\n        - Smart file organization with subfolder support\\n        - Configurable filter chains\\n\\n        ## Setup & Usage\\n        1. Run `py_venv_init.bat` to create the Python environment\\n        2. Run `RigOfficeDownloader-v4.bat` to start the application\\n        3. Use the interactive menu to configure and execute workflow steps\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Preserves document context and relationships\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser (for Selenium automation)\\n    ```\\n\\n    ---\\n\\n    #### `README_v02.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## The Problem\\n        Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:\\n        - Tedious and repetitive\\n        - Error-prone\\n        - A poor use of skilled engineering time\\n\\n        ## The Solution\\n        RigOfficeDownloader automates the document retrieval process through a three-stage workflow:\\n        1. **Document Retrieval**: Automatically scrapes document metadata\\n        2. **File Metadata**: Fetches file information for selected documents\\n        3. **Smart Downloads**: Downloads files with intelligent naming and organization\\n\\n        ## How It Works\\n        - Uses Selenium to automate web interactions with RigDoc\\n        - Exports data to Markdown for user review and selection\\n        - Applies configurable filters to pre-select relevant documents\\n        - Organizes downloads with consistent naming patterns\\n\\n        ## Getting Started\\n        1. Run `py_venv_init.bat` to set up the environment\\n        2. Run `RigOfficeDownloader-v4.bat` to launch the application\\n        3. Follow the interactive menu prompts\\n\\n        ## Impact\\n        Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.\\n    ```\\n\\n    ---\\n\\n    #### `README_v03.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## Technical Overview\\n        RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\\n\\n        ## Architecture\\n        - **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown\\n        - **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads\\n        - **File Organization**: Hierarchical naming system with metadata embedding and subfolder support\\n\\n        ## Workflow Steps\\n        1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information\\n        2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing\\n        3. **Selection Import**: `md_table_to_json()` imports user selections\\n        4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents\\n        5. **File Selection**: Export/import cycle for user selection of files\\n        6. **Download Process**: `download_files()` retrieves selected files with smart naming\\n\\n        ## Filter Chain System\\n        Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.\\n\\n        ## Setup Instructions\\n        1. Run `py_venv_init.bat` to initialize the Python environment\\n        2. Run `RigOfficeDownloader-v4.bat` to execute the application\\n\\n        ## Version History\\n        - v1: Basic document retrieval and download\\n        - v2: JSON/Markdown conversion and user selection\\n        - v3: Improved error handling and field organization\\n        - v4: Subfolder support, filter chains, field ordering\\n    ```\\n\\n    ---\\n\\n    #### `README_v04.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n        > Automate document retrieval from NOV's RigDoc system\\n\\n        ## What This Tool Does\\n        RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\\n\\n        ## Quick Start Guide\\n        1. **Setup**: Run `py_venv_init.bat` to create the Python environment\\n        2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application\\n        3. **Configure**: Enter your rig number and search URLs when prompted\\n        4. **Run**: Follow the numbered menu to execute each step of the workflow\\n\\n        ## Workflow Steps Explained\\n        0. **Change Parameters**: Update rig number and search URLs\\n        1. **Configure Filters**: Set up automatic document/file selection rules\\n        2. **Fetch Documents**: Retrieve document metadata from RigDoc\\n        3. **Review Documents**: Edit the Markdown file to select which documents to process\\n        4. **Import Selections**: Load your document selections\\n        5. **Fetch Files**: Get file metadata for selected documents\\n        6. **Review Files**: Edit the Markdown file to select which files to download\\n        7. **Import File Selections**: Load your file selections\\n        8. **Download Files**: Retrieve the selected files\\n\\n        ## Tips for Success\\n        - Use filters to automatically pre-select relevant documents\\n        - Review the Markdown files carefully before proceeding to the next step\\n        - Files will be organized in subfolders based on '/' in their generated names\\n\\n        ## Need Help?\\n        Check the source code comments for detailed information about each function and workflow step.\\n    ```\\n\\n    ---\\n\\n    #### `README_v05.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\\n\\n        ```\\n        Documents -> Files -> Downloads\\n        ```\\n\\n        ## Features\\n        - Three-stage workflow with user control points\\n        - Interactive menu for flexible execution\\n        - Configurable filter chains for automatic selection\\n        - Smart file organization with subfolder support\\n        - Markdown interfaces for document/file review\\n\\n        ## Quick Start\\n        ```\\n        1. Run py_venv_init.bat\\n        2. Run RigOfficeDownloader-v4.bat\\n        3. Follow the interactive menu\\n        ```\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Handles errors gracefully\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser\\n    ```\\n\\n    ---\\n\\n    #### `README_v06.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.\\n\\n        ## Key Features\\n        - **Three-Stage Workflow**: Document selection \\u2192 File selection \\u2192 Download\\n        - **Metadata Preservation**: Structured naming with revision/case numbers\\n        - **Interactive Review**: Markdown tables for manual inclusion flags\\n        - **Smart Organization**: Automatic subfolder creation via naming patterns\\n        - **Filter System**: Pattern-based inclusion/exclusion rules\\n        - **Browser Automation**: Smart waiting strategies and session management\\n\\n        ## Workflow Process\\n        ```python\\n        1. Fetch Documents       # Scrape metadata \\u2192 <rig>-a-docs.json\\n        2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true\\n        3. Import Selections     # Update JSON with user choices\\n        4. Fetch Files           # Get file listings \\u2192 <rig>-b-files.json\\n        5. Export Files MD       # <rig>-b-files.md - Set item_download=true\\n        6. Import File Choices   # Update file selections\\n        7. Download Files        # Auto-organized with subfolder support\\n        ```\\n\\n        ## Configuration (CONFIG Section)\\n        ```python\\n        {\\n            \\\"rig_number\\\": \\\"R0000.020\\\",  # Target rig ID\\n            \\\"search_urls\\\": [            # Predefined search templates\\n                \\\"https://rigdoc.nov.com/search/rigsearch?q=...\\\",\\n                \\\"https://rigdoc.nov.com/search/rigsearch?q=...\\\"\\n            ],\\n            \\\"filters\\\": [                # Sequential processing rules\\n                {\\n                    \\\"type\\\": \\\"docs\\\",     # Apply to documents/files\\n                    \\\"pattern\\\": \\\"*DRILL*FLOOR*\\\",  # Glob-style matching\\n                    \\\"field\\\": \\\"item_include\\\",     # Field to modify\\n                    \\\"value\\\": True       # Set True/False based on match\\n                }\\n            ]\\n        }\\n        ```\\n\\n        ## Setup & Usage\\n        ```bash\\n        # Initialize environment\\n        py_venv_init.bat\\n        py_venv_pip_install.bat\\n\\n        # Run modes\\n        RigOfficeDownloader-v4.py [rig_number] [mode]\\n\\n        Modes:\\n        --auto         # Full automation\\n        --interactive  # Step-by-step control\\n        --config       # Modify search templates/filters\\n        ```\\n\\n        ## Key Configuration Patterns\\n        - **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)\\n        - **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)\\n        - **File Types**: Auto-prioritize PDFs with `*.pdf` pattern\\n\\n        ## Requirements\\n        - Chrome Browser + ChromeDriver\\n        - Active RigDoc credentials\\n        - Python 3.8+ with dependencies from requirements.txt\\n        - Network access to rigdoc.nov.com\\n\\n        ## Advanced Features\\n        - **Path Sanitization**: Auto-clean special chars while preserving /\\n        - **Deduplication**: Hash-based conflict resolution\\n        - **Field Ordering**: Customizable JSON/Markdown columns\\n        - **Smart Scrolling**: Progressive page loading detection\\n\\n        > Reduces documentation prep time by 60-75% compared to manual retrieval\\n        > Version 4.0 | Active development with subfolder support\\n    ```\\n\\n    ---\\n\\n    #### `README_v07.md`\\n\\n    ```markdown\\n\\n        ## Overview\\n        - Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\\n        - Use case: curate, review, and batch-download rig-related documents and technical files.\\n\\n        ## Directory Structure\\n\\n        * `outputs/` (BASE\\\\_OUTPUT): All results stored here\\n        * `outputs/data/` (DATA\\\\_DIR): Document and file metadata (JSON/MD)\\n        * `outputs/downloads/` (DL\\\\_DIR): Downloaded PDF and file outputs\\n\\n        ## Pipeline Overview\\n\\n        1. Change search parameters (rig number, URLs)\\n        2. Configure filter chain (add, edit, delete, toggle, reorder filters)\\n        3. Fetch docs (scrape data from rigdoc.nov.com)\\n        4. Export docs to Markdown (for selection/editing)\\n        5. Import docs from Markdown (sync edited selection)\\n        6. Fetch candidate files linked to selected docs\\n        7. Export file list to Markdown (for editing/selecting files for download)\\n        8. Import updated file list from Markdown\\n        9. Download marked files (PDFs only, via Chrome)\\n\\n        ## Manual Editing\\n\\n        * Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing\\n        * Set `item_include` (docs) and `item_download` (files) fields\\n\\n        ## Running the Tool\\n\\n        ```bash\\n        python rigdocscraper.py\\n        ```\\n\\n        * Interactive menu enables step selection (numbers/comma/space-separated)\\n        * Supports adjusting parameters, filter configuration, and reviewing batch steps\\n        * Prompts will guide through editing, import/export, and download procedures\\n\\n        ## Troubleshooting\\n\\n        * Requires functioning Chrome installation; verify webdriver-manager compatibility\\n        * Common issues: browser launch failures, login/captcha requirements, file permissions\\n        * Output logs and warnings shown in terminal; inspect `outputs/data/` for progress\\n    ```\\n\\n    ---\\n\\n    #### `README_v08.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        > **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.\\n\\n        ---\\n\\n        ## 1. Overview\\n\\n        RigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents \\u2192 Files \\u2192 Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.\\n\\n        ---\\n\\n        ## 2. The Problem\\n\\n        - Engineers lose valuable hours **manually searching** and **downloading** technical documentation.\\n        - The repetitive process is **error-prone** and distracts from real engineering work.\\n\\n        ---\\n\\n        ## 3. The Solution\\n\\n        - **Automates** the document search, scraping, and download phases, cutting the time required by **75%+**.\\n        - Offers **three main stages**\\u2014document metadata retrieval, file metadata retrieval, and file downloads\\u2014each controlled by user-edited Markdown tables for fine-tuned selection.\\n\\n        ---\\n\\n        ## 4. Key Features\\n\\n        - **Three-Stage Workflow**\\n          1. **Documents**: Gather doc metadata, mark `item_include` in Markdown.\\n          2. **Files**: Fetch file info for included docs, mark `item_download` in Markdown.\\n          3. **Downloads**: Organize files with subfolder support based on `'/'` in `item_generated_name`.\\n\\n        - **Interactive Menu**: Allows step-by-step or fully automated runs.\\n        - **User Review via Markdown**: You decide which items to include or skip by editing `.md` tables.\\n        - **Configurable Filter Chains**: Glob-style pattern matching to auto-select or exclude docs/files.\\n        - **Smart Organization**: Hierarchical naming that embeds rig/drawing metadata, plus subfolder creation.\\n        - **Error Handling**: Graceful session management, robust page-scrolling logic, and deduplication.\\n\\n        ---\\n\\n        ## 5. Workflow Steps\\n\\n        1. **Change Parameters**: (Optional) Update rig number, search URLs.\\n        2. **Configure Filters**: Setup or edit filter rules to auto-include/exclude docs/files.\\n        3. **Fetch Docs**: Scrape document metadata into `<rig>-a-docs.json` (all `item_include=false` initially).\\n        4. **Export Docs**: Generate `<rig>-a-docs.md`; manually set `item_include=true` for desired items.\\n        5. **Import Updated Docs**: Sync changes back from Markdown to JSON.\\n        6. **Fetch Files**: Retrieve file metadata for those docs, saved as `<rig>-b-files.json` (`item_download=false`).\\n        7. **Export Files**: Create `<rig>-b-files.md`; set `item_download=true` for target files.\\n        8. **Import Updated Files**: Sync file selection from Markdown to JSON.\\n        9. **Download Files**: Acquire and organize all selected files under `outputs/downloads/<rig>`.\\n\\n        ---\\n\\n        ## 6. Architecture & Technology\\n\\n        - **Core Stack**:\\n          - **Python 3.6+**\\n          - **Selenium** for browser automation\\n          - **BeautifulSoup** for HTML parsing\\n          - **JSON/Markdown** for data storage and user edits\\n        - **Linear Multi-Stage** design with user checkpoints ensures incremental control and reusability.\\n        - **Filter System**: Pattern-based matching (inclusion/exclusion) on fields like `item_generated_name` or `item_case_description`.\\n        - **File Organization**: Subfolder creation from `'/'` in `item_generated_name`, plus sanitization of invalid characters.\\n\\n        ---\\n\\n        ## 7. Directory Structure\\n\\n    ```\\n\\n    ---\\n\\n    #### `README_v09.md`\\n\\n    ```markdown\\n        ## Intent\\n        To automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\\n\\n        ## Key Project Aspects\\n\\n        ### Primary Problem\\n        Engineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck\\u2014it's the manual preparation process.\\n\\n        ### Solution Approach\\n        A Python-based utility that:\\n        1. Automatically scrapes document metadata from RigOffice\\n        2. Extracts file information from those documents\\n        3. Downloads and organizes selected files based on user criteria\\n\\n        ### Current State\\n        Functional working prototype that:\\n        - Uses a 3-step workflow (document metadata \\u2192 file metadata \\u2192 download)\\n        - Stores intermediate results in JSON format\\n        - Allows user intervention between steps\\n        - Provides progress feedback\\n\\n        ### Critical Next Steps\\n        1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches\\n        2. **Implement file hash checking** to prevent redundant downloads\\n        3. **Improve progress visibility** during lengthy scraping operations\\n\\n        ### Core Technical Pattern\\n        A single-file, modular approach using:\\n        - Selenium for browser automation\\n        - JSON for data storage\\n        - Three-stage processing with user control points\\n        - Incremental updates to avoid redundant work\\n\\n        ### Key Success Metrics\\n        - Reduce documentation gathering time by 75%+\\n        - Ensure reliable retrieval of required documentation\\n        - Organize files in a way that streamlines workflow\\n        - Support both broad searches (by rig number) and targeted searches (by specific documents)\\n    ```\\n\\n    ---\\n\\n    #### `README_v10.md`\\n\\n    ```markdown\\n        ### RigOfficeDownloader Utility Workflow\\n\\n        This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow is clearly defined in the docstring at the beginning of the main script and follows these steps:\\n\\n        1. Fetch Documents\\n        - The utility starts by scraping document metadata from predefined search URLs\\n        - Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory\\n        - Each document entry includes metadata like title, document number, revision, etc.\\n        - All documents are initially marked with item_include=False\\n        - Each document gets an item_generated_name for better identification\\n\\n        2. Export Documents to Markdown\\n        - The JSON data is exported to a Markdown table: <rig>-a-docs.md\\n        - This allows the user to easily review and edit which documents to include\\n        - The user is expected to edit the Markdown file and set item_include=true for desired documents\\n\\n        3. Import Updated Document Data\\n        - After the user edits the Markdown file, the utility imports the changes back to the JSON file\\n        - This updates which documents are marked for file retrieval\\n\\n        4. Fetch Files for Selected Documents\\n        - For each document with item_include=true, the utility scrapes file metadata\\n        - File data is saved to <rig>-b-files.json\\n        - Each file is initially marked with item_download=False\\n        - Files inherit the document's item_generated_name with additional identifiers\\n\\n        5. Export Files to Markdown\\n        - The file data is exported to a Markdown table: <rig>-b-files.md\\n        - The user reviews and edits which files to download by setting item_download=true\\n\\n        6. Import Updated File Data\\n        - After editing, the utility imports the changes back to the JSON file\\n        - This updates which files are marked for download\\n\\n        7. Download Selected Files\\n        - Files with item_download=true are downloaded\\n        - Files are named according to their item_generated_name + extension\\n        - The utility supports creating subfolders based on '/' in the item_generated_name\\n        - Files are saved to the outputs/downloads/<rig> directory\\n\\n        Interactive Menu\\n        - The utility provides an interactive menu where the user can choose which steps to execute\\n        - This allows for flexibility in the workflow, enabling the user to run specific steps as needed\\n        - The user can also update the rig number and search URLs through this menu\\n\\n        Key Features\\n        - Automatic document and file metadata scraping\\n        - User-friendly Markdown editing interface\\n        - Customizable file naming with item_generated_name\\n        - Support for subfolder organization in downloads\\n        - Deduplication of documents and files\\n        - Configurable field ordering for JSON and Markdown exports\\n\\n        Technical Implementation\\n        - Uses Selenium with Chrome WebDriver for web scraping\\n        - Implements smart waiting strategies for page loading\\n        - Handles browser sessions with proper cleanup\\n        - Provides progress feedback during operations\\n        - Sanitizes filenames for valid paths\\n    ```\\n\\n    ---\\n\\n    #### `README_v11.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## Overview\\n        RigOfficeDownloader is an automation tool designed to streamline the process of retrieving and downloading technical documentation from NOV's RigDoc system. It eliminates the tedious, time-consuming process of manual document retrieval, allowing engineers to focus on their primary work.\\n\\n        ## Key Features\\n        - **Three-Stage Workflow**: Documents \\u2192 Files \\u2192 Downloads\\n        - **Interactive Menu**: Choose which steps to execute\\n        - **User Control Points**: Review and select documents/files via Markdown interfaces\\n        - **Smart File Organization**: Subfolder support based on naming patterns\\n        - **Configurable Filters**: Apply filter chains to automatically select relevant documents\\n\\n        ## Workflow\\n        1. **Document Retrieval**: Scrapes document metadata from RigDoc\\n        2. **Document Selection**: Exports to Markdown for user review and selection\\n        3. **File Metadata**: Fetches file information for selected documents\\n        4. **File Selection**: Exports to Markdown for user review and selection\\n        5. **Download**: Downloads selected files with intelligent naming and organization\\n\\n        ## Setup\\n        1. Run `py_venv_init.bat` to create and initialize the Python virtual environment\\n        2. The script will:\\n           - Find available Python installations\\n           - Create a virtual environment\\n           - Install required packages from requirements.txt\\n\\n        ## Usage\\n        1. Run `RigOfficeDownloader-v4.bat` to start the application\\n        2. Use the interactive menu to:\\n           - Configure search parameters\\n           - Run specific workflow steps\\n           - Apply filter chains\\n           - Review and select documents/files\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Handles errors gracefully\\n        - Preserves document context and relationships\\n\\n        ## File Structure\\n        ```\\n        outputs/\\n        \\u251c\\u2500\\u2500 data/           # JSON and Markdown files for documents and files\\n        \\u2514\\u2500\\u2500 downloads/      # Downloaded files organized by rig number\\n        ```\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser (for Selenium automation)\\n    ```\\n\\n    ---\\n\\n    #### `README_v12.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\\n\\n        ```\\n        Documents -> Files -> Downloads\\n        ```\\n\\n        ## Overview\\n        RigOfficeDownloader eliminates the tedious, time-consuming process of manually retrieving technical documentation from NOV's RigDoc system. Engineers can focus on their primary work instead of navigating complex document repositories.\\n\\n        ## Key Features\\n        - Three-stage workflow with user control points\\n        - Interactive menu for flexible execution\\n        - Configurable filter chains for automatic selection\\n        - Smart file organization with subfolder support\\n        - Markdown interfaces for document/file review\\n\\n        ## Workflow\\n        1. **Document Retrieval**: Scrapes document metadata from RigDoc\\n        2. **Document Selection**: Exports to Markdown for user review and selection\\n        3. **File Metadata**: Fetches file information for selected documents\\n        4. **File Selection**: Exports to Markdown for user review and selection\\n        5. **Download**: Downloads selected files with intelligent naming and organization\\n\\n        ## Setup\\n        1. Run `py_venv_init.bat` to create and initialize the Python virtual environment\\n        2. Run `RigOfficeDownloader-v4.bat` to start the application\\n\\n        ## Usage\\n        Choose from the interactive menu:\\n        ```\\n        [0] Change search parameters\\n        [1] Configure filter chain\\n        [2] Fetch docs (scrape initial data)\\n        [3] Export docs (to Markdown for editing)\\n        [4] Import updated doc data\\n        [5] Fetch files (prepare files for download)\\n        [6] Export files (to Markdown for editing)\\n        [7] Import updated file data\\n        [8] Download files\\n        ```\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Handles errors gracefully\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser (for Selenium automation)\\n\\n        ## Version History\\n        - v1: Basic document retrieval and download\\n        - v2: JSON/Markdown conversion and user selection\\n        - v3: Improved error handling and field organization\\n        - v4: Subfolder support, filter chains, field ordering\\n    ```\\n\\n    ---\\n\\n    #### `README_v13.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        > **Automate** document retrieval from NOV\\u2019s RigDoc system, **minimize** repetitive tasks, and **accelerate** workflow.\\n\\n        ---\\n\\n        ## Overview\\n\\n        **RigOfficeDownloader** is a single-script Python utility that **scrapes**, **filters**, and **downloads** technical documentation from NOV\\u2019s RigDoc. Rather than wasting hours searching for and reviewing documents one by one, engineers can leverage an **interactive workflow** that guides them through each phase of document collection.\\n\\n        **Key Highlights**\\n        - **Three-Stage Workflow**: Documents \\u2192 Files \\u2192 Downloads\\n        - **Interactive Menu**: Run the entire workflow or individual steps on demand\\n        - **Markdown Edits**: Quickly select or deselect items by editing `.md` tables\\n        - **Subfolder Support**: Organize downloads with nested folder paths\\n        - **Filter Chains**: Automate selection based on glob-style patterns\\n        - **Single-File Simplicity**: All core logic in one script (~500 lines)\\n\\n        ---\\n\\n        ## The Problem\\n\\n        Engineering projects require diverse technical documents from RigDoc. This **manual** retrieval is:\\n        - **Tedious & Repetitive**: Browsing multiple pages, copying metadata, creating folders\\n        - **Error-Prone**: Risk of missing crucial docs or misplacing files\\n        - **Time Consuming**: Delays the start of high-value engineering tasks\\n\\n        ---\\n\\n        ## The Solution\\n\\n        RigOfficeDownloader streamlines the entire process through an **automated workflow**:\\n        1. **Fetch** document metadata (stores in `<rig>-a-docs.json`)\\n        2. **Export** docs to Markdown (`<rig>-a-docs.md`): mark `item_include=true`\\n        3. **Import** updated doc data back into JSON\\n        4. **Fetch** file metadata for included docs\\n        5. **Export** files to Markdown (`<rig>-b-files.md`): mark `item_download=true`\\n        6. **Import** updated file data\\n        7. **Download** files with subfolder paths derived from the generated names\\n\\n        ---\\n\\n        ## Workflow Steps in Detail\\n\\n        1. **Change / Configure**\\n           - Input your **rig number** and **search URLs**.\\n           - Set up or modify **filters** (e.g., auto-include `*DRILL*FLOOR*` docs, exclude `*VOID*` docs).\\n\\n        2. **Fetch Documents**\\n           - Scrape document metadata from RigDoc URLs.\\n           - Data saved to JSON (`<rig>-a-docs.json`), all set to `item_include=false`.\\n\\n        3. **Export & Review Docs**\\n           - Creates `<rig>-a-docs.md`.\\n           - Manually set `item_include=true` for relevant docs.\\n\\n        4. **Import Updated Docs**\\n           - Reads user changes back into JSON.\\n\\n        5. **Fetch File Metadata**\\n           - For each included doc, scrapes associated files (saved in `<rig>-b-files.json`).\\n           - `item_download=false` by default.\\n\\n        6. **Export & Review Files**\\n           - Creates `<rig>-b-files.md`.\\n           - Set `item_download=true` for desired files.\\n\\n        7. **Import Updated Files**\\n           - Sync file selections back into JSON.\\n\\n        8. **Download Files**\\n           - Retrieves selected files, applying `item_generated_name` for naming.\\n           - `'/'` in `item_generated_name` spawns subfolders in `outputs/downloads/<rig>`.\\n\\n        ---\\n\\n        ## Directory Structure\\n\\n    ```\\n\\n    ---\\n\\n    #### `README_v14.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n        *Automated Document Retrieval for NOV RigDoc*\\n\\n        ## Overview\\n        Eliminates tedious manual document retrieval from NOV's RigDoc system through intelligent automation. Preserves 75%+ engineering time by streamlining the 3D modeling preparation workflow.\\n\\n        ## Key Features\\n        ```\\n        \\u25fc Three-Stage Workflow  \\u25fc Interactive Menu  \\u25fc Human Checkpoints\\n        \\u25fc Metadata-Powered File Organization  \\u25fc Pattern-Based Filter Chains\\n        \\u25fc Markdown Review Interface  \\u25fc Smart Browser Automation\\n        ```\\n\\n        ## Workflow Architecture\\n        **1. Document Retrieval**\\n        ```python\\n        fetch_docs() \\u2192 <rig>-a-docs.json  # Scrape metadata\\n        json_to_md_table() \\u2192 <rig>-a-docs.md  # Edit item_include=true\\n        md_table_to_json()  # Commit selections\\n        ```\\n        **2. File Processing**\\n        ```python\\n        fetch_files() \\u2192 <rig>-b-files.json  # Get file metadata\\n        json_to_md_table() \\u2192 <rig>-b-files.md  # Set item_download=true\\n        md_table_to_json()  # Finalize choices\\n        ```\\n        **3. Smart Download**\\n        ```python\\n        download_files()  # Auto-organize with:\\n        \\u2022 /subfolders from item_generated_name\\n        \\u2022 Sanitized filenames\\n        \\u2022 Deduplication\\n        ```\\n\\n        ## Getting Started\\n        ```bash\\n        # 1. Initialize environment\\n        py_venv_init.bat\\n\\n        # 2. Launch utility\\n        RigOfficeDownloader-v4.bat [--auto|--interactive|--config]\\n        ```\\n        **First-Run Configuration**\\n        ```python\\n        CONFIG = {\\n            \\\"rig_number\\\": \\\"R0000.020\\\",\\n            \\\"search_urls\\\": PROJECTINFO_GAD,  # Prebuilt equipment templates\\n            \\\"filters\\\": [{\\n                \\\"type\\\": \\\"docs\\\",\\n                \\\"pattern\\\": \\\"*G000*\\\",\\n                \\\"field\\\": \\\"item_include\\\",\\n                \\\"value\\\": True\\n            }]\\n        }\\n        ```\\n\\n        ## File Organization\\n        ```\\n        outputs/\\n        \\u251c\\u2500\\u2500 data/               # Metadata control files\\n        \\u2502   \\u251c\\u2500\\u2500 R1234-a-docs.json\\n        \\u2502   \\u251c\\u2500\\u2500 R1234-a-docs.md\\n        \\u2502   \\u251c\\u2500\\u2500 R1234-b-files.json\\n        \\u2502   \\u2514\\u2500\\u2500 R1234-b-files.md\\n        \\u2514\\u2500\\u2500 downloads/          # Final organized files\\n            \\u2514\\u2500\\u2500 R1234/\\n                \\u251c\\u2500\\u2500 Drilling/\\n                \\u2502   \\u2514\\u2500\\u2500 G0001_REV03--CASE45-DrillFloor.pdf\\n                \\u2514\\u2500\\u2500 Topdrive/\\n                    \\u2514\\u2500\\u2500 ASM-00_REV12--CASE32-TopDriveAssembly.dwg\\n        ```\\n\\n        ## Interactive Control\\n        ```\\n        [0] Change rig number/search URLs    [4] Import doc selections\\n        [1] Configure filter chain           [5] Fetch file metadata\\n        [2] Fetch documents                  [6] Export files to Markdown\\n        [3] Export docs to Markdown          [7] Import file selections\\n        [8] Download files                   [9] Advanced configuration\\n        ```\\n\\n        ## Why This Tool?\\n        | Manual Process               | Automated Solution           |\\n        |------------------------------|-------------------------------|\\n        | 4-6 hours per project setup  | <1 hour with 75% automation  |\\n        | Error-prone file management  | Metadata-driven organization |\\n        | No version control           | JSON/Markdown audit trail    |\\n        | Context loss between teams   | Standardized naming schema   |\\n\\n    ```\",\n  \"instruction_format\": \"# RigOfficeDownloader\\n\\nAutomate and streamline technical document retrieval from NOV RigDoc, eliminating tedious manual effort and enabling rapid, organized access to engineering documentation.\\n\\n## Purpose\\nAutomate document retrieval from NOV RigDoc, drastically reducing manual prep time, ensuring reliable, organized, and auditable project documentation access for engineering workflows.\\n\\n## Key Features\\n- Three-stage sequential pipeline: Documents \\u2192 Files \\u2192 Downloads\\n- Toggle between fully automated and interactive (menu-driven) operation\\n- Human checkpoints and user review via editable Markdown tables for both document and file selection\\n- Configurable pattern-based filter chains for auto-inclusion/exclusion of items\\n- Metadata-driven file naming, with subfolder/file organization based on metadata fields (e.g., rig number, doc type)\\n- Robust browser automation (Selenium) with smart waiting, session management, deduplication, and error handling\\n- Strict field ordering in JSON and Markdown outputs\\n- Comprehensive traceability and auditability via exported JSON and Markdown records\\n\\n## Workflow: Step-by-Step Process\\n1. Configure rig number and search URLs (define or select search templates and filters)\\n2. Fetch document metadata from RigDoc and export to `<rig>-a-docs.json`\\n3. Export document list to Markdown (`<rig>-a-docs.md`) for manual review/edit (set `item_include=true` as needed)\\n4. Import updated selections from Markdown back to JSON, reflecting included docs\\n5. Fetch file metadata for selected documents and store in `<rig>-b-files.json`\\n6. Export file list to Markdown (`<rig>-b-files.md`) for review (set `item_download=true` on desired files)\\n7. Import updated file selections from Markdown, updating which files will be downloaded\\n8. Download all marked files to `outputs/downloads/<rig>`, auto-organized via generated names/subfolder paths derived from metadata\\n\\n## Directory Structure\\n```\\noutputs/\\n\\u251c\\u2500\\u2500 data/           # All metadata/selection states (JSON + Markdown)\\n\\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.json\\n\\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.md\\n\\u2502   \\u251c\\u2500\\u2500 <rig>-b-files.json\\n\\u2502   \\u2514\\u2500\\u2500 <rig>-b-files.md\\n\\u2514\\u2500\\u2500 downloads/      # All downloaded documents, hierarchically organized\\n    \\u2514\\u2500\\u2500 <rig>/\\n        \\u251c\\u2500\\u2500 <subfolder>/\\n        \\u2502   \\u2514\\u2500\\u2500 <item_generated_name>.<ext>\\n        \\u2514\\u2500\\u2500 ...\\n```\\nFile and folder naming reflect document metadata and support subfolders using '/' within the `item_generated_name` field.\\n\\n## Setup / Prerequisites\\n- Windows OS\\n- Python 3.11+ (installer provided or specify in requirements.txt)\\n- Chrome browser (with valid Chromedriver for Selenium automation)\\n- Valid NOV RigDoc credentials\\n- Install Python dependencies: `pip install -r requirements.txt`\\n\\n## Execution / Run Modes\\n- Run interactively or fully automated via batch or script command:\\n    - `RigOfficeDownloader-v4.bat --auto` \\u2013 full pipeline, minimal user intervention\\n    - `RigOfficeDownloader-v4.bat --interactive` \\u2013 menu-driven, step-by-step workflow\\n    - `RigOfficeDownloader-v4.bat --config` \\u2013 edit search templates, update filters\\n\\n## Formulate Filters & Configuration\\n- Edit filter chains to define auto-inclusion/exclusion (glob-style patterns, e.g. `*G000*`, `*VOID*`)\\n- Adjust which document fields control item selection (such as by rig, system, doc type, status)\\n- Update configuration to specify rig number and/or template search URLs\\n\\n## Benefits & Advantages\\n- Achieve a 75%+ reduction in time spent on document gathering for engineering projects\\n- Allow engineers to focus on technical work, not mundane document management\\n- Maintain consistently structured output directories with audit trails (JSON + Markdown)\\n- Ensure robust error handling, smart deduplication, and correct organization of files\\n- Provide a solution that is scalable, cohesive, and ready for broader internal adoption\\n\\n## Summary Table of Features\\n| Capability             | Manual Process         | Automated with RigOfficeDownloader       |\\n|----------------------- |---------------------- |-----------------------------------------|\\n| Time per project setup | 4\\u20136 hours             | <1 hour                                 |\\n| File organization      | Error-prone, ad hoc   | Metadata-driven, standardized           |\\n| User review            | Tedious, repetitive   | Centralized via Markdown checkpoints    |\\n| Auditability           | Weak/none             | Full JSON/Markdown traceability         |\\n| Expandability          | N/A                   | Configurable, reusable templates        |\\n\\n## Version/Extensibility\\n- Built-in support for subfolders, filter chains, field ordering, advanced configuration\\n- Easily extensible for new departments and additional filtering/organizational patterns\\n\\n> \\\"Complexity is the enemy of execution \\u2013 Optimized for engineers, by engineers.\\\"\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": {\n    \"README.md\": {\n      \"title\": \"RigOfficeDownloader\",\n      \"purpose\": \"Automate and streamline technical document retrieval from NOV RigDoc, eliminating tedious manual effort and ensuring rapid, organized access to engineering documentation.\",\n      \"features\": [\n        \"Three-stage sequential pipeline: Documents → Files → Downloads\",\n        \"Automated and interactive (menu-driven) run modes\",\n        \"Editable Markdown checkpoints for document/file selection\",\n        \"Configurable pattern-based filter chains (auto-inclusion/exclusion)\",\n        \"Metadata-driven file naming and hierarchical subfolder organization\",\n        \"Robust Selenium-based browser automation, smart waiting, deduplication, error handling\",\n        \"Strict field ordering in JSON/Markdown outputs\",\n        \"Full traceability/auditability via JSON and Markdown records\"\n      ],\n      \"workflow\": [\n        \"1. Configure rig number and search URLs (set filters/templates)\",\n        \"2. Fetch document metadata from RigDoc to <rig>-a-docs.json\",\n        \"3. Export docs to Markdown (<rig>-a-docs.md); review/edit (set item_include)\",\n        \"4. Import updated selections from Markdown → JSON\",\n        \"5. Fetch file metadata for selected docs → <rig>-b-files.json\",\n        \"6. Export files to Markdown (<rig>-b-files.md); set item_download as needed\",\n        \"7. Import updated file selections from Markdown\",\n        \"8. Download all marked files to outputs/downloads/<rig>, auto-organized via generated subfolder paths\"\n      ],\n      \"directory_structure\": [\n        \"outputs/\",\n        \"├── data/         # All metadata/selection (JSON + Markdown)\",\n        \"│   ├── <rig>-a-docs.json\",\n        \"│   ├── <rig>-a-docs.md\",\n        \"│   ├── <rig>-b-files.json\",\n        \"│   └── <rig>-b-files.md\",\n        \"└── downloads/    # All downloaded files, by rig/subfolder structure\",\n        \"    └── <rig>/\",\n        \"        ├── <subfolder>/\",\n        \"        │   └── <item_generated_name>.<ext>\",\n        \"        └── ...\"\n      ],\n      \"setup_prerequisites\": [\n        \"Windows OS\",\n        \"Python 3.11+\",\n        \"Chrome browser (with valid Chromedriver)\",\n        \"Valid NOV RigDoc credentials\",\n        \"Install dependencies: pip install -r requirements.txt\"\n      ],\n      \"execution_run_modes\": [\n        \"RigOfficeDownloader-v4.bat --auto (full automation)\",\n        \"RigOfficeDownloader-v4.bat --interactive (menu-driven)\",\n        \"RigOfficeDownloader-v4.bat --config (edit filters/templates)\"\n      ],\n      \"configuration_filtering\": [\n        \"Edit filter chains (glob patterns: *G000*, *VOID*, etc.)\",\n        \"Specify rig number and search URLs as config\",\n        \"Modify which fields govern item selection\"\n      ],\n      \"benefits\": [\n        \"≥75% reduction in document preparation time\",\n        \"Consistent, metadata-driven file organization\",\n        \"Centralized user review via Markdown checkpoints\",\n        \"Robust error handling, deduplication, and smart automation\",\n        \"Scalable, cohesive, and audit-ready for broader adoption\"\n      ],\n      \"comparison_table\": {\n        \"Time per project\": {\n          \"Manual\": \"4–6 hours\",\n          \"Automated\": \"<1 hour\"\n        },\n        \"File organization\": {\n          \"Manual\": \"Error-prone/ad hoc\",\n          \"Automated\": \"Standardized/metadata-driven\"\n        },\n        \"User review\": {\n          \"Manual\": \"Tedious/repetitive\",\n          \"Automated\": \"Markdown checkpoints\"\n        },\n        \"Auditability\": {\n          \"Manual\": \"None\",\n          \"Automated\": \"Full JSON/Markdown traceability\"\n        },\n        \"Expandability\": {\n          \"Manual\": \"N/A\",\n          \"Automated\": \"Configurable, reusable templates\"\n        }\n      },\n      \"extensibility\": [\n        \"Built-in support: subfolders, filter chains, field ordering, advanced config\",\n        \"Easily adaptable for new departments/organizational patterns\"\n      ],\n      \"philosophy\": \"\\\"Complexity is the enemy of execution — Optimized for engineers, by engineers.\\\"\"\n    }\n  }\n}", "cost": 0.0}}}, {"instruction": "# Result Combiner\n\nYou are a specialized aggregator that combines results from multiple instruction steps into a cohesive output. Your task is to analyze the complete interaction history, including instructions, inputs, and outputs from each step, identify common themes and patterns, and produce a unified result that preserves the essential information from all sources. Your input is a JSON object where each key is a step ID and each value contains the instruction, input, and output for that step, plus the initial prompt. Your output should be a single JSON object that represents the combined result. Execute as:\n\n`{role=result_combiner;input=[interaction_history:json];process=[analyze_complete_interaction_history(),identify_common_patterns(),extract_key_elements(),merge_into_cohesive_whole(),format_as_unified_output()];constraints=[preserve_essential_information(),maintain_logical_structure(),avoid_redundancy()];requirements=[output_as_single_json(),ensure_completeness(),prioritize_clarity()];output={combined_result:json}}`", "step": "a", "title": "Result Combiner", "input": "{\n  \"initial_prompt\": \"# CONTEXT\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\nRigOfficeDownloader is a Python-based automation tool designed to streamline and significantly reduce the time required for retrieving, reviewing, and organizing technical documentation from the NOV RigDoc system. Its primary workflow utilizes a sequential pipeline (Documents \\u2192 Files \\u2192 Downloads) and allows for either automated or menu-driven interactive processes. User selection checkpoints via editable Markdown tables, filter chains for inclusion/exclusion, and metadata-driven file organization ensure accuracy and efficiency. Outputs include traceable JSON and Markdown records. The utility integrates easily into existing Windows-based environments and offers substantial labor savings (75%+ reduction in manual prep time), scalability, and simplification of thorough auditability.\\n\\n# SOLUTION\\n\\ni've given the utility the temporary name `RigOfficeDownloader`, but this might change (as it feels lazy and non-proprietary). it's a utility that'll be used internally in my department (in a large engineering company) to automate (speed up the process) when working on new projects. Although the default mode of the utility is to rely on the predefined search templates and filters to automate the full process, it is also designed (and thoughtfully set up) such that it can either be used manually or interactively (e.g. if i just want to download a list of specific documents) while adhering to the *same* concepts. although this utility springs out from the need of a small subset of the company (just a small department within the company of <20 people) it's important to always keep in mind that since it'll *eventually* be used by other people (in other departmens within the company), ALWAYS ENSURE **inherent and fundamental cohesiveness**; because **simplicity and elegance** is __INTEGRAL__ - *\\\"Complexity is the enemy of execution\\\" - Optimized for engineers, by engineers*.\\n\\n## WORKFLOW\\n\\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).\\n\\n# REQUIREMENTS\\n\\ni want you to focus on the abstract and general concepts related to **functionality**, i don't want it to destroy the \\\"canvas\\\" (of `README.md`) with unusefull noise (unneccessary bloat).\\n\\n# OBJECTIVE\\n\\nConsolidate and extract the essential functional concepts from all provided README variations for the RigOfficeDownloader utility. Produce a finalized README.md under 100 lines that:\\n\\n- Defines the application's purpose: automating and streamlining document retrieval from the NOV RigDoc system, eliminating tedious manual effort, and enabling rapid, organized access to technical documentation.\\n- Clearly lists key features and workflow architecture:\\n  - Three-stage pipeline: Documents \\u2192 Files \\u2192 Downloads\\n  - Interactive or automated menu-driven operation\\n  - User checkpoints and Markdown review/edit interfaces for selection\\n  - Configurable pattern-based filter chains for auto-selection/inclusion/exclusion\\n  - Metadata-driven naming and subfolder file organization\\n  - Smart browser automation, error handling, deduplication, and field ordering\\n- Presents the sequential workflow steps as actionable bullet points or numbered steps:\\n  1. Configure rig number and search URLs (with filter setup)\\n  2. Fetch document metadata (JSON export)\\n  3. Export documents to Markdown for user editing (set item_include)\\n  4. Import edited selections (Markdown \\u2192 JSON)\\n  5. Fetch related file metadata for selected documents\\n  6. Export file list to Markdown for review (set item_download)\\n  7. Import updated file selections\\n  8. Download marked files, organized via smart naming/subfolders\\n- Summarize directory structure for outputs (data, downloads) and illustrate how subfolders reflect metadata in file names\\n- Specify setup/prerequisites concisely:\\n  - Python 3.11+, Windows OS, Chrome browser, valid RigDoc credentials, requirements.txt\\n- Describe available run modes: --auto (automated), --interactive (step-by-step), --config (adjust templates/filters)\\n- Emphasize benefits: reduction in document gathering time (75%+), consistent organization, robust error handling, extensibility for broader internal use, and auditability through JSON/Markdown traceability\\n- Maintain original intent, technical depth, and context integrity throughout while strictly omitting unnecessary bloat and self-references.\\n- Format the README as a single clear markdown file, using concise sections, technical terminology, and actionable language for internal engineering users.\\n\\n# VARIATIONS\\n\\n    # Dir `readme_variations`\\n\\n    ### File Structure\\n\\n    ```\\n    \\u251c\\u2500\\u2500 README_v01.md\\n    \\u251c\\u2500\\u2500 README_v02.md\\n    \\u251c\\u2500\\u2500 README_v03.md\\n    \\u251c\\u2500\\u2500 README_v04.md\\n    \\u251c\\u2500\\u2500 README_v05.md\\n    \\u251c\\u2500\\u2500 README_v06.md\\n    \\u251c\\u2500\\u2500 README_v07.md\\n    \\u251c\\u2500\\u2500 README_v08.md\\n    \\u251c\\u2500\\u2500 README_v09.md\\n    \\u251c\\u2500\\u2500 README_v10.md\\n    \\u251c\\u2500\\u2500 README_v11.md\\n    \\u251c\\u2500\\u2500 README_v12.md\\n    \\u251c\\u2500\\u2500 README_v13.md\\n    \\u2514\\u2500\\u2500 README_v14.md\\n    ```\\n\\n    ---\\n\\n    #### `README_v01.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## Overview\\n        RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\\n\\n        ## Key Features\\n        - Three-stage workflow: Documents -> Files -> Downloads\\n        - Interactive menu for flexible execution\\n        - User control points via Markdown interfaces\\n        - Smart file organization with subfolder support\\n        - Configurable filter chains\\n\\n        ## Setup & Usage\\n        1. Run `py_venv_init.bat` to create the Python environment\\n        2. Run `RigOfficeDownloader-v4.bat` to start the application\\n        3. Use the interactive menu to configure and execute workflow steps\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Preserves document context and relationships\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser (for Selenium automation)\\n    ```\\n\\n    ---\\n\\n    #### `README_v02.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## The Problem\\n        Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:\\n        - Tedious and repetitive\\n        - Error-prone\\n        - A poor use of skilled engineering time\\n\\n        ## The Solution\\n        RigOfficeDownloader automates the document retrieval process through a three-stage workflow:\\n        1. **Document Retrieval**: Automatically scrapes document metadata\\n        2. **File Metadata**: Fetches file information for selected documents\\n        3. **Smart Downloads**: Downloads files with intelligent naming and organization\\n\\n        ## How It Works\\n        - Uses Selenium to automate web interactions with RigDoc\\n        - Exports data to Markdown for user review and selection\\n        - Applies configurable filters to pre-select relevant documents\\n        - Organizes downloads with consistent naming patterns\\n\\n        ## Getting Started\\n        1. Run `py_venv_init.bat` to set up the environment\\n        2. Run `RigOfficeDownloader-v4.bat` to launch the application\\n        3. Follow the interactive menu prompts\\n\\n        ## Impact\\n        Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.\\n    ```\\n\\n    ---\\n\\n    #### `README_v03.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## Technical Overview\\n        RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\\n\\n        ## Architecture\\n        - **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown\\n        - **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads\\n        - **File Organization**: Hierarchical naming system with metadata embedding and subfolder support\\n\\n        ## Workflow Steps\\n        1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information\\n        2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing\\n        3. **Selection Import**: `md_table_to_json()` imports user selections\\n        4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents\\n        5. **File Selection**: Export/import cycle for user selection of files\\n        6. **Download Process**: `download_files()` retrieves selected files with smart naming\\n\\n        ## Filter Chain System\\n        Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.\\n\\n        ## Setup Instructions\\n        1. Run `py_venv_init.bat` to initialize the Python environment\\n        2. Run `RigOfficeDownloader-v4.bat` to execute the application\\n\\n        ## Version History\\n        - v1: Basic document retrieval and download\\n        - v2: JSON/Markdown conversion and user selection\\n        - v3: Improved error handling and field organization\\n        - v4: Subfolder support, filter chains, field ordering\\n    ```\\n\\n    ---\\n\\n    #### `README_v04.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n        > Automate document retrieval from NOV's RigDoc system\\n\\n        ## What This Tool Does\\n        RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\\n\\n        ## Quick Start Guide\\n        1. **Setup**: Run `py_venv_init.bat` to create the Python environment\\n        2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application\\n        3. **Configure**: Enter your rig number and search URLs when prompted\\n        4. **Run**: Follow the numbered menu to execute each step of the workflow\\n\\n        ## Workflow Steps Explained\\n        0. **Change Parameters**: Update rig number and search URLs\\n        1. **Configure Filters**: Set up automatic document/file selection rules\\n        2. **Fetch Documents**: Retrieve document metadata from RigDoc\\n        3. **Review Documents**: Edit the Markdown file to select which documents to process\\n        4. **Import Selections**: Load your document selections\\n        5. **Fetch Files**: Get file metadata for selected documents\\n        6. **Review Files**: Edit the Markdown file to select which files to download\\n        7. **Import File Selections**: Load your file selections\\n        8. **Download Files**: Retrieve the selected files\\n\\n        ## Tips for Success\\n        - Use filters to automatically pre-select relevant documents\\n        - Review the Markdown files carefully before proceeding to the next step\\n        - Files will be organized in subfolders based on '/' in their generated names\\n\\n        ## Need Help?\\n        Check the source code comments for detailed information about each function and workflow step.\\n    ```\\n\\n    ---\\n\\n    #### `README_v05.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\\n\\n        ```\\n        Documents -> Files -> Downloads\\n        ```\\n\\n        ## Features\\n        - Three-stage workflow with user control points\\n        - Interactive menu for flexible execution\\n        - Configurable filter chains for automatic selection\\n        - Smart file organization with subfolder support\\n        - Markdown interfaces for document/file review\\n\\n        ## Quick Start\\n        ```\\n        1. Run py_venv_init.bat\\n        2. Run RigOfficeDownloader-v4.bat\\n        3. Follow the interactive menu\\n        ```\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Handles errors gracefully\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser\\n    ```\\n\\n    ---\\n\\n    #### `README_v06.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.\\n\\n        ## Key Features\\n        - **Three-Stage Workflow**: Document selection \\u2192 File selection \\u2192 Download\\n        - **Metadata Preservation**: Structured naming with revision/case numbers\\n        - **Interactive Review**: Markdown tables for manual inclusion flags\\n        - **Smart Organization**: Automatic subfolder creation via naming patterns\\n        - **Filter System**: Pattern-based inclusion/exclusion rules\\n        - **Browser Automation**: Smart waiting strategies and session management\\n\\n        ## Workflow Process\\n        ```python\\n        1. Fetch Documents       # Scrape metadata \\u2192 <rig>-a-docs.json\\n        2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true\\n        3. Import Selections     # Update JSON with user choices\\n        4. Fetch Files           # Get file listings \\u2192 <rig>-b-files.json\\n        5. Export Files MD       # <rig>-b-files.md - Set item_download=true\\n        6. Import File Choices   # Update file selections\\n        7. Download Files        # Auto-organized with subfolder support\\n        ```\\n\\n        ## Configuration (CONFIG Section)\\n        ```python\\n        {\\n            \\\"rig_number\\\": \\\"R0000.020\\\",  # Target rig ID\\n            \\\"search_urls\\\": [            # Predefined search templates\\n                \\\"https://rigdoc.nov.com/search/rigsearch?q=...\\\",\\n                \\\"https://rigdoc.nov.com/search/rigsearch?q=...\\\"\\n            ],\\n            \\\"filters\\\": [                # Sequential processing rules\\n                {\\n                    \\\"type\\\": \\\"docs\\\",     # Apply to documents/files\\n                    \\\"pattern\\\": \\\"*DRILL*FLOOR*\\\",  # Glob-style matching\\n                    \\\"field\\\": \\\"item_include\\\",     # Field to modify\\n                    \\\"value\\\": True       # Set True/False based on match\\n                }\\n            ]\\n        }\\n        ```\\n\\n        ## Setup & Usage\\n        ```bash\\n        # Initialize environment\\n        py_venv_init.bat\\n        py_venv_pip_install.bat\\n\\n        # Run modes\\n        RigOfficeDownloader-v4.py [rig_number] [mode]\\n\\n        Modes:\\n        --auto         # Full automation\\n        --interactive  # Step-by-step control\\n        --config       # Modify search templates/filters\\n        ```\\n\\n        ## Key Configuration Patterns\\n        - **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)\\n        - **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)\\n        - **File Types**: Auto-prioritize PDFs with `*.pdf` pattern\\n\\n        ## Requirements\\n        - Chrome Browser + ChromeDriver\\n        - Active RigDoc credentials\\n        - Python 3.8+ with dependencies from requirements.txt\\n        - Network access to rigdoc.nov.com\\n\\n        ## Advanced Features\\n        - **Path Sanitization**: Auto-clean special chars while preserving /\\n        - **Deduplication**: Hash-based conflict resolution\\n        - **Field Ordering**: Customizable JSON/Markdown columns\\n        - **Smart Scrolling**: Progressive page loading detection\\n\\n        > Reduces documentation prep time by 60-75% compared to manual retrieval\\n        > Version 4.0 | Active development with subfolder support\\n    ```\\n\\n    ---\\n\\n    #### `README_v07.md`\\n\\n    ```markdown\\n\\n        ## Overview\\n        - Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\\n        - Use case: curate, review, and batch-download rig-related documents and technical files.\\n\\n        ## Directory Structure\\n\\n        * `outputs/` (BASE\\\\_OUTPUT): All results stored here\\n        * `outputs/data/` (DATA\\\\_DIR): Document and file metadata (JSON/MD)\\n        * `outputs/downloads/` (DL\\\\_DIR): Downloaded PDF and file outputs\\n\\n        ## Pipeline Overview\\n\\n        1. Change search parameters (rig number, URLs)\\n        2. Configure filter chain (add, edit, delete, toggle, reorder filters)\\n        3. Fetch docs (scrape data from rigdoc.nov.com)\\n        4. Export docs to Markdown (for selection/editing)\\n        5. Import docs from Markdown (sync edited selection)\\n        6. Fetch candidate files linked to selected docs\\n        7. Export file list to Markdown (for editing/selecting files for download)\\n        8. Import updated file list from Markdown\\n        9. Download marked files (PDFs only, via Chrome)\\n\\n        ## Manual Editing\\n\\n        * Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing\\n        * Set `item_include` (docs) and `item_download` (files) fields\\n\\n        ## Running the Tool\\n\\n        ```bash\\n        python rigdocscraper.py\\n        ```\\n\\n        * Interactive menu enables step selection (numbers/comma/space-separated)\\n        * Supports adjusting parameters, filter configuration, and reviewing batch steps\\n        * Prompts will guide through editing, import/export, and download procedures\\n\\n        ## Troubleshooting\\n\\n        * Requires functioning Chrome installation; verify webdriver-manager compatibility\\n        * Common issues: browser launch failures, login/captcha requirements, file permissions\\n        * Output logs and warnings shown in terminal; inspect `outputs/data/` for progress\\n    ```\\n\\n    ---\\n\\n    #### `README_v08.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        > **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.\\n\\n        ---\\n\\n        ## 1. Overview\\n\\n        RigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents \\u2192 Files \\u2192 Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.\\n\\n        ---\\n\\n        ## 2. The Problem\\n\\n        - Engineers lose valuable hours **manually searching** and **downloading** technical documentation.\\n        - The repetitive process is **error-prone** and distracts from real engineering work.\\n\\n        ---\\n\\n        ## 3. The Solution\\n\\n        - **Automates** the document search, scraping, and download phases, cutting the time required by **75%+**.\\n        - Offers **three main stages**\\u2014document metadata retrieval, file metadata retrieval, and file downloads\\u2014each controlled by user-edited Markdown tables for fine-tuned selection.\\n\\n        ---\\n\\n        ## 4. Key Features\\n\\n        - **Three-Stage Workflow**\\n          1. **Documents**: Gather doc metadata, mark `item_include` in Markdown.\\n          2. **Files**: Fetch file info for included docs, mark `item_download` in Markdown.\\n          3. **Downloads**: Organize files with subfolder support based on `'/'` in `item_generated_name`.\\n\\n        - **Interactive Menu**: Allows step-by-step or fully automated runs.\\n        - **User Review via Markdown**: You decide which items to include or skip by editing `.md` tables.\\n        - **Configurable Filter Chains**: Glob-style pattern matching to auto-select or exclude docs/files.\\n        - **Smart Organization**: Hierarchical naming that embeds rig/drawing metadata, plus subfolder creation.\\n        - **Error Handling**: Graceful session management, robust page-scrolling logic, and deduplication.\\n\\n        ---\\n\\n        ## 5. Workflow Steps\\n\\n        1. **Change Parameters**: (Optional) Update rig number, search URLs.\\n        2. **Configure Filters**: Setup or edit filter rules to auto-include/exclude docs/files.\\n        3. **Fetch Docs**: Scrape document metadata into `<rig>-a-docs.json` (all `item_include=false` initially).\\n        4. **Export Docs**: Generate `<rig>-a-docs.md`; manually set `item_include=true` for desired items.\\n        5. **Import Updated Docs**: Sync changes back from Markdown to JSON.\\n        6. **Fetch Files**: Retrieve file metadata for those docs, saved as `<rig>-b-files.json` (`item_download=false`).\\n        7. **Export Files**: Create `<rig>-b-files.md`; set `item_download=true` for target files.\\n        8. **Import Updated Files**: Sync file selection from Markdown to JSON.\\n        9. **Download Files**: Acquire and organize all selected files under `outputs/downloads/<rig>`.\\n\\n        ---\\n\\n        ## 6. Architecture & Technology\\n\\n        - **Core Stack**:\\n          - **Python 3.6+**\\n          - **Selenium** for browser automation\\n          - **BeautifulSoup** for HTML parsing\\n          - **JSON/Markdown** for data storage and user edits\\n        - **Linear Multi-Stage** design with user checkpoints ensures incremental control and reusability.\\n        - **Filter System**: Pattern-based matching (inclusion/exclusion) on fields like `item_generated_name` or `item_case_description`.\\n        - **File Organization**: Subfolder creation from `'/'` in `item_generated_name`, plus sanitization of invalid characters.\\n\\n        ---\\n\\n        ## 7. Directory Structure\\n\\n    ```\\n\\n    ---\\n\\n    #### `README_v09.md`\\n\\n    ```markdown\\n        ## Intent\\n        To automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\\n\\n        ## Key Project Aspects\\n\\n        ### Primary Problem\\n        Engineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck\\u2014it's the manual preparation process.\\n\\n        ### Solution Approach\\n        A Python-based utility that:\\n        1. Automatically scrapes document metadata from RigOffice\\n        2. Extracts file information from those documents\\n        3. Downloads and organizes selected files based on user criteria\\n\\n        ### Current State\\n        Functional working prototype that:\\n        - Uses a 3-step workflow (document metadata \\u2192 file metadata \\u2192 download)\\n        - Stores intermediate results in JSON format\\n        - Allows user intervention between steps\\n        - Provides progress feedback\\n\\n        ### Critical Next Steps\\n        1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches\\n        2. **Implement file hash checking** to prevent redundant downloads\\n        3. **Improve progress visibility** during lengthy scraping operations\\n\\n        ### Core Technical Pattern\\n        A single-file, modular approach using:\\n        - Selenium for browser automation\\n        - JSON for data storage\\n        - Three-stage processing with user control points\\n        - Incremental updates to avoid redundant work\\n\\n        ### Key Success Metrics\\n        - Reduce documentation gathering time by 75%+\\n        - Ensure reliable retrieval of required documentation\\n        - Organize files in a way that streamlines workflow\\n        - Support both broad searches (by rig number) and targeted searches (by specific documents)\\n    ```\\n\\n    ---\\n\\n    #### `README_v10.md`\\n\\n    ```markdown\\n        ### RigOfficeDownloader Utility Workflow\\n\\n        This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow is clearly defined in the docstring at the beginning of the main script and follows these steps:\\n\\n        1. Fetch Documents\\n        - The utility starts by scraping document metadata from predefined search URLs\\n        - Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory\\n        - Each document entry includes metadata like title, document number, revision, etc.\\n        - All documents are initially marked with item_include=False\\n        - Each document gets an item_generated_name for better identification\\n\\n        2. Export Documents to Markdown\\n        - The JSON data is exported to a Markdown table: <rig>-a-docs.md\\n        - This allows the user to easily review and edit which documents to include\\n        - The user is expected to edit the Markdown file and set item_include=true for desired documents\\n\\n        3. Import Updated Document Data\\n        - After the user edits the Markdown file, the utility imports the changes back to the JSON file\\n        - This updates which documents are marked for file retrieval\\n\\n        4. Fetch Files for Selected Documents\\n        - For each document with item_include=true, the utility scrapes file metadata\\n        - File data is saved to <rig>-b-files.json\\n        - Each file is initially marked with item_download=False\\n        - Files inherit the document's item_generated_name with additional identifiers\\n\\n        5. Export Files to Markdown\\n        - The file data is exported to a Markdown table: <rig>-b-files.md\\n        - The user reviews and edits which files to download by setting item_download=true\\n\\n        6. Import Updated File Data\\n        - After editing, the utility imports the changes back to the JSON file\\n        - This updates which files are marked for download\\n\\n        7. Download Selected Files\\n        - Files with item_download=true are downloaded\\n        - Files are named according to their item_generated_name + extension\\n        - The utility supports creating subfolders based on '/' in the item_generated_name\\n        - Files are saved to the outputs/downloads/<rig> directory\\n\\n        Interactive Menu\\n        - The utility provides an interactive menu where the user can choose which steps to execute\\n        - This allows for flexibility in the workflow, enabling the user to run specific steps as needed\\n        - The user can also update the rig number and search URLs through this menu\\n\\n        Key Features\\n        - Automatic document and file metadata scraping\\n        - User-friendly Markdown editing interface\\n        - Customizable file naming with item_generated_name\\n        - Support for subfolder organization in downloads\\n        - Deduplication of documents and files\\n        - Configurable field ordering for JSON and Markdown exports\\n\\n        Technical Implementation\\n        - Uses Selenium with Chrome WebDriver for web scraping\\n        - Implements smart waiting strategies for page loading\\n        - Handles browser sessions with proper cleanup\\n        - Provides progress feedback during operations\\n        - Sanitizes filenames for valid paths\\n    ```\\n\\n    ---\\n\\n    #### `README_v11.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## Overview\\n        RigOfficeDownloader is an automation tool designed to streamline the process of retrieving and downloading technical documentation from NOV's RigDoc system. It eliminates the tedious, time-consuming process of manual document retrieval, allowing engineers to focus on their primary work.\\n\\n        ## Key Features\\n        - **Three-Stage Workflow**: Documents \\u2192 Files \\u2192 Downloads\\n        - **Interactive Menu**: Choose which steps to execute\\n        - **User Control Points**: Review and select documents/files via Markdown interfaces\\n        - **Smart File Organization**: Subfolder support based on naming patterns\\n        - **Configurable Filters**: Apply filter chains to automatically select relevant documents\\n\\n        ## Workflow\\n        1. **Document Retrieval**: Scrapes document metadata from RigDoc\\n        2. **Document Selection**: Exports to Markdown for user review and selection\\n        3. **File Metadata**: Fetches file information for selected documents\\n        4. **File Selection**: Exports to Markdown for user review and selection\\n        5. **Download**: Downloads selected files with intelligent naming and organization\\n\\n        ## Setup\\n        1. Run `py_venv_init.bat` to create and initialize the Python virtual environment\\n        2. The script will:\\n           - Find available Python installations\\n           - Create a virtual environment\\n           - Install required packages from requirements.txt\\n\\n        ## Usage\\n        1. Run `RigOfficeDownloader-v4.bat` to start the application\\n        2. Use the interactive menu to:\\n           - Configure search parameters\\n           - Run specific workflow steps\\n           - Apply filter chains\\n           - Review and select documents/files\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Handles errors gracefully\\n        - Preserves document context and relationships\\n\\n        ## File Structure\\n        ```\\n        outputs/\\n        \\u251c\\u2500\\u2500 data/           # JSON and Markdown files for documents and files\\n        \\u2514\\u2500\\u2500 downloads/      # Downloaded files organized by rig number\\n        ```\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser (for Selenium automation)\\n    ```\\n\\n    ---\\n\\n    #### `README_v12.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\\n\\n        ```\\n        Documents -> Files -> Downloads\\n        ```\\n\\n        ## Overview\\n        RigOfficeDownloader eliminates the tedious, time-consuming process of manually retrieving technical documentation from NOV's RigDoc system. Engineers can focus on their primary work instead of navigating complex document repositories.\\n\\n        ## Key Features\\n        - Three-stage workflow with user control points\\n        - Interactive menu for flexible execution\\n        - Configurable filter chains for automatic selection\\n        - Smart file organization with subfolder support\\n        - Markdown interfaces for document/file review\\n\\n        ## Workflow\\n        1. **Document Retrieval**: Scrapes document metadata from RigDoc\\n        2. **Document Selection**: Exports to Markdown for user review and selection\\n        3. **File Metadata**: Fetches file information for selected documents\\n        4. **File Selection**: Exports to Markdown for user review and selection\\n        5. **Download**: Downloads selected files with intelligent naming and organization\\n\\n        ## Setup\\n        1. Run `py_venv_init.bat` to create and initialize the Python virtual environment\\n        2. Run `RigOfficeDownloader-v4.bat` to start the application\\n\\n        ## Usage\\n        Choose from the interactive menu:\\n        ```\\n        [0] Change search parameters\\n        [1] Configure filter chain\\n        [2] Fetch docs (scrape initial data)\\n        [3] Export docs (to Markdown for editing)\\n        [4] Import updated doc data\\n        [5] Fetch files (prepare files for download)\\n        [6] Export files (to Markdown for editing)\\n        [7] Import updated file data\\n        [8] Download files\\n        ```\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Handles errors gracefully\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser (for Selenium automation)\\n\\n        ## Version History\\n        - v1: Basic document retrieval and download\\n        - v2: JSON/Markdown conversion and user selection\\n        - v3: Improved error handling and field organization\\n        - v4: Subfolder support, filter chains, field ordering\\n    ```\\n\\n    ---\\n\\n    #### `README_v13.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        > **Automate** document retrieval from NOV\\u2019s RigDoc system, **minimize** repetitive tasks, and **accelerate** workflow.\\n\\n        ---\\n\\n        ## Overview\\n\\n        **RigOfficeDownloader** is a single-script Python utility that **scrapes**, **filters**, and **downloads** technical documentation from NOV\\u2019s RigDoc. Rather than wasting hours searching for and reviewing documents one by one, engineers can leverage an **interactive workflow** that guides them through each phase of document collection.\\n\\n        **Key Highlights**\\n        - **Three-Stage Workflow**: Documents \\u2192 Files \\u2192 Downloads\\n        - **Interactive Menu**: Run the entire workflow or individual steps on demand\\n        - **Markdown Edits**: Quickly select or deselect items by editing `.md` tables\\n        - **Subfolder Support**: Organize downloads with nested folder paths\\n        - **Filter Chains**: Automate selection based on glob-style patterns\\n        - **Single-File Simplicity**: All core logic in one script (~500 lines)\\n\\n        ---\\n\\n        ## The Problem\\n\\n        Engineering projects require diverse technical documents from RigDoc. This **manual** retrieval is:\\n        - **Tedious & Repetitive**: Browsing multiple pages, copying metadata, creating folders\\n        - **Error-Prone**: Risk of missing crucial docs or misplacing files\\n        - **Time Consuming**: Delays the start of high-value engineering tasks\\n\\n        ---\\n\\n        ## The Solution\\n\\n        RigOfficeDownloader streamlines the entire process through an **automated workflow**:\\n        1. **Fetch** document metadata (stores in `<rig>-a-docs.json`)\\n        2. **Export** docs to Markdown (`<rig>-a-docs.md`): mark `item_include=true`\\n        3. **Import** updated doc data back into JSON\\n        4. **Fetch** file metadata for included docs\\n        5. **Export** files to Markdown (`<rig>-b-files.md`): mark `item_download=true`\\n        6. **Import** updated file data\\n        7. **Download** files with subfolder paths derived from the generated names\\n\\n        ---\\n\\n        ## Workflow Steps in Detail\\n\\n        1. **Change / Configure**\\n           - Input your **rig number** and **search URLs**.\\n           - Set up or modify **filters** (e.g., auto-include `*DRILL*FLOOR*` docs, exclude `*VOID*` docs).\\n\\n        2. **Fetch Documents**\\n           - Scrape document metadata from RigDoc URLs.\\n           - Data saved to JSON (`<rig>-a-docs.json`), all set to `item_include=false`.\\n\\n        3. **Export & Review Docs**\\n           - Creates `<rig>-a-docs.md`.\\n           - Manually set `item_include=true` for relevant docs.\\n\\n        4. **Import Updated Docs**\\n           - Reads user changes back into JSON.\\n\\n        5. **Fetch File Metadata**\\n           - For each included doc, scrapes associated files (saved in `<rig>-b-files.json`).\\n           - `item_download=false` by default.\\n\\n        6. **Export & Review Files**\\n           - Creates `<rig>-b-files.md`.\\n           - Set `item_download=true` for desired files.\\n\\n        7. **Import Updated Files**\\n           - Sync file selections back into JSON.\\n\\n        8. **Download Files**\\n           - Retrieves selected files, applying `item_generated_name` for naming.\\n           - `'/'` in `item_generated_name` spawns subfolders in `outputs/downloads/<rig>`.\\n\\n        ---\\n\\n        ## Directory Structure\\n\\n    ```\\n\\n    ---\\n\\n    #### `README_v14.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n        *Automated Document Retrieval for NOV RigDoc*\\n\\n        ## Overview\\n        Eliminates tedious manual document retrieval from NOV's RigDoc system through intelligent automation. Preserves 75%+ engineering time by streamlining the 3D modeling preparation workflow.\\n\\n        ## Key Features\\n        ```\\n        \\u25fc Three-Stage Workflow  \\u25fc Interactive Menu  \\u25fc Human Checkpoints\\n        \\u25fc Metadata-Powered File Organization  \\u25fc Pattern-Based Filter Chains\\n        \\u25fc Markdown Review Interface  \\u25fc Smart Browser Automation\\n        ```\\n\\n        ## Workflow Architecture\\n        **1. Document Retrieval**\\n        ```python\\n        fetch_docs() \\u2192 <rig>-a-docs.json  # Scrape metadata\\n        json_to_md_table() \\u2192 <rig>-a-docs.md  # Edit item_include=true\\n        md_table_to_json()  # Commit selections\\n        ```\\n        **2. File Processing**\\n        ```python\\n        fetch_files() \\u2192 <rig>-b-files.json  # Get file metadata\\n        json_to_md_table() \\u2192 <rig>-b-files.md  # Set item_download=true\\n        md_table_to_json()  # Finalize choices\\n        ```\\n        **3. Smart Download**\\n        ```python\\n        download_files()  # Auto-organize with:\\n        \\u2022 /subfolders from item_generated_name\\n        \\u2022 Sanitized filenames\\n        \\u2022 Deduplication\\n        ```\\n\\n        ## Getting Started\\n        ```bash\\n        # 1. Initialize environment\\n        py_venv_init.bat\\n\\n        # 2. Launch utility\\n        RigOfficeDownloader-v4.bat [--auto|--interactive|--config]\\n        ```\\n        **First-Run Configuration**\\n        ```python\\n        CONFIG = {\\n            \\\"rig_number\\\": \\\"R0000.020\\\",\\n            \\\"search_urls\\\": PROJECTINFO_GAD,  # Prebuilt equipment templates\\n            \\\"filters\\\": [{\\n                \\\"type\\\": \\\"docs\\\",\\n                \\\"pattern\\\": \\\"*G000*\\\",\\n                \\\"field\\\": \\\"item_include\\\",\\n                \\\"value\\\": True\\n            }]\\n        }\\n        ```\\n\\n        ## File Organization\\n        ```\\n        outputs/\\n        \\u251c\\u2500\\u2500 data/               # Metadata control files\\n        \\u2502   \\u251c\\u2500\\u2500 R1234-a-docs.json\\n        \\u2502   \\u251c\\u2500\\u2500 R1234-a-docs.md\\n        \\u2502   \\u251c\\u2500\\u2500 R1234-b-files.json\\n        \\u2502   \\u2514\\u2500\\u2500 R1234-b-files.md\\n        \\u2514\\u2500\\u2500 downloads/          # Final organized files\\n            \\u2514\\u2500\\u2500 R1234/\\n                \\u251c\\u2500\\u2500 Drilling/\\n                \\u2502   \\u2514\\u2500\\u2500 G0001_REV03--CASE45-DrillFloor.pdf\\n                \\u2514\\u2500\\u2500 Topdrive/\\n                    \\u2514\\u2500\\u2500 ASM-00_REV12--CASE32-TopDriveAssembly.dwg\\n        ```\\n\\n        ## Interactive Control\\n        ```\\n        [0] Change rig number/search URLs    [4] Import doc selections\\n        [1] Configure filter chain           [5] Fetch file metadata\\n        [2] Fetch documents                  [6] Export files to Markdown\\n        [3] Export docs to Markdown          [7] Import file selections\\n        [8] Download files                   [9] Advanced configuration\\n        ```\\n\\n        ## Why This Tool?\\n        | Manual Process               | Automated Solution           |\\n        |------------------------------|-------------------------------|\\n        | 4-6 hours per project setup  | <1 hour with 75% automation  |\\n        | Error-prone file management  | Metadata-driven organization |\\n        | No version control           | JSON/Markdown audit trail    |\\n        | Context loss between teams   | Standardized naming schema   |\\n\\n    ```\",\n  \"a\": {\n    \"instruction\": \"# Instruction Converter\\n\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\n\\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n    \"input\": \"# CONTEXT\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\nRigOfficeDownloader is a Python-based automation tool designed to streamline and significantly reduce the time required for retrieving, reviewing, and organizing technical documentation from the NOV RigDoc system. Its primary workflow utilizes a sequential pipeline (Documents \\u2192 Files \\u2192 Downloads) and allows for either automated or menu-driven interactive processes. User selection checkpoints via editable Markdown tables, filter chains for inclusion/exclusion, and metadata-driven file organization ensure accuracy and efficiency. Outputs include traceable JSON and Markdown records. The utility integrates easily into existing Windows-based environments and offers substantial labor savings (75%+ reduction in manual prep time), scalability, and simplification of thorough auditability.\\n\\n# SOLUTION\\n\\ni've given the utility the temporary name `RigOfficeDownloader`, but this might change (as it feels lazy and non-proprietary). it's a utility that'll be used internally in my department (in a large engineering company) to automate (speed up the process) when working on new projects. Although the default mode of the utility is to rely on the predefined search templates and filters to automate the full process, it is also designed (and thoughtfully set up) such that it can either be used manually or interactively (e.g. if i just want to download a list of specific documents) while adhering to the *same* concepts. although this utility springs out from the need of a small subset of the company (just a small department within the company of <20 people) it's important to always keep in mind that since it'll *eventually* be used by other people (in other departmens within the company), ALWAYS ENSURE **inherent and fundamental cohesiveness**; because **simplicity and elegance** is __INTEGRAL__ - *\\\"Complexity is the enemy of execution\\\" - Optimized for engineers, by engineers*.\\n\\n## WORKFLOW\\n\\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).\\n\\n# REQUIREMENTS\\n\\ni want you to focus on the abstract and general concepts related to **functionality**, i don't want it to destroy the \\\"canvas\\\" (of `README.md`) with unusefull noise (unneccessary bloat).\\n\\n# OBJECTIVE\\n\\nConsolidate and extract the essential functional concepts from all provided README variations for the RigOfficeDownloader utility. Produce a finalized README.md under 100 lines that:\\n\\n- Defines the application's purpose: automating and streamlining document retrieval from the NOV RigDoc system, eliminating tedious manual effort, and enabling rapid, organized access to technical documentation.\\n- Clearly lists key features and workflow architecture:\\n  - Three-stage pipeline: Documents \\u2192 Files \\u2192 Downloads\\n  - Interactive or automated menu-driven operation\\n  - User checkpoints and Markdown review/edit interfaces for selection\\n  - Configurable pattern-based filter chains for auto-selection/inclusion/exclusion\\n  - Metadata-driven naming and subfolder file organization\\n  - Smart browser automation, error handling, deduplication, and field ordering\\n- Presents the sequential workflow steps as actionable bullet points or numbered steps:\\n  1. Configure rig number and search URLs (with filter setup)\\n  2. Fetch document metadata (JSON export)\\n  3. Export documents to Markdown for user editing (set item_include)\\n  4. Import edited selections (Markdown \\u2192 JSON)\\n  5. Fetch related file metadata for selected documents\\n  6. Export file list to Markdown for review (set item_download)\\n  7. Import updated file selections\\n  8. Download marked files, organized via smart naming/subfolders\\n- Summarize directory structure for outputs (data, downloads) and illustrate how subfolders reflect metadata in file names\\n- Specify setup/prerequisites concisely:\\n  - Python 3.11+, Windows OS, Chrome browser, valid RigDoc credentials, requirements.txt\\n- Describe available run modes: --auto (automated), --interactive (step-by-step), --config (adjust templates/filters)\\n- Emphasize benefits: reduction in document gathering time (75%+), consistent organization, robust error handling, extensibility for broader internal use, and auditability through JSON/Markdown traceability\\n- Maintain original intent, technical depth, and context integrity throughout while strictly omitting unnecessary bloat and self-references.\\n- Format the README as a single clear markdown file, using concise sections, technical terminology, and actionable language for internal engineering users.\\n\\n# VARIATIONS\\n\\n    # Dir `readme_variations`\\n\\n    ### File Structure\\n\\n    ```\\n    \\u251c\\u2500\\u2500 README_v01.md\\n    \\u251c\\u2500\\u2500 README_v02.md\\n    \\u251c\\u2500\\u2500 README_v03.md\\n    \\u251c\\u2500\\u2500 README_v04.md\\n    \\u251c\\u2500\\u2500 README_v05.md\\n    \\u251c\\u2500\\u2500 README_v06.md\\n    \\u251c\\u2500\\u2500 README_v07.md\\n    \\u251c\\u2500\\u2500 README_v08.md\\n    \\u251c\\u2500\\u2500 README_v09.md\\n    \\u251c\\u2500\\u2500 README_v10.md\\n    \\u251c\\u2500\\u2500 README_v11.md\\n    \\u251c\\u2500\\u2500 README_v12.md\\n    \\u251c\\u2500\\u2500 README_v13.md\\n    \\u2514\\u2500\\u2500 README_v14.md\\n    ```\\n\\n    ---\\n\\n    #### `README_v01.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## Overview\\n        RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\\n\\n        ## Key Features\\n        - Three-stage workflow: Documents -> Files -> Downloads\\n        - Interactive menu for flexible execution\\n        - User control points via Markdown interfaces\\n        - Smart file organization with subfolder support\\n        - Configurable filter chains\\n\\n        ## Setup & Usage\\n        1. Run `py_venv_init.bat` to create the Python environment\\n        2. Run `RigOfficeDownloader-v4.bat` to start the application\\n        3. Use the interactive menu to configure and execute workflow steps\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Preserves document context and relationships\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser (for Selenium automation)\\n    ```\\n\\n    ---\\n\\n    #### `README_v02.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## The Problem\\n        Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:\\n        - Tedious and repetitive\\n        - Error-prone\\n        - A poor use of skilled engineering time\\n\\n        ## The Solution\\n        RigOfficeDownloader automates the document retrieval process through a three-stage workflow:\\n        1. **Document Retrieval**: Automatically scrapes document metadata\\n        2. **File Metadata**: Fetches file information for selected documents\\n        3. **Smart Downloads**: Downloads files with intelligent naming and organization\\n\\n        ## How It Works\\n        - Uses Selenium to automate web interactions with RigDoc\\n        - Exports data to Markdown for user review and selection\\n        - Applies configurable filters to pre-select relevant documents\\n        - Organizes downloads with consistent naming patterns\\n\\n        ## Getting Started\\n        1. Run `py_venv_init.bat` to set up the environment\\n        2. Run `RigOfficeDownloader-v4.bat` to launch the application\\n        3. Follow the interactive menu prompts\\n\\n        ## Impact\\n        Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.\\n    ```\\n\\n    ---\\n\\n    #### `README_v03.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## Technical Overview\\n        RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\\n\\n        ## Architecture\\n        - **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown\\n        - **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads\\n        - **File Organization**: Hierarchical naming system with metadata embedding and subfolder support\\n\\n        ## Workflow Steps\\n        1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information\\n        2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing\\n        3. **Selection Import**: `md_table_to_json()` imports user selections\\n        4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents\\n        5. **File Selection**: Export/import cycle for user selection of files\\n        6. **Download Process**: `download_files()` retrieves selected files with smart naming\\n\\n        ## Filter Chain System\\n        Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.\\n\\n        ## Setup Instructions\\n        1. Run `py_venv_init.bat` to initialize the Python environment\\n        2. Run `RigOfficeDownloader-v4.bat` to execute the application\\n\\n        ## Version History\\n        - v1: Basic document retrieval and download\\n        - v2: JSON/Markdown conversion and user selection\\n        - v3: Improved error handling and field organization\\n        - v4: Subfolder support, filter chains, field ordering\\n    ```\\n\\n    ---\\n\\n    #### `README_v04.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n        > Automate document retrieval from NOV's RigDoc system\\n\\n        ## What This Tool Does\\n        RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\\n\\n        ## Quick Start Guide\\n        1. **Setup**: Run `py_venv_init.bat` to create the Python environment\\n        2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application\\n        3. **Configure**: Enter your rig number and search URLs when prompted\\n        4. **Run**: Follow the numbered menu to execute each step of the workflow\\n\\n        ## Workflow Steps Explained\\n        0. **Change Parameters**: Update rig number and search URLs\\n        1. **Configure Filters**: Set up automatic document/file selection rules\\n        2. **Fetch Documents**: Retrieve document metadata from RigDoc\\n        3. **Review Documents**: Edit the Markdown file to select which documents to process\\n        4. **Import Selections**: Load your document selections\\n        5. **Fetch Files**: Get file metadata for selected documents\\n        6. **Review Files**: Edit the Markdown file to select which files to download\\n        7. **Import File Selections**: Load your file selections\\n        8. **Download Files**: Retrieve the selected files\\n\\n        ## Tips for Success\\n        - Use filters to automatically pre-select relevant documents\\n        - Review the Markdown files carefully before proceeding to the next step\\n        - Files will be organized in subfolders based on '/' in their generated names\\n\\n        ## Need Help?\\n        Check the source code comments for detailed information about each function and workflow step.\\n    ```\\n\\n    ---\\n\\n    #### `README_v05.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\\n\\n        ```\\n        Documents -> Files -> Downloads\\n        ```\\n\\n        ## Features\\n        - Three-stage workflow with user control points\\n        - Interactive menu for flexible execution\\n        - Configurable filter chains for automatic selection\\n        - Smart file organization with subfolder support\\n        - Markdown interfaces for document/file review\\n\\n        ## Quick Start\\n        ```\\n        1. Run py_venv_init.bat\\n        2. Run RigOfficeDownloader-v4.bat\\n        3. Follow the interactive menu\\n        ```\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Handles errors gracefully\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser\\n    ```\\n\\n    ---\\n\\n    #### `README_v06.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.\\n\\n        ## Key Features\\n        - **Three-Stage Workflow**: Document selection \\u2192 File selection \\u2192 Download\\n        - **Metadata Preservation**: Structured naming with revision/case numbers\\n        - **Interactive Review**: Markdown tables for manual inclusion flags\\n        - **Smart Organization**: Automatic subfolder creation via naming patterns\\n        - **Filter System**: Pattern-based inclusion/exclusion rules\\n        - **Browser Automation**: Smart waiting strategies and session management\\n\\n        ## Workflow Process\\n        ```python\\n        1. Fetch Documents       # Scrape metadata \\u2192 <rig>-a-docs.json\\n        2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true\\n        3. Import Selections     # Update JSON with user choices\\n        4. Fetch Files           # Get file listings \\u2192 <rig>-b-files.json\\n        5. Export Files MD       # <rig>-b-files.md - Set item_download=true\\n        6. Import File Choices   # Update file selections\\n        7. Download Files        # Auto-organized with subfolder support\\n        ```\\n\\n        ## Configuration (CONFIG Section)\\n        ```python\\n        {\\n            \\\"rig_number\\\": \\\"R0000.020\\\",  # Target rig ID\\n            \\\"search_urls\\\": [            # Predefined search templates\\n                \\\"https://rigdoc.nov.com/search/rigsearch?q=...\\\",\\n                \\\"https://rigdoc.nov.com/search/rigsearch?q=...\\\"\\n            ],\\n            \\\"filters\\\": [                # Sequential processing rules\\n                {\\n                    \\\"type\\\": \\\"docs\\\",     # Apply to documents/files\\n                    \\\"pattern\\\": \\\"*DRILL*FLOOR*\\\",  # Glob-style matching\\n                    \\\"field\\\": \\\"item_include\\\",     # Field to modify\\n                    \\\"value\\\": True       # Set True/False based on match\\n                }\\n            ]\\n        }\\n        ```\\n\\n        ## Setup & Usage\\n        ```bash\\n        # Initialize environment\\n        py_venv_init.bat\\n        py_venv_pip_install.bat\\n\\n        # Run modes\\n        RigOfficeDownloader-v4.py [rig_number] [mode]\\n\\n        Modes:\\n        --auto         # Full automation\\n        --interactive  # Step-by-step control\\n        --config       # Modify search templates/filters\\n        ```\\n\\n        ## Key Configuration Patterns\\n        - **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)\\n        - **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)\\n        - **File Types**: Auto-prioritize PDFs with `*.pdf` pattern\\n\\n        ## Requirements\\n        - Chrome Browser + ChromeDriver\\n        - Active RigDoc credentials\\n        - Python 3.8+ with dependencies from requirements.txt\\n        - Network access to rigdoc.nov.com\\n\\n        ## Advanced Features\\n        - **Path Sanitization**: Auto-clean special chars while preserving /\\n        - **Deduplication**: Hash-based conflict resolution\\n        - **Field Ordering**: Customizable JSON/Markdown columns\\n        - **Smart Scrolling**: Progressive page loading detection\\n\\n        > Reduces documentation prep time by 60-75% compared to manual retrieval\\n        > Version 4.0 | Active development with subfolder support\\n    ```\\n\\n    ---\\n\\n    #### `README_v07.md`\\n\\n    ```markdown\\n\\n        ## Overview\\n        - Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\\n        - Use case: curate, review, and batch-download rig-related documents and technical files.\\n\\n        ## Directory Structure\\n\\n        * `outputs/` (BASE\\\\_OUTPUT): All results stored here\\n        * `outputs/data/` (DATA\\\\_DIR): Document and file metadata (JSON/MD)\\n        * `outputs/downloads/` (DL\\\\_DIR): Downloaded PDF and file outputs\\n\\n        ## Pipeline Overview\\n\\n        1. Change search parameters (rig number, URLs)\\n        2. Configure filter chain (add, edit, delete, toggle, reorder filters)\\n        3. Fetch docs (scrape data from rigdoc.nov.com)\\n        4. Export docs to Markdown (for selection/editing)\\n        5. Import docs from Markdown (sync edited selection)\\n        6. Fetch candidate files linked to selected docs\\n        7. Export file list to Markdown (for editing/selecting files for download)\\n        8. Import updated file list from Markdown\\n        9. Download marked files (PDFs only, via Chrome)\\n\\n        ## Manual Editing\\n\\n        * Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing\\n        * Set `item_include` (docs) and `item_download` (files) fields\\n\\n        ## Running the Tool\\n\\n        ```bash\\n        python rigdocscraper.py\\n        ```\\n\\n        * Interactive menu enables step selection (numbers/comma/space-separated)\\n        * Supports adjusting parameters, filter configuration, and reviewing batch steps\\n        * Prompts will guide through editing, import/export, and download procedures\\n\\n        ## Troubleshooting\\n\\n        * Requires functioning Chrome installation; verify webdriver-manager compatibility\\n        * Common issues: browser launch failures, login/captcha requirements, file permissions\\n        * Output logs and warnings shown in terminal; inspect `outputs/data/` for progress\\n    ```\\n\\n    ---\\n\\n    #### `README_v08.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        > **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.\\n\\n        ---\\n\\n        ## 1. Overview\\n\\n        RigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents \\u2192 Files \\u2192 Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.\\n\\n        ---\\n\\n        ## 2. The Problem\\n\\n        - Engineers lose valuable hours **manually searching** and **downloading** technical documentation.\\n        - The repetitive process is **error-prone** and distracts from real engineering work.\\n\\n        ---\\n\\n        ## 3. The Solution\\n\\n        - **Automates** the document search, scraping, and download phases, cutting the time required by **75%+**.\\n        - Offers **three main stages**\\u2014document metadata retrieval, file metadata retrieval, and file downloads\\u2014each controlled by user-edited Markdown tables for fine-tuned selection.\\n\\n        ---\\n\\n        ## 4. Key Features\\n\\n        - **Three-Stage Workflow**\\n          1. **Documents**: Gather doc metadata, mark `item_include` in Markdown.\\n          2. **Files**: Fetch file info for included docs, mark `item_download` in Markdown.\\n          3. **Downloads**: Organize files with subfolder support based on `'/'` in `item_generated_name`.\\n\\n        - **Interactive Menu**: Allows step-by-step or fully automated runs.\\n        - **User Review via Markdown**: You decide which items to include or skip by editing `.md` tables.\\n        - **Configurable Filter Chains**: Glob-style pattern matching to auto-select or exclude docs/files.\\n        - **Smart Organization**: Hierarchical naming that embeds rig/drawing metadata, plus subfolder creation.\\n        - **Error Handling**: Graceful session management, robust page-scrolling logic, and deduplication.\\n\\n        ---\\n\\n        ## 5. Workflow Steps\\n\\n        1. **Change Parameters**: (Optional) Update rig number, search URLs.\\n        2. **Configure Filters**: Setup or edit filter rules to auto-include/exclude docs/files.\\n        3. **Fetch Docs**: Scrape document metadata into `<rig>-a-docs.json` (all `item_include=false` initially).\\n        4. **Export Docs**: Generate `<rig>-a-docs.md`; manually set `item_include=true` for desired items.\\n        5. **Import Updated Docs**: Sync changes back from Markdown to JSON.\\n        6. **Fetch Files**: Retrieve file metadata for those docs, saved as `<rig>-b-files.json` (`item_download=false`).\\n        7. **Export Files**: Create `<rig>-b-files.md`; set `item_download=true` for target files.\\n        8. **Import Updated Files**: Sync file selection from Markdown to JSON.\\n        9. **Download Files**: Acquire and organize all selected files under `outputs/downloads/<rig>`.\\n\\n        ---\\n\\n        ## 6. Architecture & Technology\\n\\n        - **Core Stack**:\\n          - **Python 3.6+**\\n          - **Selenium** for browser automation\\n          - **BeautifulSoup** for HTML parsing\\n          - **JSON/Markdown** for data storage and user edits\\n        - **Linear Multi-Stage** design with user checkpoints ensures incremental control and reusability.\\n        - **Filter System**: Pattern-based matching (inclusion/exclusion) on fields like `item_generated_name` or `item_case_description`.\\n        - **File Organization**: Subfolder creation from `'/'` in `item_generated_name`, plus sanitization of invalid characters.\\n\\n        ---\\n\\n        ## 7. Directory Structure\\n\\n    ```\\n\\n    ---\\n\\n    #### `README_v09.md`\\n\\n    ```markdown\\n        ## Intent\\n        To automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\\n\\n        ## Key Project Aspects\\n\\n        ### Primary Problem\\n        Engineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck\\u2014it's the manual preparation process.\\n\\n        ### Solution Approach\\n        A Python-based utility that:\\n        1. Automatically scrapes document metadata from RigOffice\\n        2. Extracts file information from those documents\\n        3. Downloads and organizes selected files based on user criteria\\n\\n        ### Current State\\n        Functional working prototype that:\\n        - Uses a 3-step workflow (document metadata \\u2192 file metadata \\u2192 download)\\n        - Stores intermediate results in JSON format\\n        - Allows user intervention between steps\\n        - Provides progress feedback\\n\\n        ### Critical Next Steps\\n        1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches\\n        2. **Implement file hash checking** to prevent redundant downloads\\n        3. **Improve progress visibility** during lengthy scraping operations\\n\\n        ### Core Technical Pattern\\n        A single-file, modular approach using:\\n        - Selenium for browser automation\\n        - JSON for data storage\\n        - Three-stage processing with user control points\\n        - Incremental updates to avoid redundant work\\n\\n        ### Key Success Metrics\\n        - Reduce documentation gathering time by 75%+\\n        - Ensure reliable retrieval of required documentation\\n        - Organize files in a way that streamlines workflow\\n        - Support both broad searches (by rig number) and targeted searches (by specific documents)\\n    ```\\n\\n    ---\\n\\n    #### `README_v10.md`\\n\\n    ```markdown\\n        ### RigOfficeDownloader Utility Workflow\\n\\n        This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow is clearly defined in the docstring at the beginning of the main script and follows these steps:\\n\\n        1. Fetch Documents\\n        - The utility starts by scraping document metadata from predefined search URLs\\n        - Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory\\n        - Each document entry includes metadata like title, document number, revision, etc.\\n        - All documents are initially marked with item_include=False\\n        - Each document gets an item_generated_name for better identification\\n\\n        2. Export Documents to Markdown\\n        - The JSON data is exported to a Markdown table: <rig>-a-docs.md\\n        - This allows the user to easily review and edit which documents to include\\n        - The user is expected to edit the Markdown file and set item_include=true for desired documents\\n\\n        3. Import Updated Document Data\\n        - After the user edits the Markdown file, the utility imports the changes back to the JSON file\\n        - This updates which documents are marked for file retrieval\\n\\n        4. Fetch Files for Selected Documents\\n        - For each document with item_include=true, the utility scrapes file metadata\\n        - File data is saved to <rig>-b-files.json\\n        - Each file is initially marked with item_download=False\\n        - Files inherit the document's item_generated_name with additional identifiers\\n\\n        5. Export Files to Markdown\\n        - The file data is exported to a Markdown table: <rig>-b-files.md\\n        - The user reviews and edits which files to download by setting item_download=true\\n\\n        6. Import Updated File Data\\n        - After editing, the utility imports the changes back to the JSON file\\n        - This updates which files are marked for download\\n\\n        7. Download Selected Files\\n        - Files with item_download=true are downloaded\\n        - Files are named according to their item_generated_name + extension\\n        - The utility supports creating subfolders based on '/' in the item_generated_name\\n        - Files are saved to the outputs/downloads/<rig> directory\\n\\n        Interactive Menu\\n        - The utility provides an interactive menu where the user can choose which steps to execute\\n        - This allows for flexibility in the workflow, enabling the user to run specific steps as needed\\n        - The user can also update the rig number and search URLs through this menu\\n\\n        Key Features\\n        - Automatic document and file metadata scraping\\n        - User-friendly Markdown editing interface\\n        - Customizable file naming with item_generated_name\\n        - Support for subfolder organization in downloads\\n        - Deduplication of documents and files\\n        - Configurable field ordering for JSON and Markdown exports\\n\\n        Technical Implementation\\n        - Uses Selenium with Chrome WebDriver for web scraping\\n        - Implements smart waiting strategies for page loading\\n        - Handles browser sessions with proper cleanup\\n        - Provides progress feedback during operations\\n        - Sanitizes filenames for valid paths\\n    ```\\n\\n    ---\\n\\n    #### `README_v11.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## Overview\\n        RigOfficeDownloader is an automation tool designed to streamline the process of retrieving and downloading technical documentation from NOV's RigDoc system. It eliminates the tedious, time-consuming process of manual document retrieval, allowing engineers to focus on their primary work.\\n\\n        ## Key Features\\n        - **Three-Stage Workflow**: Documents \\u2192 Files \\u2192 Downloads\\n        - **Interactive Menu**: Choose which steps to execute\\n        - **User Control Points**: Review and select documents/files via Markdown interfaces\\n        - **Smart File Organization**: Subfolder support based on naming patterns\\n        - **Configurable Filters**: Apply filter chains to automatically select relevant documents\\n\\n        ## Workflow\\n        1. **Document Retrieval**: Scrapes document metadata from RigDoc\\n        2. **Document Selection**: Exports to Markdown for user review and selection\\n        3. **File Metadata**: Fetches file information for selected documents\\n        4. **File Selection**: Exports to Markdown for user review and selection\\n        5. **Download**: Downloads selected files with intelligent naming and organization\\n\\n        ## Setup\\n        1. Run `py_venv_init.bat` to create and initialize the Python virtual environment\\n        2. The script will:\\n           - Find available Python installations\\n           - Create a virtual environment\\n           - Install required packages from requirements.txt\\n\\n        ## Usage\\n        1. Run `RigOfficeDownloader-v4.bat` to start the application\\n        2. Use the interactive menu to:\\n           - Configure search parameters\\n           - Run specific workflow steps\\n           - Apply filter chains\\n           - Review and select documents/files\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Handles errors gracefully\\n        - Preserves document context and relationships\\n\\n        ## File Structure\\n        ```\\n        outputs/\\n        \\u251c\\u2500\\u2500 data/           # JSON and Markdown files for documents and files\\n        \\u2514\\u2500\\u2500 downloads/      # Downloaded files organized by rig number\\n        ```\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser (for Selenium automation)\\n    ```\\n\\n    ---\\n\\n    #### `README_v12.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\\n\\n        ```\\n        Documents -> Files -> Downloads\\n        ```\\n\\n        ## Overview\\n        RigOfficeDownloader eliminates the tedious, time-consuming process of manually retrieving technical documentation from NOV's RigDoc system. Engineers can focus on their primary work instead of navigating complex document repositories.\\n\\n        ## Key Features\\n        - Three-stage workflow with user control points\\n        - Interactive menu for flexible execution\\n        - Configurable filter chains for automatic selection\\n        - Smart file organization with subfolder support\\n        - Markdown interfaces for document/file review\\n\\n        ## Workflow\\n        1. **Document Retrieval**: Scrapes document metadata from RigDoc\\n        2. **Document Selection**: Exports to Markdown for user review and selection\\n        3. **File Metadata**: Fetches file information for selected documents\\n        4. **File Selection**: Exports to Markdown for user review and selection\\n        5. **Download**: Downloads selected files with intelligent naming and organization\\n\\n        ## Setup\\n        1. Run `py_venv_init.bat` to create and initialize the Python virtual environment\\n        2. Run `RigOfficeDownloader-v4.bat` to start the application\\n\\n        ## Usage\\n        Choose from the interactive menu:\\n        ```\\n        [0] Change search parameters\\n        [1] Configure filter chain\\n        [2] Fetch docs (scrape initial data)\\n        [3] Export docs (to Markdown for editing)\\n        [4] Import updated doc data\\n        [5] Fetch files (prepare files for download)\\n        [6] Export files (to Markdown for editing)\\n        [7] Import updated file data\\n        [8] Download files\\n        ```\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Handles errors gracefully\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser (for Selenium automation)\\n\\n        ## Version History\\n        - v1: Basic document retrieval and download\\n        - v2: JSON/Markdown conversion and user selection\\n        - v3: Improved error handling and field organization\\n        - v4: Subfolder support, filter chains, field ordering\\n    ```\\n\\n    ---\\n\\n    #### `README_v13.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        > **Automate** document retrieval from NOV\\u2019s RigDoc system, **minimize** repetitive tasks, and **accelerate** workflow.\\n\\n        ---\\n\\n        ## Overview\\n\\n        **RigOfficeDownloader** is a single-script Python utility that **scrapes**, **filters**, and **downloads** technical documentation from NOV\\u2019s RigDoc. Rather than wasting hours searching for and reviewing documents one by one, engineers can leverage an **interactive workflow** that guides them through each phase of document collection.\\n\\n        **Key Highlights**\\n        - **Three-Stage Workflow**: Documents \\u2192 Files \\u2192 Downloads\\n        - **Interactive Menu**: Run the entire workflow or individual steps on demand\\n        - **Markdown Edits**: Quickly select or deselect items by editing `.md` tables\\n        - **Subfolder Support**: Organize downloads with nested folder paths\\n        - **Filter Chains**: Automate selection based on glob-style patterns\\n        - **Single-File Simplicity**: All core logic in one script (~500 lines)\\n\\n        ---\\n\\n        ## The Problem\\n\\n        Engineering projects require diverse technical documents from RigDoc. This **manual** retrieval is:\\n        - **Tedious & Repetitive**: Browsing multiple pages, copying metadata, creating folders\\n        - **Error-Prone**: Risk of missing crucial docs or misplacing files\\n        - **Time Consuming**: Delays the start of high-value engineering tasks\\n\\n        ---\\n\\n        ## The Solution\\n\\n        RigOfficeDownloader streamlines the entire process through an **automated workflow**:\\n        1. **Fetch** document metadata (stores in `<rig>-a-docs.json`)\\n        2. **Export** docs to Markdown (`<rig>-a-docs.md`): mark `item_include=true`\\n        3. **Import** updated doc data back into JSON\\n        4. **Fetch** file metadata for included docs\\n        5. **Export** files to Markdown (`<rig>-b-files.md`): mark `item_download=true`\\n        6. **Import** updated file data\\n        7. **Download** files with subfolder paths derived from the generated names\\n\\n        ---\\n\\n        ## Workflow Steps in Detail\\n\\n        1. **Change / Configure**\\n           - Input your **rig number** and **search URLs**.\\n           - Set up or modify **filters** (e.g., auto-include `*DRILL*FLOOR*` docs, exclude `*VOID*` docs).\\n\\n        2. **Fetch Documents**\\n           - Scrape document metadata from RigDoc URLs.\\n           - Data saved to JSON (`<rig>-a-docs.json`), all set to `item_include=false`.\\n\\n        3. **Export & Review Docs**\\n           - Creates `<rig>-a-docs.md`.\\n           - Manually set `item_include=true` for relevant docs.\\n\\n        4. **Import Updated Docs**\\n           - Reads user changes back into JSON.\\n\\n        5. **Fetch File Metadata**\\n           - For each included doc, scrapes associated files (saved in `<rig>-b-files.json`).\\n           - `item_download=false` by default.\\n\\n        6. **Export & Review Files**\\n           - Creates `<rig>-b-files.md`.\\n           - Set `item_download=true` for desired files.\\n\\n        7. **Import Updated Files**\\n           - Sync file selections back into JSON.\\n\\n        8. **Download Files**\\n           - Retrieves selected files, applying `item_generated_name` for naming.\\n           - `'/'` in `item_generated_name` spawns subfolders in `outputs/downloads/<rig>`.\\n\\n        ---\\n\\n        ## Directory Structure\\n\\n    ```\\n\\n    ---\\n\\n    #### `README_v14.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n        *Automated Document Retrieval for NOV RigDoc*\\n\\n        ## Overview\\n        Eliminates tedious manual document retrieval from NOV's RigDoc system through intelligent automation. Preserves 75%+ engineering time by streamlining the 3D modeling preparation workflow.\\n\\n        ## Key Features\\n        ```\\n        \\u25fc Three-Stage Workflow  \\u25fc Interactive Menu  \\u25fc Human Checkpoints\\n        \\u25fc Metadata-Powered File Organization  \\u25fc Pattern-Based Filter Chains\\n        \\u25fc Markdown Review Interface  \\u25fc Smart Browser Automation\\n        ```\\n\\n        ## Workflow Architecture\\n        **1. Document Retrieval**\\n        ```python\\n        fetch_docs() \\u2192 <rig>-a-docs.json  # Scrape metadata\\n        json_to_md_table() \\u2192 <rig>-a-docs.md  # Edit item_include=true\\n        md_table_to_json()  # Commit selections\\n        ```\\n        **2. File Processing**\\n        ```python\\n        fetch_files() \\u2192 <rig>-b-files.json  # Get file metadata\\n        json_to_md_table() \\u2192 <rig>-b-files.md  # Set item_download=true\\n        md_table_to_json()  # Finalize choices\\n        ```\\n        **3. Smart Download**\\n        ```python\\n        download_files()  # Auto-organize with:\\n        \\u2022 /subfolders from item_generated_name\\n        \\u2022 Sanitized filenames\\n        \\u2022 Deduplication\\n        ```\\n\\n        ## Getting Started\\n        ```bash\\n        # 1. Initialize environment\\n        py_venv_init.bat\\n\\n        # 2. Launch utility\\n        RigOfficeDownloader-v4.bat [--auto|--interactive|--config]\\n        ```\\n        **First-Run Configuration**\\n        ```python\\n        CONFIG = {\\n            \\\"rig_number\\\": \\\"R0000.020\\\",\\n            \\\"search_urls\\\": PROJECTINFO_GAD,  # Prebuilt equipment templates\\n            \\\"filters\\\": [{\\n                \\\"type\\\": \\\"docs\\\",\\n                \\\"pattern\\\": \\\"*G000*\\\",\\n                \\\"field\\\": \\\"item_include\\\",\\n                \\\"value\\\": True\\n            }]\\n        }\\n        ```\\n\\n        ## File Organization\\n        ```\\n        outputs/\\n        \\u251c\\u2500\\u2500 data/               # Metadata control files\\n        \\u2502   \\u251c\\u2500\\u2500 R1234-a-docs.json\\n        \\u2502   \\u251c\\u2500\\u2500 R1234-a-docs.md\\n        \\u2502   \\u251c\\u2500\\u2500 R1234-b-files.json\\n        \\u2502   \\u2514\\u2500\\u2500 R1234-b-files.md\\n        \\u2514\\u2500\\u2500 downloads/          # Final organized files\\n            \\u2514\\u2500\\u2500 R1234/\\n                \\u251c\\u2500\\u2500 Drilling/\\n                \\u2502   \\u2514\\u2500\\u2500 G0001_REV03--CASE45-DrillFloor.pdf\\n                \\u2514\\u2500\\u2500 Topdrive/\\n                    \\u2514\\u2500\\u2500 ASM-00_REV12--CASE32-TopDriveAssembly.dwg\\n        ```\\n\\n        ## Interactive Control\\n        ```\\n        [0] Change rig number/search URLs    [4] Import doc selections\\n        [1] Configure filter chain           [5] Fetch file metadata\\n        [2] Fetch documents                  [6] Export files to Markdown\\n        [3] Export docs to Markdown          [7] Import file selections\\n        [8] Download files                   [9] Advanced configuration\\n        ```\\n\\n        ## Why This Tool?\\n        | Manual Process               | Automated Solution           |\\n        |------------------------------|-------------------------------|\\n        | 4-6 hours per project setup  | <1 hour with 75% automation  |\\n        | Error-prone file management  | Metadata-driven organization |\\n        | No version control           | JSON/Markdown audit trail    |\\n        | Context loss between teams   | Standardized naming schema   |\\n\\n    ```\",\n    \"output\": \"{\\n  \\\"instruction_format\\\": \\\"# RigOfficeDownloader\\\\n\\\\nAutomate and streamline technical document retrieval from NOV RigDoc, eliminating tedious manual effort and enabling rapid, organized access to engineering documentation.\\\\n\\\\n## Purpose\\\\nAutomate document retrieval from NOV RigDoc, drastically reducing manual prep time, ensuring reliable, organized, and auditable project documentation access for engineering workflows.\\\\n\\\\n## Key Features\\\\n- Three-stage sequential pipeline: Documents \\u2192 Files \\u2192 Downloads\\\\n- Toggle between fully automated and interactive (menu-driven) operation\\\\n- Human checkpoints and user review via editable Markdown tables for both document and file selection\\\\n- Configurable pattern-based filter chains for auto-inclusion/exclusion of items\\\\n- Metadata-driven file naming, with subfolder/file organization based on metadata fields (e.g., rig number, doc type)\\\\n- Robust browser automation (Selenium) with smart waiting, session management, deduplication, and error handling\\\\n- Strict field ordering in JSON and Markdown outputs\\\\n- Comprehensive traceability and auditability via exported JSON and Markdown records\\\\n\\\\n## Workflow: Step-by-Step Process\\\\n1. Configure rig number and search URLs (define or select search templates and filters)\\\\n2. Fetch document metadata from RigDoc and export to `<rig>-a-docs.json`\\\\n3. Export document list to Markdown (`<rig>-a-docs.md`) for manual review/edit (set `item_include=true` as needed)\\\\n4. Import updated selections from Markdown back to JSON, reflecting included docs\\\\n5. Fetch file metadata for selected documents and store in `<rig>-b-files.json`\\\\n6. Export file list to Markdown (`<rig>-b-files.md`) for review (set `item_download=true` on desired files)\\\\n7. Import updated file selections from Markdown, updating which files will be downloaded\\\\n8. Download all marked files to `outputs/downloads/<rig>`, auto-organized via generated names/subfolder paths derived from metadata\\\\n\\\\n## Directory Structure\\\\n```\\\\noutputs/\\\\n\\u251c\\u2500\\u2500 data/           # All metadata/selection states (JSON + Markdown)\\\\n\\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.json\\\\n\\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.md\\\\n\\u2502   \\u251c\\u2500\\u2500 <rig>-b-files.json\\\\n\\u2502   \\u2514\\u2500\\u2500 <rig>-b-files.md\\\\n\\u2514\\u2500\\u2500 downloads/      # All downloaded documents, hierarchically organized\\\\n    \\u2514\\u2500\\u2500 <rig>/\\\\n        \\u251c\\u2500\\u2500 <subfolder>/\\\\n        \\u2502   \\u2514\\u2500\\u2500 <item_generated_name>.<ext>\\\\n        \\u2514\\u2500\\u2500 ...\\\\n```\\\\nFile and folder naming reflect document metadata and support subfolders using '/' within the `item_generated_name` field.\\\\n\\\\n## Setup / Prerequisites\\\\n- Windows OS\\\\n- Python 3.11+ (installer provided or specify in requirements.txt)\\\\n- Chrome browser (with valid Chromedriver for Selenium automation)\\\\n- Valid NOV RigDoc credentials\\\\n- Install Python dependencies: `pip install -r requirements.txt`\\\\n\\\\n## Execution / Run Modes\\\\n- Run interactively or fully automated via batch or script command:\\\\n    - `RigOfficeDownloader-v4.bat --auto` \\u2013 full pipeline, minimal user intervention\\\\n    - `RigOfficeDownloader-v4.bat --interactive` \\u2013 menu-driven, step-by-step workflow\\\\n    - `RigOfficeDownloader-v4.bat --config` \\u2013 edit search templates, update filters\\\\n\\\\n## Formulate Filters & Configuration\\\\n- Edit filter chains to define auto-inclusion/exclusion (glob-style patterns, e.g. `*G000*`, `*VOID*`)\\\\n- Adjust which document fields control item selection (such as by rig, system, doc type, status)\\\\n- Update configuration to specify rig number and/or template search URLs\\\\n\\\\n## Benefits & Advantages\\\\n- Achieve a 75%+ reduction in time spent on document gathering for engineering projects\\\\n- Allow engineers to focus on technical work, not mundane document management\\\\n- Maintain consistently structured output directories with audit trails (JSON + Markdown)\\\\n- Ensure robust error handling, smart deduplication, and correct organization of files\\\\n- Provide a solution that is scalable, cohesive, and ready for broader internal adoption\\\\n\\\\n## Summary Table of Features\\\\n| Capability             | Manual Process         | Automated with RigOfficeDownloader       |\\\\n|----------------------- |---------------------- |-----------------------------------------|\\\\n| Time per project setup | 4\\u20136 hours             | <1 hour                                 |\\\\n| File organization      | Error-prone, ad hoc   | Metadata-driven, standardized           |\\\\n| User review            | Tedious, repetitive   | Centralized via Markdown checkpoints    |\\\\n| Auditability           | Weak/none             | Full JSON/Markdown traceability         |\\\\n| Expandability          | N/A                   | Configurable, reusable templates        |\\\\n\\\\n## Version/Extensibility\\\\n- Built-in support for subfolders, filter chains, field ordering, advanced configuration\\\\n- Easily extensible for new departments and additional filtering/organizational patterns\\\\n\\\\n> \\\\\\\"Complexity is the enemy of execution \\u2013 Optimized for engineers, by engineers.\\\\\\\"\\\"\\n}\"\n  },\n  \"b\": {\n    \"instruction\": \"# Essence Distillation\\n\\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\\n\\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\",\n    \"input\": \"{\\n  \\\"instruction_format\\\": \\\"# RigOfficeDownloader\\\\n\\\\nAutomate and streamline technical document retrieval from NOV RigDoc, eliminating tedious manual effort and enabling rapid, organized access to engineering documentation.\\\\n\\\\n## Purpose\\\\nAutomate document retrieval from NOV RigDoc, drastically reducing manual prep time, ensuring reliable, organized, and auditable project documentation access for engineering workflows.\\\\n\\\\n## Key Features\\\\n- Three-stage sequential pipeline: Documents \\u2192 Files \\u2192 Downloads\\\\n- Toggle between fully automated and interactive (menu-driven) operation\\\\n- Human checkpoints and user review via editable Markdown tables for both document and file selection\\\\n- Configurable pattern-based filter chains for auto-inclusion/exclusion of items\\\\n- Metadata-driven file naming, with subfolder/file organization based on metadata fields (e.g., rig number, doc type)\\\\n- Robust browser automation (Selenium) with smart waiting, session management, deduplication, and error handling\\\\n- Strict field ordering in JSON and Markdown outputs\\\\n- Comprehensive traceability and auditability via exported JSON and Markdown records\\\\n\\\\n## Workflow: Step-by-Step Process\\\\n1. Configure rig number and search URLs (define or select search templates and filters)\\\\n2. Fetch document metadata from RigDoc and export to `<rig>-a-docs.json`\\\\n3. Export document list to Markdown (`<rig>-a-docs.md`) for manual review/edit (set `item_include=true` as needed)\\\\n4. Import updated selections from Markdown back to JSON, reflecting included docs\\\\n5. Fetch file metadata for selected documents and store in `<rig>-b-files.json`\\\\n6. Export file list to Markdown (`<rig>-b-files.md`) for review (set `item_download=true` on desired files)\\\\n7. Import updated file selections from Markdown, updating which files will be downloaded\\\\n8. Download all marked files to `outputs/downloads/<rig>`, auto-organized via generated names/subfolder paths derived from metadata\\\\n\\\\n## Directory Structure\\\\n```\\\\noutputs/\\\\n\\u251c\\u2500\\u2500 data/           # All metadata/selection states (JSON + Markdown)\\\\n\\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.json\\\\n\\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.md\\\\n\\u2502   \\u251c\\u2500\\u2500 <rig>-b-files.json\\\\n\\u2502   \\u2514\\u2500\\u2500 <rig>-b-files.md\\\\n\\u2514\\u2500\\u2500 downloads/      # All downloaded documents, hierarchically organized\\\\n    \\u2514\\u2500\\u2500 <rig>/\\\\n        \\u251c\\u2500\\u2500 <subfolder>/\\\\n        \\u2502   \\u2514\\u2500\\u2500 <item_generated_name>.<ext>\\\\n        \\u2514\\u2500\\u2500 ...\\\\n```\\\\nFile and folder naming reflect document metadata and support subfolders using '/' within the `item_generated_name` field.\\\\n\\\\n## Setup / Prerequisites\\\\n- Windows OS\\\\n- Python 3.11+ (installer provided or specify in requirements.txt)\\\\n- Chrome browser (with valid Chromedriver for Selenium automation)\\\\n- Valid NOV RigDoc credentials\\\\n- Install Python dependencies: `pip install -r requirements.txt`\\\\n\\\\n## Execution / Run Modes\\\\n- Run interactively or fully automated via batch or script command:\\\\n    - `RigOfficeDownloader-v4.bat --auto` \\u2013 full pipeline, minimal user intervention\\\\n    - `RigOfficeDownloader-v4.bat --interactive` \\u2013 menu-driven, step-by-step workflow\\\\n    - `RigOfficeDownloader-v4.bat --config` \\u2013 edit search templates, update filters\\\\n\\\\n## Formulate Filters & Configuration\\\\n- Edit filter chains to define auto-inclusion/exclusion (glob-style patterns, e.g. `*G000*`, `*VOID*`)\\\\n- Adjust which document fields control item selection (such as by rig, system, doc type, status)\\\\n- Update configuration to specify rig number and/or template search URLs\\\\n\\\\n## Benefits & Advantages\\\\n- Achieve a 75%+ reduction in time spent on document gathering for engineering projects\\\\n- Allow engineers to focus on technical work, not mundane document management\\\\n- Maintain consistently structured output directories with audit trails (JSON + Markdown)\\\\n- Ensure robust error handling, smart deduplication, and correct organization of files\\\\n- Provide a solution that is scalable, cohesive, and ready for broader internal adoption\\\\n\\\\n## Summary Table of Features\\\\n| Capability             | Manual Process         | Automated with RigOfficeDownloader       |\\\\n|----------------------- |---------------------- |-----------------------------------------|\\\\n| Time per project setup | 4\\u20136 hours             | <1 hour                                 |\\\\n| File organization      | Error-prone, ad hoc   | Metadata-driven, standardized           |\\\\n| User review            | Tedious, repetitive   | Centralized via Markdown checkpoints    |\\\\n| Auditability           | Weak/none             | Full JSON/Markdown traceability         |\\\\n| Expandability          | N/A                   | Configurable, reusable templates        |\\\\n\\\\n## Version/Extensibility\\\\n- Built-in support for subfolders, filter chains, field ordering, advanced configuration\\\\n- Easily extensible for new departments and additional filtering/organizational patterns\\\\n\\\\n> \\\\\\\"Complexity is the enemy of execution \\u2013 Optimized for engineers, by engineers.\\\\\\\"\\\"\\n}\",\n    \"output\": \"{\\n  \\\"distilled_essence\\\": {\\n    \\\"README.md\\\": {\\n      \\\"title\\\": \\\"RigOfficeDownloader\\\",\\n      \\\"purpose\\\": \\\"Automate and streamline technical document retrieval from NOV RigDoc, eliminating tedious manual effort and ensuring rapid, organized access to engineering documentation.\\\",\\n      \\\"features\\\": [\\n        \\\"Three-stage sequential pipeline: Documents \\u2192 Files \\u2192 Downloads\\\",\\n        \\\"Automated and interactive (menu-driven) run modes\\\",\\n        \\\"Editable Markdown checkpoints for document/file selection\\\",\\n        \\\"Configurable pattern-based filter chains (auto-inclusion/exclusion)\\\",\\n        \\\"Metadata-driven file naming and hierarchical subfolder organization\\\",\\n        \\\"Robust Selenium-based browser automation, smart waiting, deduplication, error handling\\\",\\n        \\\"Strict field ordering in JSON/Markdown outputs\\\",\\n        \\\"Full traceability/auditability via JSON and Markdown records\\\"\\n      ],\\n      \\\"workflow\\\": [\\n        \\\"1. Configure rig number and search URLs (set filters/templates)\\\",\\n        \\\"2. Fetch document metadata from RigDoc to <rig>-a-docs.json\\\",\\n        \\\"3. Export docs to Markdown (<rig>-a-docs.md); review/edit (set item_include)\\\",\\n        \\\"4. Import updated selections from Markdown \\u2192 JSON\\\",\\n        \\\"5. Fetch file metadata for selected docs \\u2192 <rig>-b-files.json\\\",\\n        \\\"6. Export files to Markdown (<rig>-b-files.md); set item_download as needed\\\",\\n        \\\"7. Import updated file selections from Markdown\\\",\\n        \\\"8. Download all marked files to outputs/downloads/<rig>, auto-organized via generated subfolder paths\\\"\\n      ],\\n      \\\"directory_structure\\\": [\\n        \\\"outputs/\\\",\\n        \\\"\\u251c\\u2500\\u2500 data/         # All metadata/selection (JSON + Markdown)\\\",\\n        \\\"\\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.json\\\",\\n        \\\"\\u2502   \\u251c\\u2500\\u2500 <rig>-a-docs.md\\\",\\n        \\\"\\u2502   \\u251c\\u2500\\u2500 <rig>-b-files.json\\\",\\n        \\\"\\u2502   \\u2514\\u2500\\u2500 <rig>-b-files.md\\\",\\n        \\\"\\u2514\\u2500\\u2500 downloads/    # All downloaded files, by rig/subfolder structure\\\",\\n        \\\"    \\u2514\\u2500\\u2500 <rig>/\\\",\\n        \\\"        \\u251c\\u2500\\u2500 <subfolder>/\\\",\\n        \\\"        \\u2502   \\u2514\\u2500\\u2500 <item_generated_name>.<ext>\\\",\\n        \\\"        \\u2514\\u2500\\u2500 ...\\\"\\n      ],\\n      \\\"setup_prerequisites\\\": [\\n        \\\"Windows OS\\\",\\n        \\\"Python 3.11+\\\",\\n        \\\"Chrome browser (with valid Chromedriver)\\\",\\n        \\\"Valid NOV RigDoc credentials\\\",\\n        \\\"Install dependencies: pip install -r requirements.txt\\\"\\n      ],\\n      \\\"execution_run_modes\\\": [\\n        \\\"RigOfficeDownloader-v4.bat --auto (full automation)\\\",\\n        \\\"RigOfficeDownloader-v4.bat --interactive (menu-driven)\\\",\\n        \\\"RigOfficeDownloader-v4.bat --config (edit filters/templates)\\\"\\n      ],\\n      \\\"configuration_filtering\\\": [\\n        \\\"Edit filter chains (glob patterns: *G000*, *VOID*, etc.)\\\",\\n        \\\"Specify rig number and search URLs as config\\\",\\n        \\\"Modify which fields govern item selection\\\"\\n      ],\\n      \\\"benefits\\\": [\\n        \\\"\\u226575% reduction in document preparation time\\\",\\n        \\\"Consistent, metadata-driven file organization\\\",\\n        \\\"Centralized user review via Markdown checkpoints\\\",\\n        \\\"Robust error handling, deduplication, and smart automation\\\",\\n        \\\"Scalable, cohesive, and audit-ready for broader adoption\\\"\\n      ],\\n      \\\"comparison_table\\\": {\\n        \\\"Time per project\\\": {\\n          \\\"Manual\\\": \\\"4\\u20136 hours\\\",\\n          \\\"Automated\\\": \\\"<1 hour\\\"\\n        },\\n        \\\"File organization\\\": {\\n          \\\"Manual\\\": \\\"Error-prone/ad hoc\\\",\\n          \\\"Automated\\\": \\\"Standardized/metadata-driven\\\"\\n        },\\n        \\\"User review\\\": {\\n          \\\"Manual\\\": \\\"Tedious/repetitive\\\",\\n          \\\"Automated\\\": \\\"Markdown checkpoints\\\"\\n        },\\n        \\\"Auditability\\\": {\\n          \\\"Manual\\\": \\\"None\\\",\\n          \\\"Automated\\\": \\\"Full JSON/Markdown traceability\\\"\\n        },\\n        \\\"Expandability\\\": {\\n          \\\"Manual\\\": \\\"N/A\\\",\\n          \\\"Automated\\\": \\\"Configurable, reusable templates\\\"\\n        }\\n      },\\n      \\\"extensibility\\\": [\\n        \\\"Built-in support: subfolders, filter chains, field ordering, advanced config\\\",\\n        \\\"Easily adaptable for new departments/organizational patterns\\\"\\n      ],\\n      \\\"philosophy\\\": \\\"\\\\\\\"Complexity is the enemy of execution \\u2014 Optimized for engineers, by engineers.\\\\\\\"\\\"\\n    }\\n  }\\n}\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"combined_result\": {\n    \"README.md\": {\n      \"title\": \"RigOfficeDownloader\",\n      \"purpose\": \"Automate and streamline technical document retrieval from NOV RigDoc, eliminating tedious manual effort and enabling rapid, organized, and auditable access to engineering documentation.\",\n      \"features\": [\n        \"Three-stage sequential pipeline: Documents → Files → Downloads\",\n        \"Automated and interactive (menu-driven) run modes\",\n        \"Editable Markdown checkpoints for document/file selection\",\n        \"Configurable pattern-based filter chains (auto-inclusion/exclusion)\",\n        \"Metadata-driven file naming and hierarchical subfolder organization\",\n        \"Robust Selenium-based browser automation, smart waiting, deduplication, and error handling\",\n        \"Strict field ordering in JSON and Markdown outputs\",\n        \"Comprehensive traceability and auditability via exported JSON and Markdown records\"\n      ],\n      \"workflow\": [\n        \"1. Configure rig number and search URLs (filter/templates setup)\",\n        \"2. Fetch document metadata from RigDoc to <rig>-a-docs.json\",\n        \"3. Export docs to Markdown (<rig>-a-docs.md); review and set item_include as needed\",\n        \"4. Import updated selections from Markdown → JSON\",\n        \"5. Fetch file metadata for selected docs → <rig>-b-files.json\",\n        \"6. Export files to Markdown (<rig>-b-files.md); set item_download as needed\",\n        \"7. Import updated file selections from Markdown\",\n        \"8. Download marked files to outputs/downloads/<rig>, auto-organized via generated subfolder paths\"\n      ],\n      \"directory_structure\": [\n        \"outputs/\",\n        \"├── data/         # All metadata/selection (JSON + Markdown)\",\n        \"│   ├── <rig>-a-docs.json\",\n        \"│   ├── <rig>-a-docs.md\",\n        \"│   ├── <rig>-b-files.json\",\n        \"│   └── <rig>-b-files.md\",\n        \"└── downloads/    # All downloaded files, by rig/subfolder structure\",\n        \"    └── <rig>/\",\n        \"        ├── <subfolder>/\",\n        \"        │   └── <item_generated_name>.<ext>\",\n        \"        └── ...\"\n      ],\n      \"setup_prerequisites\": [\n        \"Windows OS\",\n        \"Python 3.11+\",\n        \"Chrome browser (with valid Chromedriver)\",\n        \"Valid NOV RigDoc credentials\",\n        \"Install dependencies: pip install -r requirements.txt\"\n      ],\n      \"execution_run_modes\": [\n        \"RigOfficeDownloader-v4.bat --auto           # Full pipeline, minimal user intervention\",\n        \"RigOfficeDownloader-v4.bat --interactive    # Menu-driven, step-by-step workflow\",\n        \"RigOfficeDownloader-v4.bat --config         # Edit search templates and filters\"\n      ],\n      \"configuration_and_filtering\": [\n        \"Edit filter chains (glob patterns: *G000*, *VOID*, etc.) for auto-inclusion/exclusion\",\n        \"Specify rig number and search URLs in configuration\",\n        \"Modify which metadata fields control selection\"\n      ],\n      \"benefits\": [\n        \"≥75% reduction in document preparation time\",\n        \"Consistent, metadata-driven file organization\",\n        \"Centralized user review and selection via Markdown checkpoints\",\n        \"Robust error handling, deduplication, and smart automation\",\n        \"Scalable, cohesive, and audit-ready architecture for broader adoption\"\n      ],\n      \"comparison_table\": {\n        \"Time per project\": {\n          \"Manual\": \"4–6 hours\",\n          \"Automated\": \"<1 hour\"\n        },\n        \"File organization\": {\n          \"Manual\": \"Error-prone, ad hoc\",\n          \"Automated\": \"Metadata-driven, standardized\"\n        },\n        \"User review\": {\n          \"Manual\": \"Tedious and repetitive\",\n          \"Automated\": \"Centralized via Markdown checkpoints\"\n        },\n        \"Auditability\": {\n          \"Manual\": \"Weak or absent\",\n          \"Automated\": \"Full JSON/Markdown traceability\"\n        },\n        \"Expandability\": {\n          \"Manual\": \"N/A\",\n          \"Automated\": \"Configurable, reusable templates\"\n        }\n      },\n      \"extensibility\": [\n        \"Built-in support for subfolders, pattern-based filter chains, customizable field ordering, advanced configuration\",\n        \"Easily adaptable for new departments or expanded document organization/selection policies\"\n      ],\n      \"philosophy\": \"\\\"Complexity is the enemy of execution — Optimized for engineers, by engineers.\\\"\"\n    }\n  }\n}", "cost": 0.0}}}, {"instruction": "# Result Summarizer\n\nYou are a specialized aggregator that summarizes results from multiple instruction steps. Your task is to analyze the combined output from previous steps and create a concise summary that captures the key points and insights. Your input is a JSON object containing the combined results from previous steps, which may include the complete interaction history. Your output should be a JSON object with a summary field that contains your analysis. Execute as:\n\n`{role=result_summarizer;input=[combined_result:json];process=[identify_key_themes(),extract_main_points(),condense_into_summary(),highlight_important_insights()];constraints=[maintain_brevity(),preserve_essential_information(),avoid_introducing_new_concepts()];requirements=[output_as_json(),ensure_clarity(),focus_on_most_relevant_aspects()];output={summary:json}}`", "step": "b", "title": "Result Su<PERSON><PERSON><PERSON>", "input": "{\n  \"combined_result\": {\n    \"README.md\": {\n      \"title\": \"RigOfficeDownloader\",\n      \"purpose\": \"Automate and streamline technical document retrieval from NOV RigDoc, eliminating tedious manual effort and enabling rapid, organized, and auditable access to engineering documentation.\",\n      \"features\": [\n        \"Three-stage sequential pipeline: Documents → Files → Downloads\",\n        \"Automated and interactive (menu-driven) run modes\",\n        \"Editable Markdown checkpoints for document/file selection\",\n        \"Configurable pattern-based filter chains (auto-inclusion/exclusion)\",\n        \"Metadata-driven file naming and hierarchical subfolder organization\",\n        \"Robust Selenium-based browser automation, smart waiting, deduplication, and error handling\",\n        \"Strict field ordering in JSON and Markdown outputs\",\n        \"Comprehensive traceability and auditability via exported JSON and Markdown records\"\n      ],\n      \"workflow\": [\n        \"1. Configure rig number and search URLs (filter/templates setup)\",\n        \"2. Fetch document metadata from RigDoc to <rig>-a-docs.json\",\n        \"3. Export docs to Markdown (<rig>-a-docs.md); review and set item_include as needed\",\n        \"4. Import updated selections from Markdown → JSON\",\n        \"5. Fetch file metadata for selected docs → <rig>-b-files.json\",\n        \"6. Export files to Markdown (<rig>-b-files.md); set item_download as needed\",\n        \"7. Import updated file selections from Markdown\",\n        \"8. Download marked files to outputs/downloads/<rig>, auto-organized via generated subfolder paths\"\n      ],\n      \"directory_structure\": [\n        \"outputs/\",\n        \"├── data/         # All metadata/selection (JSON + Markdown)\",\n        \"│   ├── <rig>-a-docs.json\",\n        \"│   ├── <rig>-a-docs.md\",\n        \"│   ├── <rig>-b-files.json\",\n        \"│   └── <rig>-b-files.md\",\n        \"└── downloads/    # All downloaded files, by rig/subfolder structure\",\n        \"    └── <rig>/\",\n        \"        ├── <subfolder>/\",\n        \"        │   └── <item_generated_name>.<ext>\",\n        \"        └── ...\"\n      ],\n      \"setup_prerequisites\": [\n        \"Windows OS\",\n        \"Python 3.11+\",\n        \"Chrome browser (with valid Chromedriver)\",\n        \"Valid NOV RigDoc credentials\",\n        \"Install dependencies: pip install -r requirements.txt\"\n      ],\n      \"execution_run_modes\": [\n        \"RigOfficeDownloader-v4.bat --auto           # Full pipeline, minimal user intervention\",\n        \"RigOfficeDownloader-v4.bat --interactive    # Menu-driven, step-by-step workflow\",\n        \"RigOfficeDownloader-v4.bat --config         # Edit search templates and filters\"\n      ],\n      \"configuration_and_filtering\": [\n        \"Edit filter chains (glob patterns: *G000*, *VOID*, etc.) for auto-inclusion/exclusion\",\n        \"Specify rig number and search URLs in configuration\",\n        \"Modify which metadata fields control selection\"\n      ],\n      \"benefits\": [\n        \"≥75% reduction in document preparation time\",\n        \"Consistent, metadata-driven file organization\",\n        \"Centralized user review and selection via Markdown checkpoints\",\n        \"Robust error handling, deduplication, and smart automation\",\n        \"Scalable, cohesive, and audit-ready architecture for broader adoption\"\n      ],\n      \"comparison_table\": {\n        \"Time per project\": {\n          \"Manual\": \"4–6 hours\",\n          \"Automated\": \"<1 hour\"\n        },\n        \"File organization\": {\n          \"Manual\": \"Error-prone, ad hoc\",\n          \"Automated\": \"Metadata-driven, standardized\"\n        },\n        \"User review\": {\n          \"Manual\": \"Tedious and repetitive\",\n          \"Automated\": \"Centralized via Markdown checkpoints\"\n        },\n        \"Auditability\": {\n          \"Manual\": \"Weak or absent\",\n          \"Automated\": \"Full JSON/Markdown traceability\"\n        },\n        \"Expandability\": {\n          \"Manual\": \"N/A\",\n          \"Automated\": \"Configurable, reusable templates\"\n        }\n      },\n      \"extensibility\": [\n        \"Built-in support for subfolders, pattern-based filter chains, customizable field ordering, advanced configuration\",\n        \"Easily adaptable for new departments or expanded document organization/selection policies\"\n      ],\n      \"philosophy\": \"\\\"Complexity is the enemy of execution — Optimized for engineers, by engineers.\\\"\"\n    }\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"summary\": \"RigOfficeDownloader is a specialized Python automation tool designed to streamline and automate the process of retrieving technical documents from NOV RigDoc. It employs a three-stage sequential workflow—moving from document selection to file metadata extraction, and finally to automated downloads—significantly reducing manual effort and preparation time by at least 75%. The pipeline supports both fully automatic and interactive, menu-driven modes, with extensive options for filtering, pattern-based selection, and configuration. User review and selection are centralized and auditable via editable Markdown checkpoints, ensuring clarity and traceability. The tool produces organized outputs in both JSON and Markdown formats, adhering to strict structure and field ordering for consistency. Designed for Windows environments, it requires Python 3.11+, Chrome, and valid credentials. Compared to manual processes, RigOfficeDownloader offers faster, standardized, and auditable document preparation and is extensible for wider organizational needs. It embodies a philosophy of minimizing complexity for the end user while promoting robust, engineering-driven execution.\"\n}", "cost": 0.0}}}], "aggregation_summary": {"aggregator": "0222", "inputs": ["a", "b"], "final_result": "{\n  \"summary\": \"RigOfficeDownloader is a specialized Python automation tool designed to streamline and automate the process of retrieving technical documents from NOV RigDoc. It employs a three-stage sequential workflow—moving from document selection to file metadata extraction, and finally to automated downloads—significantly reducing manual effort and preparation time by at least 75%. The pipeline supports both fully automatic and interactive, menu-driven modes, with extensive options for filtering, pattern-based selection, and configuration. User review and selection are centralized and auditable via editable Markdown checkpoints, ensuring clarity and traceability. The tool produces organized outputs in both JSON and Markdown formats, adhering to strict structure and field ordering for consistency. Designed for Windows environments, it requires Python 3.11+, Chrome, and valid credentials. Compared to manual processes, RigOfficeDownloader offers faster, standardized, and auditable document preparation and is extensible for wider organizational needs. It embodies a philosophy of minimizing complexity for the end user while promoting robust, engineering-driven execution.\"\n}", "total_cost": 0.0}, "total_cost": 0.0}