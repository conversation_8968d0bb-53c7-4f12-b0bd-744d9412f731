"""
Monitor Module for Window Tiler

This module provides functionality for detecting and representing physical display monitors
in a Windows system. It includes a Monitor class that encapsulates monitor properties and
boundaries, and functions for retrieving all available monitors.
"""

import win32api


class Monitor:
    """
    Represents a physical display monitor in the system.
    
    Attributes:
        handle: The monitor handle
        is_primary: Whether this is the primary monitor
        device: Device name associated with the monitor
        monitor_area: Full monitor area coordinates (x1, y1, x2, y2)
        work_area: Work area coordinates (x1, y1, x2, y2), excluding taskbar
    """
    
    def __init__(self, monitor_handle, monitor_info):
        """
        Initialize a Monitor object.
        
        Args:
            monitor_handle: Handle to the monitor
            monitor_info: Dictionary with monitor information from win32api.GetMonitorInfo
        """
        self.handle = monitor_handle
        self.is_primary = monitor_info['Flags'] == 1
        self.device = monitor_info['Device']
        self._set_areas(monitor_info['Monitor'], monitor_info['Work'])

    def _set_areas(self, monitor_area, work_area):
        """
        Set the monitor and work areas from raw coordinates.
        
        Args:
            monitor_area: Full monitor area coordinates (x1, y1, x2, y2)
            work_area: Work area coordinates (x1, y1, x2, y2)
        """
        self.monitor_area = monitor_area
        self.work_area = work_area
        self.monitor_area_dict = self._convert_to_dict(monitor_area)
        self.work_area_dict = self._convert_to_dict(work_area)

    def _convert_to_dict(self, area):
        """
        Convert raw coordinates to a more usable dictionary format.
        
        Args:
            area: Raw area coordinates (x1, y1, x2, y2)
            
        Returns:
            dict: Dictionary with x, y, width, and height keys
        """
        return {
            'x': area[0],
            'y': area[1],
            'width': area[2] - area[0],
            'height': area[3] - area[1]
        }

    def get_dimensions(self):
        """
        Get the dimensions of the monitor.
        
        Returns:
            dict: Dictionary with width and height keys
        """
        return {
            'width': self.monitor_area[2] - self.monitor_area[0],
            'height': self.monitor_area[3] - self.monitor_area[1]
        }

    def get_work_area(self):
        """
        Get the monitor's work area (excluding taskbar).
        
        Returns:
            dict: Dictionary with x, y, width, and height keys
        """
        return self._convert_to_dict(self.work_area)
    
    def contains_point(self, x, y):
        """
        Check if a point is within this monitor's area.
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Returns:
            bool: True if the point is within this monitor, False otherwise
        """
        return (self.monitor_area[0] <= x <= self.monitor_area[2] and 
                self.monitor_area[1] <= y <= self.monitor_area[3])
    
    def point_to_relative(self, x, y):
        """
        Convert absolute screen coordinates to monitor-relative coordinates.
        
        Args:
            x: Absolute X coordinate
            y: Absolute Y coordinate
            
        Returns:
            tuple: (relative_x, relative_y) as proportions (0-1) of monitor dimensions
        """
        dimensions = self.get_dimensions()
        relative_x = (x - self.monitor_area[0]) / dimensions['width']
        relative_y = (y - self.monitor_area[1]) / dimensions['height']
        return (relative_x, relative_y)
    
    def relative_to_point(self, rel_x, rel_y):
        """
        Convert monitor-relative coordinates to absolute screen coordinates.
        
        Args:
            rel_x: Relative X coordinate (0-1)
            rel_y: Relative Y coordinate (0-1)
            
        Returns:
            tuple: (absolute_x, absolute_y) as screen coordinates
        """
        dimensions = self.get_dimensions()
        abs_x = self.monitor_area[0] + int(rel_x * dimensions['width'])
        abs_y = self.monitor_area[1] + int(rel_y * dimensions['height'])
        return (abs_x, abs_y)


def get_all_monitors():
    """
    Get all available monitors in the system.
    
    Returns:
        dict: Dictionary mapping monitor handles to Monitor objects
    """
    monitors = {}
    
    for monitor in win32api.EnumDisplayMonitors(None, None):
        monitor_handle = monitor[0]
        monitor_info = win32api.GetMonitorInfo(monitor_handle)
        monitor_obj = Monitor(monitor_handle, monitor_info)
        monitors[monitor_handle] = monitor_obj
        
    return monitors


def get_primary_monitor():
    """
    Get the primary monitor.
    
    Returns:
        Monitor: The primary monitor or None if not found
    """
    monitors = get_all_monitors()
    for monitor in monitors.values():
        if monitor.is_primary:
            return monitor
    
    # If no primary monitor was found but we have monitors, return the first one
    if monitors:
        return next(iter(monitors.values()))
    
    return None
