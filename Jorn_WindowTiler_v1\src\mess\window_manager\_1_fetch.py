# Import modules
import os
import sys
import re
import time
import random
import ctypes
import urllib.parse
import json
from enum import Enum

# Error handling
# import traceback
# import pywintypes

# Import pywin32 modules
import win32com.client
import win32com.shell.shellcon as shellcon
import win32con
import win32gui
import win32api
import win32process

# Import constants from win32con

from .window_enums import WindowCategory, WindowExplorerType

def get_all_windows():
    windows = []
    def enum_windows(hwnd, result):
        windows.append((hwnd, win32gui.GetWindowText(hwnd), win32gui.GetClassName(hwnd)))
    win32gui.EnumWindows(enum_windows, [])
    return windows


# def get_windows(is_visible=False, ):

# # from .window_class import Window
# from . import window_class

# # from window_utils import get_shell_windows_instance

# if __name__ == "__main__":
#     # shell_windows, special_folders = get_shell_windows_instance()



#     all_windows = window_utils.window_operations.get_all_windows()
#     print(all_windows)


