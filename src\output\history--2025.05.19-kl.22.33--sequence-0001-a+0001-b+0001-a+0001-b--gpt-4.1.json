{"initial_prompt": "rephrase into a generalized and clarified sentence that describes the problem the utility is made to adress (in a single unbroken line of less than 200 characters): '''Manual document retrieval in RigOffice is inefficient and repetitive, significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time.'''", "sequence_id": "sequence-0001-a+0001-b+0001-a+0001-b", "results": [{"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "rephrase into a generalized and clarified sentence that describes the problem the utility is made to adress (in a single unbroken line of less than 200 characters): '''Manual document retrieval in RigOffice is inefficient and repetitive, significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time.'''", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Compose a concise, single-line statement under 200 characters describing the utility's purpose to address inefficient, repetitive manual document retrieval in RigOffice before project initiation.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"rephrase into a generalized and clarified sentence that describes the problem the utility is made to adress (in a single unbroken line of less than 200 characters): '''Manual document retrieval in RigOffice is inefficient and repetitive, significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time.'''\",\n  \"instruction_format\": \"Compose a concise, single-line statement under 200 characters describing the utility's purpose to address inefficient, repetitive manual document retrieval in RigOffice before project initiation.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": \"The utility streamlines document retrieval in RigOffice, eliminating repetitive manual searches and saving time before projects begin.\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "{\n  \"initial_prompt\": \"rephrase into a generalized and clarified sentence that describes the problem the utility is made to adress (in a single unbroken line of less than 200 characters): '''Manual document retrieval in RigOffice is inefficient and repetitive, significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time.'''\",\n  \"distilled_essence\": \"The utility streamlines document retrieval in RigOffice, eliminating repetitive manual searches and saving time before projects begin.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Compose a concise, generalized sentence (single line, under 200 characters) describing the utility's purpose to address inefficient and repetitive manual document retrieval in RigOffice.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"rephrase into a generalized and clarified sentence that describes the problem the utility is made to adress (in a single unbroken line of less than 200 characters): '''Manual document retrieval in RigOffice is inefficient and repetitive, significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time.'''\",\n  \"instruction_format\": \"Compose a concise, generalized sentence (single line, under 200 characters) describing the utility's purpose to address inefficient and repetitive manual document retrieval in RigOffice.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": \"The utility streamlines and automates document retrieval in RigOffice to reduce inefficiency and repetitive manual effort.\"\n}", "cost": 0.0}}}], "total_cost": 0.0}