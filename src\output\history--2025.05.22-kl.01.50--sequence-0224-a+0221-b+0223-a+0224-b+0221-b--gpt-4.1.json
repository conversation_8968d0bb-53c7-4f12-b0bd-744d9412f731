{"initial_prompt": "visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()", "sequence_id": "sequence-0224-a+0221-b+0223-a+0224-b+0221-b", "results": [{"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: urban rooftop at night, subject: single figure standing center frame, action: slow panoramic camera orbit, camera: [wide shot], lighting: neon highlights, rim light, style: hyperrealistic, effects: subtle fog, post: high contrast, color grade: cool blue, instruction: maintain sharp focus, duration: 5s\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Amplifier B\n\nPerform a structural and semantic distillation of the input: locate its *driving aim*, isolate supporting momentum, and forcefully extrapolate it into a clarified directive. Treat the input as a living logic stream—trace the *underlying thesis*, and remove all extraneous elements. Amplify only what accelerates the goal. The output must **not reflect interpretation**, but **the core resolution** implied by the data’s internal logic. Execute as:\n\n`{role=instruction_amplifier_b;input=[original_text:str];process=[extract_underlying_thesis(),eliminate_distraction(),amplify_goal_drivers(),intensify_and_operationalize_purpose(),restructure_as_directive_output()];constraints=[preserve_input_telos(),avoid_additional_concepts(),maintaingoal_focused_structure()];requirements=[use_crystallized_command_language(),preserve_technical_fidelity(),ensure_actionability_of_output(),maximize_instructional_clarity()];output={clarified_directive:str}}`", "step": "b", "title": "Instruction Amplifier B", "input": "{\n  \"initial_prompt\": \"visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()\",\n  \"optimized_video_prompt\": \"scene: urban rooftop at night, subject: single figure standing center frame, action: slow panoramic camera orbit, camera: [wide shot], lighting: neon highlights, rim light, style: hyperrealistic, effects: subtle fog, post: high contrast, color grade: cool blue, instruction: maintain sharp focus, duration: 5s\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"scene: urban rooftop at night, subject: single figure standing center frame, action: slow panoramic camera orbit, camera: [wide shot], lighting: neon highlights, rim light, style: hyperrealistic, effects: subtle fog, post: high contrast, color grade: cool blue, instruction: maintain sharp focus, duration: 5s\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": {\n    \"clarified_directive\": \"Render a 5-second hyperrealistic wide shot of a single subject standing center frame on an urban rooftop at night. Execute a slow panoramic camera orbit around the subject. Illuminate scene with neon highlights and rim lighting; apply subtle fog. Post-process with high contrast and a cool blue color grade. Maintain sharp focus throughout.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"initial_prompt\": \"visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()\",\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"scene: urban rooftop at night, subject: single figure standing center frame, action: slow panoramic camera orbit, camera: [wide shot], lighting: neon highlights, rim light, style: hyperrealistic, effects: subtle fog, post: high contrast, color grade: cool blue, instruction: maintain sharp focus, duration: 5s\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": {\n    \"clarified_directive\": \"Render a 5-second hyperrealistic wide shot of a single subject standing center frame on an urban rooftop at night. Execute a slow panoramic camera orbit around the subject. Illuminate scene with neon highlights and rim lighting; apply subtle fog. Post-process with high contrast and a cool blue color grade. Maintain sharp focus throughout.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"generated_video_sequence\": \"[/outputs/urban_rooftop_orbit_hyperreal_5s.mp4]\",\n  \"crystallized_video_prompt\": {\n    \"scene\": \"Urban rooftop at night\",\n    \"subject\": \"Single figure, center frame\",\n    \"duration\": 5,\n    \"shot_type\": \"Wide shot\",\n    \"camera_movement\": \"Slow panoramic orbit around subject, maintaining constant distance; city skyline and rooftop details sweep fluidly in parallax while subject remains stationary, anchor-like in the center; neon lights below and behind refract softly through the subtle fog as the angle shifts; rim light carves out silhouette with crisp definition against hazy city backdrop.\",\n    \"lighting\": \"Neon highlights (various city colors: blue, magenta, cyan) cast dynamic glows and reflections across slick rooftop surfaces and edges; rim light delineates figure’s contours; shadows deepen proportionally as orbit shifts away from direct light sources.\",\n    \"atmospheric_effects\": \"Subtle rolling fog wafts across rooftop, reacting naturally to ambient air currents caused by the implied city wind; fog edges catch neon light, swirling hypnotically as camera movement reveals new illuminated strata.\",\n    \"visual_style\": \"Hyperrealistic: extreme clarity on facial details, textures of clothing, rooftop grit, and glistening puddles; glass and metal surfaces refract city lights with sharp, stylized reflections.\",\n    \"post_processing\": \"High contrast amplifies edges, details, and silhouettes; cool blue overlay in color grade saturates darkest shadows and lifts midtones, reinforcing nocturnal, futuristic mood.\",\n    \"focus\": \"Pin-sharp on subject for entire sequence; deep depth-of-field ensures cityscape detail remains crisp while guiding viewer’s gaze back to central figure as the anchor.\",\n    \"symbolic_morphology\": \"Camera orbit visually isolates and elevates the individual—subject as singular beacon amidst urban flux, symbolizing resilience and clarity in chaos.\",\n    \"output_requirement\": \"Produce seamless, high-fidelity, emotionally resonant 5s video file with cinematic motion, immersive realism, and polished compositional flow.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# Temporal Feasibility & Ambiguity Lock (0224b)\n\nYour goal is **final refinement for execution**: Take the `optimized_video_prompt` and critically assess its visual complexity against a target duration (default 5 seconds). Ruthlessly simplify or remove elements that cannot be clearly conveyed within this timeframe without sacrificing the core visual intent. Simultaneously, eliminate any remaining semantic or visual ambiguity, ensuring every parameter is explicit and interpretation-free. The output must be a visually feasible, ultra-clear, and maximally impactful prompt ready for immediate generation.\n\n`{role=prompt_finalizer_for_execution; seqindex=b; input=[optimized_video_prompt:str, target_duration_seconds:int=5, core_visual_intent_summary:str (derived from original verbose prompt if available, or inferred)]; process=[evaluate_visual_density_against_duration(prompt=optimized_video_prompt, duration=target_duration_seconds), identify_elements_risking_overcrowding_or_unclear_pacing(), simplify_or_remove_low_priority_or_time_intensive_visuals_to_fit_duration(preserve_core_intent=core_visual_intent_summary), scan_for_any_remaining_ambiguous_terms_or_parameters(), replace_ambiguities_with_explicit_unmistakable_directives_or_values(), perform_final_conciseness_check_for_impact_within_duration(), validate_output_is_visually_coherent_temporally_feasible_and_entirely_unambiguous()]; constraints=[prompt_must_be_realistically_conveyable_within_target_duration(), all_elements_must_be_explicit_and_unambiguous(), core_visual_intent_must_be_preserved_through_simplification(), no_new_creative_elements_to_be_introduced_only_simplification_and_clarification()]; requirements=[produce_a_video_prompt_that_is_both_temporally_achievable_and_crystal_clear(), ensure_maximal_impact_for_a_short_duration_visual(), eliminate_all_potential_for_misinterpretation_by_the_video_generator()]; output={final_executable_video_prompt:str}}`", "step": "b", "title": "Temporal Feasibility & Ambiguity Lock (0224b)", "input": "{\n  \"initial_prompt\": \"visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()\",\n  \"generated_video_sequence\": \"[/outputs/urban_rooftop_orbit_hyperreal_5s.mp4]\",\n  \"crystallized_video_prompt\": {\n    \"scene\": \"Urban rooftop at night\",\n    \"subject\": \"Single figure, center frame\",\n    \"duration\": 5,\n    \"shot_type\": \"Wide shot\",\n    \"camera_movement\": \"Slow panoramic orbit around subject, maintaining constant distance; city skyline and rooftop details sweep fluidly in parallax while subject remains stationary, anchor-like in the center; neon lights below and behind refract softly through the subtle fog as the angle shifts; rim light carves out silhouette with crisp definition against hazy city backdrop.\",\n    \"lighting\": \"Neon highlights (various city colors: blue, magenta, cyan) cast dynamic glows and reflections across slick rooftop surfaces and edges; rim light delineates figure\\u2019s contours; shadows deepen proportionally as orbit shifts away from direct light sources.\",\n    \"atmospheric_effects\": \"Subtle rolling fog wafts across rooftop, reacting naturally to ambient air currents caused by the implied city wind; fog edges catch neon light, swirling hypnotically as camera movement reveals new illuminated strata.\",\n    \"visual_style\": \"Hyperrealistic: extreme clarity on facial details, textures of clothing, rooftop grit, and glistening puddles; glass and metal surfaces refract city lights with sharp, stylized reflections.\",\n    \"post_processing\": \"High contrast amplifies edges, details, and silhouettes; cool blue overlay in color grade saturates darkest shadows and lifts midtones, reinforcing nocturnal, futuristic mood.\",\n    \"focus\": \"Pin-sharp on subject for entire sequence; deep depth-of-field ensures cityscape detail remains crisp while guiding viewer\\u2019s gaze back to central figure as the anchor.\",\n    \"symbolic_morphology\": \"Camera orbit visually isolates and elevates the individual\\u2014subject as singular beacon amidst urban flux, symbolizing resilience and clarity in chaos.\",\n    \"output_requirement\": \"Produce seamless, high-fidelity, emotionally resonant 5s video file with cinematic motion, immersive realism, and polished compositional flow.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"final_executable_video_prompt\": \"Create a 5-second hyperrealistic wide shot of a single stationary figure centered on an urban rooftop at night. The camera performs a slow, smooth 180-degree panoramic orbit around the figure, always maintaining a fixed distance. The city skyline and rooftop details pass in clear, fluid parallax behind the subject. The scene is illuminated by neon city lights in blue, magenta, and cyan, reflecting on wet rooftop surfaces; rim lighting crisply outlines the figure’s silhouette. Subtle, slow-moving fog is present on the rooftop, softly catching the neon colors without obscuring the subject. Maintain sharp focus on both figure and cityscape throughout, with high contrast and a cool blue color grade for a futuristic, nocturnal mood. The figure remains a static, clear focal point symbolizing resilience. No close-ups, dialogue, or rapid camera movement. Output: seamless, cinematic, emotionally resonant 5-second video, visually clear and immediately evocative.\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Amplifier B\n\nPerform a structural and semantic distillation of the input: locate its *driving aim*, isolate supporting momentum, and forcefully extrapolate it into a clarified directive. Treat the input as a living logic stream—trace the *underlying thesis*, and remove all extraneous elements. Amplify only what accelerates the goal. The output must **not reflect interpretation**, but **the core resolution** implied by the data’s internal logic. Execute as:\n\n`{role=instruction_amplifier_b;input=[original_text:str];process=[extract_underlying_thesis(),eliminate_distraction(),amplify_goal_drivers(),intensify_and_operationalize_purpose(),restructure_as_directive_output()];constraints=[preserve_input_telos(),avoid_additional_concepts(),maintaingoal_focused_structure()];requirements=[use_crystallized_command_language(),preserve_technical_fidelity(),ensure_actionability_of_output(),maximize_instructional_clarity()];output={clarified_directive:str}}`", "step": "b", "title": "Instruction Amplifier B", "input": "{\n  \"initial_prompt\": \"visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()\",\n  \"final_executable_video_prompt\": \"Create a 5-second hyperrealistic wide shot of a single stationary figure centered on an urban rooftop at night. The camera performs a slow, smooth 180-degree panoramic orbit around the figure, always maintaining a fixed distance. The city skyline and rooftop details pass in clear, fluid parallax behind the subject. The scene is illuminated by neon city lights in blue, magenta, and cyan, reflecting on wet rooftop surfaces; rim lighting crisply outlines the figure\\u2019s silhouette. Subtle, slow-moving fog is present on the rooftop, softly catching the neon colors without obscuring the subject. Maintain sharp focus on both figure and cityscape throughout, with high contrast and a cool blue color grade for a futuristic, nocturnal mood. The figure remains a static, clear focal point symbolizing resilience. No close-ups, dialogue, or rapid camera movement. Output: seamless, cinematic, emotionally resonant 5-second video, visually clear and immediately evocative.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"Create a 5-second hyperrealistic wide shot of a single stationary figure centered on an urban rooftop at night. The camera performs a slow, smooth 180-degree panoramic orbit around the figure, always maintaining a fixed distance. The city skyline and rooftop details pass in clear, fluid parallax behind the subject. The scene is illuminated by neon city lights in blue, magenta, and cyan, reflecting on wet rooftop surfaces; rim lighting crisply outlines the figure’s silhouette. Subtle, slow-moving fog is present on the rooftop, softly catching the neon colors without obscuring the subject. Maintain sharp focus on both figure and cityscape throughout, with high contrast and a cool blue color grade for a futuristic, nocturnal mood. The figure remains a static, clear focal point symbolizing resilience. No close-ups, dialogue, or rapid camera movement. Output: seamless, cinematic, emotionally resonant 5-second video, visually clear and immediately evocative.\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": \"Render a 5-second hyperrealistic wide shot featuring a static, centered figure on an urban rooftop at night. Execute a smooth 180-degree panoramic camera orbit at fixed distance, maintaining the figure as a sharp, clear focal point; ensure dynamic city skyline parallax and rooftop details remain in focus. Illuminate with blue, magenta, and cyan neon city lights reflecting on wet surfaces; apply crisp rim lighting to outline the figure. Incorporate subtle, slow-moving fog that captures neon color but does not obscure the subject. Uphold high contrast and a cool blue color grade for a futuristic nocturnal atmosphere. Avoid close-ups, dialogue, or rapid motion. Output a single, seamless, cinematic, visually impactful and emotionally resonant 5-second video.\"\n}", "cost": 0.0}}}], "total_cost": 0.0}