# Class-Based Architecture 

## Overview
This document outlines a proposed refactoring of the Window Tiler to use a comprehensive class-based architecture, moving away from the current mix of classes and standalone functions to create a more maintainable and extensible system.

## Current Architecture Challenges
- The project combines well-defined classes (`Window`, `Monitor`, `WrappedMonitor`) with numerous standalone functions
- User interface logic is intertwined with core functionality
- Limited clear extension points for new features
- `main()` function handles too many responsibilities

## Proposed Class Structure

```mermaid
classDiagram
    class WindowTilerApp {
        -WindowManager windowManager
        -MonitorManager monitorManager
        -LayoutManager layoutManager
        -UserInterface ui
        +run()
        +exit()
    }
    
    class WindowManager {
        -List~Window~ windows
        +detectWindows(skipExplorer, minSize)
        +getWindowsByProcess()
        +getWindowsByType()
        +filterWindows(criteria)
    }
    
    class MonitorManager {
        -List~Monitor~ monitors
        +detectMonitors()
        +getPrimaryMonitor()
        +getMonitorByIndex(index)
    }
    
    class LayoutManager {
        +applyGridLayout(windows, monitor, rows, cols)
        +applyCustomLayout(windows, monitor, layout)
        +createLayoutPreset(name, config)
    }
    
    class UserInterface {
        +showMonitorSelection()
        +showWindowGroups()
        +getLayoutConfiguration()
        +displayResults(message)
    }
    
    WindowTilerApp --> WindowManager
    WindowTilerApp --> MonitorManager
    WindowTilerApp --> LayoutManager
    WindowTilerApp --> UserInterface
```

## Existing Classes to Preserve
- `Window`: Represents a single window with properties and operations
- `Monitor`: Represents a physical display monitor
- `WindowType`: Enumeration of window categories

## Benefits

### Structural Benefits
1. **Single Responsibility Principle**: Each class has a clear, focused purpose
2. **Composition Over Inheritance**: Uses composition to build functionality
3. **Encapsulation**: Hides implementation details within appropriate classes
4. **Loose Coupling**: Classes interact through well-defined interfaces

### Practical Benefits
1. **Code Reduction**: Main function and control flow become significantly shorter
2. **Maintainability**: Changes to one component don't affect others
3. **Testability**: Each class can be tested in isolation
4. **Extensibility**: New window detection methods or layout strategies can be added without modifying existing code

## Implementation Strategy

### Phase 1: Core Structure
1. Create the base class structure
2. Move existing functions into appropriate classes with minimal changes
3. Ensure backward compatibility with existing behavior

### Phase 2: Interface Refinement
1. Define clear interfaces between classes
2. Remove any remaining tight coupling
3. Create proper error handling within each class

### Phase 3: Feature Enablement
1. Add extension points for planned features
2. Implement configuration persistence
3. Prepare for the Smart Layouts feature

## Impact on Current Development

This architectural change directly supports several items from our tasks list:
- **Error Handling**: Each class can manage its own error states
- **Refactoring**: Cleaner code organization and improved naming
- **Layout Presets**: The LayoutManager class provides a natural home for this feature
- **Custom Classification Rules**: WindowManager can easily support customizable classification

## Alignment with Project Principles
This architecture strengthens our commitment to:
- **Clarity**: Each class has a single, well-defined purpose
- **Structure**: Logical organization of code by function
- **Simplicity**: Complex operations are broken down into smaller, manageable classes
- **Elegance**: Clean interfaces between components
- **Precision**: Focused responsibilities for each class
- **Intent**: Clear design patterns that communicate purpose
