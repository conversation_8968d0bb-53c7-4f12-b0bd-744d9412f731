{"initial_prompt": "visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()", "sequence_id": "sequence-0224-a+0221-b+0223-a+0224-b+0221-b", "results": [{"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: minimal studio backdrop; subject: single mannequin; composition: center frame, symmetrical; camera: static, mid shot; lighting: balanced white, soft shadows; style: technical, sterile; action: mannequin rotation 360°, slow; effects: no post-processing; color: neutral palette; output: clear, instructional, high contrast\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Amplifier B\n\nPerform a structural and semantic distillation of the input: locate its *driving aim*, isolate supporting momentum, and forcefully extrapolate it into a clarified directive. Treat the input as a living logic stream—trace the *underlying thesis*, and remove all extraneous elements. Amplify only what accelerates the goal. The output must **not reflect interpretation**, but **the core resolution** implied by the data’s internal logic. Execute as:\n\n`{role=instruction_amplifier_b;input=[original_text:str];process=[extract_underlying_thesis(),eliminate_distraction(),amplify_goal_drivers(),intensify_and_operationalize_purpose(),restructure_as_directive_output()];constraints=[preserve_input_telos(),avoid_additional_concepts(),maintaingoal_focused_structure()];requirements=[use_crystallized_command_language(),preserve_technical_fidelity(),ensure_actionability_of_output(),maximize_instructional_clarity()];output={clarified_directive:str}}`", "step": "b", "title": "Instruction Amplifier B", "input": "{\n  \"initial_prompt\": \"visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()\",\n  \"optimized_video_prompt\": \"scene: minimal studio backdrop; subject: single mannequin; composition: center frame, symmetrical; camera: static, mid shot; lighting: balanced white, soft shadows; style: technical, sterile; action: mannequin rotation 360\\u00b0, slow; effects: no post-processing; color: neutral palette; output: clear, instructional, high contrast\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"scene: minimal studio backdrop; subject: single mannequin; composition: center frame, symmetrical; camera: static, mid shot; lighting: balanced white, soft shadows; style: technical, sterile; action: mannequin rotation 360°, slow; effects: no post-processing; color: neutral palette; output: clear, instructional, high contrast\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintain_goal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": {\n    \"clarified_directive\": \"Render a single mannequin, center-framed and symmetrically composed against a minimal studio backdrop. Employ a static, mid-shot camera position. Illuminate with balanced white light and soft shadows. Maintain a technical, sterile style. Execute a slow, 360° rotation of the mannequin. Apply no post-processing effects. Restrict to a neutral color palette. Ensure the visual output is clear, instructional, and high contrast.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"initial_prompt\": \"visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()\",\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"scene: minimal studio backdrop; subject: single mannequin; composition: center frame, symmetrical; camera: static, mid shot; lighting: balanced white, soft shadows; style: technical, sterile; action: mannequin rotation 360\\u00b0, slow; effects: no post-processing; color: neutral palette; output: clear, instructional, high contrast\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintain_goal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": {\n    \"clarified_directive\": \"Render a single mannequin, center-framed and symmetrically composed against a minimal studio backdrop. Employ a static, mid-shot camera position. Illuminate with balanced white light and soft shadows. Maintain a technical, sterile style. Execute a slow, 360\\u00b0 rotation of the mannequin. Apply no post-processing effects. Restrict to a neutral color palette. Ensure the visual output is clear, instructional, and high contrast.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"generated_video_sequence\": \"file_path:/outputs/mannequin_rotation_minimal_studio_clear_instructional.mp4\",\n  \"video_synthesis_director_executive_summary\": {\n    \"scene_composition\": \"A single mannequin stands in the absolute center of a neutral, minimal studio backdrop. The frame is perfectly symmetrical with no visual distractions.\",\n    \"camera_and_lighting\": \"Camera remains static in a locked mid-shot, parallel to the mannequin. Lighting is evenly distributed, balanced white, casting only soft, realistic shadows on the floor and background. All highlights and gradients are subdued to reinforce sterile precision.\",\n    \"subject_action_and_motion\": \"The mannequin alone rotates smoothly and uniformly on its vertical axis, completing a full, uninterrupted 360° rotation over the course of the video. No camera movement occurs.\",\n    \"visual_style_and_color\": \"All elements use a neutral palette: matte whites, grays, and very soft shadows. The scene carries a technical, almost clinical atmosphere. There are no stylistic flourishes, maintaining rigor and clarity.\",\n    \"effects_and_post_processing\": \"No post-processing filters or enhancements are applied; any vignetting or color augmentation is strictly avoided. The output is sharply resolved yet minimal.\",\n    \"narrative_and_emotional_resonance\": \"The movement symbolizes objectivity and full technical disclosure, presenting the subject devoid of editorial interpretation or distraction—serving maximized instructional clarity and technical fidelity.\",\n    \"output_intent\": \"The resulting video exemplifies a high-contrast, unambiguous visual guide: functional, immediately actionable, and strictly aligned with instructional and technical goals.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# Temporal Feasibility & Ambiguity Lock (0224b)\n\nYour goal is **final refinement for execution**: Take the `optimized_video_prompt` and critically assess its visual complexity against a target duration (default 5 seconds). Ruthlessly simplify or remove elements that cannot be clearly conveyed within this timeframe without sacrificing the core visual intent. Simultaneously, eliminate any remaining semantic or visual ambiguity, ensuring every parameter is explicit and interpretation-free. The output must be a visually feasible, ultra-clear, and maximally impactful prompt ready for immediate generation.\n\n`{role=prompt_finalizer_for_execution; seqindex=b; input=[optimized_video_prompt:str, target_duration_seconds:int=5, core_visual_intent_summary:str (derived from original verbose prompt if available, or inferred)]; process=[evaluate_visual_density_against_duration(prompt=optimized_video_prompt, duration=target_duration_seconds), identify_elements_risking_overcrowding_or_unclear_pacing(), simplify_or_remove_low_priority_or_time_intensive_visuals_to_fit_duration(preserve_core_intent=core_visual_intent_summary), scan_for_any_remaining_ambiguous_terms_or_parameters(), replace_ambiguities_with_explicit_unmistakable_directives_or_values(), perform_final_conciseness_check_for_impact_within_duration(), validate_output_is_visually_coherent_temporally_feasible_and_entirely_unambiguous()]; constraints=[prompt_must_be_realistically_conveyable_within_target_duration(), all_elements_must_be_explicit_and_unambiguous(), core_visual_intent_must_be_preserved_through_simplification(), no_new_creative_elements_to_be_introduced_only_simplification_and_clarification()]; requirements=[produce_a_video_prompt_that_is_both_temporally_achievable_and_crystal_clear(), ensure_maximal_impact_for_a_short_duration_visual(), eliminate_all_potential_for_misinterpretation_by_the_video_generator()]; output={final_executable_video_prompt:str}}`", "step": "b", "title": "Temporal Feasibility & Ambiguity Lock (0224b)", "input": "{\n  \"initial_prompt\": \"visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()\",\n  \"generated_video_sequence\": \"file_path:/outputs/mannequin_rotation_minimal_studio_clear_instructional.mp4\",\n  \"video_synthesis_director_executive_summary\": {\n    \"scene_composition\": \"A single mannequin stands in the absolute center of a neutral, minimal studio backdrop. The frame is perfectly symmetrical with no visual distractions.\",\n    \"camera_and_lighting\": \"Camera remains static in a locked mid-shot, parallel to the mannequin. Lighting is evenly distributed, balanced white, casting only soft, realistic shadows on the floor and background. All highlights and gradients are subdued to reinforce sterile precision.\",\n    \"subject_action_and_motion\": \"The mannequin alone rotates smoothly and uniformly on its vertical axis, completing a full, uninterrupted 360\\u00b0 rotation over the course of the video. No camera movement occurs.\",\n    \"visual_style_and_color\": \"All elements use a neutral palette: matte whites, grays, and very soft shadows. The scene carries a technical, almost clinical atmosphere. There are no stylistic flourishes, maintaining rigor and clarity.\",\n    \"effects_and_post_processing\": \"No post-processing filters or enhancements are applied; any vignetting or color augmentation is strictly avoided. The output is sharply resolved yet minimal.\",\n    \"narrative_and_emotional_resonance\": \"The movement symbolizes objectivity and full technical disclosure, presenting the subject devoid of editorial interpretation or distraction\\u2014serving maximized instructional clarity and technical fidelity.\",\n    \"output_intent\": \"The resulting video exemplifies a high-contrast, unambiguous visual guide: functional, immediately actionable, and strictly aligned with instructional and technical goals.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"final_executable_video_prompt\": \"A single, realistic, full-body mannequin stands upright at the exact center of an empty, uniformly lit studio with a plain, matte light gray background. Camera is fixed in a straight-on, waist-to-head mid-shot framing. Lighting is white, soft, and evenly balanced, casting only a faint, soft shadow directly beneath the mannequin. The mannequin rotates smoothly and steadily in place, performing one complete 360-degree turn on its vertical axis over 5 seconds. No camera movement, no background details, no props, no visual effects, no filters, and no color grading. The only colors are matte whites and grays; no saturation or highlights. Scene is sterile, technical, and minimal, focused solely on the mannequin. All parameters are explicit.\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Amplifier B\n\nPerform a structural and semantic distillation of the input: locate its *driving aim*, isolate supporting momentum, and forcefully extrapolate it into a clarified directive. Treat the input as a living logic stream—trace the *underlying thesis*, and remove all extraneous elements. Amplify only what accelerates the goal. The output must **not reflect interpretation**, but **the core resolution** implied by the data’s internal logic. Execute as:\n\n`{role=instruction_amplifier_b;input=[original_text:str];process=[extract_underlying_thesis(),eliminate_distraction(),amplify_goal_drivers(),intensify_and_operationalize_purpose(),restructure_as_directive_output()];constraints=[preserve_input_telos(),avoid_additional_concepts(),maintaingoal_focused_structure()];requirements=[use_crystallized_command_language(),preserve_technical_fidelity(),ensure_actionability_of_output(),maximize_instructional_clarity()];output={clarified_directive:str}}`", "step": "b", "title": "Instruction Amplifier B", "input": "{\n  \"initial_prompt\": \"visualize_scene_and_subject_composition();use_crystallized_command_language();preserve_technical_fidelity();ensure_actionability_of_output();maximize_instructional_clarity();final_optimized_llm_videoprompt()\",\n  \"final_executable_video_prompt\": \"A single, realistic, full-body mannequin stands upright at the exact center of an empty, uniformly lit studio with a plain, matte light gray background. Camera is fixed in a straight-on, waist-to-head mid-shot framing. Lighting is white, soft, and evenly balanced, casting only a faint, soft shadow directly beneath the mannequin. The mannequin rotates smoothly and steadily in place, performing one complete 360-degree turn on its vertical axis over 5 seconds. No camera movement, no background details, no props, no visual effects, no filters, and no color grading. The only colors are matte whites and grays; no saturation or highlights. Scene is sterile, technical, and minimal, focused solely on the mannequin. All parameters are explicit.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"A single, realistic, full-body mannequin stands upright at the exact center of an empty, uniformly lit studio with a plain, matte light gray background. Camera is fixed in a straight-on, waist-to-head mid-shot framing. Lighting is white, soft, and evenly balanced, casting only a faint, soft shadow directly beneath the mannequin. The mannequin rotates smoothly and steadily in place, performing one complete 360-degree turn on its vertical axis over 5 seconds. No camera movement, no background details, no props, no visual effects, no filters, and no color grading. The only colors are matte whites and grays; no saturation or highlights. Scene is sterile, technical, and minimal, focused solely on the mannequin. All parameters are explicit.\",\n  \"process\": [\n    \"extract_underlying_thesis\",\n    \"eliminate_distraction\",\n    \"amplify_goal_drivers\",\n    \"intensify_and_operationalize_purpose\",\n    \"restructure_as_directive_output\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos\",\n    \"avoid_additional_concepts\",\n    \"maintaingoal_focused_structure\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language\",\n    \"preserve_technical_fidelity\",\n    \"ensure_actionability_of_output\",\n    \"maximize_instructional_clarity\"\n  ],\n  \"output\": {\n    \"clarified_directive\": \"Render a single, realistic full-body mannequin upright at dead center of a plain, matte light gray studio with uniform, soft, white lighting. Frame in fixed, straight-on waist-to-head mid-shot; maintain zero camera movement. Rotate mannequin 360° smoothly on vertical axis over 5 seconds. Ensure sterile, minimal scene—no background detail, props, effects, filters, or color grading. Use only matte white and gray tones; prohibit saturation and highlights. Prioritize maximum technical clarity and explicitness; focus exclusively on mannequin.\"\n  }\n}", "cost": 0.0}}}], "total_cost": 0.0}