{
    // Whether to track tab access times
    "track_tab_access_times": true,

    // Time in seconds after which a tab is considered inactive
    "inactive_threshold_seconds": 3600,

    // Time in seconds within which a file is considered short-lived
    "short_lived_threshold_seconds": 300,

    // Custom layouts for tab organization
    // Each layout should have a unique name and define cells, cols, rows, and nickname
    "custom_layouts": {
        "custom_1": {
            "cells": [[0, 0, 1, 1], [1, 0, 2, 1], [0, 1, 2, 2]],
            "cols": [0.0, 0.5, 1.0],
            "rows": [0.0, 0.3, 1.0],
            "nickname": "Two Columns on Top, One Wide Column Below"
        },
        "custom_2": {
            "cells": [[0, 0, 1, 2], [1, 0, 2, 1], [1, 1, 2, 2]],
            "cols": [0.0, 0.7, 1.0],
            "rows": [0.0, 0.5, 1.0],
            "nickname": "One Tall Column on Left, Two Columns on Right"
        },
        "special_1": {
            "cells": [
                [0, 0, 1, 1],
                [0, 1, 1, 3],
                [0, 3, 1, 4],
                [1, 0, 3, 1],
                [1, 3, 3, 4],
                [3, 0, 4, 1],
                [3, 1, 4, 3],
                [3, 3, 4, 4],
                [1, 1, 2, 3],
                [2, 1, 3, 2],
                [2, 2, 3, 3]
            ],
            "cols": [0.0, 0.0233136462543, 0.524246192105, 0.829654958036, 1.0],
            "rows": [0.0, 0.077817079333, 0.469176351693, 0.962102071753, 1.0],
            "nickname": "Complex Layout with 11 Cells"
        }
    },

    // Default columns for tab export
    "default_export_columns": [
        "Group", "Index", "File", "Ext", "Lines", "Size",
        "Modified", "Last Access", "Status", "Category", "Path"
    ],

    // Default layout for tab import (leave empty to use current layout)
    "default_import_layout": "",

    // Default file path for tab export/import
    "default_file_path": "tab_organization.md"
}
