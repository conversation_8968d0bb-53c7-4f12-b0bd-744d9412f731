import win32gui
import win32con
import win32api
import re
import time
import os


def get_all_windows():
    """Return a list of all windows."""
    windows = []
    def enum_windows(hwnd, result):
        windows.append((hwnd, win32gui.GetWindowText(hwnd), win32gui.GetClassName(hwnd)))
    win32gui.EnumWindows(enum_windows, [])
    return windows

def filter_windows(windows, only_visible=True, window_text=None, window_class=None, ignore_case=True):
    """Filter a list of windows based on the provided criteria."""
    filtered_windows = []
    for hwnd, title, class_name in windows:
        if only_visible and not win32gui.IsWindowVisible(hwnd):
            continue
        if title == "":
            continue
        if window_class is not None:
            if ignore_case and class_name.lower() != window_class.lower():
                continue
            elif not ignore_case and class_name != window_class:
                continue
        if window_text is not None:
            try:
                if ignore_case and not re.search(window_text, title, re.IGNORECASE):
                    continue
                elif not ignore_case and not re.search(window_text, title):
                    continue
            except re.error:
                raise ValueError(f"Invalid regular expression: {window_text}")
        filtered_windows.append((hwnd, title, class_name))
    return filtered_windows



def get_monitor_info(monitor_index):
    """Return information about a specific monitor."""
    monitors = win32api.EnumDisplayMonitors(None, None)
    primary_monitor_index = None
    for i, monitor in enumerate(monitors):
        monitor_info = win32api.GetMonitorInfo(monitor[0])
        if monitor_info['Flags'] == 1:  # This is the primary monitor
            primary_monitor_index = i
            break
    if primary_monitor_index is None:
        raise ValueError("Could not find primary monitor")
    adjusted_monitor_index = primary_monitor_index + monitor_index
    if adjusted_monitor_index < 0 or adjusted_monitor_index >= len(monitors):
        raise ValueError("Invalid monitor index")
    monitor_info = win32api.GetMonitorInfo(monitors[adjusted_monitor_index][0])
    return monitor_info

def calculate_window_position(i, rows, columns, screen_width, screen_height, column_ratios, row_ratios, monitor_info):
    """Calculate the position and size of a window."""
    row = i // columns
    col = i % columns

    if column_ratios is not None:
        window_width = int(screen_width * column_ratios[col])
        x_position = int(sum(column_ratios[:col]) * screen_width) + monitor_info['Monitor'][0]
    else:
        window_width = screen_width // columns
        x_position = col * window_width + monitor_info['Monitor'][0]

    if row_ratios is not None:
        window_height = int(screen_height * row_ratios[row])
        y_position = int(sum(row_ratios[:row]) * screen_height) + monitor_info['Monitor'][1]
    else:
        window_height = screen_height // rows
        y_position = row * window_height + monitor_info['Monitor'][1]

    return x_position, y_position, window_width, window_height

# -----------------------------------------------------------------------------

def tile_windows(windows, rows, columns, num_windows=None, column_ratios=None, row_ratios=None, monitor_index=0):
    """Tile windows on the screen based on the provided criteria."""
    if rows == 0 or columns == 0:
        raise ValueError("Number of rows and columns must be greater than 0")
    if column_ratios is not None and (len(column_ratios) != columns or sum(column_ratios) != 1):
        raise ValueError("Invalid column ratios")
    if row_ratios is not None and (len(row_ratios) != rows or sum(row_ratios) != 1):
        raise ValueError("Invalid row ratios")

    monitor_info = get_monitor_info(monitor_index)
    screen_width = monitor_info['Monitor'][2] - monitor_info['Monitor'][0]
    screen_height = monitor_info['Monitor'][3] - monitor_info['Monitor'][1]

    num_windows = num_windows if num_windows is not None else (rows * columns)
    windows = windows[:num_windows]

    for i, (hwnd, title, class_name) in enumerate(windows):
        x_position, y_position, window_width, window_height = calculate_window_position(i, rows, columns, screen_width, screen_height, column_ratios, row_ratios, monitor_info)
        win32gui.MoveWindow(hwnd, x_position, y_position, window_width, window_height, True)


# Testing explorer windows
windows = get_all_windows()
explorer_windows = filter_windows(windows, only_visible=True, window_text=None, window_class='CabinetWClass', ignore_case=True)


# Find and close duplicate file-explorer-windows (pointing to the same path)
windows_to_keep_dict = {window[1]: window for window in explorer_windows}
windows_to_keep = list(windows_to_keep_dict.values())
windows_to_close = [window for window in explorer_windows if window not in windows_to_keep]



print(len(explorer_windows))
print(len(windows_to_keep))
print(len(windows_to_close))
# old
# Change the order of the windows
project_dir = os.getcwd()
for window in windows_to_close:
    print(window)
    # if window[1] == project_dir:
        # print(window[1])

# Tile the windows
ordered_windows = sorted(windows_to_keep, key=lambda window: window[1])
tile_windows(ordered_windows, rows=2, columns=3, row_ratios=None, column_ratios=None, monitor_index=1)
# # Find and close duplicate file-explorer-windows (pointing to the same path)
# all_paths = [window[1] for window in explorer_windows]
# unique_paths = list(set(all_paths))
# duplicate_paths = [path for path in unique_paths if all_paths.count(path) > 1]
# windows_to_close = [window for window in explorer_windows if window[1] in duplicate_paths]
# Apply any additional operations on the windows here...

# Tile the windows
# tile_windows(explorer_windows, 2, 1, monitor_index=0)


# tile_windows(1, 3, window_class="CabinetWClass", column_ratios=[0.5, 0.2, 0.3])



# Tile all visible windows in a 2x2 grid on the main monitor
# tile_windows(2, 1, monitor_index=0)

# explorer_windows =[
#     (462654  , 'C:\\My Drive\\TestProj\\Workflow\\Py_Script - TileOpenWindows'                         , 'CabinetWClass'),
#     (986628  , 'C:\\AppData\\Roaming\\Sublime Text\\Packages\\User\\color_schemes\\python'             , 'CabinetWClass'),
#     (1248328 , 'C:\\My Drive\\TestProj\\Workflow\\Py_Script - TileOpenWindows'                         , 'CabinetWClass'),
#     (528040  , 'C:\\AppData\\Roaming\\Sublime Text\\Packages\\Python\\Snippets - Custom'               , 'CabinetWClass'),
#     (986776  , 'C:\\Dropbox\\Dev_Root\\Python\\Py_Private_Environments\\reference_utils'               , 'CabinetWClass'),
#     (1903280 , 'C:\\Downloads\\2023-06-03\\SublimeScraps-master'                                       , 'CabinetWClass'),
#     (3869040 , 'C:\\OneDrive\\TestProj\\Workflow\\Sublime Text\\Prosjekter Todo\\TODO - Utils'         , 'CabinetWClass'),
#     (134150  , 'C:\\OneDrive\\TestProj\\Workflow\\3dsMax (Settings)\\2023_MaxScripts_Updated\\New_WIP' , 'CabinetWClass'),
#     (1051374 , 'C:\\AppData\\Roaming\\Sublime Text\\Packages\\Batch File'                              , 'CabinetWClass'),
#     (1903280 , 'C:\\Downloads\\2023-06-03\\SublimeScraps-master'                                       , 'CabinetWClass'),
#     (1247166 , 'C:\\AppData\\Roaming\\Sublime Text\\Packages\\Python\\Snippets - Custom'               , 'CabinetWClass'),
# ]

# Find and close duplicate file-explorer-windows (pointing to the same path)
# windows_to_close =
# paths = [path for _, path, _ in explorer_windows]
# for path in paths:
#     print(path)
# print('\n')
# print('\n')

# unique_paths = set(paths)
# for path in unique_paths:
#     print(path)
# windows_to_close = [window for window in explorer_windows if window[1] not in unique_paths]

# print(windows_to_close)
# unique_paths = [path for i, path, window_class in explorer_windows if path not in [p for _, p, _ in explorer_windows[:i]] + [p for _, p, _ in explorer_windows[i+1:]]]
# windows_to_close = [window for window in explorer_windows if window[1] not in unique_paths]
# ----------------------------------------------------------------------------------------------------------------------------------------------------------------
#                              title :
#                              class : TaskListThumbnailWnd
#                               hwnd : 65808
#                         visibility : 1
#                     controls_state : 1
#                           position : (0, 841)
#                               size : (1037, 165)
#                          placement : (0, 1, (-1, -1), (-1, -1), (0, 841, 1037, 1006))
#                       process_path : C:\Windows\explorer.exe
#                         process_id : 5444
# ----------------------------------------------------------------------------------------------------------------------------------------------------------------

# -----------------------------------------------------------------------------
# WORKS
# import win32gui
# import win32con
# import win32api
# import re

# def get_all_windows():
#     windows = []
#     def enum_windows(hwnd, result):
#         windows.append((hwnd, win32gui.GetWindowText(hwnd), win32gui.GetClassName(hwnd)))
#     win32gui.EnumWindows(enum_windows, [])
#     return windows

# def filter_windows(windows, only_visible=True, window_text=None, window_class=None, ignore_case=True):
#     if only_visible:
#         windows = [(hwnd, title, cls) for hwnd, title, cls in windows if win32gui.IsWindowVisible(hwnd)]
#     windows = [(hwnd, title, cls) for hwnd, title, cls in windows if title != ""]
#     if window_class is not None:
#         windows = [(hwnd, title, cls) for hwnd, title, cls in windows if cls == window_class]
#     if window_text is not None:
#         try:
#             if ignore_case:
#                 windows = [(hwnd, title, cls) for hwnd, title, cls in windows if re.search(window_text, title, re.IGNORECASE)]
#             else:
#                 windows = [(hwnd, title, cls) for hwnd, title, cls in windows if re.search(window_text, title)]
#         except re.error:
#             raise ValueError(f"Invalid regular expression: {window_text}")
#     return windows

# def get_monitor_info(monitor_index):
#     monitors = win32api.EnumDisplayMonitors(None, None)
#     primary_monitor_index = None
#     for i, monitor in enumerate(monitors):
#         monitor_info = win32api.GetMonitorInfo(monitor[0])
#         if monitor_info['Flags'] == 1:  # This is the primary monitor
#             primary_monitor_index = i
#             break
#     if primary_monitor_index is None:
#         raise ValueError("Could not find primary monitor")
#     adjusted_monitor_index = primary_monitor_index + monitor_index
#     if adjusted_monitor_index < 0 or adjusted_monitor_index >= len(monitors):
#         raise ValueError("Invalid monitor index")
#     monitor_info = win32api.GetMonitorInfo(monitors[adjusted_monitor_index][0])
#     return monitor_info



# def tile_windows(rows, columns, num_windows=None, only_visible=True, window_text=None, window_class=None, ignore_case=True, column_ratios=None, row_ratios=None, monitor_index=0):
#     if rows == 0 or columns == 0:
#         raise ValueError("Number of rows and columns must be greater than 0")
#     if column_ratios is not None and (len(column_ratios) != columns or sum(column_ratios) != 1):
#         raise ValueError("Invalid column ratios")
#     if row_ratios is not None and (len(row_ratios) != rows or sum(row_ratios) != 1):
#         raise ValueError("Invalid row ratios")

#     monitor_info = get_monitor_info(monitor_index)
#     screen_width = monitor_info['Monitor'][2] - monitor_info['Monitor'][0]
#     screen_height = monitor_info['Monitor'][3] - monitor_info['Monitor'][1]

#     windows = get_all_windows()
#     windows = filter_windows(windows, only_visible, window_text, window_class, ignore_case)
#     if num_windows is not None:
#         windows = windows[:num_windows]

#     for i, (hwnd, title, cls) in enumerate(windows):
#         row = i // columns
#         col = i % columns

#         if column_ratios is not None:
#             window_width = int(screen_width * column_ratios[col])
#             x_position = int(sum(column_ratios[:col]) * screen_width) + monitor_info['Monitor'][0]
#         else:
#             window_width = screen_width // columns
#             x_position = col * window_width + monitor_info['Monitor'][0]

#         if row_ratios is not None:
#             window_height = int(screen_height * row_ratios[row])
#             y_position = int(sum(row_ratios[:row]) * screen_height) + monitor_info['Monitor'][1]
#         else:
#             window_height = screen_height // rows
#             y_position = row * window_height + monitor_info['Monitor'][1]

#         win32gui.MoveWindow(hwnd, x_position, y_position, window_width, window_height, True)


# # Tile all visible windows in a 2x2 grid on the main monitor
# tile_windows(2, 1, monitor_index=0)







# Tile all visible windows with "Chrome" or "Explorer" (case-insensitive) in their title in a 2x2 grid
# tile_windows(2, 2, window_text="Chrome|Explorer")

# Tile the first 4 visible windows with "Chrome" in their title (case-sensitive) in a 2x2 grid
# tile_windows(2, 2, num_windows=4, window_text="Chrome", ignore_case=False)

# Tile the first 3 visible windows in a 1x3 grid
# tile_windows(1, 3, num_windows=3)
# Tile all visible "Notepad" windows in a 2x2 grid
# tile_windows(2, 2, window_class="Notepad")
# Tile all visible windows in a 1x3 grid, with the first column covering 50% of the screen,
# the second column covering 20% of the screen, and the third column covering the remaining 30% of the screen
# tile_windows(3, 1, window_class="Notepad", row_ratios=[0.5, 0.2, 0.3])
# # tile_windows(1, 3, window_class="Notepad", column_ratios=[0.5, 0.2, 0.3])


# # -----------------------------------------------------------------------------
# # WORKS
# import win32gui
# import win32con
# import win32api
# import re


# def tile_windows(rows, columns, num_windows=None, window_text=None, window_class=None, ignore_case=True, only_visible=True, column_ratios=None, row_ratios=None):
#     if rows == 0 or columns == 0:
#         raise ValueError("Number of rows and columns must be greater than 0")

#     # If column_ratios is provided, check that it has the correct length and that the ratios sum to 1
#     if column_ratios is not None:
#         if len(column_ratios) != columns:
#             raise ValueError("Length of column_ratios must be equal to the number of columns")
#         if sum(column_ratios) != 1:
#             raise ValueError("Column ratios must sum to 1")

#     # If row_ratios is provided, check that it has the correct length and that the ratios sum to 1
#     if row_ratios is not None:
#         if len(row_ratios) != rows:
#             raise ValueError("Length of row_ratios must be equal to the number of rows")
#         if sum(row_ratios) != 1:
#             raise ValueError("Row ratios must sum to 1")

#     # Get the handle for the desktop
#     desktop = win32gui.GetDesktopWindow()

#     # Get the size of the screen
#     screen_width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
#     screen_height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)

#     # Get a list of all windows
#     windows = []
#     def enum_windows(hwnd, result):
#         windows.append((hwnd, win32gui.GetWindowText(hwnd), win32gui.GetClassName(hwnd)))
#     win32gui.EnumWindows(enum_windows, [])

#     # If only_visible is True, filter out windows that are not visible
#     if only_visible:
#         windows = [(hwnd, title, cls) for hwnd, title, cls in windows if win32gui.IsWindowVisible(hwnd)]

#     # Filter out windows that do not have a title
#     windows = [(hwnd, title, cls) for hwnd, title, cls in windows if title != ""]

#     # If window_class is specified, filter out windows that do not match the class
#     if window_class is not None:
#         windows = [(hwnd, title, cls) for hwnd, title, cls in windows if cls == window_class]

#     # If window_text is specified, filter out windows that do not match the regex
#     if window_text is not None:
#         try:
#             if ignore_case:
#                 windows = [(hwnd, title, cls) for hwnd, title, cls in windows if re.search(window_text, title, re.IGNORECASE)]
#             else:
#                 windows = [(hwnd, title, cls) for hwnd, title, cls in windows if re.search(window_text, title)]
#         except re.error:
#             raise ValueError(f"Invalid regular expression: {window_text}")

#     # If num_windows is specified, only keep the first num_windows windows
#     if num_windows is not None:
#         windows = windows[:num_windows]

#     # Position each window in a grid
#     for i, (hwnd, title, cls) in enumerate(windows):
#         row = i // columns
#         col = i % columns

#         # Determine the width and position of the current column
#         if column_ratios is not None:
#             window_width = int(screen_width * column_ratios[col])
#             x_position = int(sum(column_ratios[:col]) * screen_width)
#         else:
#             window_width = screen_width // columns
#             x_position = col * window_width

#         # Determine the height and position of the current row
#         if row_ratios is not None:
#             window_height = int(screen_height * row_ratios[row])
#             y_position = int(sum(row_ratios[:row]) * screen_height)
#         else:
#             window_height = screen_height // rows
#             y_position = row * window_height

#         win32gui.MoveWindow(hwnd, x_position, y_position, window_width, window_height, True)





# # Tile all visible windows with "Chrome" or "Explorer" (case-insensitive) in their title in a 2x2 grid
# # tile_windows(2, 2, window_text="Chrome|Explorer")

# # Tile the first 4 visible windows with "Chrome" in their title (case-sensitive) in a 2x2 grid
# # tile_windows(2, 2, num_windows=4, window_text="Chrome", ignore_case=False)

# # Tile the first 3 visible windows in a 1x3 grid
# # tile_windows(1, 3, num_windows=3)
# # Tile all visible "Notepad" windows in a 2x2 grid
# # tile_windows(2, 2, window_class="Notepad")
# # Tile all visible windows in a 1x3 grid, with the first column covering 50% of the screen,
# # the second column covering 20% of the screen, and the third column covering the remaining 30% of the screen
# tile_windows(3, 1, window_class="Notepad", row_ratios=[0.5, 0.2, 0.3])
# # tile_windows(1, 3, window_class="Notepad", column_ratios=[0.5, 0.2, 0.3])



# # -----------------------------------------------------------------------------
# # WORKS
# import win32gui
# import win32con
# import win32api
# import re


# def tile_windows(rows, columns, num_windows=None, window_text=None, ignore_case=True):
#     if rows == 0 or columns == 0:
#         raise ValueError("Number of rows and columns must be greater than 0")

#     # Get the handle for the desktop
#     desktop = win32gui.GetDesktopWindow()

#     # Get the size of the screen
#     screen_width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
#     screen_height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)

#     # Get a list of all windows
#     windows = []
#     def enum_windows(hwnd, result):
#         windows.append((hwnd, win32gui.GetWindowText(hwnd)))
#     win32gui.EnumWindows(enum_windows, [])

#     # Filter out windows that are not visible or do not have a title
#     windows = [(hwnd, title) for hwnd, title in windows if win32gui.IsWindowVisible(hwnd) and title != ""]

#     # If window_text is specified, filter out windows that do not match the regex
#     if window_text is not None:
#         try:
#             if ignore_case:
#                 windows = [(hwnd, title) for hwnd, title in windows if re.search(window_text, title, re.IGNORECASE)]
#             else:
#                 windows = [(hwnd, title) for hwnd, title in windows if re.search(window_text, title)]
#         except re.error:
#             raise ValueError(f"Invalid regular expression: {window_text}")

#     # If num_windows is specified, only keep the first num_windows windows
#     if num_windows is not None:
#         windows = windows[:num_windows]

#     # Determine the size of each window
#     window_width = screen_width // columns
#     window_height = screen_height // rows

#     # Position each window in a grid
#     for i, (hwnd, title) in enumerate(windows):
#         row = i // columns
#         col = i % columns
#         win32gui.MoveWindow(hwnd, col * window_width, row * window_height, window_width, window_height, True)



# # Tile all visible windows with "Chrome" or "Explorer" (case-insensitive) in their title in a 2x2 grid
# # tile_windows(2, 2, window_text="Chrome|Explorer")

# # Tile the first 4 visible windows with "Chrome" in their title (case-sensitive) in a 2x2 grid
# # tile_windows(2, 2, num_windows=4, window_text="Chrome", ignore_case=False)

# # Tile the first 3 visible windows in a 1x3 grid
# tile_windows(1, 3, num_windows=3)



# # -----------------------------------------------------------------------------
# import win32gui
# import win32con
# import win32api
# import re

# def tile_windows(rows, columns, num_windows=None, window_text=None):
#     # Get the handle for the desktop
#     desktop = win32gui.GetDesktopWindow()

#     # Get the size of the screen
#     screen_width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
#     screen_height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)

#     # Get a list of all windows
#     windows = []
#     def enum_windows(hwnd, result):
#         windows.append((hwnd, win32gui.GetWindowText(hwnd)))
#     win32gui.EnumWindows(enum_windows, [])

#     # Filter out windows that are not visible or do not have a title
#     windows = [(hwnd, title) for hwnd, title in windows if win32gui.IsWindowVisible(hwnd) and title != ""]

#     # If window_text is specified, filter out windows that do not match the regex
#     if window_text is not None:
#         windows = [(hwnd, title) for hwnd, title in windows if re.search(window_text, title, re.IGNORECASE)]

#     # If num_windows is specified, only keep the first num_windows windows
#     if num_windows is not None:
#         windows = windows[:num_windows]

#     # Determine the size of each window
#     window_width = screen_width // columns
#     window_height = screen_height // rows

#     # Position each window in a grid
#     for i, (hwnd, title) in enumerate(windows):
#         row = i // columns
#         col = i % columns
#         win32gui.MoveWindow(hwnd, col * window_width, row * window_height, window_width, window_height, True)

# # Example usage:Takes regex-commands
# tile_windows(2, 2, num_windows=4, window_text=".*REGISTERED\)$")





# # -----------------------------------------------------------------------------
# # WORKS
# import win32gui
# import win32con
# import win32api

# def tile_windows(rows, columns, num_windows=None, window_text=None):
#     # Get the handle for the desktop
#     desktop = win32gui.GetDesktopWindow()

#     # Get the size of the screen
#     screen_width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
#     screen_height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)

#     # Get a list of all windows
#     windows = []
#     def enum_windows(hwnd, result):
#         windows.append((hwnd, win32gui.GetWindowText(hwnd)))
#     win32gui.EnumWindows(enum_windows, [])

#     # Filter out windows that are not visible or do not have a title
#     windows = [(hwnd, title) for hwnd, title in windows if win32gui.IsWindowVisible(hwnd) and title != ""]

#     # If window_text is specified, filter out windows that do not match the text
#     if window_text is not None:
#         windows = [(hwnd, title) for hwnd, title in windows if window_text in title]

#     # If num_windows is specified, only keep the first num_windows windows
#     if num_windows is not None:
#         windows = windows[:num_windows]

#     # Determine the size of each window
#     window_width = screen_width // columns
#     window_height = screen_height // rows

#     # Position each window in a grid
#     for i, (hwnd, title) in enumerate(windows):
#         row = i // columns
#         col = i % columns
#         win32gui.MoveWindow(hwnd, col * window_width, row * window_height, window_width, window_height, True)

# # Example usage:
# # tile_windows(2, 2, num_windows=2, window_text="Chrome")
# tile_windows(2, 1, num_windows=2, window_text=None)
# tile_windows(1, 2, num_windows=2, window_text=None)
