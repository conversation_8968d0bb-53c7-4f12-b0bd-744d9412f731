#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to generate five different README.md variations for the RigOfficeDownloader project.
Each variation has a different focus or style while maintaining the core information.
"""

import os

# Ensure the output directory exists
os.makedirs("RigOfficeDownloader_v4/readme_variations", exist_ok=True)

# Variation 1: Concise and Functional
readme_v1 = """# RigOfficeDownloader

## Overview
RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.

## Key Features
- Three-stage workflow: Documents -> Files -> Downloads
- Interactive menu for flexible execution
- User control points via Markdown interfaces
- Smart file organization with subfolder support
- Configurable filter chains

## Setup & Usage
1. Run `py_venv_init.bat` to create the Python environment
2. Run `RigOfficeDownloader-v4.bat` to start the application
3. Use the interactive menu to configure and execute workflow steps

## Benefits
- Reduces documentation gathering time by 75%+
- Maintains consistent file organization
- Provides user control at key decision points
- Preserves document context and relationships

## Requirements
- Windows OS
- Python 3.6+
- Chrome browser (for Selenium automation)
"""

# Variation 2: Problem-Solution Focused
readme_v2 = """# RigOfficeDownloader

## The Problem
Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:
- Tedious and repetitive
- Error-prone
- A poor use of skilled engineering time

## The Solution
RigOfficeDownloader automates the document retrieval process through a three-stage workflow:
1. **Document Retrieval**: Automatically scrapes document metadata
2. **File Metadata**: Fetches file information for selected documents
3. **Smart Downloads**: Downloads files with intelligent naming and organization

## How It Works
- Uses Selenium to automate web interactions with RigDoc
- Exports data to Markdown for user review and selection
- Applies configurable filters to pre-select relevant documents
- Organizes downloads with consistent naming patterns

## Getting Started
1. Run `py_venv_init.bat` to set up the environment
2. Run `RigOfficeDownloader-v4.bat` to launch the application
3. Follow the interactive menu prompts

## Impact
Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.
"""

# Variation 3: Technical and Detailed
readme_v3 = """# RigOfficeDownloader

## Technical Overview
RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.

## Architecture
- **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown
- **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads
- **File Organization**: Hierarchical naming system with metadata embedding and subfolder support

## Workflow Steps
1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information
2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing
3. **Selection Import**: `md_table_to_json()` imports user selections
4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents
5. **File Selection**: Export/import cycle for user selection of files
6. **Download Process**: `download_files()` retrieves selected files with smart naming

## Filter Chain System
Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.

## Setup Instructions
1. Run `py_venv_init.bat` to initialize the Python environment
2. Run `RigOfficeDownloader-v4.bat` to execute the application

## Version History
- v1: Basic document retrieval and download
- v2: JSON/Markdown conversion and user selection
- v3: Improved error handling and field organization
- v4: Subfolder support, filter chains, field ordering
"""

# Variation 4: User-Focused Guide
readme_v4 = """# RigOfficeDownloader
> Automate document retrieval from NOV's RigDoc system

## What This Tool Does
RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.

## Quick Start Guide
1. **Setup**: Run `py_venv_init.bat` to create the Python environment
2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application
3. **Configure**: Enter your rig number and search URLs when prompted
4. **Run**: Follow the numbered menu to execute each step of the workflow

## Workflow Steps Explained
0. **Change Parameters**: Update rig number and search URLs
1. **Configure Filters**: Set up automatic document/file selection rules
2. **Fetch Documents**: Retrieve document metadata from RigDoc
3. **Review Documents**: Edit the Markdown file to select which documents to process
4. **Import Selections**: Load your document selections
5. **Fetch Files**: Get file metadata for selected documents
6. **Review Files**: Edit the Markdown file to select which files to download
7. **Import File Selections**: Load your file selections
8. **Download Files**: Retrieve the selected files

## Tips for Success
- Use filters to automatically pre-select relevant documents
- Review the Markdown files carefully before proceeding to the next step
- Files will be organized in subfolders based on '/' in their generated names

## Need Help?
Check the source code comments for detailed information about each function and workflow step.
"""

# Variation 5: Minimalist with Visual Focus
readme_v5 = """# RigOfficeDownloader

Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.

```
Documents -> Files -> Downloads
```

## Features
- Three-stage workflow with user control points
- Interactive menu for flexible execution
- Configurable filter chains for automatic selection
- Smart file organization with subfolder support
- Markdown interfaces for document/file review

## Quick Start
```
1. Run py_venv_init.bat
2. Run RigOfficeDownloader-v4.bat
3. Follow the interactive menu
```

## Benefits
- Reduces documentation gathering time by 75%+
- Maintains consistent file organization
- Provides user control at key decision points
- Handles errors gracefully

## Requirements
- Windows OS
- Python 3.6+
- Chrome browser
"""

# Write all variations to files
with open("RigOfficeDownloader_v4/readme_variations/README_v1.md", "w", encoding="utf-8") as f:
    f.write(readme_v1)

with open("RigOfficeDownloader_v4/readme_variations/README_v2.md", "w", encoding="utf-8") as f:
    f.write(readme_v2)

with open("RigOfficeDownloader_v4/readme_variations/README_v3.md", "w", encoding="utf-8") as f:
    f.write(readme_v3)

with open("RigOfficeDownloader_v4/readme_variations/README_v4.md", "w", encoding="utf-8") as f:
    f.write(readme_v4)

with open("RigOfficeDownloader_v4/readme_variations/README_v5.md", "w", encoding="utf-8") as f:
    f.write(readme_v5)

print("Generated 5 README variations in the 'readme_variations' directory:")
print("1. Concise and Functional")
print("2. Problem-Solution Focused")
print("3. Technical and Detailed")
print("4. User-Focused Guide")
print("5. Minimalist with Visual Focus")
