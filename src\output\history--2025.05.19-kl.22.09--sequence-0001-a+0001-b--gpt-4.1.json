{"initial_prompt": "rephrase into a generalized and clarified sentence that describes the purpose of the utility in a single unbroken line of less than 100 characters: '''RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\n\nRigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\n\n> Automate document retrieval from NOV's RigDoc system\n\nRigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\n\nAutomate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\n\n- Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\n- Use case: curate, review, and batch-download rig-related documents and technical files.\n\n> **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.\n\nRigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents → Files → Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.\n\nTo automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\n\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).'''", "sequence_id": "sequence-0001-a+0001-b", "results": [{"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "rephrase into a generalized and clarified sentence that describes the purpose of the utility in a single unbroken line of less than 100 characters: '''RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\n\nRigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\n\n> Automate document retrieval from NOV's RigDoc system\n\nRigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\n\nAutomate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\n\n- Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\n- Use case: curate, review, and batch-download rig-related documents and technical files.\n\n> **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.\n\nRigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents → Files → Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.\n\nTo automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\n\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).'''", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Summarize the utility's purpose in a single clear sentence under 100 characters, describing its automation of document retrieval from NOV's RigDoc system and its role in saving engineering time.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "rephrase into a generalized and clarified sentence that describes the purpose of the utility in a single unbroken line of less than 100 characters: '''RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\n\nRigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\n\n> Automate document retrieval from NOV's RigDoc system\n\nRigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\n\nAutomate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\n\n- Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\n- Use case: curate, review, and batch-download rig-related documents and technical files.\n\n> **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.\n\nRigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents → Files → Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.\n\nTo automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.\n\nThis utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).'''", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": \"Automates document retrieval from NOV's RigDoc system to save time and streamline workflows.\"\n}", "cost": 0.0}}}], "total_cost": 0.0}