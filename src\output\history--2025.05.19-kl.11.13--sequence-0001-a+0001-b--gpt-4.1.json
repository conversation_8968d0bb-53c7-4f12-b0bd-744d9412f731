{"initial_prompt": "# GOAL: CREATE A STRUCTURED README.MD <70 LINES\n\n\n# INPUT: Please create a single README.md based on taking the best from each part of these variations:\n\n    # Dir `readme_variations`\n\n    ### File Structure\n\n    ```\n    ├── README_v1.md\n    ├── README_v2.md\n    ├── README_v3.md\n    ├── README_v4.md\n    ├── README_v5.md\n    ├── README_v6.md\n    └── README_v7.md\n    ```\n\n    ---\n\n    #### `README_v1.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        ## Overview\n        RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\n\n        ## Key Features\n        - Three-stage workflow: Documents -> Files -> Downloads\n        - Interactive menu for flexible execution\n        - User control points via Markdown interfaces\n        - Smart file organization with subfolder support\n        - Configurable filter chains\n\n        ## Setup & Usage\n        1. Run `py_venv_init.bat` to create the Python environment\n        2. Run `RigOfficeDownloader-v4.bat` to start the application\n        3. Use the interactive menu to configure and execute workflow steps\n\n        ## Benefits\n        - Reduces documentation gathering time by 75%+\n        - Maintains consistent file organization\n        - Provides user control at key decision points\n        - Preserves document context and relationships\n\n        ## Requirements\n        - Windows OS\n        - Python 3.6+\n        - Chrome browser (for Selenium automation)\n    ```\n\n    ---\n\n    #### `README_v2.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        ## The Problem\n        Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:\n        - Tedious and repetitive\n        - Error-prone\n        - A poor use of skilled engineering time\n\n        ## The Solution\n        RigOfficeDownloader automates the document retrieval process through a three-stage workflow:\n        1. **Document Retrieval**: Automatically scrapes document metadata\n        2. **File Metadata**: Fetches file information for selected documents\n        3. **Smart Downloads**: Downloads files with intelligent naming and organization\n\n        ## How It Works\n        - Uses Selenium to automate web interactions with RigDoc\n        - Exports data to Markdown for user review and selection\n        - Applies configurable filters to pre-select relevant documents\n        - Organizes downloads with consistent naming patterns\n\n        ## Getting Started\n        1. Run `py_venv_init.bat` to set up the environment\n        2. Run `RigOfficeDownloader-v4.bat` to launch the application\n        3. Follow the interactive menu prompts\n\n        ## Impact\n        Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.\n    ```\n\n    ---\n\n    #### `README_v3.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        ## Technical Overview\n        RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\n\n        ## Architecture\n        - **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown\n        - **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads\n        - **File Organization**: Hierarchical naming system with metadata embedding and subfolder support\n\n        ## Workflow Steps\n        1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information\n        2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing\n        3. **Selection Import**: `md_table_to_json()` imports user selections\n        4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents\n        5. **File Selection**: Export/import cycle for user selection of files\n        6. **Download Process**: `download_files()` retrieves selected files with smart naming\n\n        ## Filter Chain System\n        Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.\n\n        ## Setup Instructions\n        1. Run `py_venv_init.bat` to initialize the Python environment\n        2. Run `RigOfficeDownloader-v4.bat` to execute the application\n\n        ## Version History\n        - v1: Basic document retrieval and download\n        - v2: JSON/Markdown conversion and user selection\n        - v3: Improved error handling and field organization\n        - v4: Subfolder support, filter chains, field ordering\n    ```\n\n    ---\n\n    #### `README_v4.md`\n\n    ```markdown\n        # RigOfficeDownloader\n        > Automate document retrieval from NOV's RigDoc system\n\n        ## What This Tool Does\n        RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\n\n        ## Quick Start Guide\n        1. **Setup**: Run `py_venv_init.bat` to create the Python environment\n        2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application\n        3. **Configure**: Enter your rig number and search URLs when prompted\n        4. **Run**: Follow the numbered menu to execute each step of the workflow\n\n        ## Workflow Steps Explained\n        0. **Change Parameters**: Update rig number and search URLs\n        1. **Configure Filters**: Set up automatic document/file selection rules\n        2. **Fetch Documents**: Retrieve document metadata from RigDoc\n        3. **Review Documents**: Edit the Markdown file to select which documents to process\n        4. **Import Selections**: Load your document selections\n        5. **Fetch Files**: Get file metadata for selected documents\n        6. **Review Files**: Edit the Markdown file to select which files to download\n        7. **Import File Selections**: Load your file selections\n        8. **Download Files**: Retrieve the selected files\n\n        ## Tips for Success\n        - Use filters to automatically pre-select relevant documents\n        - Review the Markdown files carefully before proceeding to the next step\n        - Files will be organized in subfolders based on '/' in their generated names\n\n        ## Need Help?\n        Check the source code comments for detailed information about each function and workflow step.\n    ```\n\n    ---\n\n    #### `README_v5.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\n\n        ```\n        Documents -> Files -> Downloads\n        ```\n\n        ## Features\n        - Three-stage workflow with user control points\n        - Interactive menu for flexible execution\n        - Configurable filter chains for automatic selection\n        - Smart file organization with subfolder support\n        - Markdown interfaces for document/file review\n\n        ## Quick Start\n        ```\n        1. Run py_venv_init.bat\n        2. Run RigOfficeDownloader-v4.bat\n        3. Follow the interactive menu\n        ```\n\n        ## Benefits\n        - Reduces documentation gathering time by 75%+\n        - Maintains consistent file organization\n        - Provides user control at key decision points\n        - Handles errors gracefully\n\n        ## Requirements\n        - Windows OS\n        - Python 3.6+\n        - Chrome browser\n    ```\n\n    ---\n\n    #### `README_v6.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.\n\n        ## Key Features\n        - **Three-Stage Workflow**: Document selection → File selection → Download\n        - **Metadata Preservation**: Structured naming with revision/case numbers\n        - **Interactive Review**: Markdown tables for manual inclusion flags\n        - **Smart Organization**: Automatic subfolder creation via naming patterns\n        - **Filter System**: Pattern-based inclusion/exclusion rules\n        - **Browser Automation**: Smart waiting strategies and session management\n\n        ## Workflow Process\n        ```python\n        1. Fetch Documents       # Scrape metadata → <rig>-a-docs.json\n        2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true\n        3. Import Selections     # Update JSON with user choices\n        4. Fetch Files           # Get file listings → <rig>-b-files.json\n        5. Export Files MD       # <rig>-b-files.md - Set item_download=true\n        6. Import File Choices   # Update file selections\n        7. Download Files        # Auto-organized with subfolder support\n        ```\n\n        ## Configuration (CONFIG Section)\n        ```python\n        {\n            \"rig_number\": \"R0000.020\",  # Target rig ID\n            \"search_urls\": [            # Predefined search templates\n                \"https://rigdoc.nov.com/search/rigsearch?q=...\",\n                \"https://rigdoc.nov.com/search/rigsearch?q=...\"\n            ],\n            \"filters\": [                # Sequential processing rules\n                {\n                    \"type\": \"docs\",     # Apply to documents/files\n                    \"pattern\": \"*DRILL*FLOOR*\",  # Glob-style matching\n                    \"field\": \"item_include\",     # Field to modify\n                    \"value\": True       # Set True/False based on match\n                }\n            ]\n        }\n        ```\n\n        ## Setup & Usage\n        ```bash\n        # Initialize environment\n        py_venv_init.bat\n        py_venv_pip_install.bat\n\n        # Run modes\n        RigOfficeDownloader-v4.py [rig_number] [mode]\n\n        Modes:\n        --auto         # Full automation\n        --interactive  # Step-by-step control\n        --config       # Modify search templates/filters\n        ```\n\n        ## Key Configuration Patterns\n        - **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)\n        - **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)\n        - **File Types**: Auto-prioritize PDFs with `*.pdf` pattern\n\n        ## Requirements\n        - Chrome Browser + ChromeDriver\n        - Active RigDoc credentials\n        - Python 3.8+ with dependencies from requirements.txt\n        - Network access to rigdoc.nov.com\n\n        ## Advanced Features\n        - **Path Sanitization**: Auto-clean special chars while preserving /\n        - **Deduplication**: Hash-based conflict resolution\n        - **Field Ordering**: Customizable JSON/Markdown columns\n        - **Smart Scrolling**: Progressive page loading detection\n\n        > Reduces documentation prep time by 60-75% compared to manual retrieval\n        > Version 4.0 | Active development with subfolder support\n    ```\n\n    ---\n\n    #### `README_v7.md`\n\n    ```markdown\n\n        ## Overview\n        - Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\n        - Use case: curate, review, and batch-download rig-related documents and technical files.\n\n        ## Directory Structure\n\n        * `outputs/` (BASE\\_OUTPUT): All results stored here\n        * `outputs/data/` (DATA\\_DIR): Document and file metadata (JSON/MD)\n        * `outputs/downloads/` (DL\\_DIR): Downloaded PDF and file outputs\n\n        ## Pipeline Overview\n\n        1. Change search parameters (rig number, URLs)\n        2. Configure filter chain (add, edit, delete, toggle, reorder filters)\n        3. Fetch docs (scrape data from rigdoc.nov.com)\n        4. Export docs to Markdown (for selection/editing)\n        5. Import docs from Markdown (sync edited selection)\n        6. Fetch candidate files linked to selected docs\n        7. Export file list to Markdown (for editing/selecting files for download)\n        8. Import updated file list from Markdown\n        9. Download marked files (PDFs only, via Chrome)\n\n        ## Manual Editing\n\n        * Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing\n        * Set `item_include` (docs) and `item_download` (files) fields\n\n        ## Running the Tool\n\n        ```bash\n        python rigdocscraper.py\n        ```\n\n        * Interactive menu enables step selection (numbers/comma/space-separated)\n        * Supports adjusting parameters, filter configuration, and reviewing batch steps\n        * Prompts will guide through editing, import/export, and download procedures\n\n        ## Troubleshooting\n\n        * Requires functioning Chrome installation; verify webdriver-manager compatibility\n        * Common issues: browser launch failures, login/captcha requirements, file permissions\n        * Output logs and warnings shown in terminal; inspect `outputs/data/` for progress\n    ```\n", "sequence_id": "sequence-0001-a+0001-b", "results": [{"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "# GOAL: CREATE A STRUCTURED README.MD <70 LINES\n\n\n# INPUT: Please create a single README.md based on taking the best from each part of these variations:\n\n    # Dir `readme_variations`\n\n    ### File Structure\n\n    ```\n    ├── README_v1.md\n    ├── README_v2.md\n    ├── README_v3.md\n    ├── README_v4.md\n    ├── README_v5.md\n    ├── README_v6.md\n    └── README_v7.md\n    ```\n\n    ---\n\n    #### `README_v1.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        ## Overview\n        RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\n\n        ## Key Features\n        - Three-stage workflow: Documents -> Files -> Downloads\n        - Interactive menu for flexible execution\n        - User control points via Markdown interfaces\n        - Smart file organization with subfolder support\n        - Configurable filter chains\n\n        ## Setup & Usage\n        1. Run `py_venv_init.bat` to create the Python environment\n        2. Run `RigOfficeDownloader-v4.bat` to start the application\n        3. Use the interactive menu to configure and execute workflow steps\n\n        ## Benefits\n        - Reduces documentation gathering time by 75%+\n        - Maintains consistent file organization\n        - Provides user control at key decision points\n        - Preserves document context and relationships\n\n        ## Requirements\n        - Windows OS\n        - Python 3.6+\n        - Chrome browser (for Selenium automation)\n    ```\n\n    ---\n\n    #### `README_v2.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        ## The Problem\n        Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:\n        - Tedious and repetitive\n        - Error-prone\n        - A poor use of skilled engineering time\n\n        ## The Solution\n        RigOfficeDownloader automates the document retrieval process through a three-stage workflow:\n        1. **Document Retrieval**: Automatically scrapes document metadata\n        2. **File Metadata**: Fetches file information for selected documents\n        3. **Smart Downloads**: Downloads files with intelligent naming and organization\n\n        ## How It Works\n        - Uses Selenium to automate web interactions with RigDoc\n        - Exports data to Markdown for user review and selection\n        - Applies configurable filters to pre-select relevant documents\n        - Organizes downloads with consistent naming patterns\n\n        ## Getting Started\n        1. Run `py_venv_init.bat` to set up the environment\n        2. Run `RigOfficeDownloader-v4.bat` to launch the application\n        3. Follow the interactive menu prompts\n\n        ## Impact\n        Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.\n    ```\n\n    ---\n\n    #### `README_v3.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        ## Technical Overview\n        RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\n\n        ## Architecture\n        - **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown\n        - **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads\n        - **File Organization**: Hierarchical naming system with metadata embedding and subfolder support\n\n        ## Workflow Steps\n        1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information\n        2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing\n        3. **Selection Import**: `md_table_to_json()` imports user selections\n        4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents\n        5. **File Selection**: Export/import cycle for user selection of files\n        6. **Download Process**: `download_files()` retrieves selected files with smart naming\n\n        ## Filter Chain System\n        Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.\n\n        ## Setup Instructions\n        1. Run `py_venv_init.bat` to initialize the Python environment\n        2. Run `RigOfficeDownloader-v4.bat` to execute the application\n\n        ## Version History\n        - v1: Basic document retrieval and download\n        - v2: JSON/Markdown conversion and user selection\n        - v3: Improved error handling and field organization\n        - v4: Subfolder support, filter chains, field ordering\n    ```\n\n    ---\n\n    #### `README_v4.md`\n\n    ```markdown\n        # RigOfficeDownloader\n        > Automate document retrieval from NOV's RigDoc system\n\n        ## What This Tool Does\n        RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\n\n        ## Quick Start Guide\n        1. **Setup**: Run `py_venv_init.bat` to create the Python environment\n        2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application\n        3. **Configure**: Enter your rig number and search URLs when prompted\n        4. **Run**: Follow the numbered menu to execute each step of the workflow\n\n        ## Workflow Steps Explained\n        0. **Change Parameters**: Update rig number and search URLs\n        1. **Configure Filters**: Set up automatic document/file selection rules\n        2. **Fetch Documents**: Retrieve document metadata from RigDoc\n        3. **Review Documents**: Edit the Markdown file to select which documents to process\n        4. **Import Selections**: Load your document selections\n        5. **Fetch Files**: Get file metadata for selected documents\n        6. **Review Files**: Edit the Markdown file to select which files to download\n        7. **Import File Selections**: Load your file selections\n        8. **Download Files**: Retrieve the selected files\n\n        ## Tips for Success\n        - Use filters to automatically pre-select relevant documents\n        - Review the Markdown files carefully before proceeding to the next step\n        - Files will be organized in subfolders based on '/' in their generated names\n\n        ## Need Help?\n        Check the source code comments for detailed information about each function and workflow step.\n    ```\n\n    ---\n\n    #### `README_v5.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\n\n        ```\n        Documents -> Files -> Downloads\n        ```\n\n        ## Features\n        - Three-stage workflow with user control points\n        - Interactive menu for flexible execution\n        - Configurable filter chains for automatic selection\n        - Smart file organization with subfolder support\n        - Markdown interfaces for document/file review\n\n        ## Quick Start\n        ```\n        1. Run py_venv_init.bat\n        2. Run RigOfficeDownloader-v4.bat\n        3. Follow the interactive menu\n        ```\n\n        ## Benefits\n        - Reduces documentation gathering time by 75%+\n        - Maintains consistent file organization\n        - Provides user control at key decision points\n        - Handles errors gracefully\n\n        ## Requirements\n        - Windows OS\n        - Python 3.6+\n        - Chrome browser\n    ```\n\n    ---\n\n    #### `README_v6.md`\n\n    ```markdown\n        # RigOfficeDownloader\n\n        Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.\n\n        ## Key Features\n        - **Three-Stage Workflow**: Document selection → File selection → Download\n        - **Metadata Preservation**: Structured naming with revision/case numbers\n        - **Interactive Review**: Markdown tables for manual inclusion flags\n        - **Smart Organization**: Automatic subfolder creation via naming patterns\n        - **Filter System**: Pattern-based inclusion/exclusion rules\n        - **Browser Automation**: Smart waiting strategies and session management\n\n        ## Workflow Process\n        ```python\n        1. Fetch Documents       # Scrape metadata → <rig>-a-docs.json\n        2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true\n        3. Import Selections     # Update JSON with user choices\n        4. Fetch Files           # Get file listings → <rig>-b-files.json\n        5. Export Files MD       # <rig>-b-files.md - Set item_download=true\n        6. Import File Choices   # Update file selections\n        7. Download Files        # Auto-organized with subfolder support\n        ```\n\n        ## Configuration (CONFIG Section)\n        ```python\n        {\n            \"rig_number\": \"R0000.020\",  # Target rig ID\n            \"search_urls\": [            # Predefined search templates\n                \"https://rigdoc.nov.com/search/rigsearch?q=...\",\n                \"https://rigdoc.nov.com/search/rigsearch?q=...\"\n            ],\n            \"filters\": [                # Sequential processing rules\n                {\n                    \"type\": \"docs\",     # Apply to documents/files\n                    \"pattern\": \"*DRILL*FLOOR*\",  # Glob-style matching\n                    \"field\": \"item_include\",     # Field to modify\n                    \"value\": True       # Set True/False based on match\n                }\n            ]\n        }\n        ```\n\n        ## Setup & Usage\n        ```bash\n        # Initialize environment\n        py_venv_init.bat\n        py_venv_pip_install.bat\n\n        # Run modes\n        RigOfficeDownloader-v4.py [rig_number] [mode]\n\n        Modes:\n        --auto         # Full automation\n        --interactive  # Step-by-step control\n        --config       # Modify search templates/filters\n        ```\n\n        ## Key Configuration Patterns\n        - **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)\n        - **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)\n        - **File Types**: Auto-prioritize PDFs with `*.pdf` pattern\n\n        ## Requirements\n        - Chrome Browser + ChromeDriver\n        - Active RigDoc credentials\n        - Python 3.8+ with dependencies from requirements.txt\n        - Network access to rigdoc.nov.com\n\n        ## Advanced Features\n        - **Path Sanitization**: Auto-clean special chars while preserving /\n        - **Deduplication**: Hash-based conflict resolution\n        - **Field Ordering**: Customizable JSON/Markdown columns\n        - **Smart Scrolling**: Progressive page loading detection\n\n        > Reduces documentation prep time by 60-75% compared to manual retrieval\n        > Version 4.0 | Active development with subfolder support\n    ```\n\n    ---\n\n    #### `README_v7.md`\n\n    ```markdown\n\n        ## Overview\n        - Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\n        - Use case: curate, review, and batch-download rig-related documents and technical files.\n\n        ## Directory Structure\n\n        * `outputs/` (BASE\\_OUTPUT): All results stored here\n        * `outputs/data/` (DATA\\_DIR): Document and file metadata (JSON/MD)\n        * `outputs/downloads/` (DL\\_DIR): Downloaded PDF and file outputs\n\n        ## Pipeline Overview\n\n        1. Change search parameters (rig number, URLs)\n        2. Configure filter chain (add, edit, delete, toggle, reorder filters)\n        3. Fetch docs (scrape data from rigdoc.nov.com)\n        4. Export docs to Markdown (for selection/editing)\n        5. Import docs from Markdown (sync edited selection)\n        6. Fetch candidate files linked to selected docs\n        7. Export file list to Markdown (for editing/selecting files for download)\n        8. Import updated file list from Markdown\n        9. Download marked files (PDFs only, via Chrome)\n\n        ## Manual Editing\n\n        * Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing\n        * Set `item_include` (docs) and `item_download` (files) fields\n\n        ## Running the Tool\n\n        ```bash\n        python rigdocscraper.py\n        ```\n\n        * Interactive menu enables step selection (numbers/comma/space-separated)\n        * Supports adjusting parameters, filter configuration, and reviewing batch steps\n        * Prompts will guide through editing, import/export, and download procedures\n\n        ## Troubleshooting\n\n        * Requires functioning Chrome installation; verify webdriver-manager compatibility\n        * Common issues: browser launch failures, login/captcha requirements, file permissions\n        * Output logs and warnings shown in terminal; inspect `outputs/data/` for progress\n    ```\n", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Create a README.md for RigOfficeDownloader that combines the best elements from all provided variations, limiting the length to under 70 lines. \\n\\n1. Begin with a clear project title and concise one-line summary highlighting automation of NOV RigDoc document retrieval for engineers. \\n2. Provide a brief overview explaining the manual problem, target users (engineers preparing for 3D modeling), and time savings impact. \\n3. Summarize core features and benefits: three-stage workflow (documents/files/downloads), configurable filter chain, interactive menu, smart file organization (subfolder support), Markdown-based review, error handling, metadata preservation, and session management. \\n4. List requirements: Windows OS, Python 3.6+ (or 3.8+ if using advanced features), Chrome browser with ChromeDriver, RigDoc credentials, network access, and dependencies via requirements.txt. \\n5. Describe the workflow as a succinct, numbered or bulleted pipeline. Include precise step names and show intermediate file types and editing points (e.g., JSON/Markdown). Indicate parameter/filter configuration, document/file fetching, manual review via Markdown, importing selections, and batch downloads. \\n6. Outline key configuration patterns in a dedicated section, using code block(s) to show JSON-style filter/sample config with comments. \\n7. Briefly explain directory structure: outputs/, outputs/data/, outputs/downloads/. \\n8. Provide a Quick Start section with concrete stepwise instructions (e.g., py_venv_init.bat, py_venv_pip_install.bat, running the script or batch). Include notes about interactive modes, automation, and config options. \\n9. Add practical usage tips: editing Markdown tables before import, using filters for pre-selection, watching for logs and progress in outputs/data/. \\n10. Reserve a Troubleshooting section noting common browser/login issues, debugging with logs, and where to look for output data. \\n11. Concisely mention version history or advanced features (e.g., deduplication, smart scrolling, field ordering, path sanitization) as a one- or two-line highlight at the end. \\n12. Ensure technical accuracy, maintain procedural clarity, preserve domain-specific terminology, and keep all content as actionable commands without self-reference or first-person phrasing. Preserve original practical sequence and workflow logic throughout.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"# GOAL: CREATE A STRUCTURED README.MD <70 LINES\\n\\n\\n# INPUT: Please create a single README.md based on taking the best from each part of these variations:\\n\\n    # Dir `readme_variations`\\n\\n    ### File Structure\\n\\n    ```\\n    \\u251c\\u2500\\u2500 README_v1.md\\n    \\u251c\\u2500\\u2500 README_v2.md\\n    \\u251c\\u2500\\u2500 README_v3.md\\n    \\u251c\\u2500\\u2500 README_v4.md\\n    \\u251c\\u2500\\u2500 README_v5.md\\n    \\u251c\\u2500\\u2500 README_v6.md\\n    \\u2514\\u2500\\u2500 README_v7.md\\n    ```\\n\\n    ---\\n\\n    #### `README_v1.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## Overview\\n        RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\\n\\n        ## Key Features\\n        - Three-stage workflow: Documents -> Files -> Downloads\\n        - Interactive menu for flexible execution\\n        - User control points via Markdown interfaces\\n        - Smart file organization with subfolder support\\n        - Configurable filter chains\\n\\n        ## Setup & Usage\\n        1. Run `py_venv_init.bat` to create the Python environment\\n        2. Run `RigOfficeDownloader-v4.bat` to start the application\\n        3. Use the interactive menu to configure and execute workflow steps\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Preserves document context and relationships\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser (for Selenium automation)\\n    ```\\n\\n    ---\\n\\n    #### `README_v2.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## The Problem\\n        Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:\\n        - Tedious and repetitive\\n        - Error-prone\\n        - A poor use of skilled engineering time\\n\\n        ## The Solution\\n        RigOfficeDownloader automates the document retrieval process through a three-stage workflow:\\n        1. **Document Retrieval**: Automatically scrapes document metadata\\n        2. **File Metadata**: Fetches file information for selected documents\\n        3. **Smart Downloads**: Downloads files with intelligent naming and organization\\n\\n        ## How It Works\\n        - Uses Selenium to automate web interactions with RigDoc\\n        - Exports data to Markdown for user review and selection\\n        - Applies configurable filters to pre-select relevant documents\\n        - Organizes downloads with consistent naming patterns\\n\\n        ## Getting Started\\n        1. Run `py_venv_init.bat` to set up the environment\\n        2. Run `RigOfficeDownloader-v4.bat` to launch the application\\n        3. Follow the interactive menu prompts\\n\\n        ## Impact\\n        Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.\\n    ```\\n\\n    ---\\n\\n    #### `README_v3.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## Technical Overview\\n        RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\\n\\n        ## Architecture\\n        - **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown\\n        - **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads\\n        - **File Organization**: Hierarchical naming system with metadata embedding and subfolder support\\n\\n        ## Workflow Steps\\n        1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information\\n        2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing\\n        3. **Selection Import**: `md_table_to_json()` imports user selections\\n        4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents\\n        5. **File Selection**: Export/import cycle for user selection of files\\n        6. **Download Process**: `download_files()` retrieves selected files with smart naming\\n\\n        ## Filter Chain System\\n        Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.\\n\\n        ## Setup Instructions\\n        1. Run `py_venv_init.bat` to initialize the Python environment\\n        2. Run `RigOfficeDownloader-v4.bat` to execute the application\\n\\n        ## Version History\\n        - v1: Basic document retrieval and download\\n        - v2: JSON/Markdown conversion and user selection\\n        - v3: Improved error handling and field organization\\n        - v4: Subfolder support, filter chains, field ordering\\n    ```\\n\\n    ---\\n\\n    #### `README_v4.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n        > Automate document retrieval from NOV's RigDoc system\\n\\n        ## What This Tool Does\\n        RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\\n\\n        ## Quick Start Guide\\n        1. **Setup**: Run `py_venv_init.bat` to create the Python environment\\n        2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application\\n        3. **Configure**: Enter your rig number and search URLs when prompted\\n        4. **Run**: Follow the numbered menu to execute each step of the workflow\\n\\n        ## Workflow Steps Explained\\n        0. **Change Parameters**: Update rig number and search URLs\\n        1. **Configure Filters**: Set up automatic document/file selection rules\\n        2. **Fetch Documents**: Retrieve document metadata from RigDoc\\n        3. **Review Documents**: Edit the Markdown file to select which documents to process\\n        4. **Import Selections**: Load your document selections\\n        5. **Fetch Files**: Get file metadata for selected documents\\n        6. **Review Files**: Edit the Markdown file to select which files to download\\n        7. **Import File Selections**: Load your file selections\\n        8. **Download Files**: Retrieve the selected files\\n\\n        ## Tips for Success\\n        - Use filters to automatically pre-select relevant documents\\n        - Review the Markdown files carefully before proceeding to the next step\\n        - Files will be organized in subfolders based on '/' in their generated names\\n\\n        ## Need Help?\\n        Check the source code comments for detailed information about each function and workflow step.\\n    ```\\n\\n    ---\\n\\n    #### `README_v5.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\\n\\n        ```\\n        Documents -> Files -> Downloads\\n        ```\\n\\n        ## Features\\n        - Three-stage workflow with user control points\\n        - Interactive menu for flexible execution\\n        - Configurable filter chains for automatic selection\\n        - Smart file organization with subfolder support\\n        - Markdown interfaces for document/file review\\n\\n        ## Quick Start\\n        ```\\n        1. Run py_venv_init.bat\\n        2. Run RigOfficeDownloader-v4.bat\\n        3. Follow the interactive menu\\n        ```\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Handles errors gracefully\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser\\n    ```\\n\\n    ---\\n\\n    #### `README_v6.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.\\n\\n        ## Key Features\\n        - **Three-Stage Workflow**: Document selection \\u2192 File selection \\u2192 Download\\n        - **Metadata Preservation**: Structured naming with revision/case numbers\\n        - **Interactive Review**: Markdown tables for manual inclusion flags\\n        - **Smart Organization**: Automatic subfolder creation via naming patterns\\n        - **Filter System**: Pattern-based inclusion/exclusion rules\\n        - **Browser Automation**: Smart waiting strategies and session management\\n\\n        ## Workflow Process\\n        ```python\\n        1. Fetch Documents       # Scrape metadata \\u2192 <rig>-a-docs.json\\n        2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true\\n        3. Import Selections     # Update JSON with user choices\\n        4. Fetch Files           # Get file listings \\u2192 <rig>-b-files.json\\n        5. Export Files MD       # <rig>-b-files.md - Set item_download=true\\n        6. Import File Choices   # Update file selections\\n        7. Download Files        # Auto-organized with subfolder support\\n        ```\\n\\n        ## Configuration (CONFIG Section)\\n        ```python\\n        {\\n            \\\"rig_number\\\": \\\"R0000.020\\\",  # Target rig ID\\n            \\\"search_urls\\\": [            # Predefined search templates\\n                \\\"https://rigdoc.nov.com/search/rigsearch?q=...\\\",\\n                \\\"https://rigdoc.nov.com/search/rigsearch?q=...\\\"\\n            ],\\n            \\\"filters\\\": [                # Sequential processing rules\\n                {\\n                    \\\"type\\\": \\\"docs\\\",     # Apply to documents/files\\n                    \\\"pattern\\\": \\\"*DRILL*FLOOR*\\\",  # Glob-style matching\\n                    \\\"field\\\": \\\"item_include\\\",     # Field to modify\\n                    \\\"value\\\": True       # Set True/False based on match\\n                }\\n            ]\\n        }\\n        ```\\n\\n        ## Setup & Usage\\n        ```bash\\n        # Initialize environment\\n        py_venv_init.bat\\n        py_venv_pip_install.bat\\n\\n        # Run modes\\n        RigOfficeDownloader-v4.py [rig_number] [mode]\\n\\n        Modes:\\n        --auto         # Full automation\\n        --interactive  # Step-by-step control\\n        --config       # Modify search templates/filters\\n        ```\\n\\n        ## Key Configuration Patterns\\n        - **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)\\n        - **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)\\n        - **File Types**: Auto-prioritize PDFs with `*.pdf` pattern\\n\\n        ## Requirements\\n        - Chrome Browser + ChromeDriver\\n        - Active RigDoc credentials\\n        - Python 3.8+ with dependencies from requirements.txt\\n        - Network access to rigdoc.nov.com\\n\\n        ## Advanced Features\\n        - **Path Sanitization**: Auto-clean special chars while preserving /\\n        - **Deduplication**: Hash-based conflict resolution\\n        - **Field Ordering**: Customizable JSON/Markdown columns\\n        - **Smart Scrolling**: Progressive page loading detection\\n\\n        > Reduces documentation prep time by 60-75% compared to manual retrieval\\n        > Version 4.0 | Active development with subfolder support\\n    ```\\n\\n    ---\\n\\n    #### `README_v7.md`\\n\\n    ```markdown\\n\\n        ## Overview\\n        - Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\\n        - Use case: curate, review, and batch-download rig-related documents and technical files.\\n\\n        ## Directory Structure\\n\\n        * `outputs/` (BASE\\\\_OUTPUT): All results stored here\\n        * `outputs/data/` (DATA\\\\_DIR): Document and file metadata (JSON/MD)\\n        * `outputs/downloads/` (DL\\\\_DIR): Downloaded PDF and file outputs\\n\\n        ## Pipeline Overview\\n\\n        1. Change search parameters (rig number, URLs)\\n        2. Configure filter chain (add, edit, delete, toggle, reorder filters)\\n        3. Fetch docs (scrape data from rigdoc.nov.com)\\n        4. Export docs to Markdown (for selection/editing)\\n        5. Import docs from Markdown (sync edited selection)\\n        6. Fetch candidate files linked to selected docs\\n        7. Export file list to Markdown (for editing/selecting files for download)\\n        8. Import updated file list from Markdown\\n        9. Download marked files (PDFs only, via Chrome)\\n\\n        ## Manual Editing\\n\\n        * Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing\\n        * Set `item_include` (docs) and `item_download` (files) fields\\n\\n        ## Running the Tool\\n\\n        ```bash\\n        python rigdocscraper.py\\n        ```\\n\\n        * Interactive menu enables step selection (numbers/comma/space-separated)\\n        * Supports adjusting parameters, filter configuration, and reviewing batch steps\\n        * Prompts will guide through editing, import/export, and download procedures\\n\\n        ## Troubleshooting\\n\\n        * Requires functioning Chrome installation; verify webdriver-manager compatibility\\n        * Common issues: browser launch failures, login/captcha requirements, file permissions\\n        * Output logs and warnings shown in terminal; inspect `outputs/data/` for progress\\n    ```\\n\",\n  \"instruction_format\": \"Create a README.md for RigOfficeDownloader that combines the best elements from all provided variations, limiting the length to under 70 lines. \\n\\n1. Begin with a clear project title and concise one-line summary highlighting automation of NOV RigDoc document retrieval for engineers. \\n2. Provide a brief overview explaining the manual problem, target users (engineers preparing for 3D modeling), and time savings impact. \\n3. Summarize core features and benefits: three-stage workflow (documents/files/downloads), configurable filter chain, interactive menu, smart file organization (subfolder support), Markdown-based review, error handling, metadata preservation, and session management. \\n4. List requirements: Windows OS, Python 3.6+ (or 3.8+ if using advanced features), Chrome browser with ChromeDriver, RigDoc credentials, network access, and dependencies via requirements.txt. \\n5. Describe the workflow as a succinct, numbered or bulleted pipeline. Include precise step names and show intermediate file types and editing points (e.g., JSON/Markdown). Indicate parameter/filter configuration, document/file fetching, manual review via Markdown, importing selections, and batch downloads. \\n6. Outline key configuration patterns in a dedicated section, using code block(s) to show JSON-style filter/sample config with comments. \\n7. Briefly explain directory structure: outputs/, outputs/data/, outputs/downloads/. \\n8. Provide a Quick Start section with concrete stepwise instructions (e.g., py_venv_init.bat, py_venv_pip_install.bat, running the script or batch). Include notes about interactive modes, automation, and config options. \\n9. Add practical usage tips: editing Markdown tables before import, using filters for pre-selection, watching for logs and progress in outputs/data/. \\n10. Reserve a Troubleshooting section noting common browser/login issues, debugging with logs, and where to look for output data. \\n11. Concisely mention version history or advanced features (e.g., deduplication, smart scrolling, field ordering, path sanitization) as a one- or two-line highlight at the end. \\n12. Ensure technical accuracy, maintain procedural clarity, preserve domain-specific terminology, and keep all content as actionable commands without self-reference or first-person phrasing. Preserve original practical sequence and workflow logic throughout.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": [\n    \"# RigOfficeDownloader\",\n    \"*Automates NOV RigDoc document retrieval, streamlining technical documentation gathering for engineering and 3D modeling workflows.*\",\n    \"\",\n    \"## Overview\",\n    \"Eliminates manual searching, selection, and downloading in RigDoc. Designed for engineers and project teams, reducing prep time by 60–75%.\",\n    \"\",\n    \"## Features & Benefits\",\n    \"- Three-stage pipeline: document selection → file selection → batch download\",\n    \"- Interactive menu for granular step control\",\n    \"- Configurable filter chain for automatic inclusion/exclusion\",\n    \"- Markdown-based review and selection of records\",\n    \"- Smart, metadata-driven file organization with subfolder support\",\n    \"- Preserves document relationships and context\",\n    \"- Robust error handling and session management\",\n    \"- Supports manual checkpoints and batch processing\",\n    \"\",\n    \"## Requirements\",\n    \"- Windows OS\",\n    \"- Python 3.6+ (3.8+ for advanced features)\",\n    \"- Chrome browser and ChromeDriver\",\n    \"- Active RigDoc credentials\",\n    \"- Network access to rigdoc.nov.com\",\n    \"- Dependencies installed via requirements.txt\",\n    \"\",\n    \"## Workflow Pipeline\",\n    \"1. Configure search parameters (rig number, search URLs)\",\n    \"2. Set up filters (pattern-based rules for docs/files)\",\n    \"3. Fetch document metadata  →  JSON export in outputs/data/\",\n    \"4. Export documents to Markdown  →  manual selection (set `item_include`)\",\n    \"5. Import edited Markdown to update document selections\",\n    \"6. Fetch file metadata for selected documents  →  JSON export\",\n    \"7. Export files to Markdown  →  manual selection (set `item_download`)\",\n    \"8. Import edited Markdown to update file selections\",\n    \"9. Batch download specified files  →  organized in outputs/downloads/\",\n    \"\",\n    \"## Example Configuration\",\n    \"```json\",\n    \"{\",\n    \"  \\\"rig_number\\\": \\\"R0000.020\\\",\",\n    \"  \\\"search_urls\\\": [\\\"https://rigdoc.nov.com/search/rigsearch?q=...\\\"],\",\n    \"  \\\"filters\\\": [\",\n    \"    { \\\"type\\\": \\\"docs\\\", \\\"pattern\\\": \\\"*DRILL*FLOOR*\\\", \\\"field\\\": \\\"item_include\\\", \\\"value\\\": true },\",\n    \"    { \\\"type\\\": \\\"files\\\", \\\"pattern\\\": \\\"*.pdf\\\", \\\"field\\\": \\\"item_download\\\", \\\"value\\\": true }\",\n    \"  ]\",\n    \"}\",\n    \"```\",\n    \"\",\n    \"## Directory Structure\",\n    \"- `outputs/` – root for all results\",\n    \"- `outputs/data/` – JSON/Markdown metadata exports and logs\",\n    \"- `outputs/downloads/` – downloaded document and file outputs\",\n    \"\",\n    \"## Quick Start\",\n    \"1. Run `py_venv_init.bat` and `py_venv_pip_install.bat`\",\n    \"2. Launch with `RigOfficeDownloader-v4.bat` or `python rigdocscraper.py`\",\n    \"3. Use the interactive menu to select workflow steps\",\n    \"\",\n    \"### Modes\",\n    \"- `--auto` for full automation\",\n    \"- `--interactive` for step-by-step execution\",\n    \"- `--config` to modify parameters or filters\",\n    \"\",\n    \"## Practical Usage Tips\",\n    \"- Edit exported `.md` files to fine-tune records before progressing\",\n    \"- Leverage filters for efficient pre-selection of relevant docs/files\",\n    \"- Monitor processing progress and logs in `outputs/data/`\",\n    \"\",\n    \"## Troubleshooting\",\n    \"- Ensure Chrome and ChromeDriver are installed and compatible\",\n    \"- Validate RigDoc login and network access\",\n    \"- Check terminal logs and `outputs/data/` for errors or progress\",\n    \"- Resolve browser launch issues, login/captcha problems, or file permission errors as noted in logs\",\n    \"\",\n    \"## Advanced & Version Info\",\n    \"Version 4.0+ adds subfolder support, deduplication, field ordering, path sanitization, smart scrolling, and improved batch control.\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Result Combiner\n\nYou are a specialized aggregator that combines results from multiple instruction steps into a cohesive output. Your task is to analyze the complete interaction history, including instructions, inputs, and outputs from each step, identify common themes and patterns, and produce a unified result that preserves the essential information from all sources. Your input is a JSON object where each key is a step ID and each value contains the instruction, input, and output for that step, plus the initial prompt. Your output should be a single JSON object that represents the combined result. Execute as:\n\n`{role=result_combiner;input=[interaction_history:json];process=[analyze_complete_interaction_history(),identify_common_patterns(),extract_key_elements(),merge_into_cohesive_whole(),format_as_unified_output()];constraints=[preserve_essential_information(),maintain_logical_structure(),avoid_redundancy()];requirements=[output_as_single_json(),ensure_completeness(),prioritize_clarity()];output={combined_result:json}}`", "step": "a", "title": "Result Combiner", "input": "{\n  \"initial_prompt\": \"# GOAL: CREATE A STRUCTURED README.MD <70 LINES\\n\\n\\n# INPUT: Please create a single README.md based on taking the best from each part of these variations:\\n\\n    # Dir `readme_variations`\\n\\n    ### File Structure\\n\\n    ```\\n    \\u251c\\u2500\\u2500 README_v1.md\\n    \\u251c\\u2500\\u2500 README_v2.md\\n    \\u251c\\u2500\\u2500 README_v3.md\\n    \\u251c\\u2500\\u2500 README_v4.md\\n    \\u251c\\u2500\\u2500 README_v5.md\\n    \\u251c\\u2500\\u2500 README_v6.md\\n    \\u2514\\u2500\\u2500 README_v7.md\\n    ```\\n\\n    ---\\n\\n    #### `README_v1.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## Overview\\n        RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\\n\\n        ## Key Features\\n        - Three-stage workflow: Documents -> Files -> Downloads\\n        - Interactive menu for flexible execution\\n        - User control points via Markdown interfaces\\n        - Smart file organization with subfolder support\\n        - Configurable filter chains\\n\\n        ## Setup & Usage\\n        1. Run `py_venv_init.bat` to create the Python environment\\n        2. Run `RigOfficeDownloader-v4.bat` to start the application\\n        3. Use the interactive menu to configure and execute workflow steps\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Preserves document context and relationships\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser (for Selenium automation)\\n    ```\\n\\n    ---\\n\\n    #### `README_v2.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## The Problem\\n        Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:\\n        - Tedious and repetitive\\n        - Error-prone\\n        - A poor use of skilled engineering time\\n\\n        ## The Solution\\n        RigOfficeDownloader automates the document retrieval process through a three-stage workflow:\\n        1. **Document Retrieval**: Automatically scrapes document metadata\\n        2. **File Metadata**: Fetches file information for selected documents\\n        3. **Smart Downloads**: Downloads files with intelligent naming and organization\\n\\n        ## How It Works\\n        - Uses Selenium to automate web interactions with RigDoc\\n        - Exports data to Markdown for user review and selection\\n        - Applies configurable filters to pre-select relevant documents\\n        - Organizes downloads with consistent naming patterns\\n\\n        ## Getting Started\\n        1. Run `py_venv_init.bat` to set up the environment\\n        2. Run `RigOfficeDownloader-v4.bat` to launch the application\\n        3. Follow the interactive menu prompts\\n\\n        ## Impact\\n        Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.\\n    ```\\n\\n    ---\\n\\n    #### `README_v3.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## Technical Overview\\n        RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\\n\\n        ## Architecture\\n        - **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown\\n        - **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads\\n        - **File Organization**: Hierarchical naming system with metadata embedding and subfolder support\\n\\n        ## Workflow Steps\\n        1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information\\n        2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing\\n        3. **Selection Import**: `md_table_to_json()` imports user selections\\n        4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents\\n        5. **File Selection**: Export/import cycle for user selection of files\\n        6. **Download Process**: `download_files()` retrieves selected files with smart naming\\n\\n        ## Filter Chain System\\n        Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.\\n\\n        ## Setup Instructions\\n        1. Run `py_venv_init.bat` to initialize the Python environment\\n        2. Run `RigOfficeDownloader-v4.bat` to execute the application\\n\\n        ## Version History\\n        - v1: Basic document retrieval and download\\n        - v2: JSON/Markdown conversion and user selection\\n        - v3: Improved error handling and field organization\\n        - v4: Subfolder support, filter chains, field ordering\\n    ```\\n\\n    ---\\n\\n    #### `README_v4.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n        > Automate document retrieval from NOV's RigDoc system\\n\\n        ## What This Tool Does\\n        RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\\n\\n        ## Quick Start Guide\\n        1. **Setup**: Run `py_venv_init.bat` to create the Python environment\\n        2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application\\n        3. **Configure**: Enter your rig number and search URLs when prompted\\n        4. **Run**: Follow the numbered menu to execute each step of the workflow\\n\\n        ## Workflow Steps Explained\\n        0. **Change Parameters**: Update rig number and search URLs\\n        1. **Configure Filters**: Set up automatic document/file selection rules\\n        2. **Fetch Documents**: Retrieve document metadata from RigDoc\\n        3. **Review Documents**: Edit the Markdown file to select which documents to process\\n        4. **Import Selections**: Load your document selections\\n        5. **Fetch Files**: Get file metadata for selected documents\\n        6. **Review Files**: Edit the Markdown file to select which files to download\\n        7. **Import File Selections**: Load your file selections\\n        8. **Download Files**: Retrieve the selected files\\n\\n        ## Tips for Success\\n        - Use filters to automatically pre-select relevant documents\\n        - Review the Markdown files carefully before proceeding to the next step\\n        - Files will be organized in subfolders based on '/' in their generated names\\n\\n        ## Need Help?\\n        Check the source code comments for detailed information about each function and workflow step.\\n    ```\\n\\n    ---\\n\\n    #### `README_v5.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\\n\\n        ```\\n        Documents -> Files -> Downloads\\n        ```\\n\\n        ## Features\\n        - Three-stage workflow with user control points\\n        - Interactive menu for flexible execution\\n        - Configurable filter chains for automatic selection\\n        - Smart file organization with subfolder support\\n        - Markdown interfaces for document/file review\\n\\n        ## Quick Start\\n        ```\\n        1. Run py_venv_init.bat\\n        2. Run RigOfficeDownloader-v4.bat\\n        3. Follow the interactive menu\\n        ```\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Handles errors gracefully\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser\\n    ```\\n\\n    ---\\n\\n    #### `README_v6.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.\\n\\n        ## Key Features\\n        - **Three-Stage Workflow**: Document selection \\u2192 File selection \\u2192 Download\\n        - **Metadata Preservation**: Structured naming with revision/case numbers\\n        - **Interactive Review**: Markdown tables for manual inclusion flags\\n        - **Smart Organization**: Automatic subfolder creation via naming patterns\\n        - **Filter System**: Pattern-based inclusion/exclusion rules\\n        - **Browser Automation**: Smart waiting strategies and session management\\n\\n        ## Workflow Process\\n        ```python\\n        1. Fetch Documents       # Scrape metadata \\u2192 <rig>-a-docs.json\\n        2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true\\n        3. Import Selections     # Update JSON with user choices\\n        4. Fetch Files           # Get file listings \\u2192 <rig>-b-files.json\\n        5. Export Files MD       # <rig>-b-files.md - Set item_download=true\\n        6. Import File Choices   # Update file selections\\n        7. Download Files        # Auto-organized with subfolder support\\n        ```\\n\\n        ## Configuration (CONFIG Section)\\n        ```python\\n        {\\n            \\\"rig_number\\\": \\\"R0000.020\\\",  # Target rig ID\\n            \\\"search_urls\\\": [            # Predefined search templates\\n                \\\"https://rigdoc.nov.com/search/rigsearch?q=...\\\",\\n                \\\"https://rigdoc.nov.com/search/rigsearch?q=...\\\"\\n            ],\\n            \\\"filters\\\": [                # Sequential processing rules\\n                {\\n                    \\\"type\\\": \\\"docs\\\",     # Apply to documents/files\\n                    \\\"pattern\\\": \\\"*DRILL*FLOOR*\\\",  # Glob-style matching\\n                    \\\"field\\\": \\\"item_include\\\",     # Field to modify\\n                    \\\"value\\\": True       # Set True/False based on match\\n                }\\n            ]\\n        }\\n        ```\\n\\n        ## Setup & Usage\\n        ```bash\\n        # Initialize environment\\n        py_venv_init.bat\\n        py_venv_pip_install.bat\\n\\n        # Run modes\\n        RigOfficeDownloader-v4.py [rig_number] [mode]\\n\\n        Modes:\\n        --auto         # Full automation\\n        --interactive  # Step-by-step control\\n        --config       # Modify search templates/filters\\n        ```\\n\\n        ## Key Configuration Patterns\\n        - **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)\\n        - **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)\\n        - **File Types**: Auto-prioritize PDFs with `*.pdf` pattern\\n\\n        ## Requirements\\n        - Chrome Browser + ChromeDriver\\n        - Active RigDoc credentials\\n        - Python 3.8+ with dependencies from requirements.txt\\n        - Network access to rigdoc.nov.com\\n\\n        ## Advanced Features\\n        - **Path Sanitization**: Auto-clean special chars while preserving /\\n        - **Deduplication**: Hash-based conflict resolution\\n        - **Field Ordering**: Customizable JSON/Markdown columns\\n        - **Smart Scrolling**: Progressive page loading detection\\n\\n        > Reduces documentation prep time by 60-75% compared to manual retrieval\\n        > Version 4.0 | Active development with subfolder support\\n    ```\\n\\n    ---\\n\\n    #### `README_v7.md`\\n\\n    ```markdown\\n\\n        ## Overview\\n        - Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\\n        - Use case: curate, review, and batch-download rig-related documents and technical files.\\n\\n        ## Directory Structure\\n\\n        * `outputs/` (BASE\\\\_OUTPUT): All results stored here\\n        * `outputs/data/` (DATA\\\\_DIR): Document and file metadata (JSON/MD)\\n        * `outputs/downloads/` (DL\\\\_DIR): Downloaded PDF and file outputs\\n\\n        ## Pipeline Overview\\n\\n        1. Change search parameters (rig number, URLs)\\n        2. Configure filter chain (add, edit, delete, toggle, reorder filters)\\n        3. Fetch docs (scrape data from rigdoc.nov.com)\\n        4. Export docs to Markdown (for selection/editing)\\n        5. Import docs from Markdown (sync edited selection)\\n        6. Fetch candidate files linked to selected docs\\n        7. Export file list to Markdown (for editing/selecting files for download)\\n        8. Import updated file list from Markdown\\n        9. Download marked files (PDFs only, via Chrome)\\n\\n        ## Manual Editing\\n\\n        * Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing\\n        * Set `item_include` (docs) and `item_download` (files) fields\\n\\n        ## Running the Tool\\n\\n        ```bash\\n        python rigdocscraper.py\\n        ```\\n\\n        * Interactive menu enables step selection (numbers/comma/space-separated)\\n        * Supports adjusting parameters, filter configuration, and reviewing batch steps\\n        * Prompts will guide through editing, import/export, and download procedures\\n\\n        ## Troubleshooting\\n\\n        * Requires functioning Chrome installation; verify webdriver-manager compatibility\\n        * Common issues: browser launch failures, login/captcha requirements, file permissions\\n        * Output logs and warnings shown in terminal; inspect `outputs/data/` for progress\\n    ```\\n\",\n  \"a\": {\n    \"instruction\": \"# Instruction Converter\\n\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\n\\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n    \"input\": \"# GOAL: CREATE A STRUCTURED README.MD <70 LINES\\n\\n\\n# INPUT: Please create a single README.md based on taking the best from each part of these variations:\\n\\n    # Dir `readme_variations`\\n\\n    ### File Structure\\n\\n    ```\\n    \\u251c\\u2500\\u2500 README_v1.md\\n    \\u251c\\u2500\\u2500 README_v2.md\\n    \\u251c\\u2500\\u2500 README_v3.md\\n    \\u251c\\u2500\\u2500 README_v4.md\\n    \\u251c\\u2500\\u2500 README_v5.md\\n    \\u251c\\u2500\\u2500 README_v6.md\\n    \\u2514\\u2500\\u2500 README_v7.md\\n    ```\\n\\n    ---\\n\\n    #### `README_v1.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## Overview\\n        RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.\\n\\n        ## Key Features\\n        - Three-stage workflow: Documents -> Files -> Downloads\\n        - Interactive menu for flexible execution\\n        - User control points via Markdown interfaces\\n        - Smart file organization with subfolder support\\n        - Configurable filter chains\\n\\n        ## Setup & Usage\\n        1. Run `py_venv_init.bat` to create the Python environment\\n        2. Run `RigOfficeDownloader-v4.bat` to start the application\\n        3. Use the interactive menu to configure and execute workflow steps\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Preserves document context and relationships\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser (for Selenium automation)\\n    ```\\n\\n    ---\\n\\n    #### `README_v2.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## The Problem\\n        Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:\\n        - Tedious and repetitive\\n        - Error-prone\\n        - A poor use of skilled engineering time\\n\\n        ## The Solution\\n        RigOfficeDownloader automates the document retrieval process through a three-stage workflow:\\n        1. **Document Retrieval**: Automatically scrapes document metadata\\n        2. **File Metadata**: Fetches file information for selected documents\\n        3. **Smart Downloads**: Downloads files with intelligent naming and organization\\n\\n        ## How It Works\\n        - Uses Selenium to automate web interactions with RigDoc\\n        - Exports data to Markdown for user review and selection\\n        - Applies configurable filters to pre-select relevant documents\\n        - Organizes downloads with consistent naming patterns\\n\\n        ## Getting Started\\n        1. Run `py_venv_init.bat` to set up the environment\\n        2. Run `RigOfficeDownloader-v4.bat` to launch the application\\n        3. Follow the interactive menu prompts\\n\\n        ## Impact\\n        Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.\\n    ```\\n\\n    ---\\n\\n    #### `README_v3.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        ## Technical Overview\\n        RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.\\n\\n        ## Architecture\\n        - **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown\\n        - **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads\\n        - **File Organization**: Hierarchical naming system with metadata embedding and subfolder support\\n\\n        ## Workflow Steps\\n        1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information\\n        2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing\\n        3. **Selection Import**: `md_table_to_json()` imports user selections\\n        4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents\\n        5. **File Selection**: Export/import cycle for user selection of files\\n        6. **Download Process**: `download_files()` retrieves selected files with smart naming\\n\\n        ## Filter Chain System\\n        Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.\\n\\n        ## Setup Instructions\\n        1. Run `py_venv_init.bat` to initialize the Python environment\\n        2. Run `RigOfficeDownloader-v4.bat` to execute the application\\n\\n        ## Version History\\n        - v1: Basic document retrieval and download\\n        - v2: JSON/Markdown conversion and user selection\\n        - v3: Improved error handling and field organization\\n        - v4: Subfolder support, filter chains, field ordering\\n    ```\\n\\n    ---\\n\\n    #### `README_v4.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n        > Automate document retrieval from NOV's RigDoc system\\n\\n        ## What This Tool Does\\n        RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.\\n\\n        ## Quick Start Guide\\n        1. **Setup**: Run `py_venv_init.bat` to create the Python environment\\n        2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application\\n        3. **Configure**: Enter your rig number and search URLs when prompted\\n        4. **Run**: Follow the numbered menu to execute each step of the workflow\\n\\n        ## Workflow Steps Explained\\n        0. **Change Parameters**: Update rig number and search URLs\\n        1. **Configure Filters**: Set up automatic document/file selection rules\\n        2. **Fetch Documents**: Retrieve document metadata from RigDoc\\n        3. **Review Documents**: Edit the Markdown file to select which documents to process\\n        4. **Import Selections**: Load your document selections\\n        5. **Fetch Files**: Get file metadata for selected documents\\n        6. **Review Files**: Edit the Markdown file to select which files to download\\n        7. **Import File Selections**: Load your file selections\\n        8. **Download Files**: Retrieve the selected files\\n\\n        ## Tips for Success\\n        - Use filters to automatically pre-select relevant documents\\n        - Review the Markdown files carefully before proceeding to the next step\\n        - Files will be organized in subfolders based on '/' in their generated names\\n\\n        ## Need Help?\\n        Check the source code comments for detailed information about each function and workflow step.\\n    ```\\n\\n    ---\\n\\n    #### `README_v5.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.\\n\\n        ```\\n        Documents -> Files -> Downloads\\n        ```\\n\\n        ## Features\\n        - Three-stage workflow with user control points\\n        - Interactive menu for flexible execution\\n        - Configurable filter chains for automatic selection\\n        - Smart file organization with subfolder support\\n        - Markdown interfaces for document/file review\\n\\n        ## Quick Start\\n        ```\\n        1. Run py_venv_init.bat\\n        2. Run RigOfficeDownloader-v4.bat\\n        3. Follow the interactive menu\\n        ```\\n\\n        ## Benefits\\n        - Reduces documentation gathering time by 75%+\\n        - Maintains consistent file organization\\n        - Provides user control at key decision points\\n        - Handles errors gracefully\\n\\n        ## Requirements\\n        - Windows OS\\n        - Python 3.6+\\n        - Chrome browser\\n    ```\\n\\n    ---\\n\\n    #### `README_v6.md`\\n\\n    ```markdown\\n        # RigOfficeDownloader\\n\\n        Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.\\n\\n        ## Key Features\\n        - **Three-Stage Workflow**: Document selection \\u2192 File selection \\u2192 Download\\n        - **Metadata Preservation**: Structured naming with revision/case numbers\\n        - **Interactive Review**: Markdown tables for manual inclusion flags\\n        - **Smart Organization**: Automatic subfolder creation via naming patterns\\n        - **Filter System**: Pattern-based inclusion/exclusion rules\\n        - **Browser Automation**: Smart waiting strategies and session management\\n\\n        ## Workflow Process\\n        ```python\\n        1. Fetch Documents       # Scrape metadata \\u2192 <rig>-a-docs.json\\n        2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true\\n        3. Import Selections     # Update JSON with user choices\\n        4. Fetch Files           # Get file listings \\u2192 <rig>-b-files.json\\n        5. Export Files MD       # <rig>-b-files.md - Set item_download=true\\n        6. Import File Choices   # Update file selections\\n        7. Download Files        # Auto-organized with subfolder support\\n        ```\\n\\n        ## Configuration (CONFIG Section)\\n        ```python\\n        {\\n            \\\"rig_number\\\": \\\"R0000.020\\\",  # Target rig ID\\n            \\\"search_urls\\\": [            # Predefined search templates\\n                \\\"https://rigdoc.nov.com/search/rigsearch?q=...\\\",\\n                \\\"https://rigdoc.nov.com/search/rigsearch?q=...\\\"\\n            ],\\n            \\\"filters\\\": [                # Sequential processing rules\\n                {\\n                    \\\"type\\\": \\\"docs\\\",     # Apply to documents/files\\n                    \\\"pattern\\\": \\\"*DRILL*FLOOR*\\\",  # Glob-style matching\\n                    \\\"field\\\": \\\"item_include\\\",     # Field to modify\\n                    \\\"value\\\": True       # Set True/False based on match\\n                }\\n            ]\\n        }\\n        ```\\n\\n        ## Setup & Usage\\n        ```bash\\n        # Initialize environment\\n        py_venv_init.bat\\n        py_venv_pip_install.bat\\n\\n        # Run modes\\n        RigOfficeDownloader-v4.py [rig_number] [mode]\\n\\n        Modes:\\n        --auto         # Full automation\\n        --interactive  # Step-by-step control\\n        --config       # Modify search templates/filters\\n        ```\\n\\n        ## Key Configuration Patterns\\n        - **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)\\n        - **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)\\n        - **File Types**: Auto-prioritize PDFs with `*.pdf` pattern\\n\\n        ## Requirements\\n        - Chrome Browser + ChromeDriver\\n        - Active RigDoc credentials\\n        - Python 3.8+ with dependencies from requirements.txt\\n        - Network access to rigdoc.nov.com\\n\\n        ## Advanced Features\\n        - **Path Sanitization**: Auto-clean special chars while preserving /\\n        - **Deduplication**: Hash-based conflict resolution\\n        - **Field Ordering**: Customizable JSON/Markdown columns\\n        - **Smart Scrolling**: Progressive page loading detection\\n\\n        > Reduces documentation prep time by 60-75% compared to manual retrieval\\n        > Version 4.0 | Active development with subfolder support\\n    ```\\n\\n    ---\\n\\n    #### `README_v7.md`\\n\\n    ```markdown\\n\\n        ## Overview\\n        - Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.\\n        - Use case: curate, review, and batch-download rig-related documents and technical files.\\n\\n        ## Directory Structure\\n\\n        * `outputs/` (BASE\\\\_OUTPUT): All results stored here\\n        * `outputs/data/` (DATA\\\\_DIR): Document and file metadata (JSON/MD)\\n        * `outputs/downloads/` (DL\\\\_DIR): Downloaded PDF and file outputs\\n\\n        ## Pipeline Overview\\n\\n        1. Change search parameters (rig number, URLs)\\n        2. Configure filter chain (add, edit, delete, toggle, reorder filters)\\n        3. Fetch docs (scrape data from rigdoc.nov.com)\\n        4. Export docs to Markdown (for selection/editing)\\n        5. Import docs from Markdown (sync edited selection)\\n        6. Fetch candidate files linked to selected docs\\n        7. Export file list to Markdown (for editing/selecting files for download)\\n        8. Import updated file list from Markdown\\n        9. Download marked files (PDFs only, via Chrome)\\n\\n        ## Manual Editing\\n\\n        * Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing\\n        * Set `item_include` (docs) and `item_download` (files) fields\\n\\n        ## Running the Tool\\n\\n        ```bash\\n        python rigdocscraper.py\\n        ```\\n\\n        * Interactive menu enables step selection (numbers/comma/space-separated)\\n        * Supports adjusting parameters, filter configuration, and reviewing batch steps\\n        * Prompts will guide through editing, import/export, and download procedures\\n\\n        ## Troubleshooting\\n\\n        * Requires functioning Chrome installation; verify webdriver-manager compatibility\\n        * Common issues: browser launch failures, login/captcha requirements, file permissions\\n        * Output logs and warnings shown in terminal; inspect `outputs/data/` for progress\\n    ```\\n\",\n    \"output\": \"{\\n  \\\"instruction_format\\\": \\\"Create a README.md for RigOfficeDownloader that combines the best elements from all provided variations, limiting the length to under 70 lines. \\\\n\\\\n1. Begin with a clear project title and concise one-line summary highlighting automation of NOV RigDoc document retrieval for engineers. \\\\n2. Provide a brief overview explaining the manual problem, target users (engineers preparing for 3D modeling), and time savings impact. \\\\n3. Summarize core features and benefits: three-stage workflow (documents/files/downloads), configurable filter chain, interactive menu, smart file organization (subfolder support), Markdown-based review, error handling, metadata preservation, and session management. \\\\n4. List requirements: Windows OS, Python 3.6+ (or 3.8+ if using advanced features), Chrome browser with ChromeDriver, RigDoc credentials, network access, and dependencies via requirements.txt. \\\\n5. Describe the workflow as a succinct, numbered or bulleted pipeline. Include precise step names and show intermediate file types and editing points (e.g., JSON/Markdown). Indicate parameter/filter configuration, document/file fetching, manual review via Markdown, importing selections, and batch downloads. \\\\n6. Outline key configuration patterns in a dedicated section, using code block(s) to show JSON-style filter/sample config with comments. \\\\n7. Briefly explain directory structure: outputs/, outputs/data/, outputs/downloads/. \\\\n8. Provide a Quick Start section with concrete stepwise instructions (e.g., py_venv_init.bat, py_venv_pip_install.bat, running the script or batch). Include notes about interactive modes, automation, and config options. \\\\n9. Add practical usage tips: editing Markdown tables before import, using filters for pre-selection, watching for logs and progress in outputs/data/. \\\\n10. Reserve a Troubleshooting section noting common browser/login issues, debugging with logs, and where to look for output data. \\\\n11. Concisely mention version history or advanced features (e.g., deduplication, smart scrolling, field ordering, path sanitization) as a one- or two-line highlight at the end. \\\\n12. Ensure technical accuracy, maintain procedural clarity, preserve domain-specific terminology, and keep all content as actionable commands without self-reference or first-person phrasing. Preserve original practical sequence and workflow logic throughout.\\\"\\n}\"\n  },\n  \"b\": {\n    \"instruction\": \"# Essence Distillation\\n\\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\\n\\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\",\n    \"input\": \"{\\n  \\\"instruction_format\\\": \\\"Create a README.md for RigOfficeDownloader that combines the best elements from all provided variations, limiting the length to under 70 lines. \\\\n\\\\n1. Begin with a clear project title and concise one-line summary highlighting automation of NOV RigDoc document retrieval for engineers. \\\\n2. Provide a brief overview explaining the manual problem, target users (engineers preparing for 3D modeling), and time savings impact. \\\\n3. Summarize core features and benefits: three-stage workflow (documents/files/downloads), configurable filter chain, interactive menu, smart file organization (subfolder support), Markdown-based review, error handling, metadata preservation, and session management. \\\\n4. List requirements: Windows OS, Python 3.6+ (or 3.8+ if using advanced features), Chrome browser with ChromeDriver, RigDoc credentials, network access, and dependencies via requirements.txt. \\\\n5. Describe the workflow as a succinct, numbered or bulleted pipeline. Include precise step names and show intermediate file types and editing points (e.g., JSON/Markdown). Indicate parameter/filter configuration, document/file fetching, manual review via Markdown, importing selections, and batch downloads. \\\\n6. Outline key configuration patterns in a dedicated section, using code block(s) to show JSON-style filter/sample config with comments. \\\\n7. Briefly explain directory structure: outputs/, outputs/data/, outputs/downloads/. \\\\n8. Provide a Quick Start section with concrete stepwise instructions (e.g., py_venv_init.bat, py_venv_pip_install.bat, running the script or batch). Include notes about interactive modes, automation, and config options. \\\\n9. Add practical usage tips: editing Markdown tables before import, using filters for pre-selection, watching for logs and progress in outputs/data/. \\\\n10. Reserve a Troubleshooting section noting common browser/login issues, debugging with logs, and where to look for output data. \\\\n11. Concisely mention version history or advanced features (e.g., deduplication, smart scrolling, field ordering, path sanitization) as a one- or two-line highlight at the end. \\\\n12. Ensure technical accuracy, maintain procedural clarity, preserve domain-specific terminology, and keep all content as actionable commands without self-reference or first-person phrasing. Preserve original practical sequence and workflow logic throughout.\\\"\\n}\",\n    \"output\": \"{\\n  \\\"distilled_essence\\\": [\\n    \\\"# RigOfficeDownloader\\\",\\n    \\\"*Automates NOV RigDoc document retrieval, streamlining technical documentation gathering for engineering and 3D modeling workflows.*\\\",\\n    \\\"\\\",\\n    \\\"## Overview\\\",\\n    \\\"Eliminates manual searching, selection, and downloading in RigDoc. Designed for engineers and project teams, reducing prep time by 60\\u201375%.\\\",\\n    \\\"\\\",\\n    \\\"## Features & Benefits\\\",\\n    \\\"- Three-stage pipeline: document selection \\u2192 file selection \\u2192 batch download\\\",\\n    \\\"- Interactive menu for granular step control\\\",\\n    \\\"- Configurable filter chain for automatic inclusion/exclusion\\\",\\n    \\\"- Markdown-based review and selection of records\\\",\\n    \\\"- Smart, metadata-driven file organization with subfolder support\\\",\\n    \\\"- Preserves document relationships and context\\\",\\n    \\\"- Robust error handling and session management\\\",\\n    \\\"- Supports manual checkpoints and batch processing\\\",\\n    \\\"\\\",\\n    \\\"## Requirements\\\",\\n    \\\"- Windows OS\\\",\\n    \\\"- Python 3.6+ (3.8+ for advanced features)\\\",\\n    \\\"- Chrome browser and ChromeDriver\\\",\\n    \\\"- Active RigDoc credentials\\\",\\n    \\\"- Network access to rigdoc.nov.com\\\",\\n    \\\"- Dependencies installed via requirements.txt\\\",\\n    \\\"\\\",\\n    \\\"## Workflow Pipeline\\\",\\n    \\\"1. Configure search parameters (rig number, search URLs)\\\",\\n    \\\"2. Set up filters (pattern-based rules for docs/files)\\\",\\n    \\\"3. Fetch document metadata \\u00a0\\u2192 \\u00a0JSON export in outputs/data/\\\",\\n    \\\"4. Export documents to Markdown \\u00a0\\u2192 \\u00a0manual selection (set `item_include`)\\\",\\n    \\\"5. Import edited Markdown to update document selections\\\",\\n    \\\"6. Fetch file metadata for selected documents \\u00a0\\u2192 \\u00a0JSON export\\\",\\n    \\\"7. Export files to Markdown \\u00a0\\u2192 \\u00a0manual selection (set `item_download`)\\\",\\n    \\\"8. Import edited Markdown to update file selections\\\",\\n    \\\"9. Batch download specified files \\u00a0\\u2192 \\u00a0organized in outputs/downloads/\\\",\\n    \\\"\\\",\\n    \\\"## Example Configuration\\\",\\n    \\\"```json\\\",\\n    \\\"{\\\",\\n    \\\"  \\\\\\\"rig_number\\\\\\\": \\\\\\\"R0000.020\\\\\\\",\\\",\\n    \\\"  \\\\\\\"search_urls\\\\\\\": [\\\\\\\"https://rigdoc.nov.com/search/rigsearch?q=...\\\\\\\"],\\\",\\n    \\\"  \\\\\\\"filters\\\\\\\": [\\\",\\n    \\\"    { \\\\\\\"type\\\\\\\": \\\\\\\"docs\\\\\\\", \\\\\\\"pattern\\\\\\\": \\\\\\\"*DRILL*FLOOR*\\\\\\\", \\\\\\\"field\\\\\\\": \\\\\\\"item_include\\\\\\\", \\\\\\\"value\\\\\\\": true },\\\",\\n    \\\"    { \\\\\\\"type\\\\\\\": \\\\\\\"files\\\\\\\", \\\\\\\"pattern\\\\\\\": \\\\\\\"*.pdf\\\\\\\", \\\\\\\"field\\\\\\\": \\\\\\\"item_download\\\\\\\", \\\\\\\"value\\\\\\\": true }\\\",\\n    \\\"  ]\\\",\\n    \\\"}\\\",\\n    \\\"```\\\",\\n    \\\"\\\",\\n    \\\"## Directory Structure\\\",\\n    \\\"- `outputs/` \\u2013 root for all results\\\",\\n    \\\"- `outputs/data/` \\u2013 JSON/Markdown metadata exports and logs\\\",\\n    \\\"- `outputs/downloads/` \\u2013 downloaded document and file outputs\\\",\\n    \\\"\\\",\\n    \\\"## Quick Start\\\",\\n    \\\"1. Run `py_venv_init.bat` and `py_venv_pip_install.bat`\\\",\\n    \\\"2. Launch with `RigOfficeDownloader-v4.bat` or `python rigdocscraper.py`\\\",\\n    \\\"3. Use the interactive menu to select workflow steps\\\",\\n    \\\"\\\",\\n    \\\"### Modes\\\",\\n    \\\"- `--auto` for full automation\\\",\\n    \\\"- `--interactive` for step-by-step execution\\\",\\n    \\\"- `--config` to modify parameters or filters\\\",\\n    \\\"\\\",\\n    \\\"## Practical Usage Tips\\\",\\n    \\\"- Edit exported `.md` files to fine-tune records before progressing\\\",\\n    \\\"- Leverage filters for efficient pre-selection of relevant docs/files\\\",\\n    \\\"- Monitor processing progress and logs in `outputs/data/`\\\",\\n    \\\"\\\",\\n    \\\"## Troubleshooting\\\",\\n    \\\"- Ensure Chrome and ChromeDriver are installed and compatible\\\",\\n    \\\"- Validate RigDoc login and network access\\\",\\n    \\\"- Check terminal logs and `outputs/data/` for errors or progress\\\",\\n    \\\"- Resolve browser launch issues, login/captcha problems, or file permission errors as noted in logs\\\",\\n    \\\"\\\",\\n    \\\"## Advanced & Version Info\\\",\\n    \\\"Version 4.0+ adds subfolder support, deduplication, field ordering, path sanitization, smart scrolling, and improved batch control.\\\"\\n  ]\\n}\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"combined_result\": [\n    \"# RigOfficeDownloader\",\n    \"*Automates NOV RigDoc document retrieval, streamlining technical documentation gathering for engineering and 3D modeling workflows.*\",\n    \"\",\n    \"## Overview\",\n    \"Eliminates manual searching, selection, and downloading in RigDoc. Designed for engineers and project teams, reducing prep time by 60–75%.\",\n    \"\",\n    \"## Features & Benefits\",\n    \"- Three-stage pipeline: document selection → file selection → batch download\",\n    \"- Interactive menu for granular step control\",\n    \"- Configurable filter chain for automatic inclusion/exclusion\",\n    \"- Markdown-based review and selection of records\",\n    \"- Smart, metadata-driven file organization with subfolder support\",\n    \"- Preserves document relationships and context\",\n    \"- Robust error handling and session management\",\n    \"- Supports manual checkpoints and batch processing\",\n    \"\",\n    \"## Requirements\",\n    \"- Windows OS\",\n    \"- Python 3.6+ (3.8+ for advanced features)\",\n    \"- Chrome browser and ChromeDriver\",\n    \"- Active RigDoc credentials\",\n    \"- Network access to rigdoc.nov.com\",\n    \"- Dependencies installed via requirements.txt\",\n    \"\",\n    \"## Workflow Pipeline\",\n    \"1. Configure search parameters (rig number, search URLs)\",\n    \"2. Set up filters (pattern-based rules for docs/files)\",\n    \"3. Fetch document metadata  →  JSON export in outputs/data/\",\n    \"4. Export documents to Markdown  →  manual selection (set `item_include`)\",\n    \"5. Import edited Markdown to update document selections\",\n    \"6. Fetch file metadata for selected documents  →  JSON export\",\n    \"7. Export files to Markdown  →  manual selection (set `item_download`)\",\n    \"8. Import edited Markdown to update file selections\",\n    \"9. Batch download specified files  →  organized in outputs/downloads/\",\n    \"\",\n    \"## Example Configuration\",\n    \"```json\",\n    \"{\",\n    \"  \\\"rig_number\\\": \\\"R0000.020\\\",\",\n    \"  \\\"search_urls\\\": [\\\"https://rigdoc.nov.com/search/rigsearch?q=...\\\"],\",\n    \"  \\\"filters\\\": [\",\n    \"    { \\\"type\\\": \\\"docs\\\", \\\"pattern\\\": \\\"*DRILL*FLOOR*\\\", \\\"field\\\": \\\"item_include\\\", \\\"value\\\": true },\",\n    \"    { \\\"type\\\": \\\"files\\\", \\\"pattern\\\": \\\"*.pdf\\\", \\\"field\\\": \\\"item_download\\\", \\\"value\\\": true }\",\n    \"  ]\",\n    \"}\",\n    \"```\",\n    \"\",\n    \"## Directory Structure\",\n    \"- `outputs/` – root for all results\",\n    \"- `outputs/data/` – JSON/Markdown metadata exports and logs\",\n    \"- `outputs/downloads/` – downloaded document and file outputs\",\n    \"\",\n    \"## Quick Start\",\n    \"1. Run `py_venv_init.bat` and `py_venv_pip_install.bat`\",\n    \"2. Launch with `RigOfficeDownloader-v4.bat` or `python rigdocscraper.py`\",\n    \"3. Use the interactive menu to select workflow steps\",\n    \"\",\n    \"### Modes\",\n    \"- `--auto` for full automation\",\n    \"- `--interactive` for step-by-step execution\",\n    \"- `--config` to modify parameters or filters\",\n    \"\",\n    \"## Practical Usage Tips\",\n    \"- Edit exported `.md` files to fine-tune records before progressing\",\n    \"- Leverage filters for efficient pre-selection of relevant docs/files\",\n    \"- Monitor processing progress and logs in `outputs/data/`\",\n    \"\",\n    \"## Troubleshooting\",\n    \"- Ensure Chrome and ChromeDriver are installed and compatible\",\n    \"- Validate RigDoc login and network access\",\n    \"- Check terminal logs and `outputs/data/` for errors or progress\",\n    \"- Resolve browser launch issues, login/captcha problems, or file permission errors as noted in logs\",\n    \"\",\n    \"## Advanced & Version Info\",\n    \"Version 4.0+ adds subfolder support, deduplication, field ordering, path sanitization, smart scrolling, and improved batch control.\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Result Summarizer\n\nYou are a specialized aggregator that summarizes results from multiple instruction steps. Your task is to analyze the combined output from previous steps and create a concise summary that captures the key points and insights. Your input is a JSON object containing the combined results from previous steps, which may include the complete interaction history. Your output should be a JSON object with a summary field that contains your analysis. Execute as:\n\n`{role=result_summarizer;input=[combined_result:json];process=[identify_key_themes(),extract_main_points(),condense_into_summary(),highlight_important_insights()];constraints=[maintain_brevity(),preserve_essential_information(),avoid_introducing_new_concepts()];requirements=[output_as_json(),ensure_clarity(),focus_on_most_relevant_aspects()];output={summary:json}}`", "step": "b", "title": "Result Su<PERSON><PERSON><PERSON>", "input": "{\n  \"combined_result\": [\n    \"# RigOfficeDownloader\",\n    \"*Automates NOV RigDoc document retrieval, streamlining technical documentation gathering for engineering and 3D modeling workflows.*\",\n    \"\",\n    \"## Overview\",\n    \"Eliminates manual searching, selection, and downloading in RigDoc. Designed for engineers and project teams, reducing prep time by 60–75%.\",\n    \"\",\n    \"## Features & Benefits\",\n    \"- Three-stage pipeline: document selection → file selection → batch download\",\n    \"- Interactive menu for granular step control\",\n    \"- Configurable filter chain for automatic inclusion/exclusion\",\n    \"- Markdown-based review and selection of records\",\n    \"- Smart, metadata-driven file organization with subfolder support\",\n    \"- Preserves document relationships and context\",\n    \"- Robust error handling and session management\",\n    \"- Supports manual checkpoints and batch processing\",\n    \"\",\n    \"## Requirements\",\n    \"- Windows OS\",\n    \"- Python 3.6+ (3.8+ for advanced features)\",\n    \"- Chrome browser and ChromeDriver\",\n    \"- Active RigDoc credentials\",\n    \"- Network access to rigdoc.nov.com\",\n    \"- Dependencies installed via requirements.txt\",\n    \"\",\n    \"## Workflow Pipeline\",\n    \"1. Configure search parameters (rig number, search URLs)\",\n    \"2. Set up filters (pattern-based rules for docs/files)\",\n    \"3. Fetch document metadata  →  JSON export in outputs/data/\",\n    \"4. Export documents to Markdown  →  manual selection (set `item_include`)\",\n    \"5. Import edited Markdown to update document selections\",\n    \"6. Fetch file metadata for selected documents  →  JSON export\",\n    \"7. Export files to Markdown  →  manual selection (set `item_download`)\",\n    \"8. Import edited Markdown to update file selections\",\n    \"9. Batch download specified files  →  organized in outputs/downloads/\",\n    \"\",\n    \"## Example Configuration\",\n    \"```json\",\n    \"{\",\n    \"  \\\"rig_number\\\": \\\"R0000.020\\\",\",\n    \"  \\\"search_urls\\\": [\\\"https://rigdoc.nov.com/search/rigsearch?q=...\\\"],\",\n    \"  \\\"filters\\\": [\",\n    \"    { \\\"type\\\": \\\"docs\\\", \\\"pattern\\\": \\\"*DRILL*FLOOR*\\\", \\\"field\\\": \\\"item_include\\\", \\\"value\\\": true },\",\n    \"    { \\\"type\\\": \\\"files\\\", \\\"pattern\\\": \\\"*.pdf\\\", \\\"field\\\": \\\"item_download\\\", \\\"value\\\": true }\",\n    \"  ]\",\n    \"}\",\n    \"```\",\n    \"\",\n    \"## Directory Structure\",\n    \"- `outputs/` – root for all results\",\n    \"- `outputs/data/` – JSON/Markdown metadata exports and logs\",\n    \"- `outputs/downloads/` – downloaded document and file outputs\",\n    \"\",\n    \"## Quick Start\",\n    \"1. Run `py_venv_init.bat` and `py_venv_pip_install.bat`\",\n    \"2. Launch with `RigOfficeDownloader-v4.bat` or `python rigdocscraper.py`\",\n    \"3. Use the interactive menu to select workflow steps\",\n    \"\",\n    \"### Modes\",\n    \"- `--auto` for full automation\",\n    \"- `--interactive` for step-by-step execution\",\n    \"- `--config` to modify parameters or filters\",\n    \"\",\n    \"## Practical Usage Tips\",\n    \"- Edit exported `.md` files to fine-tune records before progressing\",\n    \"- Leverage filters for efficient pre-selection of relevant docs/files\",\n    \"- Monitor processing progress and logs in `outputs/data/`\",\n    \"\",\n    \"## Troubleshooting\",\n    \"- Ensure Chrome and ChromeDriver are installed and compatible\",\n    \"- Validate RigDoc login and network access\",\n    \"- Check terminal logs and `outputs/data/` for errors or progress\",\n    \"- Resolve browser launch issues, login/captcha problems, or file permission errors as noted in logs\",\n    \"\",\n    \"## Advanced & Version Info\",\n    \"Version 4.0+ adds subfolder support, deduplication, field ordering, path sanitization, smart scrolling, and improved batch control.\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"summary\": \"RigOfficeDownloader is a tool designed to automate the retrieval of technical documents from the NOV RigDoc system, significantly reducing the manual effort involved for engineering and 3D modeling teams. It features a three-stage pipeline (document selection, file selection, batch download), interactive and automated modes, and employs configurable filters to streamline and customize the selection process. The workflow exports and processes metadata using JSON and Markdown files, enabling granular control and review before batch downloading files in a smartly organized directory structure. The tool requires Windows, Python 3.6+, Chrome/ChromeDriver, and valid RigDoc credentials. Version 4.0+ introduces enhancements such as subfolder organization, deduplication, and more robust session handling. Comprehensive troubleshooting and workflow guidance are included, making it accessible for both automated and interactive use cases.\"\n}", "cost": 0.0}}}], "aggregation_summary": {"aggregator": "0222", "inputs": ["a", "b"], "final_result": "{\n  \"summary\": \"RigOfficeDownloader is a tool designed to automate the retrieval of technical documents from the NOV RigDoc system, significantly reducing the manual effort involved for engineering and 3D modeling teams. It features a three-stage pipeline (document selection, file selection, batch download), interactive and automated modes, and employs configurable filters to streamline and customize the selection process. The workflow exports and processes metadata using JSON and Markdown files, enabling granular control and review before batch downloading files in a smartly organized directory structure. The tool requires Windows, Python 3.6+, Chrome/ChromeDriver, and valid RigDoc credentials. Version 4.0+ introduces enhancements such as subfolder organization, deduplication, and more robust session handling. Comprehensive troubleshooting and workflow guidance are included, making it accessible for both automated and interactive use cases.\"\n}", "total_cost": 0.0}, "total_cost": 0.0}