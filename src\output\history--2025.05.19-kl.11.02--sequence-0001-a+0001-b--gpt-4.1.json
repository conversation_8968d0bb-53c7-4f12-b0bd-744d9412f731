{"initial_prompt": "# GOAL: CREATE A STRUCTURED README.MD <70 LINES\n\n PROJECT_RIG_ID = \"R5225\"\nPROJECTINFO_GAD = [\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocNumber=*G00*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocNumber=*GAD-00*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DRILL*FLOOR*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DERRICK*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*EQUIPMENT*LAYOUT*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ELEVATION*VIEW*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TOP*DRIVE*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TRAVELLINGBLOCK*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ROUGHNECK*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*HYDRATONG*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MUDBUCKET*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*HYDRARACKER*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*BELLY*BOARD*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*FINGER*BOARD*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RACKING*BOARD*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CATWALK*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MOUSE*HOLE*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*GUIDE*ARM*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*EBT*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ELEVATED*BACKUP*TONG*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CATHEAD*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SPIDER*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ROTARY*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SLIPS*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*AHC*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CMC*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*STANDBUILDING*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TUBULAR*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RTX*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*PIPE*GUIDE*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*LIFTING*CYLINDER*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*STAR*RACKER*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*UTILITY*HANDLING*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RACKING*SYSTEM*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RAISED*SYSTEM*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DFMA*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MPMA*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MANUPILATOR*ARM*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*PIPE*CHUTE*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RISER*CHUTE*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SERVICE*BASKET*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MAINTENANCE*BASKET*\",\n]\nFILTERS_SIMULATOR_PATTERNS_INCLUDE = [\n    \"*DRILL*FLOOR*\",\n    \"*ELEVATION*VIEW*\",\n    \"*EQUIPMENT*LAYOUT*\",\n    \"*G000*\",\n    \"*GAD-00*\",\n    \"*A000*\",\n    \"*ASM-00*\",\n]\nFILTERS_SIMULATOR_PATTERNS_SKIP = [\n    \"*Agitator*\",\n    \"*BOP *\",\n    \"*Bulk*Systems*\",\n    \"*Cabinet*\",\n    \"*Centrifuge*\",\n    \"*Compressor*\",\n    \"*Crane*\",\n    \"*Cuttings*\",\n    \"*Cyberbase*\",\n    \"*DCDA *\",\n    \"*DCI*\",\n    \"*Deadline*\",\n    \"*Gantry*Crane*\",\n    \"*HPU*\",\n    \"*HVAC*\",\n    \"*Knuckle*Crane*\",\n    \"*LMRP*\",\n    \"*Manrider*\",\n    \"*Mixer*\",\n    \"*Mixing*\",\n    \"*Mud *\",\n    \"*Pumps*\",\n    \"*Sdi*\",\n    \"*Standby*\",\n    \"*Station*\",\n    \"*Tank *\",\n    \"*Valve*\",\n    \"*VOID*\",\n    \"*Winch*\",\n    \"*X-tree*\",\n    \"*Yoke*\",\n]\nCONFIG = {\n    \"rig_number\": f\"{PROJECT_RIG_ID}.020\",\n    \"search_urls\": PROJECTINFO_GAD,\n    \"show_progress\": True,\n    \"filters\": [\n        {\n            \"type\": \"docs\",\n            \"enabled\": True,\n            \"pattern\": FILTERS_SIMULATOR_PATTERNS_INCLUDE,\n            \"match_field\": \"item_generated_name\",\n            \"field\": \"item_include\",\n            \"value\": True,\n            \"comment\": \"\",\n        },\n        {\n            \"type\": \"docs\",\n            \"enabled\": True,\n            \"pattern\": FILTERS_SIMULATOR_PATTERNS_SKIP,\n            \"match_field\": \"item_generated_name\",\n            \"field\": \"item_include\",\n            \"value\": False,\n            \"comment\": \"\",\n        },\n        {\n            \"type\": \"docs\",\n            \"enabled\": True,\n            \"pattern\": FILTERS_SIMULATOR_PATTERNS_SKIP,\n            \"match_field\": \"item_case_description\",\n            \"field\": \"item_include\",\n            \"value\": False,\n            \"comment\": \"\",\n        },\n        {\n            \"type\": \"docs\",\n            \"enabled\": True,\n            \"pattern\": [\"(VOID)\"],\n            \"match_field\": \"item_case_description\",\n            \"field\": \"item_include\",\n            \"value\": False,\n            \"comment\": \"\",\n        },\n        {\n            \"type\": \"files\",\n            \"enabled\": True,\n            \"pattern\": [\"*.pdf\"],\n            \"match_field\": \"item_file_ext\",\n            \"field\": \"item_download\",\n            \"value\": True,\n            \"comment\": \"\",\n        },\n        {\n            \"type\": \"files\",\n            \"enabled\": True,\n            \"pattern\": FILTERS_SIMULATOR_PATTERNS_SKIP,\n            \"match_field\": \"item_case_description\",\n            \"field\": \"item_download\",\n            \"value\": False,\n            \"comment\": \"\",\n        },\n        {\n            \"type\": \"files\",\n            \"enabled\": True,\n            \"pattern\": [\"*Partslist*\"],\n            \"match_field\": \"item_generated_name\",\n            \"field\": \"item_download\",\n            \"value\": False,\n            \"comment\": \"\",\n        },\n    ],\n}\nimport json\nimport os\nimport re\nimport shutil\nimport time\nimport timeit\nfrom contextlib import contextmanager\nfrom urllib.parse import urljoin\nimport colorama\nimport dateutil.parser\nfrom ansimarkup import ansiprint\nfrom bs4 import BeautifulSoup\nfrom selenium import webdriver\nfrom selenium.webdriver.chrome.options import Options\nfrom selenium.webdriver.chrome.service import Service\nfrom selenium.webdriver.support.ui import WebDriverWait\nfrom selenium.webdriver.common.by import By\nfrom selenium.webdriver.support import expected_conditions as EC\nfrom selenium.common.exceptions import TimeoutException\nfrom webdriver_manager.chrome import ChromeDriverManager\ncolorama.init()\nBASE_OUTPUT = os.path.abspath(os.path.join(os.getcwd(), \"outputs\"))\nDATA_DIR    = os.path.join(BASE_OUTPUT, \"data\")\nDL_DIR      = os.path.join(BASE_OUTPUT, \"downloads\")\nfor d in (BASE_OUTPUT, DATA_DIR, DL_DIR):\n    os.makedirs(d, exist_ok=True)\nDEFAULT_DOC_FIELD_ORDER = [\n    \"item_include\",\n    \"item_generated_name\",\n    \"item_type\",\n    \"item_case_no\",\n    \"item_drawing_type\",\n    \"item_doc_no\",\n    \"item_date\",\n    \"item_title\",\n    \"item_case_description\",\n    \"item_status\",\n    \"item_revision\",\n    \"item_responsible\",\n    \"item_url\",\n]\nDEFAULT_FILE_FIELD_ORDER = [\n    \"item_type\",\n    \"item_generated_name\",\n    \"item_case_no\",\n    \"item_drawing_type\",\n    \"item_title\",\n    \"item_doc_no\",\n    \"item_file_ext\",\n    \"item_download\",\n    \"item_file_size\",\n    \"item_file_id\",\n    \"item_date\",\n    \"item_revision\",\n    \"item_case_description\",\n    \"item_status\",\n    \"item_responsible\",\n    \"item_url\",\n]\ndef cprint(x, color='#FFFFFF'):\n    ansiprint(f'<fg {color}>{x}')\nprint_err  = lambda x: cprint(x, '#C92B65')\nprint_warn = lambda x: cprint(x, '#E6992B')\nprint_ok   = lambda x: cprint(x, '#1FCE46')\nprint_inf  = lambda x: cprint(x, '#FFFFFF')\nprint_sub  = lambda x: cprint(x, '#74705D')\nclass SleepCondition:\n    def __init__(self, s):\n        self.s = s\n    def __call__(self, _):\n        time.sleep(self.s)\n        return True\ndef reformat_date(s):\n    try:\n        dt = dateutil.parser.parse(s)\n        return dt.strftime('%Y.%m.%d')\n    except:\n        return s\ndef match_pattern(text, pattern):\n    if isinstance(pattern, list):\n        return any(match_pattern(text, p) for p in pattern)\n    regex_pattern = pattern.replace(\".\", \"\\\\.\").replace(\"*\", \".*\").replace(\"?\", \".\")\n    return bool(re.search(f\"^{regex_pattern}$\", text, re.IGNORECASE))\ndef sanitize_filename(s):\n    replacements = {\n        \"'\": \"\", '\"': \"\", '`': \"\",\n        '\\\\': \"_\", '|': \"_\", '?': \"_\", '*': \"_\", '<': \"_\", '>': \"_\", ':': \"_\"\n    }\n    result = s\n    for char, replacement in replacements.items():\n        result = result.replace(char, replacement)\n    result = ''.join(c for c in result if c.isprintable() or c == '/')\n    result = re.sub(r'\\s+', ' ', result)\n    return result.strip()\ndef reorder_dict_keys(data_dict, field_order):\n    reordered = {}\n    for key in field_order:\n        if key in data_dict:\n            reordered[key] = data_dict[key]\n    return reordered\ndef remove_duplicates(data, exclude_keys=None, priority_key='item_download', priority_vals=None):\n    if not exclude_keys:\n        exclude_keys=[]\n    if not priority_vals:\n        priority_vals=[]\n    exset = set(exclude_keys)\n    pmap  = {v:i for i,v in enumerate(priority_vals)}\n    out   = {}\n    for d in data:\n        filt = {k:v for k,v in d.items() if k not in exset}\n        hkey = frozenset(filt.items())\n        if hkey not in out:\n            out[hkey] = d\n        else:\n            if priority_key and priority_vals:\n                newv = d.get(priority_key)\n                oldv = out[hkey].get(priority_key)\n                if (newv in pmap) and (oldv in pmap) and pmap[newv] < pmap[oldv]:\n                    out[hkey] = d\n                elif newv in pmap and oldv not in pmap:\n                    out[hkey] = d\n    return list(out.values())\ndef dedupe_and_save(new_items, path, field_order=None):\n    if os.path.exists(path):\n        try:\n            with open(path,'r',encoding='utf-8') as f:\n                old = json.load(f)\n        except:\n            old = []\n    else:\n        old = []\n    merged = old + new_items\n    merged = remove_duplicates(\n        merged,\n        exclude_keys=['item_download','item_generated_path'],\n        priority_key='item_download',\n        priority_vals=[False,True]\n    )\n    if field_order:\n        merged = [reorder_dict_keys(d, field_order) for d in merged]\n    tmp = path + '.temp'\n    with open(tmp,'w',encoding='utf-8') as f:\n        json.dump(merged,f,indent=2)\n    os.replace(tmp,path)\n    rel_path = os.path.relpath(path, os.getcwd())\n    print_inf(f\"Saved {len(merged)} items => {rel_path}\")\ndef append_items(new_items, path, field_order=None):\n    if new_items:\n        dedupe_and_save(new_items, path, field_order=field_order)\ndef json_to_md_table(json_path, md_path, field_order=None, filter_chain=None):\n    if not os.path.exists(json_path):\n        print_err(f\"No JSON found: {json_path}\")\n        return\n    with open(json_path,'r',encoding='utf-8') as f:\n        data = json.load(f)\n    if not data:\n        print_warn(f\"No data in {json_path}\")\n        return\n    item_type = \"docs\" if \"-a-docs.\" in json_path else \"files\"\n    if filter_chain and isinstance(filter_chain, list):\n        for filter_idx, filter_config in enumerate(filter_chain):\n            if not filter_config.get(\"enabled\", False) or filter_config.get(\"type\") != item_type:\n                continue\n            pattern = filter_config.get(\"pattern\", \"\")\n            field = filter_config.get(\"field\", \"\")\n            value = filter_config.get(\"value\", True)\n            comment = filter_config.get(\"comment\", \"\")\n            match_field = filter_config.get(\"match_field\", \"item_generated_name\")\n            if pattern and field:\n                filtered_count = 0\n                for d in data:\n                    field_value = d.get(match_field, \"\")\n                    if field_value and match_pattern(field_value, pattern):\n                        if d.get(field) != value:\n                            d[field] = value\n                            filtered_count += 1\n                if filtered_count > 0:\n                    action = \"Set\" if value else \"Cleared\"\n                    if isinstance(pattern, list):\n                        pattern_str = f\"[{', '.join(pattern)}]\"\n                    else:\n                        pattern_str = f\"'{pattern}'\"\n                    match_field_str = f\" in {match_field}\" if match_field != \"item_generated_name\" else \"\"\n                    print_ok(f\"Filter #{filter_idx+1}: {action} {field}={value} for {filtered_count} items matching {pattern_str}{match_field_str}\")\n                    if comment:\n                        print_sub(f\"  → {comment}\")\n    if not field_order:\n        colset = set()\n        for d in data:\n            colset.update(d.keys())\n        field_order = sorted(colset)\n    lines = []\n    lines.append(\"| \" + \" | \".join(field_order) + \" |\")\n    lines.append(\"|\" + \"|\".join([\"---\"]*len(field_order)) + \"|\")\n    for d in data:\n        row = []\n        for c in field_order:\n            val = d.get(c,\"\")\n            if isinstance(val,bool):\n                val = str(val).lower()\n            val = str(val).replace(\"|\",\"\\\\|\")\n            row.append(val)\n        lines.append(\"| \" + \" | \".join(row) + \" |\")\n    with open(md_path,'w',encoding='utf-8') as f:\n        f.write(\"\\n\".join(lines))\n    rel_md_path = os.path.relpath(md_path, os.getcwd())\n    print_ok(f\"Exported {len(data)} items => {rel_md_path}\")\ndef md_table_to_json(md_path, json_path, field_order=None):\n    if not os.path.exists(md_path):\n        print_err(f\"No Markdown file found: {md_path}\")\n        return\n    with open(md_path,'r',encoding='utf-8') as f:\n        lines=f.read().splitlines()\n    table_lines=[ln.strip() for ln in lines if ln.strip().startswith(\"|\")]\n    if len(table_lines)<2:\n        print_err(\"No valid table rows found in the Markdown.\")\n        return\n    header=table_lines[0]\n    data_lines=table_lines[2:]\n    cols=[x.strip() for x in header.strip('|').split('|')]\n    new_data=[]\n    for ln in data_lines:\n        rowcols=[x.strip() for x in ln.strip('|').split('|')]\n        if len(rowcols)!=len(cols):\n            print_warn(f\"Skipping mismatch line:\\n{ln}\")\n            continue\n        d={}\n        for i,c in enumerate(cols):\n            val=rowcols[i]\n            if val.lower()==\"true\":\n                val=True\n            elif val.lower()==\"false\":\n                val=False\n            d[c]=val\n        new_data.append(d)\n    if field_order:\n        new_data = [reorder_dict_keys(item, field_order) for item in new_data]\n    with open(json_path,'w',encoding='utf-8') as f:\n        json.dump(new_data,f,indent=2)\n    rel_json_path = os.path.relpath(json_path, os.getcwd())\n    print_ok(f\"Imported {len(new_data)} items => {rel_json_path}\")\nclass RigDocScraper:\n    def __init__(self, rig, show_progress=False):\n        self.rig=rig\n        self.show=show_progress\n        self.driver=None\n        self.docs_path = os.path.join(DATA_DIR, f'{rig}-a-docs.json')\n        self.files_path= os.path.join(DATA_DIR, f'{rig}-b-files.json')\n    @contextmanager\n    def browser_session(self, download_mode=False):\n        profdir = None\n        try:\n            opts = Options()\n            opts.add_argument(\"--log-level=3\")\n            if download_mode:\n                appdata = os.environ.get(\"APPDATA\", os.getcwd())\n                profdir = os.path.join(appdata, \"chromedriver_profile\")\n                os.makedirs(profdir, exist_ok=True)\n                opts.add_argument(f\"--user-data-dir={profdir}\")\n                dl_dir = os.path.join(DL_DIR, self.rig)\n                os.makedirs(dl_dir, exist_ok=True)\n                prefs = {\n                    \"download.default_directory\": dl_dir,\n                    \"download.prompt_for_download\": False,\n                    \"download.directory_upgrade\": True,\n                    \"safebrowsing.enabled\": True\n                }\n                opts.add_experimental_option(\"prefs\", prefs)\n            svc = Service(ChromeDriverManager().install())\n            self.driver = webdriver.Chrome(service=svc, options=opts)\n            wait_time = 2 if download_mode else 5\n            WebDriverWait(self.driver, wait_time).until(SleepCondition(wait_time))\n            self.driver.implicitly_wait(5 if download_mode else 10)\n            if self.show:\n                print_sub(f\"[Browser] Started {'download-enabled ' if download_mode else ''}session\")\n            yield self.driver\n        finally:\n            if self.driver:\n                if self.show:\n                    print_sub(\"[Browser] Closing session\")\n                self.driver.quit()\n                self.driver = None\n                if download_mode and profdir:\n                    try:\n                        shutil.rmtree(profdir)\n                    except Exception as e:\n                        if self.show:\n                            print_warn(f\"[Browser] Failed to clean up profile: {e}\")\n    def fetch_docs(self, urls):\n        if not os.path.exists(self.docs_path):\n            with open(self.docs_path,'w',encoding='utf-8') as f:\n                json.dump([],f)\n        with self.browser_session() as driver:\n            for url in urls:\n                if self.show:\n                    print_sub(f\"[DocScrape] => {url}\")\n                driver.get(url)\n                is_rig = (\"rigsearch\" in url.lower()) or (\"advancedsearch\" in url.lower())\n                max_wait = 10 if is_rig else 5\n                wait_scroll = 5 if is_rig else 2\n                try:\n                    WebDriverWait(driver, max_wait).until(\n                        EC.presence_of_element_located((By.CSS_SELECTOR, \"a.search-result-link\"))\n                    )\n                except TimeoutException:\n                    print_warn(f\"Waited up to {max_wait}s, but no 'search-result-link' found. Proceeding anyway...\")\n                inc = 0\n                prev = driver.execute_script(\"return document.body.scrollHeight\")\n                while inc < 150:\n                    inc += 1\n                    driver.execute_script(\"window.scrollTo(0, document.body.scrollHeight);\")\n                    time.sleep(wait_scroll)\n                    newh = driver.execute_script(\"return document.body.scrollHeight\")\n                    if self.show:\n                        print_sub(f\" -> Scroll {inc}, h={newh}\")\n                    if newh == prev:\n                        break\n                    prev = newh\n                soup = BeautifulSoup(driver.page_source, \"html.parser\")\n                row_elems = soup.find_all(\"a\", class_=\"search-result-link\")\n                rows = [(td.find_parent(\"tr\")).find_all(\"td\") for td in row_elems]\n                if self.show:\n                    print_inf(f\"Found {len(rows)} doc rows.\")\n                new_docs = []\n                for row_cells in rows:\n                    doc_title   = re.sub(r'[\\n\\r]+',' ', row_cells[1].a.text.strip())\n                    sub_type    = row_cells[2].get_text(strip=True)\n                    doc_no      = row_cells[3].get_text(strip=True)\n                    responsible = row_cells[4].get_text(strip=True)\n                    case_no     = row_cells[5].get_text(strip=True)\n                    case_desc   = row_cells[6].get_text(strip=True)\n                    revision    = row_cells[7].get_text(strip=True)\n                    status      = row_cells[8].get_text(strip=True)\n                    dt_         = reformat_date(row_cells[9].get_text(strip=True))\n                    doc_url     = urljoin(\"https://rigdoc.nov.com/\", row_cells[1].a.get(\"href\"))\n                    if \"document-void\" in (row_cells[1].a.get(\"class\", [])) or \"*VOID*\" in doc_title.upper():\n                        status = \"(VOID)\"\n                    doc_generated_name = (\n                        f\"{doc_no.upper()}_REV{revision.upper()}--{case_no.upper()}-{doc_title.title()}\"\n                    ).replace(\"  \", \" \")\n                    doc_generated_name_sanitized = sanitize_filename(doc_generated_name)\n                    new_docs.append({\n                        \"item_type\": \"doc\",\n                        \"item_title\": doc_title,\n                        \"item_drawing_type\": sub_type,\n                        \"item_doc_no\": doc_no,\n                        \"item_responsible\": responsible,\n                        \"item_case_no\": case_no,\n                        \"item_case_description\": case_desc,\n                        \"item_revision\": revision,\n                        \"item_status\": status,\n                        \"item_date\": dt_,\n                        \"item_url\": doc_url,\n                        \"item_include\": False,\n                        \"item_generated_name\": doc_generated_name_sanitized\n                    })\n                append_items(new_docs, self.docs_path, field_order=DEFAULT_DOC_FIELD_ORDER)\n        rel_docs_path = os.path.relpath(self.docs_path, os.getcwd())\n        print_ok(f\"Docs => {rel_docs_path}\")\n    def fetch_files(self):\n        if not os.path.exists(self.docs_path):\n            print_err(f\"No docs found: {self.docs_path}\")\n            return\n        with open(self.docs_path,'r',encoding='utf-8') as f:\n            all_docs=json.load(f)\n        docs_for_files=[d for d in all_docs if d.get(\"item_type\")==\"doc\" and d.get(\"item_include\")==True]\n        if not docs_for_files:\n            print_warn(\"No docs with item_include==True => No files to fetch.\")\n            return\n        if not os.path.exists(self.files_path):\n            with open(self.files_path,'w',encoding='utf-8') as f:\n                json.dump([],f)\n        with self.browser_session() as driver:\n            for i,doc in enumerate(docs_for_files,1):\n                if self.show:\n                    print_sub(f\"[FileScrape] doc {i}/{len(docs_for_files)} => {doc.get('item_doc_no')}\")\n                doc_url = doc.get(\"item_url\",\"\")\n                if not doc_url:\n                    continue\n                wp_url = doc_url.replace(\"nov.com/documents/\",\"nov.com/wp/documents/\")\n                if \"nov.com/wp/documents/\" not in wp_url:\n                    continue\n                driver.get(wp_url)\n                WebDriverWait(driver,5).until(SleepCondition(5))\n                soup=BeautifulSoup(driver.page_source,\"html.parser\")\n                frows=soup.select(\"div.revision-panel-body div.file-detail-row\")\n                flinks=[]\n                for row in frows:\n                    lk=row.select_one(\"div.file-detail a\")\n                    if lk and lk.get(\"href\"):\n                        flinks.append(urljoin(\"https://rigdoc.nov.com/\", lk[\"href\"]))\n                doc_generated_name = doc.get(\"item_generated_name\",\"DOC_UNKNOWN\")\n                new_files=[]\n                for idx,flink in enumerate(flinks,1):\n                    fapi=flink.replace(\"/_download\",\"\")\n                    driver.get(fapi)\n                    WebDriverWait(driver,0.5).until(SleepCondition(0.5))\n                    raw=BeautifulSoup(driver.page_source,\"html.parser\").get_text(strip=True)\n                    info=json.loads(raw)\n                    fext=info.get(\"extension\",\"\")\n                    fsz =info.get(\"filesize\",0)\n                    fsmb=f\"{(fsz/(1024*1024)):.2f} MB\"\n                    ftitle=info.get(\"title\",\"\").strip()\n                    file_generated_name = (f\"{doc_generated_name}.id.{idx}-{len(flinks)}.{ftitle.title()}\").replace(\"  \",\" \")\n                    if fext and not file_generated_name.lower().endswith(f\".{fext.lower()}\"):\n                        file_generated_name = f\"{file_generated_name}{fext.lower()}\"\n                    file_generated_name_sanitized = sanitize_filename(file_generated_name)\n                    new_files.append({\n                        \"item_type\": \"file\",\n                        \"item_title\": ftitle,\n                        \"item_file_id\": f\"{len(flinks)}->({idx}/{len(flinks)}) FID:{info.get('fileId','-')}\",\n                        \"item_file_ext\": f\"{fext.lower()}\" if fext else \"\",\n                        \"item_file_size\": fsmb,\n                        \"item_date\": reformat_date(info.get(\"insertedDate\",\"\")),\n                        \"item_revision\": doc.get(\"item_revision\",\"\"),\n                        \"item_responsible\": info.get(\"insertedBy\",\"\"),\n                        \"item_status\": info.get(\"lastChangedDate\") or \"-\",\n                        \"item_case_no\": doc.get(\"item_case_no\",\"\"),\n                        \"item_doc_no\": doc.get(\"item_doc_no\",\"\"),\n                        \"item_drawing_type\": doc.get(\"item_drawing_type\",\"\"),\n                        \"item_case_description\": doc.get(\"item_case_description\",\"\"),\n                        \"item_url\": flink,\n                        \"item_download\": False,\n                        \"item_generated_name\": file_generated_name_sanitized\n                    })\n                append_items(new_files, self.files_path, field_order=DEFAULT_FILE_FIELD_ORDER)\n        rel_files_path = os.path.relpath(self.files_path, os.getcwd())\n        print_ok(f\"Files => {rel_files_path}\")\n    def download_files(self):\n        if not os.path.exists(self.files_path):\n            print_err(f\"No files JSON found at {self.files_path}\")\n            return\n        with open(self.files_path, 'r', encoding='utf-8') as f:\n            data = json.load(f)\n        to_download = [x for x in data if x.get(\"item_type\") == \"file\" and x.get(\"item_download\") == True]\n        if not to_download:\n            print_warn(\"No files with item_download==True.\")\n            return\n        download_dir = os.path.join(DL_DIR, self.rig)\n        print_inf(f\"Will download {len(to_download)} files => {download_dir}\")\n        with self.browser_session(download_mode=True) as driver:\n            for i, fitem in enumerate(to_download, 1):\n                furl = fitem.get(\"item_url\", \"\")\n                if not furl:\n                    continue\n                base_name = fitem.get(\"item_generated_name\", f\"file_{i}\")\n                if not base_name:\n                    base_name = f\"file_{i}\"\n                base_name = sanitize_filename(base_name)\n                parts = base_name.split(\"/\")\n                if parts:\n                    last_part = parts[-1]\n                    ext = fitem.get(\"item_file_ext\", \"\").lower()\n                    if ext and not last_part.lower().endswith(f\"{ext}\"):\n                        ext = f\".{ext}\" if not ext.startswith(\".\") else ext\n                        final_filename = last_part + ext\n                    else:\n                        final_filename = last_part\n                    subfolders = parts[:-1]\n                else:\n                    ext = fitem.get(\"item_file_ext\", \"\")\n                    ext = f\".{ext}\" if ext and not ext.startswith(\".\") else ext\n                    final_filename = f\"file_{i}{ext}\"\n                    subfolders = []\n                final_subdir = os.path.join(download_dir, *subfolders)\n                os.makedirs(final_subdir, exist_ok=True)\n                final_destination = os.path.join(final_subdir, final_filename)\n                if os.path.exists(final_destination):\n                    print_ok(f'Skipped (already exists): \"{final_destination}\"')\n                    continue\n                if self.show:\n                    print_sub(\"-\" * 80)\n                    print_inf(f'[Download {i}/{len(to_download)}] => \"{final_filename}\"')\n                start = timeit.default_timer()\n                existing_files = set(f for f in os.listdir(download_dir) if os.path.isfile(os.path.join(download_dir, f)))\n                driver.get(furl)\n                time.sleep(1)\n                done = False\n                while (timeit.default_timer() - start) < 30:\n                    time.sleep(1)\n                    new_files = set(f for f in os.listdir(download_dir) if os.path.isfile(os.path.join(download_dir, f))) - existing_files\n                    if new_files:\n                        candidate = list(new_files)[0]\n                        ext2 = os.path.splitext(candidate)[1].lower()\n                        if ext2 not in [\".tmp\", \".crdownload\"]:\n                            src = os.path.join(download_dir, candidate)\n                            try:\n                                os.rename(src, final_destination)\n                                done = True\n                                print_ok(f'-> Downloaded \"{final_destination}\"')\n                                break\n                            except Exception as e:\n                                print_warn(f'Failed to rename: {e}')\n                if not done:\n                    print_warn(f'Timed out: \"{final_destination}\"')\n        print_inf(\"All file downloads attempted.\")\ndef run_script(cfg):\n    rig = cfg.get(\"rig_number\", \"R9999\")\n    urls = cfg.get(\"search_urls\", [])\n    sp = cfg.get(\"show_progress\", False)\n    filters = cfg.get(\"filters\", [])\n    scraper = RigDocScraper(rig, show_progress=sp)\n    while True:\n        doc_json = os.path.join(DATA_DIR, f'{rig}-a-docs.json')\n        doc_md = os.path.join(DATA_DIR, f'{rig}-a-docs.md')\n        file_json = os.path.join(DATA_DIR, f'{rig}-b-files.json')\n        file_md = os.path.join(DATA_DIR, f'{rig}-b-files.md')\n        def configure_filters():\n            while True:\n                print_inf(\"\\nCurrent filter chain:\")\n                if not filters:\n                    print_sub(\"  No filters configured\")\n                else:\n                    for i, f in enumerate(filters):\n                        status = \"ENABLED\" if f.get(\"enabled\", False) else \"DISABLED\"\n                        type_str = f.get(\"type\", \"unknown\")\n                        pattern = f.get(\"pattern\", \"\")\n                        field = f.get(\"field\", \"\")\n                        value = f.get(\"value\", True)\n                        comment = f.get(\"comment\", \"\")\n                        if isinstance(pattern, list):\n                            pattern_str = f\"[{', '.join(pattern)}]\"\n                        else:\n                            pattern_str = f\"'{pattern}'\"\n                        match_field = f.get(\"match_field\", \"item_generated_name\")\n                        match_field_str = f\" in {match_field}\" if match_field != \"item_generated_name\" else \"\"\n                        color_fn = print_ok if f.get(\"enabled\", False) else print_sub\n                        color_fn(f\"  [{i+1}] {status} - {type_str}: {pattern_str}{match_field_str} → {field}={value}\")\n                        if comment:\n                            print_sub(f\"      {comment}\")\n                print_inf(\"\\nFilter options:\")\n                print_inf(\"  [a] Add new filter\")\n                print_inf(\"  [e] Edit filter\")\n                print_inf(\"  [d] Delete filter\")\n                print_inf(\"  [t] Toggle filter\")\n                print_inf(\"  [m] Move filter\")\n                print_inf(\"  [s] Save and return\")\n                choice = input(\"\\nEnter option: \").strip().lower()\n                if choice == 'a':\n                    print_inf(\"\\nAdd new filter:\")\n                    filter_type = input(\"  Type (docs/files): \").strip().lower()\n                    if filter_type not in ['docs', 'files']:\n                        print_err(\"  Invalid type. Must be 'docs' or 'files'\")\n                        continue\n                    pattern_input = input(\"  Pattern (e.g., *g0001*, *.pdf) or comma-separated list: \").strip()\n                    if not pattern_input:\n                        print_err(\"  Pattern cannot be empty\")\n                        continue\n                    if \",\" in pattern_input:\n                        pattern = [p.strip() for p in pattern_input.split(\",\") if p.strip()]\n                    else:\n                        pattern = pattern_input\n                    match_field = input(f\"  Field to match against [item_generated_name]: \").strip()\n                    if not match_field:\n                        match_field = 'item_generated_name'\n                    field = input(f\"  Field to set ({'item_include' if filter_type == 'docs' else 'item_download'}): \").strip()\n                    if not field:\n                        field = 'item_include' if filter_type == 'docs' else 'item_download'\n                    value_input = input(f\"  Value to set (true/false) [true]: \").strip().lower()\n                    value = False if value_input in ['false', 'f', 'no', 'n', '0'] else True\n                    comment = input(\"  Comment (optional): \").strip()\n                    new_filter = {\n                        \"type\": filter_type,\n                        \"enabled\": True,\n                        \"pattern\": pattern,\n                        \"match_field\": match_field,\n                        \"field\": field,\n                        \"value\": value,\n                        \"comment\": comment\n                    }\n                    filters.append(new_filter)\n                    print_ok(\"  Filter added\")\n                elif choice == 'e':\n                    if not filters:\n                        print_err(\"  No filters to edit\")\n                        continue\n                    idx_input = input(f\"  Filter number to edit (1-{len(filters)}): \").strip()\n                    try:\n                        idx = int(idx_input) - 1\n                        if idx < 0 or idx >= len(filters):\n                            raise ValueError()\n                    except ValueError:\n                        print_err(\"  Invalid filter number\")\n                        continue\n                    f = filters[idx]\n                    print_inf(f\"\\nEditing filter #{idx+1}:\")\n                    filter_type = input(f\"  Type (docs/files) [{f.get('type', '')}]: \").strip().lower()\n                    if filter_type and filter_type in ['docs', 'files']:\n                        f['type'] = filter_type\n                    current_pattern = f.get('pattern', '')\n                    if isinstance(current_pattern, list):\n                        current_pattern_str = \", \".join(current_pattern)\n                    else:\n                        current_pattern_str = current_pattern\n                    pattern_input = input(f\"  Pattern [{current_pattern_str}]: \").strip()\n                    if pattern_input:\n                        if \",\" in pattern_input:\n                            f['pattern'] = [p.strip() for p in pattern_input.split(\",\") if p.strip()]\n                        else:\n                            f['pattern'] = pattern_input\n                    match_field = input(f\"  Field to match against [{f.get('match_field', 'item_generated_name')}]: \").strip()\n                    if match_field:\n                        f['match_field'] = match_field\n                    elif 'match_field' not in f:\n                        f['match_field'] = 'item_generated_name'\n                    field = input(f\"  Field to set [{f.get('field', '')}]: \").strip()\n                    if field:\n                        f['field'] = field\n                    value_input = input(f\"  Value (true/false) [{f.get('value', True)}]: \").strip().lower()\n                    if value_input:\n                        f['value'] = False if value_input in ['false', 'f', 'no', 'n', '0'] else True\n                    comment = input(f\"  Comment [{f.get('comment', '')}]: \").strip()\n                    if comment or comment == '':\n                        f['comment'] = comment\n                    print_ok(\"  Filter updated\")\n                elif choice == 'd':\n                    if not filters:\n                        print_err(\"  No filters to delete\")\n                        continue\n                    idx_input = input(f\"  Filter number to delete (1-{len(filters)}): \").strip()\n                    try:\n                        idx = int(idx_input) - 1\n                        if idx < 0 or idx >= len(filters):\n                            raise ValueError()\n                    except ValueError:\n                        print_err(\"  Invalid filter number\")\n                        continue\n                    del filters[idx]\n                    print_ok(\"  Filter deleted\")\n                elif choice == 't':\n                    if not filters:\n                        print_err(\"  No filters to toggle\")\n                        continue\n                    idx_input = input(f\"  Filter number to toggle (1-{len(filters)}): \").strip()\n                    try:\n                        idx = int(idx_input) - 1\n                        if idx < 0 or idx >= len(filters):\n                            raise ValueError()\n                    except ValueError:\n                        print_err(\"  Invalid filter number\")\n                        continue\n                    filters[idx]['enabled'] = not filters[idx].get('enabled', False)\n                    status = \"enabled\" if filters[idx]['enabled'] else \"disabled\"\n                    print_ok(f\"  Filter {status}\")\n                elif choice == 'm':\n                    if len(filters) < 2:\n                        print_err(\"  Need at least 2 filters to move\")\n                        continue\n                    from_idx_input = input(f\"  Filter number to move (1-{len(filters)}): \").strip()\n                    try:\n                        from_idx = int(from_idx_input) - 1\n                        if from_idx < 0 or from_idx >= len(filters):\n                            raise ValueError()\n                    except ValueError:\n                        print_err(\"  Invalid filter number\")\n                        continue\n                    to_idx_input = input(f\"  New position (1-{len(filters)}): \").strip()\n                    try:\n                        to_idx = int(to_idx_input) - 1\n                        if to_idx < 0 or to_idx >= len(filters):\n                            raise ValueError()\n                    except ValueError:\n                        print_err(\"  Invalid position\")\n                        continue\n                    if from_idx == to_idx:\n                        print_sub(\"  No change needed\")\n                        continue\n                    filter_to_move = filters.pop(from_idx)\n                    filters.insert(to_idx, filter_to_move)\n                    print_ok(f\"  Filter moved from position {from_idx+1} to {to_idx+1}\")\n                elif choice == 's':\n                    break\n                else:\n                    print_err(\"  Invalid option\")\n            return True\n        steps = [\n            (\"Change search parameters\", None),\n            (\"Configure filter chain\", configure_filters),\n            (\"Fetch docs (scrape initial data)\", lambda: scraper.fetch_docs(urls)),\n            (\"Export docs (to Markdown for editing)\", lambda: json_to_md_table(\n                doc_json, doc_md, field_order=DEFAULT_DOC_FIELD_ORDER, filter_chain=filters\n            )),\n            (\"Import updated doc data\", lambda: md_table_to_json(doc_md, doc_json, field_order=DEFAULT_DOC_FIELD_ORDER)),\n            (\"Fetch files (prepare files for download)\", scraper.fetch_files),\n            (\"Export files (to Markdown for editing)\", lambda: json_to_md_table(\n                file_json, file_md, field_order=DEFAULT_FILE_FIELD_ORDER, filter_chain=filters\n            )),\n            (\"Import updated file data\", lambda: md_table_to_json(file_md, file_json, field_order=DEFAULT_FILE_FIELD_ORDER)),\n            (\"Download files\", scraper.download_files),\n        ]\n        print(\"\\nChoose procedure(s) to run (space/comma-separated numbers, or q to quit):\")\n        for i, (desc, _) in enumerate(steps):\n            print(f\"  [{i}] {desc}\")\n        choice = input(\"Your choice: \").strip()\n        if choice.lower() == 'q':\n            print(\"Exiting.\")\n            break\n        try:\n            indices = [int(x.strip()) for x in choice.replace(',', ' ').split() if x.strip().isdigit()]\n        except (ValueError, IndexError):\n            print(\"Invalid selection. Please try again.\")\n            continue\n        for i in indices:\n            if i == 0:\n                rig_input = input(f\"  rig_number ({rig}): \").strip()\n                if rig_input:\n                    rig = rig_input\n                urls_input = input(f\"  search_urls ({urls}): \").strip()\n                if urls_input:\n                    urls = [u.strip() for u in urls_input.split(',') if u.strip()]\n                scraper = RigDocScraper(rig, show_progress=sp)\n            elif 0 < i < len(steps):\n                desc, action = steps[i]\n                print_inf(f\"\\nRunning: {desc}\")\n                action()\n        print_inf(\"\\nSelected steps completed.\\n\")\nif __name__==\"__main__\":\n    run_script(CONFIG)", "sequence_id": "sequence-0001-a+0001-b", "results": [{"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "# GOAL: CREATE A STRUCTURED README.MD <70 LINES\n\n PROJECT_RIG_ID = \"R5225\"\nPROJECTINFO_GAD = [\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocNumber=*G00*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocNumber=*GAD-00*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DRILL*FLOOR*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DERRICK*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*EQUIPMENT*LAYOUT*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ELEVATION*VIEW*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TOP*DRIVE*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TRAVELLINGBLOCK*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ROUGHNECK*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*HYDRATONG*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MUDBUCKET*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*HYDRARACKER*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*BELLY*BOARD*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*FINGER*BOARD*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RACKING*BOARD*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CATWALK*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MOUSE*HOLE*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*GUIDE*ARM*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*EBT*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ELEVATED*BACKUP*TONG*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CATHEAD*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SPIDER*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ROTARY*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SLIPS*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*AHC*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CMC*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*STANDBUILDING*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TUBULAR*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RTX*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*PIPE*GUIDE*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*LIFTING*CYLINDER*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*STAR*RACKER*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*UTILITY*HANDLING*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RACKING*SYSTEM*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RAISED*SYSTEM*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DFMA*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MPMA*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MANUPILATOR*ARM*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*PIPE*CHUTE*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RISER*CHUTE*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SERVICE*BASKET*\",\n    f\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MAINTENANCE*BASKET*\",\n]\nFILTERS_SIMULATOR_PATTERNS_INCLUDE = [\n    \"*DRILL*FLOOR*\",\n    \"*ELEVATION*VIEW*\",\n    \"*EQUIPMENT*LAYOUT*\",\n    \"*G000*\",\n    \"*GAD-00*\",\n    \"*A000*\",\n    \"*ASM-00*\",\n]\nFILTERS_SIMULATOR_PATTERNS_SKIP = [\n    \"*Agitator*\",\n    \"*BOP *\",\n    \"*Bulk*Systems*\",\n    \"*Cabinet*\",\n    \"*Centrifuge*\",\n    \"*Compressor*\",\n    \"*Crane*\",\n    \"*Cuttings*\",\n    \"*Cyberbase*\",\n    \"*DCDA *\",\n    \"*DCI*\",\n    \"*Deadline*\",\n    \"*Gantry*Crane*\",\n    \"*HPU*\",\n    \"*HVAC*\",\n    \"*Knuckle*Crane*\",\n    \"*LMRP*\",\n    \"*Manrider*\",\n    \"*Mixer*\",\n    \"*Mixing*\",\n    \"*Mud *\",\n    \"*Pumps*\",\n    \"*Sdi*\",\n    \"*Standby*\",\n    \"*Station*\",\n    \"*Tank *\",\n    \"*Valve*\",\n    \"*VOID*\",\n    \"*Winch*\",\n    \"*X-tree*\",\n    \"*Yoke*\",\n]\nCONFIG = {\n    \"rig_number\": f\"{PROJECT_RIG_ID}.020\",\n    \"search_urls\": PROJECTINFO_GAD,\n    \"show_progress\": True,\n    \"filters\": [\n        {\n            \"type\": \"docs\",\n            \"enabled\": True,\n            \"pattern\": FILTERS_SIMULATOR_PATTERNS_INCLUDE,\n            \"match_field\": \"item_generated_name\",\n            \"field\": \"item_include\",\n            \"value\": True,\n            \"comment\": \"\",\n        },\n        {\n            \"type\": \"docs\",\n            \"enabled\": True,\n            \"pattern\": FILTERS_SIMULATOR_PATTERNS_SKIP,\n            \"match_field\": \"item_generated_name\",\n            \"field\": \"item_include\",\n            \"value\": False,\n            \"comment\": \"\",\n        },\n        {\n            \"type\": \"docs\",\n            \"enabled\": True,\n            \"pattern\": FILTERS_SIMULATOR_PATTERNS_SKIP,\n            \"match_field\": \"item_case_description\",\n            \"field\": \"item_include\",\n            \"value\": False,\n            \"comment\": \"\",\n        },\n        {\n            \"type\": \"docs\",\n            \"enabled\": True,\n            \"pattern\": [\"(VOID)\"],\n            \"match_field\": \"item_case_description\",\n            \"field\": \"item_include\",\n            \"value\": False,\n            \"comment\": \"\",\n        },\n        {\n            \"type\": \"files\",\n            \"enabled\": True,\n            \"pattern\": [\"*.pdf\"],\n            \"match_field\": \"item_file_ext\",\n            \"field\": \"item_download\",\n            \"value\": True,\n            \"comment\": \"\",\n        },\n        {\n            \"type\": \"files\",\n            \"enabled\": True,\n            \"pattern\": FILTERS_SIMULATOR_PATTERNS_SKIP,\n            \"match_field\": \"item_case_description\",\n            \"field\": \"item_download\",\n            \"value\": False,\n            \"comment\": \"\",\n        },\n        {\n            \"type\": \"files\",\n            \"enabled\": True,\n            \"pattern\": [\"*Partslist*\"],\n            \"match_field\": \"item_generated_name\",\n            \"field\": \"item_download\",\n            \"value\": False,\n            \"comment\": \"\",\n        },\n    ],\n}\nimport json\nimport os\nimport re\nimport shutil\nimport time\nimport timeit\nfrom contextlib import contextmanager\nfrom urllib.parse import urljoin\nimport colorama\nimport dateutil.parser\nfrom ansimarkup import ansiprint\nfrom bs4 import BeautifulSoup\nfrom selenium import webdriver\nfrom selenium.webdriver.chrome.options import Options\nfrom selenium.webdriver.chrome.service import Service\nfrom selenium.webdriver.support.ui import WebDriverWait\nfrom selenium.webdriver.common.by import By\nfrom selenium.webdriver.support import expected_conditions as EC\nfrom selenium.common.exceptions import TimeoutException\nfrom webdriver_manager.chrome import ChromeDriverManager\ncolorama.init()\nBASE_OUTPUT = os.path.abspath(os.path.join(os.getcwd(), \"outputs\"))\nDATA_DIR    = os.path.join(BASE_OUTPUT, \"data\")\nDL_DIR      = os.path.join(BASE_OUTPUT, \"downloads\")\nfor d in (BASE_OUTPUT, DATA_DIR, DL_DIR):\n    os.makedirs(d, exist_ok=True)\nDEFAULT_DOC_FIELD_ORDER = [\n    \"item_include\",\n    \"item_generated_name\",\n    \"item_type\",\n    \"item_case_no\",\n    \"item_drawing_type\",\n    \"item_doc_no\",\n    \"item_date\",\n    \"item_title\",\n    \"item_case_description\",\n    \"item_status\",\n    \"item_revision\",\n    \"item_responsible\",\n    \"item_url\",\n]\nDEFAULT_FILE_FIELD_ORDER = [\n    \"item_type\",\n    \"item_generated_name\",\n    \"item_case_no\",\n    \"item_drawing_type\",\n    \"item_title\",\n    \"item_doc_no\",\n    \"item_file_ext\",\n    \"item_download\",\n    \"item_file_size\",\n    \"item_file_id\",\n    \"item_date\",\n    \"item_revision\",\n    \"item_case_description\",\n    \"item_status\",\n    \"item_responsible\",\n    \"item_url\",\n]\ndef cprint(x, color='#FFFFFF'):\n    ansiprint(f'<fg {color}>{x}')\nprint_err  = lambda x: cprint(x, '#C92B65')\nprint_warn = lambda x: cprint(x, '#E6992B')\nprint_ok   = lambda x: cprint(x, '#1FCE46')\nprint_inf  = lambda x: cprint(x, '#FFFFFF')\nprint_sub  = lambda x: cprint(x, '#74705D')\nclass SleepCondition:\n    def __init__(self, s):\n        self.s = s\n    def __call__(self, _):\n        time.sleep(self.s)\n        return True\ndef reformat_date(s):\n    try:\n        dt = dateutil.parser.parse(s)\n        return dt.strftime('%Y.%m.%d')\n    except:\n        return s\ndef match_pattern(text, pattern):\n    if isinstance(pattern, list):\n        return any(match_pattern(text, p) for p in pattern)\n    regex_pattern = pattern.replace(\".\", \"\\\\.\").replace(\"*\", \".*\").replace(\"?\", \".\")\n    return bool(re.search(f\"^{regex_pattern}$\", text, re.IGNORECASE))\ndef sanitize_filename(s):\n    replacements = {\n        \"'\": \"\", '\"': \"\", '`': \"\",\n        '\\\\': \"_\", '|': \"_\", '?': \"_\", '*': \"_\", '<': \"_\", '>': \"_\", ':': \"_\"\n    }\n    result = s\n    for char, replacement in replacements.items():\n        result = result.replace(char, replacement)\n    result = ''.join(c for c in result if c.isprintable() or c == '/')\n    result = re.sub(r'\\s+', ' ', result)\n    return result.strip()\ndef reorder_dict_keys(data_dict, field_order):\n    reordered = {}\n    for key in field_order:\n        if key in data_dict:\n            reordered[key] = data_dict[key]\n    return reordered\ndef remove_duplicates(data, exclude_keys=None, priority_key='item_download', priority_vals=None):\n    if not exclude_keys:\n        exclude_keys=[]\n    if not priority_vals:\n        priority_vals=[]\n    exset = set(exclude_keys)\n    pmap  = {v:i for i,v in enumerate(priority_vals)}\n    out   = {}\n    for d in data:\n        filt = {k:v for k,v in d.items() if k not in exset}\n        hkey = frozenset(filt.items())\n        if hkey not in out:\n            out[hkey] = d\n        else:\n            if priority_key and priority_vals:\n                newv = d.get(priority_key)\n                oldv = out[hkey].get(priority_key)\n                if (newv in pmap) and (oldv in pmap) and pmap[newv] < pmap[oldv]:\n                    out[hkey] = d\n                elif newv in pmap and oldv not in pmap:\n                    out[hkey] = d\n    return list(out.values())\ndef dedupe_and_save(new_items, path, field_order=None):\n    if os.path.exists(path):\n        try:\n            with open(path,'r',encoding='utf-8') as f:\n                old = json.load(f)\n        except:\n            old = []\n    else:\n        old = []\n    merged = old + new_items\n    merged = remove_duplicates(\n        merged,\n        exclude_keys=['item_download','item_generated_path'],\n        priority_key='item_download',\n        priority_vals=[False,True]\n    )\n    if field_order:\n        merged = [reorder_dict_keys(d, field_order) for d in merged]\n    tmp = path + '.temp'\n    with open(tmp,'w',encoding='utf-8') as f:\n        json.dump(merged,f,indent=2)\n    os.replace(tmp,path)\n    rel_path = os.path.relpath(path, os.getcwd())\n    print_inf(f\"Saved {len(merged)} items => {rel_path}\")\ndef append_items(new_items, path, field_order=None):\n    if new_items:\n        dedupe_and_save(new_items, path, field_order=field_order)\ndef json_to_md_table(json_path, md_path, field_order=None, filter_chain=None):\n    if not os.path.exists(json_path):\n        print_err(f\"No JSON found: {json_path}\")\n        return\n    with open(json_path,'r',encoding='utf-8') as f:\n        data = json.load(f)\n    if not data:\n        print_warn(f\"No data in {json_path}\")\n        return\n    item_type = \"docs\" if \"-a-docs.\" in json_path else \"files\"\n    if filter_chain and isinstance(filter_chain, list):\n        for filter_idx, filter_config in enumerate(filter_chain):\n            if not filter_config.get(\"enabled\", False) or filter_config.get(\"type\") != item_type:\n                continue\n            pattern = filter_config.get(\"pattern\", \"\")\n            field = filter_config.get(\"field\", \"\")\n            value = filter_config.get(\"value\", True)\n            comment = filter_config.get(\"comment\", \"\")\n            match_field = filter_config.get(\"match_field\", \"item_generated_name\")\n            if pattern and field:\n                filtered_count = 0\n                for d in data:\n                    field_value = d.get(match_field, \"\")\n                    if field_value and match_pattern(field_value, pattern):\n                        if d.get(field) != value:\n                            d[field] = value\n                            filtered_count += 1\n                if filtered_count > 0:\n                    action = \"Set\" if value else \"Cleared\"\n                    if isinstance(pattern, list):\n                        pattern_str = f\"[{', '.join(pattern)}]\"\n                    else:\n                        pattern_str = f\"'{pattern}'\"\n                    match_field_str = f\" in {match_field}\" if match_field != \"item_generated_name\" else \"\"\n                    print_ok(f\"Filter #{filter_idx+1}: {action} {field}={value} for {filtered_count} items matching {pattern_str}{match_field_str}\")\n                    if comment:\n                        print_sub(f\"  → {comment}\")\n    if not field_order:\n        colset = set()\n        for d in data:\n            colset.update(d.keys())\n        field_order = sorted(colset)\n    lines = []\n    lines.append(\"| \" + \" | \".join(field_order) + \" |\")\n    lines.append(\"|\" + \"|\".join([\"---\"]*len(field_order)) + \"|\")\n    for d in data:\n        row = []\n        for c in field_order:\n            val = d.get(c,\"\")\n            if isinstance(val,bool):\n                val = str(val).lower()\n            val = str(val).replace(\"|\",\"\\\\|\")\n            row.append(val)\n        lines.append(\"| \" + \" | \".join(row) + \" |\")\n    with open(md_path,'w',encoding='utf-8') as f:\n        f.write(\"\\n\".join(lines))\n    rel_md_path = os.path.relpath(md_path, os.getcwd())\n    print_ok(f\"Exported {len(data)} items => {rel_md_path}\")\ndef md_table_to_json(md_path, json_path, field_order=None):\n    if not os.path.exists(md_path):\n        print_err(f\"No Markdown file found: {md_path}\")\n        return\n    with open(md_path,'r',encoding='utf-8') as f:\n        lines=f.read().splitlines()\n    table_lines=[ln.strip() for ln in lines if ln.strip().startswith(\"|\")]\n    if len(table_lines)<2:\n        print_err(\"No valid table rows found in the Markdown.\")\n        return\n    header=table_lines[0]\n    data_lines=table_lines[2:]\n    cols=[x.strip() for x in header.strip('|').split('|')]\n    new_data=[]\n    for ln in data_lines:\n        rowcols=[x.strip() for x in ln.strip('|').split('|')]\n        if len(rowcols)!=len(cols):\n            print_warn(f\"Skipping mismatch line:\\n{ln}\")\n            continue\n        d={}\n        for i,c in enumerate(cols):\n            val=rowcols[i]\n            if val.lower()==\"true\":\n                val=True\n            elif val.lower()==\"false\":\n                val=False\n            d[c]=val\n        new_data.append(d)\n    if field_order:\n        new_data = [reorder_dict_keys(item, field_order) for item in new_data]\n    with open(json_path,'w',encoding='utf-8') as f:\n        json.dump(new_data,f,indent=2)\n    rel_json_path = os.path.relpath(json_path, os.getcwd())\n    print_ok(f\"Imported {len(new_data)} items => {rel_json_path}\")\nclass RigDocScraper:\n    def __init__(self, rig, show_progress=False):\n        self.rig=rig\n        self.show=show_progress\n        self.driver=None\n        self.docs_path = os.path.join(DATA_DIR, f'{rig}-a-docs.json')\n        self.files_path= os.path.join(DATA_DIR, f'{rig}-b-files.json')\n    @contextmanager\n    def browser_session(self, download_mode=False):\n        profdir = None\n        try:\n            opts = Options()\n            opts.add_argument(\"--log-level=3\")\n            if download_mode:\n                appdata = os.environ.get(\"APPDATA\", os.getcwd())\n                profdir = os.path.join(appdata, \"chromedriver_profile\")\n                os.makedirs(profdir, exist_ok=True)\n                opts.add_argument(f\"--user-data-dir={profdir}\")\n                dl_dir = os.path.join(DL_DIR, self.rig)\n                os.makedirs(dl_dir, exist_ok=True)\n                prefs = {\n                    \"download.default_directory\": dl_dir,\n                    \"download.prompt_for_download\": False,\n                    \"download.directory_upgrade\": True,\n                    \"safebrowsing.enabled\": True\n                }\n                opts.add_experimental_option(\"prefs\", prefs)\n            svc = Service(ChromeDriverManager().install())\n            self.driver = webdriver.Chrome(service=svc, options=opts)\n            wait_time = 2 if download_mode else 5\n            WebDriverWait(self.driver, wait_time).until(SleepCondition(wait_time))\n            self.driver.implicitly_wait(5 if download_mode else 10)\n            if self.show:\n                print_sub(f\"[Browser] Started {'download-enabled ' if download_mode else ''}session\")\n            yield self.driver\n        finally:\n            if self.driver:\n                if self.show:\n                    print_sub(\"[Browser] Closing session\")\n                self.driver.quit()\n                self.driver = None\n                if download_mode and profdir:\n                    try:\n                        shutil.rmtree(profdir)\n                    except Exception as e:\n                        if self.show:\n                            print_warn(f\"[Browser] Failed to clean up profile: {e}\")\n    def fetch_docs(self, urls):\n        if not os.path.exists(self.docs_path):\n            with open(self.docs_path,'w',encoding='utf-8') as f:\n                json.dump([],f)\n        with self.browser_session() as driver:\n            for url in urls:\n                if self.show:\n                    print_sub(f\"[DocScrape] => {url}\")\n                driver.get(url)\n                is_rig = (\"rigsearch\" in url.lower()) or (\"advancedsearch\" in url.lower())\n                max_wait = 10 if is_rig else 5\n                wait_scroll = 5 if is_rig else 2\n                try:\n                    WebDriverWait(driver, max_wait).until(\n                        EC.presence_of_element_located((By.CSS_SELECTOR, \"a.search-result-link\"))\n                    )\n                except TimeoutException:\n                    print_warn(f\"Waited up to {max_wait}s, but no 'search-result-link' found. Proceeding anyway...\")\n                inc = 0\n                prev = driver.execute_script(\"return document.body.scrollHeight\")\n                while inc < 150:\n                    inc += 1\n                    driver.execute_script(\"window.scrollTo(0, document.body.scrollHeight);\")\n                    time.sleep(wait_scroll)\n                    newh = driver.execute_script(\"return document.body.scrollHeight\")\n                    if self.show:\n                        print_sub(f\" -> Scroll {inc}, h={newh}\")\n                    if newh == prev:\n                        break\n                    prev = newh\n                soup = BeautifulSoup(driver.page_source, \"html.parser\")\n                row_elems = soup.find_all(\"a\", class_=\"search-result-link\")\n                rows = [(td.find_parent(\"tr\")).find_all(\"td\") for td in row_elems]\n                if self.show:\n                    print_inf(f\"Found {len(rows)} doc rows.\")\n                new_docs = []\n                for row_cells in rows:\n                    doc_title   = re.sub(r'[\\n\\r]+',' ', row_cells[1].a.text.strip())\n                    sub_type    = row_cells[2].get_text(strip=True)\n                    doc_no      = row_cells[3].get_text(strip=True)\n                    responsible = row_cells[4].get_text(strip=True)\n                    case_no     = row_cells[5].get_text(strip=True)\n                    case_desc   = row_cells[6].get_text(strip=True)\n                    revision    = row_cells[7].get_text(strip=True)\n                    status      = row_cells[8].get_text(strip=True)\n                    dt_         = reformat_date(row_cells[9].get_text(strip=True))\n                    doc_url     = urljoin(\"https://rigdoc.nov.com/\", row_cells[1].a.get(\"href\"))\n                    if \"document-void\" in (row_cells[1].a.get(\"class\", [])) or \"*VOID*\" in doc_title.upper():\n                        status = \"(VOID)\"\n                    doc_generated_name = (\n                        f\"{doc_no.upper()}_REV{revision.upper()}--{case_no.upper()}-{doc_title.title()}\"\n                    ).replace(\"  \", \" \")\n                    doc_generated_name_sanitized = sanitize_filename(doc_generated_name)\n                    new_docs.append({\n                        \"item_type\": \"doc\",\n                        \"item_title\": doc_title,\n                        \"item_drawing_type\": sub_type,\n                        \"item_doc_no\": doc_no,\n                        \"item_responsible\": responsible,\n                        \"item_case_no\": case_no,\n                        \"item_case_description\": case_desc,\n                        \"item_revision\": revision,\n                        \"item_status\": status,\n                        \"item_date\": dt_,\n                        \"item_url\": doc_url,\n                        \"item_include\": False,\n                        \"item_generated_name\": doc_generated_name_sanitized\n                    })\n                append_items(new_docs, self.docs_path, field_order=DEFAULT_DOC_FIELD_ORDER)\n        rel_docs_path = os.path.relpath(self.docs_path, os.getcwd())\n        print_ok(f\"Docs => {rel_docs_path}\")\n    def fetch_files(self):\n        if not os.path.exists(self.docs_path):\n            print_err(f\"No docs found: {self.docs_path}\")\n            return\n        with open(self.docs_path,'r',encoding='utf-8') as f:\n            all_docs=json.load(f)\n        docs_for_files=[d for d in all_docs if d.get(\"item_type\")==\"doc\" and d.get(\"item_include\")==True]\n        if not docs_for_files:\n            print_warn(\"No docs with item_include==True => No files to fetch.\")\n            return\n        if not os.path.exists(self.files_path):\n            with open(self.files_path,'w',encoding='utf-8') as f:\n                json.dump([],f)\n        with self.browser_session() as driver:\n            for i,doc in enumerate(docs_for_files,1):\n                if self.show:\n                    print_sub(f\"[FileScrape] doc {i}/{len(docs_for_files)} => {doc.get('item_doc_no')}\")\n                doc_url = doc.get(\"item_url\",\"\")\n                if not doc_url:\n                    continue\n                wp_url = doc_url.replace(\"nov.com/documents/\",\"nov.com/wp/documents/\")\n                if \"nov.com/wp/documents/\" not in wp_url:\n                    continue\n                driver.get(wp_url)\n                WebDriverWait(driver,5).until(SleepCondition(5))\n                soup=BeautifulSoup(driver.page_source,\"html.parser\")\n                frows=soup.select(\"div.revision-panel-body div.file-detail-row\")\n                flinks=[]\n                for row in frows:\n                    lk=row.select_one(\"div.file-detail a\")\n                    if lk and lk.get(\"href\"):\n                        flinks.append(urljoin(\"https://rigdoc.nov.com/\", lk[\"href\"]))\n                doc_generated_name = doc.get(\"item_generated_name\",\"DOC_UNKNOWN\")\n                new_files=[]\n                for idx,flink in enumerate(flinks,1):\n                    fapi=flink.replace(\"/_download\",\"\")\n                    driver.get(fapi)\n                    WebDriverWait(driver,0.5).until(SleepCondition(0.5))\n                    raw=BeautifulSoup(driver.page_source,\"html.parser\").get_text(strip=True)\n                    info=json.loads(raw)\n                    fext=info.get(\"extension\",\"\")\n                    fsz =info.get(\"filesize\",0)\n                    fsmb=f\"{(fsz/(1024*1024)):.2f} MB\"\n                    ftitle=info.get(\"title\",\"\").strip()\n                    file_generated_name = (f\"{doc_generated_name}.id.{idx}-{len(flinks)}.{ftitle.title()}\").replace(\"  \",\" \")\n                    if fext and not file_generated_name.lower().endswith(f\".{fext.lower()}\"):\n                        file_generated_name = f\"{file_generated_name}{fext.lower()}\"\n                    file_generated_name_sanitized = sanitize_filename(file_generated_name)\n                    new_files.append({\n                        \"item_type\": \"file\",\n                        \"item_title\": ftitle,\n                        \"item_file_id\": f\"{len(flinks)}->({idx}/{len(flinks)}) FID:{info.get('fileId','-')}\",\n                        \"item_file_ext\": f\"{fext.lower()}\" if fext else \"\",\n                        \"item_file_size\": fsmb,\n                        \"item_date\": reformat_date(info.get(\"insertedDate\",\"\")),\n                        \"item_revision\": doc.get(\"item_revision\",\"\"),\n                        \"item_responsible\": info.get(\"insertedBy\",\"\"),\n                        \"item_status\": info.get(\"lastChangedDate\") or \"-\",\n                        \"item_case_no\": doc.get(\"item_case_no\",\"\"),\n                        \"item_doc_no\": doc.get(\"item_doc_no\",\"\"),\n                        \"item_drawing_type\": doc.get(\"item_drawing_type\",\"\"),\n                        \"item_case_description\": doc.get(\"item_case_description\",\"\"),\n                        \"item_url\": flink,\n                        \"item_download\": False,\n                        \"item_generated_name\": file_generated_name_sanitized\n                    })\n                append_items(new_files, self.files_path, field_order=DEFAULT_FILE_FIELD_ORDER)\n        rel_files_path = os.path.relpath(self.files_path, os.getcwd())\n        print_ok(f\"Files => {rel_files_path}\")\n    def download_files(self):\n        if not os.path.exists(self.files_path):\n            print_err(f\"No files JSON found at {self.files_path}\")\n            return\n        with open(self.files_path, 'r', encoding='utf-8') as f:\n            data = json.load(f)\n        to_download = [x for x in data if x.get(\"item_type\") == \"file\" and x.get(\"item_download\") == True]\n        if not to_download:\n            print_warn(\"No files with item_download==True.\")\n            return\n        download_dir = os.path.join(DL_DIR, self.rig)\n        print_inf(f\"Will download {len(to_download)} files => {download_dir}\")\n        with self.browser_session(download_mode=True) as driver:\n            for i, fitem in enumerate(to_download, 1):\n                furl = fitem.get(\"item_url\", \"\")\n                if not furl:\n                    continue\n                base_name = fitem.get(\"item_generated_name\", f\"file_{i}\")\n                if not base_name:\n                    base_name = f\"file_{i}\"\n                base_name = sanitize_filename(base_name)\n                parts = base_name.split(\"/\")\n                if parts:\n                    last_part = parts[-1]\n                    ext = fitem.get(\"item_file_ext\", \"\").lower()\n                    if ext and not last_part.lower().endswith(f\"{ext}\"):\n                        ext = f\".{ext}\" if not ext.startswith(\".\") else ext\n                        final_filename = last_part + ext\n                    else:\n                        final_filename = last_part\n                    subfolders = parts[:-1]\n                else:\n                    ext = fitem.get(\"item_file_ext\", \"\")\n                    ext = f\".{ext}\" if ext and not ext.startswith(\".\") else ext\n                    final_filename = f\"file_{i}{ext}\"\n                    subfolders = []\n                final_subdir = os.path.join(download_dir, *subfolders)\n                os.makedirs(final_subdir, exist_ok=True)\n                final_destination = os.path.join(final_subdir, final_filename)\n                if os.path.exists(final_destination):\n                    print_ok(f'Skipped (already exists): \"{final_destination}\"')\n                    continue\n                if self.show:\n                    print_sub(\"-\" * 80)\n                    print_inf(f'[Download {i}/{len(to_download)}] => \"{final_filename}\"')\n                start = timeit.default_timer()\n                existing_files = set(f for f in os.listdir(download_dir) if os.path.isfile(os.path.join(download_dir, f)))\n                driver.get(furl)\n                time.sleep(1)\n                done = False\n                while (timeit.default_timer() - start) < 30:\n                    time.sleep(1)\n                    new_files = set(f for f in os.listdir(download_dir) if os.path.isfile(os.path.join(download_dir, f))) - existing_files\n                    if new_files:\n                        candidate = list(new_files)[0]\n                        ext2 = os.path.splitext(candidate)[1].lower()\n                        if ext2 not in [\".tmp\", \".crdownload\"]:\n                            src = os.path.join(download_dir, candidate)\n                            try:\n                                os.rename(src, final_destination)\n                                done = True\n                                print_ok(f'-> Downloaded \"{final_destination}\"')\n                                break\n                            except Exception as e:\n                                print_warn(f'Failed to rename: {e}')\n                if not done:\n                    print_warn(f'Timed out: \"{final_destination}\"')\n        print_inf(\"All file downloads attempted.\")\ndef run_script(cfg):\n    rig = cfg.get(\"rig_number\", \"R9999\")\n    urls = cfg.get(\"search_urls\", [])\n    sp = cfg.get(\"show_progress\", False)\n    filters = cfg.get(\"filters\", [])\n    scraper = RigDocScraper(rig, show_progress=sp)\n    while True:\n        doc_json = os.path.join(DATA_DIR, f'{rig}-a-docs.json')\n        doc_md = os.path.join(DATA_DIR, f'{rig}-a-docs.md')\n        file_json = os.path.join(DATA_DIR, f'{rig}-b-files.json')\n        file_md = os.path.join(DATA_DIR, f'{rig}-b-files.md')\n        def configure_filters():\n            while True:\n                print_inf(\"\\nCurrent filter chain:\")\n                if not filters:\n                    print_sub(\"  No filters configured\")\n                else:\n                    for i, f in enumerate(filters):\n                        status = \"ENABLED\" if f.get(\"enabled\", False) else \"DISABLED\"\n                        type_str = f.get(\"type\", \"unknown\")\n                        pattern = f.get(\"pattern\", \"\")\n                        field = f.get(\"field\", \"\")\n                        value = f.get(\"value\", True)\n                        comment = f.get(\"comment\", \"\")\n                        if isinstance(pattern, list):\n                            pattern_str = f\"[{', '.join(pattern)}]\"\n                        else:\n                            pattern_str = f\"'{pattern}'\"\n                        match_field = f.get(\"match_field\", \"item_generated_name\")\n                        match_field_str = f\" in {match_field}\" if match_field != \"item_generated_name\" else \"\"\n                        color_fn = print_ok if f.get(\"enabled\", False) else print_sub\n                        color_fn(f\"  [{i+1}] {status} - {type_str}: {pattern_str}{match_field_str} → {field}={value}\")\n                        if comment:\n                            print_sub(f\"      {comment}\")\n                print_inf(\"\\nFilter options:\")\n                print_inf(\"  [a] Add new filter\")\n                print_inf(\"  [e] Edit filter\")\n                print_inf(\"  [d] Delete filter\")\n                print_inf(\"  [t] Toggle filter\")\n                print_inf(\"  [m] Move filter\")\n                print_inf(\"  [s] Save and return\")\n                choice = input(\"\\nEnter option: \").strip().lower()\n                if choice == 'a':\n                    print_inf(\"\\nAdd new filter:\")\n                    filter_type = input(\"  Type (docs/files): \").strip().lower()\n                    if filter_type not in ['docs', 'files']:\n                        print_err(\"  Invalid type. Must be 'docs' or 'files'\")\n                        continue\n                    pattern_input = input(\"  Pattern (e.g., *g0001*, *.pdf) or comma-separated list: \").strip()\n                    if not pattern_input:\n                        print_err(\"  Pattern cannot be empty\")\n                        continue\n                    if \",\" in pattern_input:\n                        pattern = [p.strip() for p in pattern_input.split(\",\") if p.strip()]\n                    else:\n                        pattern = pattern_input\n                    match_field = input(f\"  Field to match against [item_generated_name]: \").strip()\n                    if not match_field:\n                        match_field = 'item_generated_name'\n                    field = input(f\"  Field to set ({'item_include' if filter_type == 'docs' else 'item_download'}): \").strip()\n                    if not field:\n                        field = 'item_include' if filter_type == 'docs' else 'item_download'\n                    value_input = input(f\"  Value to set (true/false) [true]: \").strip().lower()\n                    value = False if value_input in ['false', 'f', 'no', 'n', '0'] else True\n                    comment = input(\"  Comment (optional): \").strip()\n                    new_filter = {\n                        \"type\": filter_type,\n                        \"enabled\": True,\n                        \"pattern\": pattern,\n                        \"match_field\": match_field,\n                        \"field\": field,\n                        \"value\": value,\n                        \"comment\": comment\n                    }\n                    filters.append(new_filter)\n                    print_ok(\"  Filter added\")\n                elif choice == 'e':\n                    if not filters:\n                        print_err(\"  No filters to edit\")\n                        continue\n                    idx_input = input(f\"  Filter number to edit (1-{len(filters)}): \").strip()\n                    try:\n                        idx = int(idx_input) - 1\n                        if idx < 0 or idx >= len(filters):\n                            raise ValueError()\n                    except ValueError:\n                        print_err(\"  Invalid filter number\")\n                        continue\n                    f = filters[idx]\n                    print_inf(f\"\\nEditing filter #{idx+1}:\")\n                    filter_type = input(f\"  Type (docs/files) [{f.get('type', '')}]: \").strip().lower()\n                    if filter_type and filter_type in ['docs', 'files']:\n                        f['type'] = filter_type\n                    current_pattern = f.get('pattern', '')\n                    if isinstance(current_pattern, list):\n                        current_pattern_str = \", \".join(current_pattern)\n                    else:\n                        current_pattern_str = current_pattern\n                    pattern_input = input(f\"  Pattern [{current_pattern_str}]: \").strip()\n                    if pattern_input:\n                        if \",\" in pattern_input:\n                            f['pattern'] = [p.strip() for p in pattern_input.split(\",\") if p.strip()]\n                        else:\n                            f['pattern'] = pattern_input\n                    match_field = input(f\"  Field to match against [{f.get('match_field', 'item_generated_name')}]: \").strip()\n                    if match_field:\n                        f['match_field'] = match_field\n                    elif 'match_field' not in f:\n                        f['match_field'] = 'item_generated_name'\n                    field = input(f\"  Field to set [{f.get('field', '')}]: \").strip()\n                    if field:\n                        f['field'] = field\n                    value_input = input(f\"  Value (true/false) [{f.get('value', True)}]: \").strip().lower()\n                    if value_input:\n                        f['value'] = False if value_input in ['false', 'f', 'no', 'n', '0'] else True\n                    comment = input(f\"  Comment [{f.get('comment', '')}]: \").strip()\n                    if comment or comment == '':\n                        f['comment'] = comment\n                    print_ok(\"  Filter updated\")\n                elif choice == 'd':\n                    if not filters:\n                        print_err(\"  No filters to delete\")\n                        continue\n                    idx_input = input(f\"  Filter number to delete (1-{len(filters)}): \").strip()\n                    try:\n                        idx = int(idx_input) - 1\n                        if idx < 0 or idx >= len(filters):\n                            raise ValueError()\n                    except ValueError:\n                        print_err(\"  Invalid filter number\")\n                        continue\n                    del filters[idx]\n                    print_ok(\"  Filter deleted\")\n                elif choice == 't':\n                    if not filters:\n                        print_err(\"  No filters to toggle\")\n                        continue\n                    idx_input = input(f\"  Filter number to toggle (1-{len(filters)}): \").strip()\n                    try:\n                        idx = int(idx_input) - 1\n                        if idx < 0 or idx >= len(filters):\n                            raise ValueError()\n                    except ValueError:\n                        print_err(\"  Invalid filter number\")\n                        continue\n                    filters[idx]['enabled'] = not filters[idx].get('enabled', False)\n                    status = \"enabled\" if filters[idx]['enabled'] else \"disabled\"\n                    print_ok(f\"  Filter {status}\")\n                elif choice == 'm':\n                    if len(filters) < 2:\n                        print_err(\"  Need at least 2 filters to move\")\n                        continue\n                    from_idx_input = input(f\"  Filter number to move (1-{len(filters)}): \").strip()\n                    try:\n                        from_idx = int(from_idx_input) - 1\n                        if from_idx < 0 or from_idx >= len(filters):\n                            raise ValueError()\n                    except ValueError:\n                        print_err(\"  Invalid filter number\")\n                        continue\n                    to_idx_input = input(f\"  New position (1-{len(filters)}): \").strip()\n                    try:\n                        to_idx = int(to_idx_input) - 1\n                        if to_idx < 0 or to_idx >= len(filters):\n                            raise ValueError()\n                    except ValueError:\n                        print_err(\"  Invalid position\")\n                        continue\n                    if from_idx == to_idx:\n                        print_sub(\"  No change needed\")\n                        continue\n                    filter_to_move = filters.pop(from_idx)\n                    filters.insert(to_idx, filter_to_move)\n                    print_ok(f\"  Filter moved from position {from_idx+1} to {to_idx+1}\")\n                elif choice == 's':\n                    break\n                else:\n                    print_err(\"  Invalid option\")\n            return True\n        steps = [\n            (\"Change search parameters\", None),\n            (\"Configure filter chain\", configure_filters),\n            (\"Fetch docs (scrape initial data)\", lambda: scraper.fetch_docs(urls)),\n            (\"Export docs (to Markdown for editing)\", lambda: json_to_md_table(\n                doc_json, doc_md, field_order=DEFAULT_DOC_FIELD_ORDER, filter_chain=filters\n            )),\n            (\"Import updated doc data\", lambda: md_table_to_json(doc_md, doc_json, field_order=DEFAULT_DOC_FIELD_ORDER)),\n            (\"Fetch files (prepare files for download)\", scraper.fetch_files),\n            (\"Export files (to Markdown for editing)\", lambda: json_to_md_table(\n                file_json, file_md, field_order=DEFAULT_FILE_FIELD_ORDER, filter_chain=filters\n            )),\n            (\"Import updated file data\", lambda: md_table_to_json(file_md, file_json, field_order=DEFAULT_FILE_FIELD_ORDER)),\n            (\"Download files\", scraper.download_files),\n        ]\n        print(\"\\nChoose procedure(s) to run (space/comma-separated numbers, or q to quit):\")\n        for i, (desc, _) in enumerate(steps):\n            print(f\"  [{i}] {desc}\")\n        choice = input(\"Your choice: \").strip()\n        if choice.lower() == 'q':\n            print(\"Exiting.\")\n            break\n        try:\n            indices = [int(x.strip()) for x in choice.replace(',', ' ').split() if x.strip().isdigit()]\n        except (ValueError, IndexError):\n            print(\"Invalid selection. Please try again.\")\n            continue\n        for i in indices:\n            if i == 0:\n                rig_input = input(f\"  rig_number ({rig}): \").strip()\n                if rig_input:\n                    rig = rig_input\n                urls_input = input(f\"  search_urls ({urls}): \").strip()\n                if urls_input:\n                    urls = [u.strip() for u in urls_input.split(',') if u.strip()]\n                scraper = RigDocScraper(rig, show_progress=sp)\n            elif 0 < i < len(steps):\n                desc, action = steps[i]\n                print_inf(f\"\\nRunning: {desc}\")\n                action()\n        print_inf(\"\\nSelected steps completed.\\n\")\nif __name__==\"__main__\":\n    run_script(CONFIG)", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Create a structured README.md file (less than 70 lines) documenting the RigDocScraper project. Include the following sections in order:\\n\\n1. Project Title and Rig Number (use PROJECT_RIG_ID=\\\"R5225\\\").\\n2. Brief Project Description and Main Use Case.\\n3. Prerequisites: List all required Python packages and setup steps.\\n4. Directory Structure: Outline BASE_OUTPUT, DATA_DIR, DL_DIR, and where outputs are stored.\\n5. Configuration: Describe how to set or change PROJECTINFO_GAD, FILTERS_SIMULATOR_PATTERNS_INCLUDE, FILTERS_SIMULATOR_PATTERNS_SKIP, and CONFIG parameters.\\n6. Pipeline Overview: Stepwise summary of the process:\\n    - 1. Adjust search parameters (rig number, URLs).\\n    - 2. Configure filter chain (with editing options).\\n    - 3. Fetch docs (web scrape document data).\\n    - 4. Export docs to Markdown for review/editing.\\n    - 5. Import docs from Markdown to JSON.\\n    - 6. Fetch candidate files linked to included docs.\\n    - 7. Export file list to Markdown for review/editing.\\n    - 8. Import updated file list from Markdown.\\n    - 9. Download marked files (PDFs only).\\n7. Filter System: Explain how inclusion/exclusion logic and pattern matching works for both docs and files; reference example patterns.\\n8. Manual Editing: Specify that Markdown tables in outputs/data can be directly edited to select docs/files for download.\\n9. Running the Tool: List the command to execute the script, how to select pipeline steps, and notes about interactive prompts.\\n10. Troubleshooting: Add notes on browser requirements, typical errors, and where to look in the output.\\n11. References: Link to relevant documentation or `rigdoc.nov.com` as appropriate.\\n\\nEnsure usage of command/instructional voice, include code block examples for configuration and usage commands, and maintain concise, technical phrasing throughout. Avoid first-person language. Preserve project-specific terminology, filter logic, and maintain the order of steps as in the input.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"# GOAL: CREATE A STRUCTURED README.MD <70 LINES\\n\\n PROJECT_RIG_ID = \\\"R5225\\\"\\nPROJECTINFO_GAD = [\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocNumber=*G00*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocNumber=*GAD-00*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DRILL*FLOOR*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DERRICK*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*EQUIPMENT*LAYOUT*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ELEVATION*VIEW*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TOP*DRIVE*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TRAVELLINGBLOCK*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ROUGHNECK*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*HYDRATONG*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MUDBUCKET*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*HYDRARACKER*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*BELLY*BOARD*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*FINGER*BOARD*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RACKING*BOARD*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CATWALK*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MOUSE*HOLE*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*GUIDE*ARM*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*EBT*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ELEVATED*BACKUP*TONG*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CATHEAD*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SPIDER*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ROTARY*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SLIPS*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*AHC*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CMC*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*STANDBUILDING*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TUBULAR*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RTX*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*PIPE*GUIDE*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*LIFTING*CYLINDER*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*STAR*RACKER*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*UTILITY*HANDLING*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RACKING*SYSTEM*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RAISED*SYSTEM*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DFMA*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MPMA*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MANUPILATOR*ARM*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*PIPE*CHUTE*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RISER*CHUTE*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SERVICE*BASKET*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MAINTENANCE*BASKET*\\\",\\n]\\nFILTERS_SIMULATOR_PATTERNS_INCLUDE = [\\n    \\\"*DRILL*FLOOR*\\\",\\n    \\\"*ELEVATION*VIEW*\\\",\\n    \\\"*EQUIPMENT*LAYOUT*\\\",\\n    \\\"*G000*\\\",\\n    \\\"*GAD-00*\\\",\\n    \\\"*A000*\\\",\\n    \\\"*ASM-00*\\\",\\n]\\nFILTERS_SIMULATOR_PATTERNS_SKIP = [\\n    \\\"*Agitator*\\\",\\n    \\\"*BOP *\\\",\\n    \\\"*Bulk*Systems*\\\",\\n    \\\"*Cabinet*\\\",\\n    \\\"*Centrifuge*\\\",\\n    \\\"*Compressor*\\\",\\n    \\\"*Crane*\\\",\\n    \\\"*Cuttings*\\\",\\n    \\\"*Cyberbase*\\\",\\n    \\\"*DCDA *\\\",\\n    \\\"*DCI*\\\",\\n    \\\"*Deadline*\\\",\\n    \\\"*Gantry*Crane*\\\",\\n    \\\"*HPU*\\\",\\n    \\\"*HVAC*\\\",\\n    \\\"*Knuckle*Crane*\\\",\\n    \\\"*LMRP*\\\",\\n    \\\"*Manrider*\\\",\\n    \\\"*Mixer*\\\",\\n    \\\"*Mixing*\\\",\\n    \\\"*Mud *\\\",\\n    \\\"*Pumps*\\\",\\n    \\\"*Sdi*\\\",\\n    \\\"*Standby*\\\",\\n    \\\"*Station*\\\",\\n    \\\"*Tank *\\\",\\n    \\\"*Valve*\\\",\\n    \\\"*VOID*\\\",\\n    \\\"*Winch*\\\",\\n    \\\"*X-tree*\\\",\\n    \\\"*Yoke*\\\",\\n]\\nCONFIG = {\\n    \\\"rig_number\\\": f\\\"{PROJECT_RIG_ID}.020\\\",\\n    \\\"search_urls\\\": PROJECTINFO_GAD,\\n    \\\"show_progress\\\": True,\\n    \\\"filters\\\": [\\n        {\\n            \\\"type\\\": \\\"docs\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": FILTERS_SIMULATOR_PATTERNS_INCLUDE,\\n            \\\"match_field\\\": \\\"item_generated_name\\\",\\n            \\\"field\\\": \\\"item_include\\\",\\n            \\\"value\\\": True,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"docs\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": FILTERS_SIMULATOR_PATTERNS_SKIP,\\n            \\\"match_field\\\": \\\"item_generated_name\\\",\\n            \\\"field\\\": \\\"item_include\\\",\\n            \\\"value\\\": False,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"docs\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": FILTERS_SIMULATOR_PATTERNS_SKIP,\\n            \\\"match_field\\\": \\\"item_case_description\\\",\\n            \\\"field\\\": \\\"item_include\\\",\\n            \\\"value\\\": False,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"docs\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": [\\\"(VOID)\\\"],\\n            \\\"match_field\\\": \\\"item_case_description\\\",\\n            \\\"field\\\": \\\"item_include\\\",\\n            \\\"value\\\": False,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"files\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": [\\\"*.pdf\\\"],\\n            \\\"match_field\\\": \\\"item_file_ext\\\",\\n            \\\"field\\\": \\\"item_download\\\",\\n            \\\"value\\\": True,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"files\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": FILTERS_SIMULATOR_PATTERNS_SKIP,\\n            \\\"match_field\\\": \\\"item_case_description\\\",\\n            \\\"field\\\": \\\"item_download\\\",\\n            \\\"value\\\": False,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"files\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": [\\\"*Partslist*\\\"],\\n            \\\"match_field\\\": \\\"item_generated_name\\\",\\n            \\\"field\\\": \\\"item_download\\\",\\n            \\\"value\\\": False,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n    ],\\n}\\nimport json\\nimport os\\nimport re\\nimport shutil\\nimport time\\nimport timeit\\nfrom contextlib import contextmanager\\nfrom urllib.parse import urljoin\\nimport colorama\\nimport dateutil.parser\\nfrom ansimarkup import ansiprint\\nfrom bs4 import BeautifulSoup\\nfrom selenium import webdriver\\nfrom selenium.webdriver.chrome.options import Options\\nfrom selenium.webdriver.chrome.service import Service\\nfrom selenium.webdriver.support.ui import WebDriverWait\\nfrom selenium.webdriver.common.by import By\\nfrom selenium.webdriver.support import expected_conditions as EC\\nfrom selenium.common.exceptions import TimeoutException\\nfrom webdriver_manager.chrome import ChromeDriverManager\\ncolorama.init()\\nBASE_OUTPUT = os.path.abspath(os.path.join(os.getcwd(), \\\"outputs\\\"))\\nDATA_DIR    = os.path.join(BASE_OUTPUT, \\\"data\\\")\\nDL_DIR      = os.path.join(BASE_OUTPUT, \\\"downloads\\\")\\nfor d in (BASE_OUTPUT, DATA_DIR, DL_DIR):\\n    os.makedirs(d, exist_ok=True)\\nDEFAULT_DOC_FIELD_ORDER = [\\n    \\\"item_include\\\",\\n    \\\"item_generated_name\\\",\\n    \\\"item_type\\\",\\n    \\\"item_case_no\\\",\\n    \\\"item_drawing_type\\\",\\n    \\\"item_doc_no\\\",\\n    \\\"item_date\\\",\\n    \\\"item_title\\\",\\n    \\\"item_case_description\\\",\\n    \\\"item_status\\\",\\n    \\\"item_revision\\\",\\n    \\\"item_responsible\\\",\\n    \\\"item_url\\\",\\n]\\nDEFAULT_FILE_FIELD_ORDER = [\\n    \\\"item_type\\\",\\n    \\\"item_generated_name\\\",\\n    \\\"item_case_no\\\",\\n    \\\"item_drawing_type\\\",\\n    \\\"item_title\\\",\\n    \\\"item_doc_no\\\",\\n    \\\"item_file_ext\\\",\\n    \\\"item_download\\\",\\n    \\\"item_file_size\\\",\\n    \\\"item_file_id\\\",\\n    \\\"item_date\\\",\\n    \\\"item_revision\\\",\\n    \\\"item_case_description\\\",\\n    \\\"item_status\\\",\\n    \\\"item_responsible\\\",\\n    \\\"item_url\\\",\\n]\\ndef cprint(x, color='#FFFFFF'):\\n    ansiprint(f'<fg {color}>{x}')\\nprint_err  = lambda x: cprint(x, '#C92B65')\\nprint_warn = lambda x: cprint(x, '#E6992B')\\nprint_ok   = lambda x: cprint(x, '#1FCE46')\\nprint_inf  = lambda x: cprint(x, '#FFFFFF')\\nprint_sub  = lambda x: cprint(x, '#74705D')\\nclass SleepCondition:\\n    def __init__(self, s):\\n        self.s = s\\n    def __call__(self, _):\\n        time.sleep(self.s)\\n        return True\\ndef reformat_date(s):\\n    try:\\n        dt = dateutil.parser.parse(s)\\n        return dt.strftime('%Y.%m.%d')\\n    except:\\n        return s\\ndef match_pattern(text, pattern):\\n    if isinstance(pattern, list):\\n        return any(match_pattern(text, p) for p in pattern)\\n    regex_pattern = pattern.replace(\\\".\\\", \\\"\\\\\\\\.\\\").replace(\\\"*\\\", \\\".*\\\").replace(\\\"?\\\", \\\".\\\")\\n    return bool(re.search(f\\\"^{regex_pattern}$\\\", text, re.IGNORECASE))\\ndef sanitize_filename(s):\\n    replacements = {\\n        \\\"'\\\": \\\"\\\", '\\\"': \\\"\\\", '`': \\\"\\\",\\n        '\\\\\\\\': \\\"_\\\", '|': \\\"_\\\", '?': \\\"_\\\", '*': \\\"_\\\", '<': \\\"_\\\", '>': \\\"_\\\", ':': \\\"_\\\"\\n    }\\n    result = s\\n    for char, replacement in replacements.items():\\n        result = result.replace(char, replacement)\\n    result = ''.join(c for c in result if c.isprintable() or c == '/')\\n    result = re.sub(r'\\\\s+', ' ', result)\\n    return result.strip()\\ndef reorder_dict_keys(data_dict, field_order):\\n    reordered = {}\\n    for key in field_order:\\n        if key in data_dict:\\n            reordered[key] = data_dict[key]\\n    return reordered\\ndef remove_duplicates(data, exclude_keys=None, priority_key='item_download', priority_vals=None):\\n    if not exclude_keys:\\n        exclude_keys=[]\\n    if not priority_vals:\\n        priority_vals=[]\\n    exset = set(exclude_keys)\\n    pmap  = {v:i for i,v in enumerate(priority_vals)}\\n    out   = {}\\n    for d in data:\\n        filt = {k:v for k,v in d.items() if k not in exset}\\n        hkey = frozenset(filt.items())\\n        if hkey not in out:\\n            out[hkey] = d\\n        else:\\n            if priority_key and priority_vals:\\n                newv = d.get(priority_key)\\n                oldv = out[hkey].get(priority_key)\\n                if (newv in pmap) and (oldv in pmap) and pmap[newv] < pmap[oldv]:\\n                    out[hkey] = d\\n                elif newv in pmap and oldv not in pmap:\\n                    out[hkey] = d\\n    return list(out.values())\\ndef dedupe_and_save(new_items, path, field_order=None):\\n    if os.path.exists(path):\\n        try:\\n            with open(path,'r',encoding='utf-8') as f:\\n                old = json.load(f)\\n        except:\\n            old = []\\n    else:\\n        old = []\\n    merged = old + new_items\\n    merged = remove_duplicates(\\n        merged,\\n        exclude_keys=['item_download','item_generated_path'],\\n        priority_key='item_download',\\n        priority_vals=[False,True]\\n    )\\n    if field_order:\\n        merged = [reorder_dict_keys(d, field_order) for d in merged]\\n    tmp = path + '.temp'\\n    with open(tmp,'w',encoding='utf-8') as f:\\n        json.dump(merged,f,indent=2)\\n    os.replace(tmp,path)\\n    rel_path = os.path.relpath(path, os.getcwd())\\n    print_inf(f\\\"Saved {len(merged)} items => {rel_path}\\\")\\ndef append_items(new_items, path, field_order=None):\\n    if new_items:\\n        dedupe_and_save(new_items, path, field_order=field_order)\\ndef json_to_md_table(json_path, md_path, field_order=None, filter_chain=None):\\n    if not os.path.exists(json_path):\\n        print_err(f\\\"No JSON found: {json_path}\\\")\\n        return\\n    with open(json_path,'r',encoding='utf-8') as f:\\n        data = json.load(f)\\n    if not data:\\n        print_warn(f\\\"No data in {json_path}\\\")\\n        return\\n    item_type = \\\"docs\\\" if \\\"-a-docs.\\\" in json_path else \\\"files\\\"\\n    if filter_chain and isinstance(filter_chain, list):\\n        for filter_idx, filter_config in enumerate(filter_chain):\\n            if not filter_config.get(\\\"enabled\\\", False) or filter_config.get(\\\"type\\\") != item_type:\\n                continue\\n            pattern = filter_config.get(\\\"pattern\\\", \\\"\\\")\\n            field = filter_config.get(\\\"field\\\", \\\"\\\")\\n            value = filter_config.get(\\\"value\\\", True)\\n            comment = filter_config.get(\\\"comment\\\", \\\"\\\")\\n            match_field = filter_config.get(\\\"match_field\\\", \\\"item_generated_name\\\")\\n            if pattern and field:\\n                filtered_count = 0\\n                for d in data:\\n                    field_value = d.get(match_field, \\\"\\\")\\n                    if field_value and match_pattern(field_value, pattern):\\n                        if d.get(field) != value:\\n                            d[field] = value\\n                            filtered_count += 1\\n                if filtered_count > 0:\\n                    action = \\\"Set\\\" if value else \\\"Cleared\\\"\\n                    if isinstance(pattern, list):\\n                        pattern_str = f\\\"[{', '.join(pattern)}]\\\"\\n                    else:\\n                        pattern_str = f\\\"'{pattern}'\\\"\\n                    match_field_str = f\\\" in {match_field}\\\" if match_field != \\\"item_generated_name\\\" else \\\"\\\"\\n                    print_ok(f\\\"Filter #{filter_idx+1}: {action} {field}={value} for {filtered_count} items matching {pattern_str}{match_field_str}\\\")\\n                    if comment:\\n                        print_sub(f\\\"  \\u2192 {comment}\\\")\\n    if not field_order:\\n        colset = set()\\n        for d in data:\\n            colset.update(d.keys())\\n        field_order = sorted(colset)\\n    lines = []\\n    lines.append(\\\"| \\\" + \\\" | \\\".join(field_order) + \\\" |\\\")\\n    lines.append(\\\"|\\\" + \\\"|\\\".join([\\\"---\\\"]*len(field_order)) + \\\"|\\\")\\n    for d in data:\\n        row = []\\n        for c in field_order:\\n            val = d.get(c,\\\"\\\")\\n            if isinstance(val,bool):\\n                val = str(val).lower()\\n            val = str(val).replace(\\\"|\\\",\\\"\\\\\\\\|\\\")\\n            row.append(val)\\n        lines.append(\\\"| \\\" + \\\" | \\\".join(row) + \\\" |\\\")\\n    with open(md_path,'w',encoding='utf-8') as f:\\n        f.write(\\\"\\\\n\\\".join(lines))\\n    rel_md_path = os.path.relpath(md_path, os.getcwd())\\n    print_ok(f\\\"Exported {len(data)} items => {rel_md_path}\\\")\\ndef md_table_to_json(md_path, json_path, field_order=None):\\n    if not os.path.exists(md_path):\\n        print_err(f\\\"No Markdown file found: {md_path}\\\")\\n        return\\n    with open(md_path,'r',encoding='utf-8') as f:\\n        lines=f.read().splitlines()\\n    table_lines=[ln.strip() for ln in lines if ln.strip().startswith(\\\"|\\\")]\\n    if len(table_lines)<2:\\n        print_err(\\\"No valid table rows found in the Markdown.\\\")\\n        return\\n    header=table_lines[0]\\n    data_lines=table_lines[2:]\\n    cols=[x.strip() for x in header.strip('|').split('|')]\\n    new_data=[]\\n    for ln in data_lines:\\n        rowcols=[x.strip() for x in ln.strip('|').split('|')]\\n        if len(rowcols)!=len(cols):\\n            print_warn(f\\\"Skipping mismatch line:\\\\n{ln}\\\")\\n            continue\\n        d={}\\n        for i,c in enumerate(cols):\\n            val=rowcols[i]\\n            if val.lower()==\\\"true\\\":\\n                val=True\\n            elif val.lower()==\\\"false\\\":\\n                val=False\\n            d[c]=val\\n        new_data.append(d)\\n    if field_order:\\n        new_data = [reorder_dict_keys(item, field_order) for item in new_data]\\n    with open(json_path,'w',encoding='utf-8') as f:\\n        json.dump(new_data,f,indent=2)\\n    rel_json_path = os.path.relpath(json_path, os.getcwd())\\n    print_ok(f\\\"Imported {len(new_data)} items => {rel_json_path}\\\")\\nclass RigDocScraper:\\n    def __init__(self, rig, show_progress=False):\\n        self.rig=rig\\n        self.show=show_progress\\n        self.driver=None\\n        self.docs_path = os.path.join(DATA_DIR, f'{rig}-a-docs.json')\\n        self.files_path= os.path.join(DATA_DIR, f'{rig}-b-files.json')\\n    @contextmanager\\n    def browser_session(self, download_mode=False):\\n        profdir = None\\n        try:\\n            opts = Options()\\n            opts.add_argument(\\\"--log-level=3\\\")\\n            if download_mode:\\n                appdata = os.environ.get(\\\"APPDATA\\\", os.getcwd())\\n                profdir = os.path.join(appdata, \\\"chromedriver_profile\\\")\\n                os.makedirs(profdir, exist_ok=True)\\n                opts.add_argument(f\\\"--user-data-dir={profdir}\\\")\\n                dl_dir = os.path.join(DL_DIR, self.rig)\\n                os.makedirs(dl_dir, exist_ok=True)\\n                prefs = {\\n                    \\\"download.default_directory\\\": dl_dir,\\n                    \\\"download.prompt_for_download\\\": False,\\n                    \\\"download.directory_upgrade\\\": True,\\n                    \\\"safebrowsing.enabled\\\": True\\n                }\\n                opts.add_experimental_option(\\\"prefs\\\", prefs)\\n            svc = Service(ChromeDriverManager().install())\\n            self.driver = webdriver.Chrome(service=svc, options=opts)\\n            wait_time = 2 if download_mode else 5\\n            WebDriverWait(self.driver, wait_time).until(SleepCondition(wait_time))\\n            self.driver.implicitly_wait(5 if download_mode else 10)\\n            if self.show:\\n                print_sub(f\\\"[Browser] Started {'download-enabled ' if download_mode else ''}session\\\")\\n            yield self.driver\\n        finally:\\n            if self.driver:\\n                if self.show:\\n                    print_sub(\\\"[Browser] Closing session\\\")\\n                self.driver.quit()\\n                self.driver = None\\n                if download_mode and profdir:\\n                    try:\\n                        shutil.rmtree(profdir)\\n                    except Exception as e:\\n                        if self.show:\\n                            print_warn(f\\\"[Browser] Failed to clean up profile: {e}\\\")\\n    def fetch_docs(self, urls):\\n        if not os.path.exists(self.docs_path):\\n            with open(self.docs_path,'w',encoding='utf-8') as f:\\n                json.dump([],f)\\n        with self.browser_session() as driver:\\n            for url in urls:\\n                if self.show:\\n                    print_sub(f\\\"[DocScrape] => {url}\\\")\\n                driver.get(url)\\n                is_rig = (\\\"rigsearch\\\" in url.lower()) or (\\\"advancedsearch\\\" in url.lower())\\n                max_wait = 10 if is_rig else 5\\n                wait_scroll = 5 if is_rig else 2\\n                try:\\n                    WebDriverWait(driver, max_wait).until(\\n                        EC.presence_of_element_located((By.CSS_SELECTOR, \\\"a.search-result-link\\\"))\\n                    )\\n                except TimeoutException:\\n                    print_warn(f\\\"Waited up to {max_wait}s, but no 'search-result-link' found. Proceeding anyway...\\\")\\n                inc = 0\\n                prev = driver.execute_script(\\\"return document.body.scrollHeight\\\")\\n                while inc < 150:\\n                    inc += 1\\n                    driver.execute_script(\\\"window.scrollTo(0, document.body.scrollHeight);\\\")\\n                    time.sleep(wait_scroll)\\n                    newh = driver.execute_script(\\\"return document.body.scrollHeight\\\")\\n                    if self.show:\\n                        print_sub(f\\\" -> Scroll {inc}, h={newh}\\\")\\n                    if newh == prev:\\n                        break\\n                    prev = newh\\n                soup = BeautifulSoup(driver.page_source, \\\"html.parser\\\")\\n                row_elems = soup.find_all(\\\"a\\\", class_=\\\"search-result-link\\\")\\n                rows = [(td.find_parent(\\\"tr\\\")).find_all(\\\"td\\\") for td in row_elems]\\n                if self.show:\\n                    print_inf(f\\\"Found {len(rows)} doc rows.\\\")\\n                new_docs = []\\n                for row_cells in rows:\\n                    doc_title   = re.sub(r'[\\\\n\\\\r]+',' ', row_cells[1].a.text.strip())\\n                    sub_type    = row_cells[2].get_text(strip=True)\\n                    doc_no      = row_cells[3].get_text(strip=True)\\n                    responsible = row_cells[4].get_text(strip=True)\\n                    case_no     = row_cells[5].get_text(strip=True)\\n                    case_desc   = row_cells[6].get_text(strip=True)\\n                    revision    = row_cells[7].get_text(strip=True)\\n                    status      = row_cells[8].get_text(strip=True)\\n                    dt_         = reformat_date(row_cells[9].get_text(strip=True))\\n                    doc_url     = urljoin(\\\"https://rigdoc.nov.com/\\\", row_cells[1].a.get(\\\"href\\\"))\\n                    if \\\"document-void\\\" in (row_cells[1].a.get(\\\"class\\\", [])) or \\\"*VOID*\\\" in doc_title.upper():\\n                        status = \\\"(VOID)\\\"\\n                    doc_generated_name = (\\n                        f\\\"{doc_no.upper()}_REV{revision.upper()}--{case_no.upper()}-{doc_title.title()}\\\"\\n                    ).replace(\\\"  \\\", \\\" \\\")\\n                    doc_generated_name_sanitized = sanitize_filename(doc_generated_name)\\n                    new_docs.append({\\n                        \\\"item_type\\\": \\\"doc\\\",\\n                        \\\"item_title\\\": doc_title,\\n                        \\\"item_drawing_type\\\": sub_type,\\n                        \\\"item_doc_no\\\": doc_no,\\n                        \\\"item_responsible\\\": responsible,\\n                        \\\"item_case_no\\\": case_no,\\n                        \\\"item_case_description\\\": case_desc,\\n                        \\\"item_revision\\\": revision,\\n                        \\\"item_status\\\": status,\\n                        \\\"item_date\\\": dt_,\\n                        \\\"item_url\\\": doc_url,\\n                        \\\"item_include\\\": False,\\n                        \\\"item_generated_name\\\": doc_generated_name_sanitized\\n                    })\\n                append_items(new_docs, self.docs_path, field_order=DEFAULT_DOC_FIELD_ORDER)\\n        rel_docs_path = os.path.relpath(self.docs_path, os.getcwd())\\n        print_ok(f\\\"Docs => {rel_docs_path}\\\")\\n    def fetch_files(self):\\n        if not os.path.exists(self.docs_path):\\n            print_err(f\\\"No docs found: {self.docs_path}\\\")\\n            return\\n        with open(self.docs_path,'r',encoding='utf-8') as f:\\n            all_docs=json.load(f)\\n        docs_for_files=[d for d in all_docs if d.get(\\\"item_type\\\")==\\\"doc\\\" and d.get(\\\"item_include\\\")==True]\\n        if not docs_for_files:\\n            print_warn(\\\"No docs with item_include==True => No files to fetch.\\\")\\n            return\\n        if not os.path.exists(self.files_path):\\n            with open(self.files_path,'w',encoding='utf-8') as f:\\n                json.dump([],f)\\n        with self.browser_session() as driver:\\n            for i,doc in enumerate(docs_for_files,1):\\n                if self.show:\\n                    print_sub(f\\\"[FileScrape] doc {i}/{len(docs_for_files)} => {doc.get('item_doc_no')}\\\")\\n                doc_url = doc.get(\\\"item_url\\\",\\\"\\\")\\n                if not doc_url:\\n                    continue\\n                wp_url = doc_url.replace(\\\"nov.com/documents/\\\",\\\"nov.com/wp/documents/\\\")\\n                if \\\"nov.com/wp/documents/\\\" not in wp_url:\\n                    continue\\n                driver.get(wp_url)\\n                WebDriverWait(driver,5).until(SleepCondition(5))\\n                soup=BeautifulSoup(driver.page_source,\\\"html.parser\\\")\\n                frows=soup.select(\\\"div.revision-panel-body div.file-detail-row\\\")\\n                flinks=[]\\n                for row in frows:\\n                    lk=row.select_one(\\\"div.file-detail a\\\")\\n                    if lk and lk.get(\\\"href\\\"):\\n                        flinks.append(urljoin(\\\"https://rigdoc.nov.com/\\\", lk[\\\"href\\\"]))\\n                doc_generated_name = doc.get(\\\"item_generated_name\\\",\\\"DOC_UNKNOWN\\\")\\n                new_files=[]\\n                for idx,flink in enumerate(flinks,1):\\n                    fapi=flink.replace(\\\"/_download\\\",\\\"\\\")\\n                    driver.get(fapi)\\n                    WebDriverWait(driver,0.5).until(SleepCondition(0.5))\\n                    raw=BeautifulSoup(driver.page_source,\\\"html.parser\\\").get_text(strip=True)\\n                    info=json.loads(raw)\\n                    fext=info.get(\\\"extension\\\",\\\"\\\")\\n                    fsz =info.get(\\\"filesize\\\",0)\\n                    fsmb=f\\\"{(fsz/(1024*1024)):.2f} MB\\\"\\n                    ftitle=info.get(\\\"title\\\",\\\"\\\").strip()\\n                    file_generated_name = (f\\\"{doc_generated_name}.id.{idx}-{len(flinks)}.{ftitle.title()}\\\").replace(\\\"  \\\",\\\" \\\")\\n                    if fext and not file_generated_name.lower().endswith(f\\\".{fext.lower()}\\\"):\\n                        file_generated_name = f\\\"{file_generated_name}{fext.lower()}\\\"\\n                    file_generated_name_sanitized = sanitize_filename(file_generated_name)\\n                    new_files.append({\\n                        \\\"item_type\\\": \\\"file\\\",\\n                        \\\"item_title\\\": ftitle,\\n                        \\\"item_file_id\\\": f\\\"{len(flinks)}->({idx}/{len(flinks)}) FID:{info.get('fileId','-')}\\\",\\n                        \\\"item_file_ext\\\": f\\\"{fext.lower()}\\\" if fext else \\\"\\\",\\n                        \\\"item_file_size\\\": fsmb,\\n                        \\\"item_date\\\": reformat_date(info.get(\\\"insertedDate\\\",\\\"\\\")),\\n                        \\\"item_revision\\\": doc.get(\\\"item_revision\\\",\\\"\\\"),\\n                        \\\"item_responsible\\\": info.get(\\\"insertedBy\\\",\\\"\\\"),\\n                        \\\"item_status\\\": info.get(\\\"lastChangedDate\\\") or \\\"-\\\",\\n                        \\\"item_case_no\\\": doc.get(\\\"item_case_no\\\",\\\"\\\"),\\n                        \\\"item_doc_no\\\": doc.get(\\\"item_doc_no\\\",\\\"\\\"),\\n                        \\\"item_drawing_type\\\": doc.get(\\\"item_drawing_type\\\",\\\"\\\"),\\n                        \\\"item_case_description\\\": doc.get(\\\"item_case_description\\\",\\\"\\\"),\\n                        \\\"item_url\\\": flink,\\n                        \\\"item_download\\\": False,\\n                        \\\"item_generated_name\\\": file_generated_name_sanitized\\n                    })\\n                append_items(new_files, self.files_path, field_order=DEFAULT_FILE_FIELD_ORDER)\\n        rel_files_path = os.path.relpath(self.files_path, os.getcwd())\\n        print_ok(f\\\"Files => {rel_files_path}\\\")\\n    def download_files(self):\\n        if not os.path.exists(self.files_path):\\n            print_err(f\\\"No files JSON found at {self.files_path}\\\")\\n            return\\n        with open(self.files_path, 'r', encoding='utf-8') as f:\\n            data = json.load(f)\\n        to_download = [x for x in data if x.get(\\\"item_type\\\") == \\\"file\\\" and x.get(\\\"item_download\\\") == True]\\n        if not to_download:\\n            print_warn(\\\"No files with item_download==True.\\\")\\n            return\\n        download_dir = os.path.join(DL_DIR, self.rig)\\n        print_inf(f\\\"Will download {len(to_download)} files => {download_dir}\\\")\\n        with self.browser_session(download_mode=True) as driver:\\n            for i, fitem in enumerate(to_download, 1):\\n                furl = fitem.get(\\\"item_url\\\", \\\"\\\")\\n                if not furl:\\n                    continue\\n                base_name = fitem.get(\\\"item_generated_name\\\", f\\\"file_{i}\\\")\\n                if not base_name:\\n                    base_name = f\\\"file_{i}\\\"\\n                base_name = sanitize_filename(base_name)\\n                parts = base_name.split(\\\"/\\\")\\n                if parts:\\n                    last_part = parts[-1]\\n                    ext = fitem.get(\\\"item_file_ext\\\", \\\"\\\").lower()\\n                    if ext and not last_part.lower().endswith(f\\\"{ext}\\\"):\\n                        ext = f\\\".{ext}\\\" if not ext.startswith(\\\".\\\") else ext\\n                        final_filename = last_part + ext\\n                    else:\\n                        final_filename = last_part\\n                    subfolders = parts[:-1]\\n                else:\\n                    ext = fitem.get(\\\"item_file_ext\\\", \\\"\\\")\\n                    ext = f\\\".{ext}\\\" if ext and not ext.startswith(\\\".\\\") else ext\\n                    final_filename = f\\\"file_{i}{ext}\\\"\\n                    subfolders = []\\n                final_subdir = os.path.join(download_dir, *subfolders)\\n                os.makedirs(final_subdir, exist_ok=True)\\n                final_destination = os.path.join(final_subdir, final_filename)\\n                if os.path.exists(final_destination):\\n                    print_ok(f'Skipped (already exists): \\\"{final_destination}\\\"')\\n                    continue\\n                if self.show:\\n                    print_sub(\\\"-\\\" * 80)\\n                    print_inf(f'[Download {i}/{len(to_download)}] => \\\"{final_filename}\\\"')\\n                start = timeit.default_timer()\\n                existing_files = set(f for f in os.listdir(download_dir) if os.path.isfile(os.path.join(download_dir, f)))\\n                driver.get(furl)\\n                time.sleep(1)\\n                done = False\\n                while (timeit.default_timer() - start) < 30:\\n                    time.sleep(1)\\n                    new_files = set(f for f in os.listdir(download_dir) if os.path.isfile(os.path.join(download_dir, f))) - existing_files\\n                    if new_files:\\n                        candidate = list(new_files)[0]\\n                        ext2 = os.path.splitext(candidate)[1].lower()\\n                        if ext2 not in [\\\".tmp\\\", \\\".crdownload\\\"]:\\n                            src = os.path.join(download_dir, candidate)\\n                            try:\\n                                os.rename(src, final_destination)\\n                                done = True\\n                                print_ok(f'-> Downloaded \\\"{final_destination}\\\"')\\n                                break\\n                            except Exception as e:\\n                                print_warn(f'Failed to rename: {e}')\\n                if not done:\\n                    print_warn(f'Timed out: \\\"{final_destination}\\\"')\\n        print_inf(\\\"All file downloads attempted.\\\")\\ndef run_script(cfg):\\n    rig = cfg.get(\\\"rig_number\\\", \\\"R9999\\\")\\n    urls = cfg.get(\\\"search_urls\\\", [])\\n    sp = cfg.get(\\\"show_progress\\\", False)\\n    filters = cfg.get(\\\"filters\\\", [])\\n    scraper = RigDocScraper(rig, show_progress=sp)\\n    while True:\\n        doc_json = os.path.join(DATA_DIR, f'{rig}-a-docs.json')\\n        doc_md = os.path.join(DATA_DIR, f'{rig}-a-docs.md')\\n        file_json = os.path.join(DATA_DIR, f'{rig}-b-files.json')\\n        file_md = os.path.join(DATA_DIR, f'{rig}-b-files.md')\\n        def configure_filters():\\n            while True:\\n                print_inf(\\\"\\\\nCurrent filter chain:\\\")\\n                if not filters:\\n                    print_sub(\\\"  No filters configured\\\")\\n                else:\\n                    for i, f in enumerate(filters):\\n                        status = \\\"ENABLED\\\" if f.get(\\\"enabled\\\", False) else \\\"DISABLED\\\"\\n                        type_str = f.get(\\\"type\\\", \\\"unknown\\\")\\n                        pattern = f.get(\\\"pattern\\\", \\\"\\\")\\n                        field = f.get(\\\"field\\\", \\\"\\\")\\n                        value = f.get(\\\"value\\\", True)\\n                        comment = f.get(\\\"comment\\\", \\\"\\\")\\n                        if isinstance(pattern, list):\\n                            pattern_str = f\\\"[{', '.join(pattern)}]\\\"\\n                        else:\\n                            pattern_str = f\\\"'{pattern}'\\\"\\n                        match_field = f.get(\\\"match_field\\\", \\\"item_generated_name\\\")\\n                        match_field_str = f\\\" in {match_field}\\\" if match_field != \\\"item_generated_name\\\" else \\\"\\\"\\n                        color_fn = print_ok if f.get(\\\"enabled\\\", False) else print_sub\\n                        color_fn(f\\\"  [{i+1}] {status} - {type_str}: {pattern_str}{match_field_str} \\u2192 {field}={value}\\\")\\n                        if comment:\\n                            print_sub(f\\\"      {comment}\\\")\\n                print_inf(\\\"\\\\nFilter options:\\\")\\n                print_inf(\\\"  [a] Add new filter\\\")\\n                print_inf(\\\"  [e] Edit filter\\\")\\n                print_inf(\\\"  [d] Delete filter\\\")\\n                print_inf(\\\"  [t] Toggle filter\\\")\\n                print_inf(\\\"  [m] Move filter\\\")\\n                print_inf(\\\"  [s] Save and return\\\")\\n                choice = input(\\\"\\\\nEnter option: \\\").strip().lower()\\n                if choice == 'a':\\n                    print_inf(\\\"\\\\nAdd new filter:\\\")\\n                    filter_type = input(\\\"  Type (docs/files): \\\").strip().lower()\\n                    if filter_type not in ['docs', 'files']:\\n                        print_err(\\\"  Invalid type. Must be 'docs' or 'files'\\\")\\n                        continue\\n                    pattern_input = input(\\\"  Pattern (e.g., *g0001*, *.pdf) or comma-separated list: \\\").strip()\\n                    if not pattern_input:\\n                        print_err(\\\"  Pattern cannot be empty\\\")\\n                        continue\\n                    if \\\",\\\" in pattern_input:\\n                        pattern = [p.strip() for p in pattern_input.split(\\\",\\\") if p.strip()]\\n                    else:\\n                        pattern = pattern_input\\n                    match_field = input(f\\\"  Field to match against [item_generated_name]: \\\").strip()\\n                    if not match_field:\\n                        match_field = 'item_generated_name'\\n                    field = input(f\\\"  Field to set ({'item_include' if filter_type == 'docs' else 'item_download'}): \\\").strip()\\n                    if not field:\\n                        field = 'item_include' if filter_type == 'docs' else 'item_download'\\n                    value_input = input(f\\\"  Value to set (true/false) [true]: \\\").strip().lower()\\n                    value = False if value_input in ['false', 'f', 'no', 'n', '0'] else True\\n                    comment = input(\\\"  Comment (optional): \\\").strip()\\n                    new_filter = {\\n                        \\\"type\\\": filter_type,\\n                        \\\"enabled\\\": True,\\n                        \\\"pattern\\\": pattern,\\n                        \\\"match_field\\\": match_field,\\n                        \\\"field\\\": field,\\n                        \\\"value\\\": value,\\n                        \\\"comment\\\": comment\\n                    }\\n                    filters.append(new_filter)\\n                    print_ok(\\\"  Filter added\\\")\\n                elif choice == 'e':\\n                    if not filters:\\n                        print_err(\\\"  No filters to edit\\\")\\n                        continue\\n                    idx_input = input(f\\\"  Filter number to edit (1-{len(filters)}): \\\").strip()\\n                    try:\\n                        idx = int(idx_input) - 1\\n                        if idx < 0 or idx >= len(filters):\\n                            raise ValueError()\\n                    except ValueError:\\n                        print_err(\\\"  Invalid filter number\\\")\\n                        continue\\n                    f = filters[idx]\\n                    print_inf(f\\\"\\\\nEditing filter #{idx+1}:\\\")\\n                    filter_type = input(f\\\"  Type (docs/files) [{f.get('type', '')}]: \\\").strip().lower()\\n                    if filter_type and filter_type in ['docs', 'files']:\\n                        f['type'] = filter_type\\n                    current_pattern = f.get('pattern', '')\\n                    if isinstance(current_pattern, list):\\n                        current_pattern_str = \\\", \\\".join(current_pattern)\\n                    else:\\n                        current_pattern_str = current_pattern\\n                    pattern_input = input(f\\\"  Pattern [{current_pattern_str}]: \\\").strip()\\n                    if pattern_input:\\n                        if \\\",\\\" in pattern_input:\\n                            f['pattern'] = [p.strip() for p in pattern_input.split(\\\",\\\") if p.strip()]\\n                        else:\\n                            f['pattern'] = pattern_input\\n                    match_field = input(f\\\"  Field to match against [{f.get('match_field', 'item_generated_name')}]: \\\").strip()\\n                    if match_field:\\n                        f['match_field'] = match_field\\n                    elif 'match_field' not in f:\\n                        f['match_field'] = 'item_generated_name'\\n                    field = input(f\\\"  Field to set [{f.get('field', '')}]: \\\").strip()\\n                    if field:\\n                        f['field'] = field\\n                    value_input = input(f\\\"  Value (true/false) [{f.get('value', True)}]: \\\").strip().lower()\\n                    if value_input:\\n                        f['value'] = False if value_input in ['false', 'f', 'no', 'n', '0'] else True\\n                    comment = input(f\\\"  Comment [{f.get('comment', '')}]: \\\").strip()\\n                    if comment or comment == '':\\n                        f['comment'] = comment\\n                    print_ok(\\\"  Filter updated\\\")\\n                elif choice == 'd':\\n                    if not filters:\\n                        print_err(\\\"  No filters to delete\\\")\\n                        continue\\n                    idx_input = input(f\\\"  Filter number to delete (1-{len(filters)}): \\\").strip()\\n                    try:\\n                        idx = int(idx_input) - 1\\n                        if idx < 0 or idx >= len(filters):\\n                            raise ValueError()\\n                    except ValueError:\\n                        print_err(\\\"  Invalid filter number\\\")\\n                        continue\\n                    del filters[idx]\\n                    print_ok(\\\"  Filter deleted\\\")\\n                elif choice == 't':\\n                    if not filters:\\n                        print_err(\\\"  No filters to toggle\\\")\\n                        continue\\n                    idx_input = input(f\\\"  Filter number to toggle (1-{len(filters)}): \\\").strip()\\n                    try:\\n                        idx = int(idx_input) - 1\\n                        if idx < 0 or idx >= len(filters):\\n                            raise ValueError()\\n                    except ValueError:\\n                        print_err(\\\"  Invalid filter number\\\")\\n                        continue\\n                    filters[idx]['enabled'] = not filters[idx].get('enabled', False)\\n                    status = \\\"enabled\\\" if filters[idx]['enabled'] else \\\"disabled\\\"\\n                    print_ok(f\\\"  Filter {status}\\\")\\n                elif choice == 'm':\\n                    if len(filters) < 2:\\n                        print_err(\\\"  Need at least 2 filters to move\\\")\\n                        continue\\n                    from_idx_input = input(f\\\"  Filter number to move (1-{len(filters)}): \\\").strip()\\n                    try:\\n                        from_idx = int(from_idx_input) - 1\\n                        if from_idx < 0 or from_idx >= len(filters):\\n                            raise ValueError()\\n                    except ValueError:\\n                        print_err(\\\"  Invalid filter number\\\")\\n                        continue\\n                    to_idx_input = input(f\\\"  New position (1-{len(filters)}): \\\").strip()\\n                    try:\\n                        to_idx = int(to_idx_input) - 1\\n                        if to_idx < 0 or to_idx >= len(filters):\\n                            raise ValueError()\\n                    except ValueError:\\n                        print_err(\\\"  Invalid position\\\")\\n                        continue\\n                    if from_idx == to_idx:\\n                        print_sub(\\\"  No change needed\\\")\\n                        continue\\n                    filter_to_move = filters.pop(from_idx)\\n                    filters.insert(to_idx, filter_to_move)\\n                    print_ok(f\\\"  Filter moved from position {from_idx+1} to {to_idx+1}\\\")\\n                elif choice == 's':\\n                    break\\n                else:\\n                    print_err(\\\"  Invalid option\\\")\\n            return True\\n        steps = [\\n            (\\\"Change search parameters\\\", None),\\n            (\\\"Configure filter chain\\\", configure_filters),\\n            (\\\"Fetch docs (scrape initial data)\\\", lambda: scraper.fetch_docs(urls)),\\n            (\\\"Export docs (to Markdown for editing)\\\", lambda: json_to_md_table(\\n                doc_json, doc_md, field_order=DEFAULT_DOC_FIELD_ORDER, filter_chain=filters\\n            )),\\n            (\\\"Import updated doc data\\\", lambda: md_table_to_json(doc_md, doc_json, field_order=DEFAULT_DOC_FIELD_ORDER)),\\n            (\\\"Fetch files (prepare files for download)\\\", scraper.fetch_files),\\n            (\\\"Export files (to Markdown for editing)\\\", lambda: json_to_md_table(\\n                file_json, file_md, field_order=DEFAULT_FILE_FIELD_ORDER, filter_chain=filters\\n            )),\\n            (\\\"Import updated file data\\\", lambda: md_table_to_json(file_md, file_json, field_order=DEFAULT_FILE_FIELD_ORDER)),\\n            (\\\"Download files\\\", scraper.download_files),\\n        ]\\n        print(\\\"\\\\nChoose procedure(s) to run (space/comma-separated numbers, or q to quit):\\\")\\n        for i, (desc, _) in enumerate(steps):\\n            print(f\\\"  [{i}] {desc}\\\")\\n        choice = input(\\\"Your choice: \\\").strip()\\n        if choice.lower() == 'q':\\n            print(\\\"Exiting.\\\")\\n            break\\n        try:\\n            indices = [int(x.strip()) for x in choice.replace(',', ' ').split() if x.strip().isdigit()]\\n        except (ValueError, IndexError):\\n            print(\\\"Invalid selection. Please try again.\\\")\\n            continue\\n        for i in indices:\\n            if i == 0:\\n                rig_input = input(f\\\"  rig_number ({rig}): \\\").strip()\\n                if rig_input:\\n                    rig = rig_input\\n                urls_input = input(f\\\"  search_urls ({urls}): \\\").strip()\\n                if urls_input:\\n                    urls = [u.strip() for u in urls_input.split(',') if u.strip()]\\n                scraper = RigDocScraper(rig, show_progress=sp)\\n            elif 0 < i < len(steps):\\n                desc, action = steps[i]\\n                print_inf(f\\\"\\\\nRunning: {desc}\\\")\\n                action()\\n        print_inf(\\\"\\\\nSelected steps completed.\\\\n\\\")\\nif __name__==\\\"__main__\\\":\\n    run_script(CONFIG)\",\n  \"instruction_format\": \"Create a structured README.md file (less than 70 lines) documenting the RigDocScraper project. Include the following sections in order:\\n\\n1. Project Title and Rig Number (use PROJECT_RIG_ID=\\\"R5225\\\").\\n2. Brief Project Description and Main Use Case.\\n3. Prerequisites: List all required Python packages and setup steps.\\n4. Directory Structure: Outline BASE_OUTPUT, DATA_DIR, DL_DIR, and where outputs are stored.\\n5. Configuration: Describe how to set or change PROJECTINFO_GAD, FILTERS_SIMULATOR_PATTERNS_INCLUDE, FILTERS_SIMULATOR_PATTERNS_SKIP, and CONFIG parameters.\\n6. Pipeline Overview: Stepwise summary of the process:\\n    - 1. Adjust search parameters (rig number, URLs).\\n    - 2. Configure filter chain (with editing options).\\n    - 3. Fetch docs (web scrape document data).\\n    - 4. Export docs to Markdown for review/editing.\\n    - 5. Import docs from Markdown to JSON.\\n    - 6. Fetch candidate files linked to included docs.\\n    - 7. Export file list to Markdown for review/editing.\\n    - 8. Import updated file list from Markdown.\\n    - 9. Download marked files (PDFs only).\\n7. Filter System: Explain how inclusion/exclusion logic and pattern matching works for both docs and files; reference example patterns.\\n8. Manual Editing: Specify that Markdown tables in outputs/data can be directly edited to select docs/files for download.\\n9. Running the Tool: List the command to execute the script, how to select pipeline steps, and notes about interactive prompts.\\n10. Troubleshooting: Add notes on browser requirements, typical errors, and where to look in the output.\\n11. References: Link to relevant documentation or `rigdoc.nov.com` as appropriate.\\n\\nEnsure usage of command/instructional voice, include code block examples for configuration and usage commands, and maintain concise, technical phrasing throughout. Avoid first-person language. Preserve project-specific terminology, filter logic, and maintain the order of steps as in the input.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": {\n    \"README.md\": [\n      \"# RigDocScraper – Rig R5225\",\n      \"\",\n      \"## Overview\",\n      \"Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) for NOV Rig R5225 from rigdoc.nov.com. Use case: curate, review, and batch-download rig-related documents and technical files.\",\n      \"\",\n      \"## Prerequisites\",\n      \"- Python 3.8+\",\n      \"- Required packages:\",\n      \"  - selenium, beautifulsoup4, colorama, dateutil, ansimarkup, webdriver-manager\",\n      \"- Chrome browser installed\",\n      \"- ChromeDriver (managed via webdriver-manager)\",\n      \"```bash\",\n      \"pip install selenium beautifulsoup4 colorama python-dateutil ansimarkup webdriver-manager\",\n      \"```\",\n      \"\",\n      \"## Directory Structure\",\n      \"- `outputs/` (BASE_OUTPUT): All results stored here\",\n      \"- `outputs/data/` (DATA_DIR): Document and file metadata (JSON/MD)\",\n      \"- `outputs/downloads/` (DL_DIR): Downloaded PDF and file outputs\",\n      \"\",\n      \"## Configuration\",\n      \"- Edit parameters in `CONFIG` at script start:\",\n      \"  - `rig_number`: e.g., \\\"R5225.020\\\"\",\n      \"  - `search_urls`: list of document search URLs (`PROJECTINFO_GAD`)\",\n      \"  - `filters`: filter chain to include/exclude docs/files (see below)\",\n      \"- Example patterns:\",\n      \"  - `FILTERS_SIMULATOR_PATTERNS_INCLUDE`: doc types to include (e.g. \\\"*DRILL*FLOOR*\\\")\",\n      \"  - `FILTERS_SIMULATOR_PATTERNS_SKIP`: exclusion patterns\",\n      \"```python\",\n      \"PROJECTINFO_GAD = [ ... ]  # List of URLs\",\n      \"CONFIG = {\",\n      \"    'rig_number': 'R5225.020',\",\n      \"    'search_urls': PROJECTINFO_GAD,\",\n      \"    'filters': [ ... ]\",\n      \"}\",\n      \"```\",\n      \"\",\n      \"## Pipeline Overview\",\n      \"1. Change search parameters (rig number, URLs)\",\n      \"2. Configure filter chain (add, edit, delete, toggle, reorder filters)\",\n      \"3. Fetch docs (scrape data from rigdoc.nov.com)\",\n      \"4. Export docs to Markdown (for selection/editing)\",\n      \"5. Import docs from Markdown (sync edited selection)\",\n      \"6. Fetch candidate files linked to selected docs\",\n      \"7. Export file list to Markdown (for editing/selecting files for download)\",\n      \"8. Import updated file list from Markdown\",\n      \"9. Download marked files (PDFs only, via Chrome)\",\n      \"\",\n      \"## Filter System\",\n      \"- Filters use `pattern` matching (wildcards) on specific fields (e.g., `item_generated_name`)\",\n      \"- Types: `docs` (for document records), `files` (for downloadable files)\",\n      \"- Inclusion example (set True):\",\n      \"  - `pattern`: [\\\"*EQUIPMENT*LAYOUT*\\\"]\",\n      \"- Exclusion example (set False):\",\n      \"  - `pattern`: [\\\"*VOID*\\\"], field: `item_case_description`\",\n      \"\",\n      \"## Manual Editing\",\n      \"- Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing\",\n      \"- Set `item_include` (docs) and `item_download` (files) fields\",\n      \"\",\n      \"## Running the Tool\",\n      \"```bash\",\n      \"python rigdocscraper.py\",\n      \"```\",\n      \"- Interactive menu enables step selection (numbers/comma/space-separated)\",\n      \"- Supports adjusting parameters, filter configuration, and reviewing batch steps\",\n      \"- Prompts will guide through editing, import/export, and download procedures\",\n      \"\",\n      \"## Troubleshooting\",\n      \"- Requires functioning Chrome installation; verify webdriver-manager compatibility\",\n      \"- Common issues: browser launch failures, login/captcha requirements, file permissions\",\n      \"- Output logs and warnings shown in terminal; inspect `outputs/data/` for progress\",\n      \"\",\n      \"## References\",\n      \"- Main resource: https://rigdoc.nov.com\",\n      \"- For documentation and filter patterns, see comments in `rigdocscraper.py`\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Result Combiner\n\nYou are a specialized aggregator that combines results from multiple instruction steps into a cohesive output. Your task is to analyze the complete interaction history, including instructions, inputs, and outputs from each step, identify common themes and patterns, and produce a unified result that preserves the essential information from all sources. Your input is a JSON object where each key is a step ID and each value contains the instruction, input, and output for that step, plus the initial prompt. Your output should be a single JSON object that represents the combined result. Execute as:\n\n`{role=result_combiner;input=[interaction_history:json];process=[analyze_complete_interaction_history(),identify_common_patterns(),extract_key_elements(),merge_into_cohesive_whole(),format_as_unified_output()];constraints=[preserve_essential_information(),maintain_logical_structure(),avoid_redundancy()];requirements=[output_as_single_json(),ensure_completeness(),prioritize_clarity()];output={combined_result:json}}`", "step": "a", "title": "Result Combiner", "input": "{\n  \"initial_prompt\": \"# GOAL: CREATE A STRUCTURED README.MD <70 LINES\\n\\n PROJECT_RIG_ID = \\\"R5225\\\"\\nPROJECTINFO_GAD = [\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocNumber=*G00*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocNumber=*GAD-00*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DRILL*FLOOR*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DERRICK*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*EQUIPMENT*LAYOUT*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ELEVATION*VIEW*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TOP*DRIVE*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TRAVELLINGBLOCK*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ROUGHNECK*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*HYDRATONG*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MUDBUCKET*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*HYDRARACKER*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*BELLY*BOARD*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*FINGER*BOARD*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RACKING*BOARD*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CATWALK*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MOUSE*HOLE*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*GUIDE*ARM*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*EBT*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ELEVATED*BACKUP*TONG*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CATHEAD*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SPIDER*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ROTARY*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SLIPS*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*AHC*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CMC*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*STANDBUILDING*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TUBULAR*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RTX*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*PIPE*GUIDE*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*LIFTING*CYLINDER*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*STAR*RACKER*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*UTILITY*HANDLING*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RACKING*SYSTEM*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RAISED*SYSTEM*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DFMA*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MPMA*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MANUPILATOR*ARM*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*PIPE*CHUTE*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RISER*CHUTE*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SERVICE*BASKET*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MAINTENANCE*BASKET*\\\",\\n]\\nFILTERS_SIMULATOR_PATTERNS_INCLUDE = [\\n    \\\"*DRILL*FLOOR*\\\",\\n    \\\"*ELEVATION*VIEW*\\\",\\n    \\\"*EQUIPMENT*LAYOUT*\\\",\\n    \\\"*G000*\\\",\\n    \\\"*GAD-00*\\\",\\n    \\\"*A000*\\\",\\n    \\\"*ASM-00*\\\",\\n]\\nFILTERS_SIMULATOR_PATTERNS_SKIP = [\\n    \\\"*Agitator*\\\",\\n    \\\"*BOP *\\\",\\n    \\\"*Bulk*Systems*\\\",\\n    \\\"*Cabinet*\\\",\\n    \\\"*Centrifuge*\\\",\\n    \\\"*Compressor*\\\",\\n    \\\"*Crane*\\\",\\n    \\\"*Cuttings*\\\",\\n    \\\"*Cyberbase*\\\",\\n    \\\"*DCDA *\\\",\\n    \\\"*DCI*\\\",\\n    \\\"*Deadline*\\\",\\n    \\\"*Gantry*Crane*\\\",\\n    \\\"*HPU*\\\",\\n    \\\"*HVAC*\\\",\\n    \\\"*Knuckle*Crane*\\\",\\n    \\\"*LMRP*\\\",\\n    \\\"*Manrider*\\\",\\n    \\\"*Mixer*\\\",\\n    \\\"*Mixing*\\\",\\n    \\\"*Mud *\\\",\\n    \\\"*Pumps*\\\",\\n    \\\"*Sdi*\\\",\\n    \\\"*Standby*\\\",\\n    \\\"*Station*\\\",\\n    \\\"*Tank *\\\",\\n    \\\"*Valve*\\\",\\n    \\\"*VOID*\\\",\\n    \\\"*Winch*\\\",\\n    \\\"*X-tree*\\\",\\n    \\\"*Yoke*\\\",\\n]\\nCONFIG = {\\n    \\\"rig_number\\\": f\\\"{PROJECT_RIG_ID}.020\\\",\\n    \\\"search_urls\\\": PROJECTINFO_GAD,\\n    \\\"show_progress\\\": True,\\n    \\\"filters\\\": [\\n        {\\n            \\\"type\\\": \\\"docs\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": FILTERS_SIMULATOR_PATTERNS_INCLUDE,\\n            \\\"match_field\\\": \\\"item_generated_name\\\",\\n            \\\"field\\\": \\\"item_include\\\",\\n            \\\"value\\\": True,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"docs\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": FILTERS_SIMULATOR_PATTERNS_SKIP,\\n            \\\"match_field\\\": \\\"item_generated_name\\\",\\n            \\\"field\\\": \\\"item_include\\\",\\n            \\\"value\\\": False,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"docs\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": FILTERS_SIMULATOR_PATTERNS_SKIP,\\n            \\\"match_field\\\": \\\"item_case_description\\\",\\n            \\\"field\\\": \\\"item_include\\\",\\n            \\\"value\\\": False,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"docs\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": [\\\"(VOID)\\\"],\\n            \\\"match_field\\\": \\\"item_case_description\\\",\\n            \\\"field\\\": \\\"item_include\\\",\\n            \\\"value\\\": False,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"files\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": [\\\"*.pdf\\\"],\\n            \\\"match_field\\\": \\\"item_file_ext\\\",\\n            \\\"field\\\": \\\"item_download\\\",\\n            \\\"value\\\": True,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"files\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": FILTERS_SIMULATOR_PATTERNS_SKIP,\\n            \\\"match_field\\\": \\\"item_case_description\\\",\\n            \\\"field\\\": \\\"item_download\\\",\\n            \\\"value\\\": False,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"files\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": [\\\"*Partslist*\\\"],\\n            \\\"match_field\\\": \\\"item_generated_name\\\",\\n            \\\"field\\\": \\\"item_download\\\",\\n            \\\"value\\\": False,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n    ],\\n}\\nimport json\\nimport os\\nimport re\\nimport shutil\\nimport time\\nimport timeit\\nfrom contextlib import contextmanager\\nfrom urllib.parse import urljoin\\nimport colorama\\nimport dateutil.parser\\nfrom ansimarkup import ansiprint\\nfrom bs4 import BeautifulSoup\\nfrom selenium import webdriver\\nfrom selenium.webdriver.chrome.options import Options\\nfrom selenium.webdriver.chrome.service import Service\\nfrom selenium.webdriver.support.ui import WebDriverWait\\nfrom selenium.webdriver.common.by import By\\nfrom selenium.webdriver.support import expected_conditions as EC\\nfrom selenium.common.exceptions import TimeoutException\\nfrom webdriver_manager.chrome import ChromeDriverManager\\ncolorama.init()\\nBASE_OUTPUT = os.path.abspath(os.path.join(os.getcwd(), \\\"outputs\\\"))\\nDATA_DIR    = os.path.join(BASE_OUTPUT, \\\"data\\\")\\nDL_DIR      = os.path.join(BASE_OUTPUT, \\\"downloads\\\")\\nfor d in (BASE_OUTPUT, DATA_DIR, DL_DIR):\\n    os.makedirs(d, exist_ok=True)\\nDEFAULT_DOC_FIELD_ORDER = [\\n    \\\"item_include\\\",\\n    \\\"item_generated_name\\\",\\n    \\\"item_type\\\",\\n    \\\"item_case_no\\\",\\n    \\\"item_drawing_type\\\",\\n    \\\"item_doc_no\\\",\\n    \\\"item_date\\\",\\n    \\\"item_title\\\",\\n    \\\"item_case_description\\\",\\n    \\\"item_status\\\",\\n    \\\"item_revision\\\",\\n    \\\"item_responsible\\\",\\n    \\\"item_url\\\",\\n]\\nDEFAULT_FILE_FIELD_ORDER = [\\n    \\\"item_type\\\",\\n    \\\"item_generated_name\\\",\\n    \\\"item_case_no\\\",\\n    \\\"item_drawing_type\\\",\\n    \\\"item_title\\\",\\n    \\\"item_doc_no\\\",\\n    \\\"item_file_ext\\\",\\n    \\\"item_download\\\",\\n    \\\"item_file_size\\\",\\n    \\\"item_file_id\\\",\\n    \\\"item_date\\\",\\n    \\\"item_revision\\\",\\n    \\\"item_case_description\\\",\\n    \\\"item_status\\\",\\n    \\\"item_responsible\\\",\\n    \\\"item_url\\\",\\n]\\ndef cprint(x, color='#FFFFFF'):\\n    ansiprint(f'<fg {color}>{x}')\\nprint_err  = lambda x: cprint(x, '#C92B65')\\nprint_warn = lambda x: cprint(x, '#E6992B')\\nprint_ok   = lambda x: cprint(x, '#1FCE46')\\nprint_inf  = lambda x: cprint(x, '#FFFFFF')\\nprint_sub  = lambda x: cprint(x, '#74705D')\\nclass SleepCondition:\\n    def __init__(self, s):\\n        self.s = s\\n    def __call__(self, _):\\n        time.sleep(self.s)\\n        return True\\ndef reformat_date(s):\\n    try:\\n        dt = dateutil.parser.parse(s)\\n        return dt.strftime('%Y.%m.%d')\\n    except:\\n        return s\\ndef match_pattern(text, pattern):\\n    if isinstance(pattern, list):\\n        return any(match_pattern(text, p) for p in pattern)\\n    regex_pattern = pattern.replace(\\\".\\\", \\\"\\\\\\\\.\\\").replace(\\\"*\\\", \\\".*\\\").replace(\\\"?\\\", \\\".\\\")\\n    return bool(re.search(f\\\"^{regex_pattern}$\\\", text, re.IGNORECASE))\\ndef sanitize_filename(s):\\n    replacements = {\\n        \\\"'\\\": \\\"\\\", '\\\"': \\\"\\\", '`': \\\"\\\",\\n        '\\\\\\\\': \\\"_\\\", '|': \\\"_\\\", '?': \\\"_\\\", '*': \\\"_\\\", '<': \\\"_\\\", '>': \\\"_\\\", ':': \\\"_\\\"\\n    }\\n    result = s\\n    for char, replacement in replacements.items():\\n        result = result.replace(char, replacement)\\n    result = ''.join(c for c in result if c.isprintable() or c == '/')\\n    result = re.sub(r'\\\\s+', ' ', result)\\n    return result.strip()\\ndef reorder_dict_keys(data_dict, field_order):\\n    reordered = {}\\n    for key in field_order:\\n        if key in data_dict:\\n            reordered[key] = data_dict[key]\\n    return reordered\\ndef remove_duplicates(data, exclude_keys=None, priority_key='item_download', priority_vals=None):\\n    if not exclude_keys:\\n        exclude_keys=[]\\n    if not priority_vals:\\n        priority_vals=[]\\n    exset = set(exclude_keys)\\n    pmap  = {v:i for i,v in enumerate(priority_vals)}\\n    out   = {}\\n    for d in data:\\n        filt = {k:v for k,v in d.items() if k not in exset}\\n        hkey = frozenset(filt.items())\\n        if hkey not in out:\\n            out[hkey] = d\\n        else:\\n            if priority_key and priority_vals:\\n                newv = d.get(priority_key)\\n                oldv = out[hkey].get(priority_key)\\n                if (newv in pmap) and (oldv in pmap) and pmap[newv] < pmap[oldv]:\\n                    out[hkey] = d\\n                elif newv in pmap and oldv not in pmap:\\n                    out[hkey] = d\\n    return list(out.values())\\ndef dedupe_and_save(new_items, path, field_order=None):\\n    if os.path.exists(path):\\n        try:\\n            with open(path,'r',encoding='utf-8') as f:\\n                old = json.load(f)\\n        except:\\n            old = []\\n    else:\\n        old = []\\n    merged = old + new_items\\n    merged = remove_duplicates(\\n        merged,\\n        exclude_keys=['item_download','item_generated_path'],\\n        priority_key='item_download',\\n        priority_vals=[False,True]\\n    )\\n    if field_order:\\n        merged = [reorder_dict_keys(d, field_order) for d in merged]\\n    tmp = path + '.temp'\\n    with open(tmp,'w',encoding='utf-8') as f:\\n        json.dump(merged,f,indent=2)\\n    os.replace(tmp,path)\\n    rel_path = os.path.relpath(path, os.getcwd())\\n    print_inf(f\\\"Saved {len(merged)} items => {rel_path}\\\")\\ndef append_items(new_items, path, field_order=None):\\n    if new_items:\\n        dedupe_and_save(new_items, path, field_order=field_order)\\ndef json_to_md_table(json_path, md_path, field_order=None, filter_chain=None):\\n    if not os.path.exists(json_path):\\n        print_err(f\\\"No JSON found: {json_path}\\\")\\n        return\\n    with open(json_path,'r',encoding='utf-8') as f:\\n        data = json.load(f)\\n    if not data:\\n        print_warn(f\\\"No data in {json_path}\\\")\\n        return\\n    item_type = \\\"docs\\\" if \\\"-a-docs.\\\" in json_path else \\\"files\\\"\\n    if filter_chain and isinstance(filter_chain, list):\\n        for filter_idx, filter_config in enumerate(filter_chain):\\n            if not filter_config.get(\\\"enabled\\\", False) or filter_config.get(\\\"type\\\") != item_type:\\n                continue\\n            pattern = filter_config.get(\\\"pattern\\\", \\\"\\\")\\n            field = filter_config.get(\\\"field\\\", \\\"\\\")\\n            value = filter_config.get(\\\"value\\\", True)\\n            comment = filter_config.get(\\\"comment\\\", \\\"\\\")\\n            match_field = filter_config.get(\\\"match_field\\\", \\\"item_generated_name\\\")\\n            if pattern and field:\\n                filtered_count = 0\\n                for d in data:\\n                    field_value = d.get(match_field, \\\"\\\")\\n                    if field_value and match_pattern(field_value, pattern):\\n                        if d.get(field) != value:\\n                            d[field] = value\\n                            filtered_count += 1\\n                if filtered_count > 0:\\n                    action = \\\"Set\\\" if value else \\\"Cleared\\\"\\n                    if isinstance(pattern, list):\\n                        pattern_str = f\\\"[{', '.join(pattern)}]\\\"\\n                    else:\\n                        pattern_str = f\\\"'{pattern}'\\\"\\n                    match_field_str = f\\\" in {match_field}\\\" if match_field != \\\"item_generated_name\\\" else \\\"\\\"\\n                    print_ok(f\\\"Filter #{filter_idx+1}: {action} {field}={value} for {filtered_count} items matching {pattern_str}{match_field_str}\\\")\\n                    if comment:\\n                        print_sub(f\\\"  \\u2192 {comment}\\\")\\n    if not field_order:\\n        colset = set()\\n        for d in data:\\n            colset.update(d.keys())\\n        field_order = sorted(colset)\\n    lines = []\\n    lines.append(\\\"| \\\" + \\\" | \\\".join(field_order) + \\\" |\\\")\\n    lines.append(\\\"|\\\" + \\\"|\\\".join([\\\"---\\\"]*len(field_order)) + \\\"|\\\")\\n    for d in data:\\n        row = []\\n        for c in field_order:\\n            val = d.get(c,\\\"\\\")\\n            if isinstance(val,bool):\\n                val = str(val).lower()\\n            val = str(val).replace(\\\"|\\\",\\\"\\\\\\\\|\\\")\\n            row.append(val)\\n        lines.append(\\\"| \\\" + \\\" | \\\".join(row) + \\\" |\\\")\\n    with open(md_path,'w',encoding='utf-8') as f:\\n        f.write(\\\"\\\\n\\\".join(lines))\\n    rel_md_path = os.path.relpath(md_path, os.getcwd())\\n    print_ok(f\\\"Exported {len(data)} items => {rel_md_path}\\\")\\ndef md_table_to_json(md_path, json_path, field_order=None):\\n    if not os.path.exists(md_path):\\n        print_err(f\\\"No Markdown file found: {md_path}\\\")\\n        return\\n    with open(md_path,'r',encoding='utf-8') as f:\\n        lines=f.read().splitlines()\\n    table_lines=[ln.strip() for ln in lines if ln.strip().startswith(\\\"|\\\")]\\n    if len(table_lines)<2:\\n        print_err(\\\"No valid table rows found in the Markdown.\\\")\\n        return\\n    header=table_lines[0]\\n    data_lines=table_lines[2:]\\n    cols=[x.strip() for x in header.strip('|').split('|')]\\n    new_data=[]\\n    for ln in data_lines:\\n        rowcols=[x.strip() for x in ln.strip('|').split('|')]\\n        if len(rowcols)!=len(cols):\\n            print_warn(f\\\"Skipping mismatch line:\\\\n{ln}\\\")\\n            continue\\n        d={}\\n        for i,c in enumerate(cols):\\n            val=rowcols[i]\\n            if val.lower()==\\\"true\\\":\\n                val=True\\n            elif val.lower()==\\\"false\\\":\\n                val=False\\n            d[c]=val\\n        new_data.append(d)\\n    if field_order:\\n        new_data = [reorder_dict_keys(item, field_order) for item in new_data]\\n    with open(json_path,'w',encoding='utf-8') as f:\\n        json.dump(new_data,f,indent=2)\\n    rel_json_path = os.path.relpath(json_path, os.getcwd())\\n    print_ok(f\\\"Imported {len(new_data)} items => {rel_json_path}\\\")\\nclass RigDocScraper:\\n    def __init__(self, rig, show_progress=False):\\n        self.rig=rig\\n        self.show=show_progress\\n        self.driver=None\\n        self.docs_path = os.path.join(DATA_DIR, f'{rig}-a-docs.json')\\n        self.files_path= os.path.join(DATA_DIR, f'{rig}-b-files.json')\\n    @contextmanager\\n    def browser_session(self, download_mode=False):\\n        profdir = None\\n        try:\\n            opts = Options()\\n            opts.add_argument(\\\"--log-level=3\\\")\\n            if download_mode:\\n                appdata = os.environ.get(\\\"APPDATA\\\", os.getcwd())\\n                profdir = os.path.join(appdata, \\\"chromedriver_profile\\\")\\n                os.makedirs(profdir, exist_ok=True)\\n                opts.add_argument(f\\\"--user-data-dir={profdir}\\\")\\n                dl_dir = os.path.join(DL_DIR, self.rig)\\n                os.makedirs(dl_dir, exist_ok=True)\\n                prefs = {\\n                    \\\"download.default_directory\\\": dl_dir,\\n                    \\\"download.prompt_for_download\\\": False,\\n                    \\\"download.directory_upgrade\\\": True,\\n                    \\\"safebrowsing.enabled\\\": True\\n                }\\n                opts.add_experimental_option(\\\"prefs\\\", prefs)\\n            svc = Service(ChromeDriverManager().install())\\n            self.driver = webdriver.Chrome(service=svc, options=opts)\\n            wait_time = 2 if download_mode else 5\\n            WebDriverWait(self.driver, wait_time).until(SleepCondition(wait_time))\\n            self.driver.implicitly_wait(5 if download_mode else 10)\\n            if self.show:\\n                print_sub(f\\\"[Browser] Started {'download-enabled ' if download_mode else ''}session\\\")\\n            yield self.driver\\n        finally:\\n            if self.driver:\\n                if self.show:\\n                    print_sub(\\\"[Browser] Closing session\\\")\\n                self.driver.quit()\\n                self.driver = None\\n                if download_mode and profdir:\\n                    try:\\n                        shutil.rmtree(profdir)\\n                    except Exception as e:\\n                        if self.show:\\n                            print_warn(f\\\"[Browser] Failed to clean up profile: {e}\\\")\\n    def fetch_docs(self, urls):\\n        if not os.path.exists(self.docs_path):\\n            with open(self.docs_path,'w',encoding='utf-8') as f:\\n                json.dump([],f)\\n        with self.browser_session() as driver:\\n            for url in urls:\\n                if self.show:\\n                    print_sub(f\\\"[DocScrape] => {url}\\\")\\n                driver.get(url)\\n                is_rig = (\\\"rigsearch\\\" in url.lower()) or (\\\"advancedsearch\\\" in url.lower())\\n                max_wait = 10 if is_rig else 5\\n                wait_scroll = 5 if is_rig else 2\\n                try:\\n                    WebDriverWait(driver, max_wait).until(\\n                        EC.presence_of_element_located((By.CSS_SELECTOR, \\\"a.search-result-link\\\"))\\n                    )\\n                except TimeoutException:\\n                    print_warn(f\\\"Waited up to {max_wait}s, but no 'search-result-link' found. Proceeding anyway...\\\")\\n                inc = 0\\n                prev = driver.execute_script(\\\"return document.body.scrollHeight\\\")\\n                while inc < 150:\\n                    inc += 1\\n                    driver.execute_script(\\\"window.scrollTo(0, document.body.scrollHeight);\\\")\\n                    time.sleep(wait_scroll)\\n                    newh = driver.execute_script(\\\"return document.body.scrollHeight\\\")\\n                    if self.show:\\n                        print_sub(f\\\" -> Scroll {inc}, h={newh}\\\")\\n                    if newh == prev:\\n                        break\\n                    prev = newh\\n                soup = BeautifulSoup(driver.page_source, \\\"html.parser\\\")\\n                row_elems = soup.find_all(\\\"a\\\", class_=\\\"search-result-link\\\")\\n                rows = [(td.find_parent(\\\"tr\\\")).find_all(\\\"td\\\") for td in row_elems]\\n                if self.show:\\n                    print_inf(f\\\"Found {len(rows)} doc rows.\\\")\\n                new_docs = []\\n                for row_cells in rows:\\n                    doc_title   = re.sub(r'[\\\\n\\\\r]+',' ', row_cells[1].a.text.strip())\\n                    sub_type    = row_cells[2].get_text(strip=True)\\n                    doc_no      = row_cells[3].get_text(strip=True)\\n                    responsible = row_cells[4].get_text(strip=True)\\n                    case_no     = row_cells[5].get_text(strip=True)\\n                    case_desc   = row_cells[6].get_text(strip=True)\\n                    revision    = row_cells[7].get_text(strip=True)\\n                    status      = row_cells[8].get_text(strip=True)\\n                    dt_         = reformat_date(row_cells[9].get_text(strip=True))\\n                    doc_url     = urljoin(\\\"https://rigdoc.nov.com/\\\", row_cells[1].a.get(\\\"href\\\"))\\n                    if \\\"document-void\\\" in (row_cells[1].a.get(\\\"class\\\", [])) or \\\"*VOID*\\\" in doc_title.upper():\\n                        status = \\\"(VOID)\\\"\\n                    doc_generated_name = (\\n                        f\\\"{doc_no.upper()}_REV{revision.upper()}--{case_no.upper()}-{doc_title.title()}\\\"\\n                    ).replace(\\\"  \\\", \\\" \\\")\\n                    doc_generated_name_sanitized = sanitize_filename(doc_generated_name)\\n                    new_docs.append({\\n                        \\\"item_type\\\": \\\"doc\\\",\\n                        \\\"item_title\\\": doc_title,\\n                        \\\"item_drawing_type\\\": sub_type,\\n                        \\\"item_doc_no\\\": doc_no,\\n                        \\\"item_responsible\\\": responsible,\\n                        \\\"item_case_no\\\": case_no,\\n                        \\\"item_case_description\\\": case_desc,\\n                        \\\"item_revision\\\": revision,\\n                        \\\"item_status\\\": status,\\n                        \\\"item_date\\\": dt_,\\n                        \\\"item_url\\\": doc_url,\\n                        \\\"item_include\\\": False,\\n                        \\\"item_generated_name\\\": doc_generated_name_sanitized\\n                    })\\n                append_items(new_docs, self.docs_path, field_order=DEFAULT_DOC_FIELD_ORDER)\\n        rel_docs_path = os.path.relpath(self.docs_path, os.getcwd())\\n        print_ok(f\\\"Docs => {rel_docs_path}\\\")\\n    def fetch_files(self):\\n        if not os.path.exists(self.docs_path):\\n            print_err(f\\\"No docs found: {self.docs_path}\\\")\\n            return\\n        with open(self.docs_path,'r',encoding='utf-8') as f:\\n            all_docs=json.load(f)\\n        docs_for_files=[d for d in all_docs if d.get(\\\"item_type\\\")==\\\"doc\\\" and d.get(\\\"item_include\\\")==True]\\n        if not docs_for_files:\\n            print_warn(\\\"No docs with item_include==True => No files to fetch.\\\")\\n            return\\n        if not os.path.exists(self.files_path):\\n            with open(self.files_path,'w',encoding='utf-8') as f:\\n                json.dump([],f)\\n        with self.browser_session() as driver:\\n            for i,doc in enumerate(docs_for_files,1):\\n                if self.show:\\n                    print_sub(f\\\"[FileScrape] doc {i}/{len(docs_for_files)} => {doc.get('item_doc_no')}\\\")\\n                doc_url = doc.get(\\\"item_url\\\",\\\"\\\")\\n                if not doc_url:\\n                    continue\\n                wp_url = doc_url.replace(\\\"nov.com/documents/\\\",\\\"nov.com/wp/documents/\\\")\\n                if \\\"nov.com/wp/documents/\\\" not in wp_url:\\n                    continue\\n                driver.get(wp_url)\\n                WebDriverWait(driver,5).until(SleepCondition(5))\\n                soup=BeautifulSoup(driver.page_source,\\\"html.parser\\\")\\n                frows=soup.select(\\\"div.revision-panel-body div.file-detail-row\\\")\\n                flinks=[]\\n                for row in frows:\\n                    lk=row.select_one(\\\"div.file-detail a\\\")\\n                    if lk and lk.get(\\\"href\\\"):\\n                        flinks.append(urljoin(\\\"https://rigdoc.nov.com/\\\", lk[\\\"href\\\"]))\\n                doc_generated_name = doc.get(\\\"item_generated_name\\\",\\\"DOC_UNKNOWN\\\")\\n                new_files=[]\\n                for idx,flink in enumerate(flinks,1):\\n                    fapi=flink.replace(\\\"/_download\\\",\\\"\\\")\\n                    driver.get(fapi)\\n                    WebDriverWait(driver,0.5).until(SleepCondition(0.5))\\n                    raw=BeautifulSoup(driver.page_source,\\\"html.parser\\\").get_text(strip=True)\\n                    info=json.loads(raw)\\n                    fext=info.get(\\\"extension\\\",\\\"\\\")\\n                    fsz =info.get(\\\"filesize\\\",0)\\n                    fsmb=f\\\"{(fsz/(1024*1024)):.2f} MB\\\"\\n                    ftitle=info.get(\\\"title\\\",\\\"\\\").strip()\\n                    file_generated_name = (f\\\"{doc_generated_name}.id.{idx}-{len(flinks)}.{ftitle.title()}\\\").replace(\\\"  \\\",\\\" \\\")\\n                    if fext and not file_generated_name.lower().endswith(f\\\".{fext.lower()}\\\"):\\n                        file_generated_name = f\\\"{file_generated_name}{fext.lower()}\\\"\\n                    file_generated_name_sanitized = sanitize_filename(file_generated_name)\\n                    new_files.append({\\n                        \\\"item_type\\\": \\\"file\\\",\\n                        \\\"item_title\\\": ftitle,\\n                        \\\"item_file_id\\\": f\\\"{len(flinks)}->({idx}/{len(flinks)}) FID:{info.get('fileId','-')}\\\",\\n                        \\\"item_file_ext\\\": f\\\"{fext.lower()}\\\" if fext else \\\"\\\",\\n                        \\\"item_file_size\\\": fsmb,\\n                        \\\"item_date\\\": reformat_date(info.get(\\\"insertedDate\\\",\\\"\\\")),\\n                        \\\"item_revision\\\": doc.get(\\\"item_revision\\\",\\\"\\\"),\\n                        \\\"item_responsible\\\": info.get(\\\"insertedBy\\\",\\\"\\\"),\\n                        \\\"item_status\\\": info.get(\\\"lastChangedDate\\\") or \\\"-\\\",\\n                        \\\"item_case_no\\\": doc.get(\\\"item_case_no\\\",\\\"\\\"),\\n                        \\\"item_doc_no\\\": doc.get(\\\"item_doc_no\\\",\\\"\\\"),\\n                        \\\"item_drawing_type\\\": doc.get(\\\"item_drawing_type\\\",\\\"\\\"),\\n                        \\\"item_case_description\\\": doc.get(\\\"item_case_description\\\",\\\"\\\"),\\n                        \\\"item_url\\\": flink,\\n                        \\\"item_download\\\": False,\\n                        \\\"item_generated_name\\\": file_generated_name_sanitized\\n                    })\\n                append_items(new_files, self.files_path, field_order=DEFAULT_FILE_FIELD_ORDER)\\n        rel_files_path = os.path.relpath(self.files_path, os.getcwd())\\n        print_ok(f\\\"Files => {rel_files_path}\\\")\\n    def download_files(self):\\n        if not os.path.exists(self.files_path):\\n            print_err(f\\\"No files JSON found at {self.files_path}\\\")\\n            return\\n        with open(self.files_path, 'r', encoding='utf-8') as f:\\n            data = json.load(f)\\n        to_download = [x for x in data if x.get(\\\"item_type\\\") == \\\"file\\\" and x.get(\\\"item_download\\\") == True]\\n        if not to_download:\\n            print_warn(\\\"No files with item_download==True.\\\")\\n            return\\n        download_dir = os.path.join(DL_DIR, self.rig)\\n        print_inf(f\\\"Will download {len(to_download)} files => {download_dir}\\\")\\n        with self.browser_session(download_mode=True) as driver:\\n            for i, fitem in enumerate(to_download, 1):\\n                furl = fitem.get(\\\"item_url\\\", \\\"\\\")\\n                if not furl:\\n                    continue\\n                base_name = fitem.get(\\\"item_generated_name\\\", f\\\"file_{i}\\\")\\n                if not base_name:\\n                    base_name = f\\\"file_{i}\\\"\\n                base_name = sanitize_filename(base_name)\\n                parts = base_name.split(\\\"/\\\")\\n                if parts:\\n                    last_part = parts[-1]\\n                    ext = fitem.get(\\\"item_file_ext\\\", \\\"\\\").lower()\\n                    if ext and not last_part.lower().endswith(f\\\"{ext}\\\"):\\n                        ext = f\\\".{ext}\\\" if not ext.startswith(\\\".\\\") else ext\\n                        final_filename = last_part + ext\\n                    else:\\n                        final_filename = last_part\\n                    subfolders = parts[:-1]\\n                else:\\n                    ext = fitem.get(\\\"item_file_ext\\\", \\\"\\\")\\n                    ext = f\\\".{ext}\\\" if ext and not ext.startswith(\\\".\\\") else ext\\n                    final_filename = f\\\"file_{i}{ext}\\\"\\n                    subfolders = []\\n                final_subdir = os.path.join(download_dir, *subfolders)\\n                os.makedirs(final_subdir, exist_ok=True)\\n                final_destination = os.path.join(final_subdir, final_filename)\\n                if os.path.exists(final_destination):\\n                    print_ok(f'Skipped (already exists): \\\"{final_destination}\\\"')\\n                    continue\\n                if self.show:\\n                    print_sub(\\\"-\\\" * 80)\\n                    print_inf(f'[Download {i}/{len(to_download)}] => \\\"{final_filename}\\\"')\\n                start = timeit.default_timer()\\n                existing_files = set(f for f in os.listdir(download_dir) if os.path.isfile(os.path.join(download_dir, f)))\\n                driver.get(furl)\\n                time.sleep(1)\\n                done = False\\n                while (timeit.default_timer() - start) < 30:\\n                    time.sleep(1)\\n                    new_files = set(f for f in os.listdir(download_dir) if os.path.isfile(os.path.join(download_dir, f))) - existing_files\\n                    if new_files:\\n                        candidate = list(new_files)[0]\\n                        ext2 = os.path.splitext(candidate)[1].lower()\\n                        if ext2 not in [\\\".tmp\\\", \\\".crdownload\\\"]:\\n                            src = os.path.join(download_dir, candidate)\\n                            try:\\n                                os.rename(src, final_destination)\\n                                done = True\\n                                print_ok(f'-> Downloaded \\\"{final_destination}\\\"')\\n                                break\\n                            except Exception as e:\\n                                print_warn(f'Failed to rename: {e}')\\n                if not done:\\n                    print_warn(f'Timed out: \\\"{final_destination}\\\"')\\n        print_inf(\\\"All file downloads attempted.\\\")\\ndef run_script(cfg):\\n    rig = cfg.get(\\\"rig_number\\\", \\\"R9999\\\")\\n    urls = cfg.get(\\\"search_urls\\\", [])\\n    sp = cfg.get(\\\"show_progress\\\", False)\\n    filters = cfg.get(\\\"filters\\\", [])\\n    scraper = RigDocScraper(rig, show_progress=sp)\\n    while True:\\n        doc_json = os.path.join(DATA_DIR, f'{rig}-a-docs.json')\\n        doc_md = os.path.join(DATA_DIR, f'{rig}-a-docs.md')\\n        file_json = os.path.join(DATA_DIR, f'{rig}-b-files.json')\\n        file_md = os.path.join(DATA_DIR, f'{rig}-b-files.md')\\n        def configure_filters():\\n            while True:\\n                print_inf(\\\"\\\\nCurrent filter chain:\\\")\\n                if not filters:\\n                    print_sub(\\\"  No filters configured\\\")\\n                else:\\n                    for i, f in enumerate(filters):\\n                        status = \\\"ENABLED\\\" if f.get(\\\"enabled\\\", False) else \\\"DISABLED\\\"\\n                        type_str = f.get(\\\"type\\\", \\\"unknown\\\")\\n                        pattern = f.get(\\\"pattern\\\", \\\"\\\")\\n                        field = f.get(\\\"field\\\", \\\"\\\")\\n                        value = f.get(\\\"value\\\", True)\\n                        comment = f.get(\\\"comment\\\", \\\"\\\")\\n                        if isinstance(pattern, list):\\n                            pattern_str = f\\\"[{', '.join(pattern)}]\\\"\\n                        else:\\n                            pattern_str = f\\\"'{pattern}'\\\"\\n                        match_field = f.get(\\\"match_field\\\", \\\"item_generated_name\\\")\\n                        match_field_str = f\\\" in {match_field}\\\" if match_field != \\\"item_generated_name\\\" else \\\"\\\"\\n                        color_fn = print_ok if f.get(\\\"enabled\\\", False) else print_sub\\n                        color_fn(f\\\"  [{i+1}] {status} - {type_str}: {pattern_str}{match_field_str} \\u2192 {field}={value}\\\")\\n                        if comment:\\n                            print_sub(f\\\"      {comment}\\\")\\n                print_inf(\\\"\\\\nFilter options:\\\")\\n                print_inf(\\\"  [a] Add new filter\\\")\\n                print_inf(\\\"  [e] Edit filter\\\")\\n                print_inf(\\\"  [d] Delete filter\\\")\\n                print_inf(\\\"  [t] Toggle filter\\\")\\n                print_inf(\\\"  [m] Move filter\\\")\\n                print_inf(\\\"  [s] Save and return\\\")\\n                choice = input(\\\"\\\\nEnter option: \\\").strip().lower()\\n                if choice == 'a':\\n                    print_inf(\\\"\\\\nAdd new filter:\\\")\\n                    filter_type = input(\\\"  Type (docs/files): \\\").strip().lower()\\n                    if filter_type not in ['docs', 'files']:\\n                        print_err(\\\"  Invalid type. Must be 'docs' or 'files'\\\")\\n                        continue\\n                    pattern_input = input(\\\"  Pattern (e.g., *g0001*, *.pdf) or comma-separated list: \\\").strip()\\n                    if not pattern_input:\\n                        print_err(\\\"  Pattern cannot be empty\\\")\\n                        continue\\n                    if \\\",\\\" in pattern_input:\\n                        pattern = [p.strip() for p in pattern_input.split(\\\",\\\") if p.strip()]\\n                    else:\\n                        pattern = pattern_input\\n                    match_field = input(f\\\"  Field to match against [item_generated_name]: \\\").strip()\\n                    if not match_field:\\n                        match_field = 'item_generated_name'\\n                    field = input(f\\\"  Field to set ({'item_include' if filter_type == 'docs' else 'item_download'}): \\\").strip()\\n                    if not field:\\n                        field = 'item_include' if filter_type == 'docs' else 'item_download'\\n                    value_input = input(f\\\"  Value to set (true/false) [true]: \\\").strip().lower()\\n                    value = False if value_input in ['false', 'f', 'no', 'n', '0'] else True\\n                    comment = input(\\\"  Comment (optional): \\\").strip()\\n                    new_filter = {\\n                        \\\"type\\\": filter_type,\\n                        \\\"enabled\\\": True,\\n                        \\\"pattern\\\": pattern,\\n                        \\\"match_field\\\": match_field,\\n                        \\\"field\\\": field,\\n                        \\\"value\\\": value,\\n                        \\\"comment\\\": comment\\n                    }\\n                    filters.append(new_filter)\\n                    print_ok(\\\"  Filter added\\\")\\n                elif choice == 'e':\\n                    if not filters:\\n                        print_err(\\\"  No filters to edit\\\")\\n                        continue\\n                    idx_input = input(f\\\"  Filter number to edit (1-{len(filters)}): \\\").strip()\\n                    try:\\n                        idx = int(idx_input) - 1\\n                        if idx < 0 or idx >= len(filters):\\n                            raise ValueError()\\n                    except ValueError:\\n                        print_err(\\\"  Invalid filter number\\\")\\n                        continue\\n                    f = filters[idx]\\n                    print_inf(f\\\"\\\\nEditing filter #{idx+1}:\\\")\\n                    filter_type = input(f\\\"  Type (docs/files) [{f.get('type', '')}]: \\\").strip().lower()\\n                    if filter_type and filter_type in ['docs', 'files']:\\n                        f['type'] = filter_type\\n                    current_pattern = f.get('pattern', '')\\n                    if isinstance(current_pattern, list):\\n                        current_pattern_str = \\\", \\\".join(current_pattern)\\n                    else:\\n                        current_pattern_str = current_pattern\\n                    pattern_input = input(f\\\"  Pattern [{current_pattern_str}]: \\\").strip()\\n                    if pattern_input:\\n                        if \\\",\\\" in pattern_input:\\n                            f['pattern'] = [p.strip() for p in pattern_input.split(\\\",\\\") if p.strip()]\\n                        else:\\n                            f['pattern'] = pattern_input\\n                    match_field = input(f\\\"  Field to match against [{f.get('match_field', 'item_generated_name')}]: \\\").strip()\\n                    if match_field:\\n                        f['match_field'] = match_field\\n                    elif 'match_field' not in f:\\n                        f['match_field'] = 'item_generated_name'\\n                    field = input(f\\\"  Field to set [{f.get('field', '')}]: \\\").strip()\\n                    if field:\\n                        f['field'] = field\\n                    value_input = input(f\\\"  Value (true/false) [{f.get('value', True)}]: \\\").strip().lower()\\n                    if value_input:\\n                        f['value'] = False if value_input in ['false', 'f', 'no', 'n', '0'] else True\\n                    comment = input(f\\\"  Comment [{f.get('comment', '')}]: \\\").strip()\\n                    if comment or comment == '':\\n                        f['comment'] = comment\\n                    print_ok(\\\"  Filter updated\\\")\\n                elif choice == 'd':\\n                    if not filters:\\n                        print_err(\\\"  No filters to delete\\\")\\n                        continue\\n                    idx_input = input(f\\\"  Filter number to delete (1-{len(filters)}): \\\").strip()\\n                    try:\\n                        idx = int(idx_input) - 1\\n                        if idx < 0 or idx >= len(filters):\\n                            raise ValueError()\\n                    except ValueError:\\n                        print_err(\\\"  Invalid filter number\\\")\\n                        continue\\n                    del filters[idx]\\n                    print_ok(\\\"  Filter deleted\\\")\\n                elif choice == 't':\\n                    if not filters:\\n                        print_err(\\\"  No filters to toggle\\\")\\n                        continue\\n                    idx_input = input(f\\\"  Filter number to toggle (1-{len(filters)}): \\\").strip()\\n                    try:\\n                        idx = int(idx_input) - 1\\n                        if idx < 0 or idx >= len(filters):\\n                            raise ValueError()\\n                    except ValueError:\\n                        print_err(\\\"  Invalid filter number\\\")\\n                        continue\\n                    filters[idx]['enabled'] = not filters[idx].get('enabled', False)\\n                    status = \\\"enabled\\\" if filters[idx]['enabled'] else \\\"disabled\\\"\\n                    print_ok(f\\\"  Filter {status}\\\")\\n                elif choice == 'm':\\n                    if len(filters) < 2:\\n                        print_err(\\\"  Need at least 2 filters to move\\\")\\n                        continue\\n                    from_idx_input = input(f\\\"  Filter number to move (1-{len(filters)}): \\\").strip()\\n                    try:\\n                        from_idx = int(from_idx_input) - 1\\n                        if from_idx < 0 or from_idx >= len(filters):\\n                            raise ValueError()\\n                    except ValueError:\\n                        print_err(\\\"  Invalid filter number\\\")\\n                        continue\\n                    to_idx_input = input(f\\\"  New position (1-{len(filters)}): \\\").strip()\\n                    try:\\n                        to_idx = int(to_idx_input) - 1\\n                        if to_idx < 0 or to_idx >= len(filters):\\n                            raise ValueError()\\n                    except ValueError:\\n                        print_err(\\\"  Invalid position\\\")\\n                        continue\\n                    if from_idx == to_idx:\\n                        print_sub(\\\"  No change needed\\\")\\n                        continue\\n                    filter_to_move = filters.pop(from_idx)\\n                    filters.insert(to_idx, filter_to_move)\\n                    print_ok(f\\\"  Filter moved from position {from_idx+1} to {to_idx+1}\\\")\\n                elif choice == 's':\\n                    break\\n                else:\\n                    print_err(\\\"  Invalid option\\\")\\n            return True\\n        steps = [\\n            (\\\"Change search parameters\\\", None),\\n            (\\\"Configure filter chain\\\", configure_filters),\\n            (\\\"Fetch docs (scrape initial data)\\\", lambda: scraper.fetch_docs(urls)),\\n            (\\\"Export docs (to Markdown for editing)\\\", lambda: json_to_md_table(\\n                doc_json, doc_md, field_order=DEFAULT_DOC_FIELD_ORDER, filter_chain=filters\\n            )),\\n            (\\\"Import updated doc data\\\", lambda: md_table_to_json(doc_md, doc_json, field_order=DEFAULT_DOC_FIELD_ORDER)),\\n            (\\\"Fetch files (prepare files for download)\\\", scraper.fetch_files),\\n            (\\\"Export files (to Markdown for editing)\\\", lambda: json_to_md_table(\\n                file_json, file_md, field_order=DEFAULT_FILE_FIELD_ORDER, filter_chain=filters\\n            )),\\n            (\\\"Import updated file data\\\", lambda: md_table_to_json(file_md, file_json, field_order=DEFAULT_FILE_FIELD_ORDER)),\\n            (\\\"Download files\\\", scraper.download_files),\\n        ]\\n        print(\\\"\\\\nChoose procedure(s) to run (space/comma-separated numbers, or q to quit):\\\")\\n        for i, (desc, _) in enumerate(steps):\\n            print(f\\\"  [{i}] {desc}\\\")\\n        choice = input(\\\"Your choice: \\\").strip()\\n        if choice.lower() == 'q':\\n            print(\\\"Exiting.\\\")\\n            break\\n        try:\\n            indices = [int(x.strip()) for x in choice.replace(',', ' ').split() if x.strip().isdigit()]\\n        except (ValueError, IndexError):\\n            print(\\\"Invalid selection. Please try again.\\\")\\n            continue\\n        for i in indices:\\n            if i == 0:\\n                rig_input = input(f\\\"  rig_number ({rig}): \\\").strip()\\n                if rig_input:\\n                    rig = rig_input\\n                urls_input = input(f\\\"  search_urls ({urls}): \\\").strip()\\n                if urls_input:\\n                    urls = [u.strip() for u in urls_input.split(',') if u.strip()]\\n                scraper = RigDocScraper(rig, show_progress=sp)\\n            elif 0 < i < len(steps):\\n                desc, action = steps[i]\\n                print_inf(f\\\"\\\\nRunning: {desc}\\\")\\n                action()\\n        print_inf(\\\"\\\\nSelected steps completed.\\\\n\\\")\\nif __name__==\\\"__main__\\\":\\n    run_script(CONFIG)\",\n  \"a\": {\n    \"instruction\": \"# Instruction Converter\\n\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\n\\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n    \"input\": \"# GOAL: CREATE A STRUCTURED README.MD <70 LINES\\n\\n PROJECT_RIG_ID = \\\"R5225\\\"\\nPROJECTINFO_GAD = [\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocNumber=*G00*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocNumber=*GAD-00*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DRILL*FLOOR*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DERRICK*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*EQUIPMENT*LAYOUT*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ELEVATION*VIEW*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TOP*DRIVE*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TRAVELLINGBLOCK*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ROUGHNECK*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*HYDRATONG*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MUDBUCKET*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*HYDRARACKER*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*BELLY*BOARD*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*FINGER*BOARD*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RACKING*BOARD*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CATWALK*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MOUSE*HOLE*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*GUIDE*ARM*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*EBT*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ELEVATED*BACKUP*TONG*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CATHEAD*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SPIDER*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ROTARY*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SLIPS*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*AHC*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CMC*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*STANDBUILDING*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TUBULAR*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RTX*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*PIPE*GUIDE*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*LIFTING*CYLINDER*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*STAR*RACKER*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*UTILITY*HANDLING*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RACKING*SYSTEM*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RAISED*SYSTEM*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DFMA*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MPMA*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MANUPILATOR*ARM*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*PIPE*CHUTE*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RISER*CHUTE*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SERVICE*BASKET*\\\",\\n    f\\\"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MAINTENANCE*BASKET*\\\",\\n]\\nFILTERS_SIMULATOR_PATTERNS_INCLUDE = [\\n    \\\"*DRILL*FLOOR*\\\",\\n    \\\"*ELEVATION*VIEW*\\\",\\n    \\\"*EQUIPMENT*LAYOUT*\\\",\\n    \\\"*G000*\\\",\\n    \\\"*GAD-00*\\\",\\n    \\\"*A000*\\\",\\n    \\\"*ASM-00*\\\",\\n]\\nFILTERS_SIMULATOR_PATTERNS_SKIP = [\\n    \\\"*Agitator*\\\",\\n    \\\"*BOP *\\\",\\n    \\\"*Bulk*Systems*\\\",\\n    \\\"*Cabinet*\\\",\\n    \\\"*Centrifuge*\\\",\\n    \\\"*Compressor*\\\",\\n    \\\"*Crane*\\\",\\n    \\\"*Cuttings*\\\",\\n    \\\"*Cyberbase*\\\",\\n    \\\"*DCDA *\\\",\\n    \\\"*DCI*\\\",\\n    \\\"*Deadline*\\\",\\n    \\\"*Gantry*Crane*\\\",\\n    \\\"*HPU*\\\",\\n    \\\"*HVAC*\\\",\\n    \\\"*Knuckle*Crane*\\\",\\n    \\\"*LMRP*\\\",\\n    \\\"*Manrider*\\\",\\n    \\\"*Mixer*\\\",\\n    \\\"*Mixing*\\\",\\n    \\\"*Mud *\\\",\\n    \\\"*Pumps*\\\",\\n    \\\"*Sdi*\\\",\\n    \\\"*Standby*\\\",\\n    \\\"*Station*\\\",\\n    \\\"*Tank *\\\",\\n    \\\"*Valve*\\\",\\n    \\\"*VOID*\\\",\\n    \\\"*Winch*\\\",\\n    \\\"*X-tree*\\\",\\n    \\\"*Yoke*\\\",\\n]\\nCONFIG = {\\n    \\\"rig_number\\\": f\\\"{PROJECT_RIG_ID}.020\\\",\\n    \\\"search_urls\\\": PROJECTINFO_GAD,\\n    \\\"show_progress\\\": True,\\n    \\\"filters\\\": [\\n        {\\n            \\\"type\\\": \\\"docs\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": FILTERS_SIMULATOR_PATTERNS_INCLUDE,\\n            \\\"match_field\\\": \\\"item_generated_name\\\",\\n            \\\"field\\\": \\\"item_include\\\",\\n            \\\"value\\\": True,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"docs\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": FILTERS_SIMULATOR_PATTERNS_SKIP,\\n            \\\"match_field\\\": \\\"item_generated_name\\\",\\n            \\\"field\\\": \\\"item_include\\\",\\n            \\\"value\\\": False,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"docs\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": FILTERS_SIMULATOR_PATTERNS_SKIP,\\n            \\\"match_field\\\": \\\"item_case_description\\\",\\n            \\\"field\\\": \\\"item_include\\\",\\n            \\\"value\\\": False,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"docs\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": [\\\"(VOID)\\\"],\\n            \\\"match_field\\\": \\\"item_case_description\\\",\\n            \\\"field\\\": \\\"item_include\\\",\\n            \\\"value\\\": False,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"files\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": [\\\"*.pdf\\\"],\\n            \\\"match_field\\\": \\\"item_file_ext\\\",\\n            \\\"field\\\": \\\"item_download\\\",\\n            \\\"value\\\": True,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"files\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": FILTERS_SIMULATOR_PATTERNS_SKIP,\\n            \\\"match_field\\\": \\\"item_case_description\\\",\\n            \\\"field\\\": \\\"item_download\\\",\\n            \\\"value\\\": False,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n        {\\n            \\\"type\\\": \\\"files\\\",\\n            \\\"enabled\\\": True,\\n            \\\"pattern\\\": [\\\"*Partslist*\\\"],\\n            \\\"match_field\\\": \\\"item_generated_name\\\",\\n            \\\"field\\\": \\\"item_download\\\",\\n            \\\"value\\\": False,\\n            \\\"comment\\\": \\\"\\\",\\n        },\\n    ],\\n}\\nimport json\\nimport os\\nimport re\\nimport shutil\\nimport time\\nimport timeit\\nfrom contextlib import contextmanager\\nfrom urllib.parse import urljoin\\nimport colorama\\nimport dateutil.parser\\nfrom ansimarkup import ansiprint\\nfrom bs4 import BeautifulSoup\\nfrom selenium import webdriver\\nfrom selenium.webdriver.chrome.options import Options\\nfrom selenium.webdriver.chrome.service import Service\\nfrom selenium.webdriver.support.ui import WebDriverWait\\nfrom selenium.webdriver.common.by import By\\nfrom selenium.webdriver.support import expected_conditions as EC\\nfrom selenium.common.exceptions import TimeoutException\\nfrom webdriver_manager.chrome import ChromeDriverManager\\ncolorama.init()\\nBASE_OUTPUT = os.path.abspath(os.path.join(os.getcwd(), \\\"outputs\\\"))\\nDATA_DIR    = os.path.join(BASE_OUTPUT, \\\"data\\\")\\nDL_DIR      = os.path.join(BASE_OUTPUT, \\\"downloads\\\")\\nfor d in (BASE_OUTPUT, DATA_DIR, DL_DIR):\\n    os.makedirs(d, exist_ok=True)\\nDEFAULT_DOC_FIELD_ORDER = [\\n    \\\"item_include\\\",\\n    \\\"item_generated_name\\\",\\n    \\\"item_type\\\",\\n    \\\"item_case_no\\\",\\n    \\\"item_drawing_type\\\",\\n    \\\"item_doc_no\\\",\\n    \\\"item_date\\\",\\n    \\\"item_title\\\",\\n    \\\"item_case_description\\\",\\n    \\\"item_status\\\",\\n    \\\"item_revision\\\",\\n    \\\"item_responsible\\\",\\n    \\\"item_url\\\",\\n]\\nDEFAULT_FILE_FIELD_ORDER = [\\n    \\\"item_type\\\",\\n    \\\"item_generated_name\\\",\\n    \\\"item_case_no\\\",\\n    \\\"item_drawing_type\\\",\\n    \\\"item_title\\\",\\n    \\\"item_doc_no\\\",\\n    \\\"item_file_ext\\\",\\n    \\\"item_download\\\",\\n    \\\"item_file_size\\\",\\n    \\\"item_file_id\\\",\\n    \\\"item_date\\\",\\n    \\\"item_revision\\\",\\n    \\\"item_case_description\\\",\\n    \\\"item_status\\\",\\n    \\\"item_responsible\\\",\\n    \\\"item_url\\\",\\n]\\ndef cprint(x, color='#FFFFFF'):\\n    ansiprint(f'<fg {color}>{x}')\\nprint_err  = lambda x: cprint(x, '#C92B65')\\nprint_warn = lambda x: cprint(x, '#E6992B')\\nprint_ok   = lambda x: cprint(x, '#1FCE46')\\nprint_inf  = lambda x: cprint(x, '#FFFFFF')\\nprint_sub  = lambda x: cprint(x, '#74705D')\\nclass SleepCondition:\\n    def __init__(self, s):\\n        self.s = s\\n    def __call__(self, _):\\n        time.sleep(self.s)\\n        return True\\ndef reformat_date(s):\\n    try:\\n        dt = dateutil.parser.parse(s)\\n        return dt.strftime('%Y.%m.%d')\\n    except:\\n        return s\\ndef match_pattern(text, pattern):\\n    if isinstance(pattern, list):\\n        return any(match_pattern(text, p) for p in pattern)\\n    regex_pattern = pattern.replace(\\\".\\\", \\\"\\\\\\\\.\\\").replace(\\\"*\\\", \\\".*\\\").replace(\\\"?\\\", \\\".\\\")\\n    return bool(re.search(f\\\"^{regex_pattern}$\\\", text, re.IGNORECASE))\\ndef sanitize_filename(s):\\n    replacements = {\\n        \\\"'\\\": \\\"\\\", '\\\"': \\\"\\\", '`': \\\"\\\",\\n        '\\\\\\\\': \\\"_\\\", '|': \\\"_\\\", '?': \\\"_\\\", '*': \\\"_\\\", '<': \\\"_\\\", '>': \\\"_\\\", ':': \\\"_\\\"\\n    }\\n    result = s\\n    for char, replacement in replacements.items():\\n        result = result.replace(char, replacement)\\n    result = ''.join(c for c in result if c.isprintable() or c == '/')\\n    result = re.sub(r'\\\\s+', ' ', result)\\n    return result.strip()\\ndef reorder_dict_keys(data_dict, field_order):\\n    reordered = {}\\n    for key in field_order:\\n        if key in data_dict:\\n            reordered[key] = data_dict[key]\\n    return reordered\\ndef remove_duplicates(data, exclude_keys=None, priority_key='item_download', priority_vals=None):\\n    if not exclude_keys:\\n        exclude_keys=[]\\n    if not priority_vals:\\n        priority_vals=[]\\n    exset = set(exclude_keys)\\n    pmap  = {v:i for i,v in enumerate(priority_vals)}\\n    out   = {}\\n    for d in data:\\n        filt = {k:v for k,v in d.items() if k not in exset}\\n        hkey = frozenset(filt.items())\\n        if hkey not in out:\\n            out[hkey] = d\\n        else:\\n            if priority_key and priority_vals:\\n                newv = d.get(priority_key)\\n                oldv = out[hkey].get(priority_key)\\n                if (newv in pmap) and (oldv in pmap) and pmap[newv] < pmap[oldv]:\\n                    out[hkey] = d\\n                elif newv in pmap and oldv not in pmap:\\n                    out[hkey] = d\\n    return list(out.values())\\ndef dedupe_and_save(new_items, path, field_order=None):\\n    if os.path.exists(path):\\n        try:\\n            with open(path,'r',encoding='utf-8') as f:\\n                old = json.load(f)\\n        except:\\n            old = []\\n    else:\\n        old = []\\n    merged = old + new_items\\n    merged = remove_duplicates(\\n        merged,\\n        exclude_keys=['item_download','item_generated_path'],\\n        priority_key='item_download',\\n        priority_vals=[False,True]\\n    )\\n    if field_order:\\n        merged = [reorder_dict_keys(d, field_order) for d in merged]\\n    tmp = path + '.temp'\\n    with open(tmp,'w',encoding='utf-8') as f:\\n        json.dump(merged,f,indent=2)\\n    os.replace(tmp,path)\\n    rel_path = os.path.relpath(path, os.getcwd())\\n    print_inf(f\\\"Saved {len(merged)} items => {rel_path}\\\")\\ndef append_items(new_items, path, field_order=None):\\n    if new_items:\\n        dedupe_and_save(new_items, path, field_order=field_order)\\ndef json_to_md_table(json_path, md_path, field_order=None, filter_chain=None):\\n    if not os.path.exists(json_path):\\n        print_err(f\\\"No JSON found: {json_path}\\\")\\n        return\\n    with open(json_path,'r',encoding='utf-8') as f:\\n        data = json.load(f)\\n    if not data:\\n        print_warn(f\\\"No data in {json_path}\\\")\\n        return\\n    item_type = \\\"docs\\\" if \\\"-a-docs.\\\" in json_path else \\\"files\\\"\\n    if filter_chain and isinstance(filter_chain, list):\\n        for filter_idx, filter_config in enumerate(filter_chain):\\n            if not filter_config.get(\\\"enabled\\\", False) or filter_config.get(\\\"type\\\") != item_type:\\n                continue\\n            pattern = filter_config.get(\\\"pattern\\\", \\\"\\\")\\n            field = filter_config.get(\\\"field\\\", \\\"\\\")\\n            value = filter_config.get(\\\"value\\\", True)\\n            comment = filter_config.get(\\\"comment\\\", \\\"\\\")\\n            match_field = filter_config.get(\\\"match_field\\\", \\\"item_generated_name\\\")\\n            if pattern and field:\\n                filtered_count = 0\\n                for d in data:\\n                    field_value = d.get(match_field, \\\"\\\")\\n                    if field_value and match_pattern(field_value, pattern):\\n                        if d.get(field) != value:\\n                            d[field] = value\\n                            filtered_count += 1\\n                if filtered_count > 0:\\n                    action = \\\"Set\\\" if value else \\\"Cleared\\\"\\n                    if isinstance(pattern, list):\\n                        pattern_str = f\\\"[{', '.join(pattern)}]\\\"\\n                    else:\\n                        pattern_str = f\\\"'{pattern}'\\\"\\n                    match_field_str = f\\\" in {match_field}\\\" if match_field != \\\"item_generated_name\\\" else \\\"\\\"\\n                    print_ok(f\\\"Filter #{filter_idx+1}: {action} {field}={value} for {filtered_count} items matching {pattern_str}{match_field_str}\\\")\\n                    if comment:\\n                        print_sub(f\\\"  \\u2192 {comment}\\\")\\n    if not field_order:\\n        colset = set()\\n        for d in data:\\n            colset.update(d.keys())\\n        field_order = sorted(colset)\\n    lines = []\\n    lines.append(\\\"| \\\" + \\\" | \\\".join(field_order) + \\\" |\\\")\\n    lines.append(\\\"|\\\" + \\\"|\\\".join([\\\"---\\\"]*len(field_order)) + \\\"|\\\")\\n    for d in data:\\n        row = []\\n        for c in field_order:\\n            val = d.get(c,\\\"\\\")\\n            if isinstance(val,bool):\\n                val = str(val).lower()\\n            val = str(val).replace(\\\"|\\\",\\\"\\\\\\\\|\\\")\\n            row.append(val)\\n        lines.append(\\\"| \\\" + \\\" | \\\".join(row) + \\\" |\\\")\\n    with open(md_path,'w',encoding='utf-8') as f:\\n        f.write(\\\"\\\\n\\\".join(lines))\\n    rel_md_path = os.path.relpath(md_path, os.getcwd())\\n    print_ok(f\\\"Exported {len(data)} items => {rel_md_path}\\\")\\ndef md_table_to_json(md_path, json_path, field_order=None):\\n    if not os.path.exists(md_path):\\n        print_err(f\\\"No Markdown file found: {md_path}\\\")\\n        return\\n    with open(md_path,'r',encoding='utf-8') as f:\\n        lines=f.read().splitlines()\\n    table_lines=[ln.strip() for ln in lines if ln.strip().startswith(\\\"|\\\")]\\n    if len(table_lines)<2:\\n        print_err(\\\"No valid table rows found in the Markdown.\\\")\\n        return\\n    header=table_lines[0]\\n    data_lines=table_lines[2:]\\n    cols=[x.strip() for x in header.strip('|').split('|')]\\n    new_data=[]\\n    for ln in data_lines:\\n        rowcols=[x.strip() for x in ln.strip('|').split('|')]\\n        if len(rowcols)!=len(cols):\\n            print_warn(f\\\"Skipping mismatch line:\\\\n{ln}\\\")\\n            continue\\n        d={}\\n        for i,c in enumerate(cols):\\n            val=rowcols[i]\\n            if val.lower()==\\\"true\\\":\\n                val=True\\n            elif val.lower()==\\\"false\\\":\\n                val=False\\n            d[c]=val\\n        new_data.append(d)\\n    if field_order:\\n        new_data = [reorder_dict_keys(item, field_order) for item in new_data]\\n    with open(json_path,'w',encoding='utf-8') as f:\\n        json.dump(new_data,f,indent=2)\\n    rel_json_path = os.path.relpath(json_path, os.getcwd())\\n    print_ok(f\\\"Imported {len(new_data)} items => {rel_json_path}\\\")\\nclass RigDocScraper:\\n    def __init__(self, rig, show_progress=False):\\n        self.rig=rig\\n        self.show=show_progress\\n        self.driver=None\\n        self.docs_path = os.path.join(DATA_DIR, f'{rig}-a-docs.json')\\n        self.files_path= os.path.join(DATA_DIR, f'{rig}-b-files.json')\\n    @contextmanager\\n    def browser_session(self, download_mode=False):\\n        profdir = None\\n        try:\\n            opts = Options()\\n            opts.add_argument(\\\"--log-level=3\\\")\\n            if download_mode:\\n                appdata = os.environ.get(\\\"APPDATA\\\", os.getcwd())\\n                profdir = os.path.join(appdata, \\\"chromedriver_profile\\\")\\n                os.makedirs(profdir, exist_ok=True)\\n                opts.add_argument(f\\\"--user-data-dir={profdir}\\\")\\n                dl_dir = os.path.join(DL_DIR, self.rig)\\n                os.makedirs(dl_dir, exist_ok=True)\\n                prefs = {\\n                    \\\"download.default_directory\\\": dl_dir,\\n                    \\\"download.prompt_for_download\\\": False,\\n                    \\\"download.directory_upgrade\\\": True,\\n                    \\\"safebrowsing.enabled\\\": True\\n                }\\n                opts.add_experimental_option(\\\"prefs\\\", prefs)\\n            svc = Service(ChromeDriverManager().install())\\n            self.driver = webdriver.Chrome(service=svc, options=opts)\\n            wait_time = 2 if download_mode else 5\\n            WebDriverWait(self.driver, wait_time).until(SleepCondition(wait_time))\\n            self.driver.implicitly_wait(5 if download_mode else 10)\\n            if self.show:\\n                print_sub(f\\\"[Browser] Started {'download-enabled ' if download_mode else ''}session\\\")\\n            yield self.driver\\n        finally:\\n            if self.driver:\\n                if self.show:\\n                    print_sub(\\\"[Browser] Closing session\\\")\\n                self.driver.quit()\\n                self.driver = None\\n                if download_mode and profdir:\\n                    try:\\n                        shutil.rmtree(profdir)\\n                    except Exception as e:\\n                        if self.show:\\n                            print_warn(f\\\"[Browser] Failed to clean up profile: {e}\\\")\\n    def fetch_docs(self, urls):\\n        if not os.path.exists(self.docs_path):\\n            with open(self.docs_path,'w',encoding='utf-8') as f:\\n                json.dump([],f)\\n        with self.browser_session() as driver:\\n            for url in urls:\\n                if self.show:\\n                    print_sub(f\\\"[DocScrape] => {url}\\\")\\n                driver.get(url)\\n                is_rig = (\\\"rigsearch\\\" in url.lower()) or (\\\"advancedsearch\\\" in url.lower())\\n                max_wait = 10 if is_rig else 5\\n                wait_scroll = 5 if is_rig else 2\\n                try:\\n                    WebDriverWait(driver, max_wait).until(\\n                        EC.presence_of_element_located((By.CSS_SELECTOR, \\\"a.search-result-link\\\"))\\n                    )\\n                except TimeoutException:\\n                    print_warn(f\\\"Waited up to {max_wait}s, but no 'search-result-link' found. Proceeding anyway...\\\")\\n                inc = 0\\n                prev = driver.execute_script(\\\"return document.body.scrollHeight\\\")\\n                while inc < 150:\\n                    inc += 1\\n                    driver.execute_script(\\\"window.scrollTo(0, document.body.scrollHeight);\\\")\\n                    time.sleep(wait_scroll)\\n                    newh = driver.execute_script(\\\"return document.body.scrollHeight\\\")\\n                    if self.show:\\n                        print_sub(f\\\" -> Scroll {inc}, h={newh}\\\")\\n                    if newh == prev:\\n                        break\\n                    prev = newh\\n                soup = BeautifulSoup(driver.page_source, \\\"html.parser\\\")\\n                row_elems = soup.find_all(\\\"a\\\", class_=\\\"search-result-link\\\")\\n                rows = [(td.find_parent(\\\"tr\\\")).find_all(\\\"td\\\") for td in row_elems]\\n                if self.show:\\n                    print_inf(f\\\"Found {len(rows)} doc rows.\\\")\\n                new_docs = []\\n                for row_cells in rows:\\n                    doc_title   = re.sub(r'[\\\\n\\\\r]+',' ', row_cells[1].a.text.strip())\\n                    sub_type    = row_cells[2].get_text(strip=True)\\n                    doc_no      = row_cells[3].get_text(strip=True)\\n                    responsible = row_cells[4].get_text(strip=True)\\n                    case_no     = row_cells[5].get_text(strip=True)\\n                    case_desc   = row_cells[6].get_text(strip=True)\\n                    revision    = row_cells[7].get_text(strip=True)\\n                    status      = row_cells[8].get_text(strip=True)\\n                    dt_         = reformat_date(row_cells[9].get_text(strip=True))\\n                    doc_url     = urljoin(\\\"https://rigdoc.nov.com/\\\", row_cells[1].a.get(\\\"href\\\"))\\n                    if \\\"document-void\\\" in (row_cells[1].a.get(\\\"class\\\", [])) or \\\"*VOID*\\\" in doc_title.upper():\\n                        status = \\\"(VOID)\\\"\\n                    doc_generated_name = (\\n                        f\\\"{doc_no.upper()}_REV{revision.upper()}--{case_no.upper()}-{doc_title.title()}\\\"\\n                    ).replace(\\\"  \\\", \\\" \\\")\\n                    doc_generated_name_sanitized = sanitize_filename(doc_generated_name)\\n                    new_docs.append({\\n                        \\\"item_type\\\": \\\"doc\\\",\\n                        \\\"item_title\\\": doc_title,\\n                        \\\"item_drawing_type\\\": sub_type,\\n                        \\\"item_doc_no\\\": doc_no,\\n                        \\\"item_responsible\\\": responsible,\\n                        \\\"item_case_no\\\": case_no,\\n                        \\\"item_case_description\\\": case_desc,\\n                        \\\"item_revision\\\": revision,\\n                        \\\"item_status\\\": status,\\n                        \\\"item_date\\\": dt_,\\n                        \\\"item_url\\\": doc_url,\\n                        \\\"item_include\\\": False,\\n                        \\\"item_generated_name\\\": doc_generated_name_sanitized\\n                    })\\n                append_items(new_docs, self.docs_path, field_order=DEFAULT_DOC_FIELD_ORDER)\\n        rel_docs_path = os.path.relpath(self.docs_path, os.getcwd())\\n        print_ok(f\\\"Docs => {rel_docs_path}\\\")\\n    def fetch_files(self):\\n        if not os.path.exists(self.docs_path):\\n            print_err(f\\\"No docs found: {self.docs_path}\\\")\\n            return\\n        with open(self.docs_path,'r',encoding='utf-8') as f:\\n            all_docs=json.load(f)\\n        docs_for_files=[d for d in all_docs if d.get(\\\"item_type\\\")==\\\"doc\\\" and d.get(\\\"item_include\\\")==True]\\n        if not docs_for_files:\\n            print_warn(\\\"No docs with item_include==True => No files to fetch.\\\")\\n            return\\n        if not os.path.exists(self.files_path):\\n            with open(self.files_path,'w',encoding='utf-8') as f:\\n                json.dump([],f)\\n        with self.browser_session() as driver:\\n            for i,doc in enumerate(docs_for_files,1):\\n                if self.show:\\n                    print_sub(f\\\"[FileScrape] doc {i}/{len(docs_for_files)} => {doc.get('item_doc_no')}\\\")\\n                doc_url = doc.get(\\\"item_url\\\",\\\"\\\")\\n                if not doc_url:\\n                    continue\\n                wp_url = doc_url.replace(\\\"nov.com/documents/\\\",\\\"nov.com/wp/documents/\\\")\\n                if \\\"nov.com/wp/documents/\\\" not in wp_url:\\n                    continue\\n                driver.get(wp_url)\\n                WebDriverWait(driver,5).until(SleepCondition(5))\\n                soup=BeautifulSoup(driver.page_source,\\\"html.parser\\\")\\n                frows=soup.select(\\\"div.revision-panel-body div.file-detail-row\\\")\\n                flinks=[]\\n                for row in frows:\\n                    lk=row.select_one(\\\"div.file-detail a\\\")\\n                    if lk and lk.get(\\\"href\\\"):\\n                        flinks.append(urljoin(\\\"https://rigdoc.nov.com/\\\", lk[\\\"href\\\"]))\\n                doc_generated_name = doc.get(\\\"item_generated_name\\\",\\\"DOC_UNKNOWN\\\")\\n                new_files=[]\\n                for idx,flink in enumerate(flinks,1):\\n                    fapi=flink.replace(\\\"/_download\\\",\\\"\\\")\\n                    driver.get(fapi)\\n                    WebDriverWait(driver,0.5).until(SleepCondition(0.5))\\n                    raw=BeautifulSoup(driver.page_source,\\\"html.parser\\\").get_text(strip=True)\\n                    info=json.loads(raw)\\n                    fext=info.get(\\\"extension\\\",\\\"\\\")\\n                    fsz =info.get(\\\"filesize\\\",0)\\n                    fsmb=f\\\"{(fsz/(1024*1024)):.2f} MB\\\"\\n                    ftitle=info.get(\\\"title\\\",\\\"\\\").strip()\\n                    file_generated_name = (f\\\"{doc_generated_name}.id.{idx}-{len(flinks)}.{ftitle.title()}\\\").replace(\\\"  \\\",\\\" \\\")\\n                    if fext and not file_generated_name.lower().endswith(f\\\".{fext.lower()}\\\"):\\n                        file_generated_name = f\\\"{file_generated_name}{fext.lower()}\\\"\\n                    file_generated_name_sanitized = sanitize_filename(file_generated_name)\\n                    new_files.append({\\n                        \\\"item_type\\\": \\\"file\\\",\\n                        \\\"item_title\\\": ftitle,\\n                        \\\"item_file_id\\\": f\\\"{len(flinks)}->({idx}/{len(flinks)}) FID:{info.get('fileId','-')}\\\",\\n                        \\\"item_file_ext\\\": f\\\"{fext.lower()}\\\" if fext else \\\"\\\",\\n                        \\\"item_file_size\\\": fsmb,\\n                        \\\"item_date\\\": reformat_date(info.get(\\\"insertedDate\\\",\\\"\\\")),\\n                        \\\"item_revision\\\": doc.get(\\\"item_revision\\\",\\\"\\\"),\\n                        \\\"item_responsible\\\": info.get(\\\"insertedBy\\\",\\\"\\\"),\\n                        \\\"item_status\\\": info.get(\\\"lastChangedDate\\\") or \\\"-\\\",\\n                        \\\"item_case_no\\\": doc.get(\\\"item_case_no\\\",\\\"\\\"),\\n                        \\\"item_doc_no\\\": doc.get(\\\"item_doc_no\\\",\\\"\\\"),\\n                        \\\"item_drawing_type\\\": doc.get(\\\"item_drawing_type\\\",\\\"\\\"),\\n                        \\\"item_case_description\\\": doc.get(\\\"item_case_description\\\",\\\"\\\"),\\n                        \\\"item_url\\\": flink,\\n                        \\\"item_download\\\": False,\\n                        \\\"item_generated_name\\\": file_generated_name_sanitized\\n                    })\\n                append_items(new_files, self.files_path, field_order=DEFAULT_FILE_FIELD_ORDER)\\n        rel_files_path = os.path.relpath(self.files_path, os.getcwd())\\n        print_ok(f\\\"Files => {rel_files_path}\\\")\\n    def download_files(self):\\n        if not os.path.exists(self.files_path):\\n            print_err(f\\\"No files JSON found at {self.files_path}\\\")\\n            return\\n        with open(self.files_path, 'r', encoding='utf-8') as f:\\n            data = json.load(f)\\n        to_download = [x for x in data if x.get(\\\"item_type\\\") == \\\"file\\\" and x.get(\\\"item_download\\\") == True]\\n        if not to_download:\\n            print_warn(\\\"No files with item_download==True.\\\")\\n            return\\n        download_dir = os.path.join(DL_DIR, self.rig)\\n        print_inf(f\\\"Will download {len(to_download)} files => {download_dir}\\\")\\n        with self.browser_session(download_mode=True) as driver:\\n            for i, fitem in enumerate(to_download, 1):\\n                furl = fitem.get(\\\"item_url\\\", \\\"\\\")\\n                if not furl:\\n                    continue\\n                base_name = fitem.get(\\\"item_generated_name\\\", f\\\"file_{i}\\\")\\n                if not base_name:\\n                    base_name = f\\\"file_{i}\\\"\\n                base_name = sanitize_filename(base_name)\\n                parts = base_name.split(\\\"/\\\")\\n                if parts:\\n                    last_part = parts[-1]\\n                    ext = fitem.get(\\\"item_file_ext\\\", \\\"\\\").lower()\\n                    if ext and not last_part.lower().endswith(f\\\"{ext}\\\"):\\n                        ext = f\\\".{ext}\\\" if not ext.startswith(\\\".\\\") else ext\\n                        final_filename = last_part + ext\\n                    else:\\n                        final_filename = last_part\\n                    subfolders = parts[:-1]\\n                else:\\n                    ext = fitem.get(\\\"item_file_ext\\\", \\\"\\\")\\n                    ext = f\\\".{ext}\\\" if ext and not ext.startswith(\\\".\\\") else ext\\n                    final_filename = f\\\"file_{i}{ext}\\\"\\n                    subfolders = []\\n                final_subdir = os.path.join(download_dir, *subfolders)\\n                os.makedirs(final_subdir, exist_ok=True)\\n                final_destination = os.path.join(final_subdir, final_filename)\\n                if os.path.exists(final_destination):\\n                    print_ok(f'Skipped (already exists): \\\"{final_destination}\\\"')\\n                    continue\\n                if self.show:\\n                    print_sub(\\\"-\\\" * 80)\\n                    print_inf(f'[Download {i}/{len(to_download)}] => \\\"{final_filename}\\\"')\\n                start = timeit.default_timer()\\n                existing_files = set(f for f in os.listdir(download_dir) if os.path.isfile(os.path.join(download_dir, f)))\\n                driver.get(furl)\\n                time.sleep(1)\\n                done = False\\n                while (timeit.default_timer() - start) < 30:\\n                    time.sleep(1)\\n                    new_files = set(f for f in os.listdir(download_dir) if os.path.isfile(os.path.join(download_dir, f))) - existing_files\\n                    if new_files:\\n                        candidate = list(new_files)[0]\\n                        ext2 = os.path.splitext(candidate)[1].lower()\\n                        if ext2 not in [\\\".tmp\\\", \\\".crdownload\\\"]:\\n                            src = os.path.join(download_dir, candidate)\\n                            try:\\n                                os.rename(src, final_destination)\\n                                done = True\\n                                print_ok(f'-> Downloaded \\\"{final_destination}\\\"')\\n                                break\\n                            except Exception as e:\\n                                print_warn(f'Failed to rename: {e}')\\n                if not done:\\n                    print_warn(f'Timed out: \\\"{final_destination}\\\"')\\n        print_inf(\\\"All file downloads attempted.\\\")\\ndef run_script(cfg):\\n    rig = cfg.get(\\\"rig_number\\\", \\\"R9999\\\")\\n    urls = cfg.get(\\\"search_urls\\\", [])\\n    sp = cfg.get(\\\"show_progress\\\", False)\\n    filters = cfg.get(\\\"filters\\\", [])\\n    scraper = RigDocScraper(rig, show_progress=sp)\\n    while True:\\n        doc_json = os.path.join(DATA_DIR, f'{rig}-a-docs.json')\\n        doc_md = os.path.join(DATA_DIR, f'{rig}-a-docs.md')\\n        file_json = os.path.join(DATA_DIR, f'{rig}-b-files.json')\\n        file_md = os.path.join(DATA_DIR, f'{rig}-b-files.md')\\n        def configure_filters():\\n            while True:\\n                print_inf(\\\"\\\\nCurrent filter chain:\\\")\\n                if not filters:\\n                    print_sub(\\\"  No filters configured\\\")\\n                else:\\n                    for i, f in enumerate(filters):\\n                        status = \\\"ENABLED\\\" if f.get(\\\"enabled\\\", False) else \\\"DISABLED\\\"\\n                        type_str = f.get(\\\"type\\\", \\\"unknown\\\")\\n                        pattern = f.get(\\\"pattern\\\", \\\"\\\")\\n                        field = f.get(\\\"field\\\", \\\"\\\")\\n                        value = f.get(\\\"value\\\", True)\\n                        comment = f.get(\\\"comment\\\", \\\"\\\")\\n                        if isinstance(pattern, list):\\n                            pattern_str = f\\\"[{', '.join(pattern)}]\\\"\\n                        else:\\n                            pattern_str = f\\\"'{pattern}'\\\"\\n                        match_field = f.get(\\\"match_field\\\", \\\"item_generated_name\\\")\\n                        match_field_str = f\\\" in {match_field}\\\" if match_field != \\\"item_generated_name\\\" else \\\"\\\"\\n                        color_fn = print_ok if f.get(\\\"enabled\\\", False) else print_sub\\n                        color_fn(f\\\"  [{i+1}] {status} - {type_str}: {pattern_str}{match_field_str} \\u2192 {field}={value}\\\")\\n                        if comment:\\n                            print_sub(f\\\"      {comment}\\\")\\n                print_inf(\\\"\\\\nFilter options:\\\")\\n                print_inf(\\\"  [a] Add new filter\\\")\\n                print_inf(\\\"  [e] Edit filter\\\")\\n                print_inf(\\\"  [d] Delete filter\\\")\\n                print_inf(\\\"  [t] Toggle filter\\\")\\n                print_inf(\\\"  [m] Move filter\\\")\\n                print_inf(\\\"  [s] Save and return\\\")\\n                choice = input(\\\"\\\\nEnter option: \\\").strip().lower()\\n                if choice == 'a':\\n                    print_inf(\\\"\\\\nAdd new filter:\\\")\\n                    filter_type = input(\\\"  Type (docs/files): \\\").strip().lower()\\n                    if filter_type not in ['docs', 'files']:\\n                        print_err(\\\"  Invalid type. Must be 'docs' or 'files'\\\")\\n                        continue\\n                    pattern_input = input(\\\"  Pattern (e.g., *g0001*, *.pdf) or comma-separated list: \\\").strip()\\n                    if not pattern_input:\\n                        print_err(\\\"  Pattern cannot be empty\\\")\\n                        continue\\n                    if \\\",\\\" in pattern_input:\\n                        pattern = [p.strip() for p in pattern_input.split(\\\",\\\") if p.strip()]\\n                    else:\\n                        pattern = pattern_input\\n                    match_field = input(f\\\"  Field to match against [item_generated_name]: \\\").strip()\\n                    if not match_field:\\n                        match_field = 'item_generated_name'\\n                    field = input(f\\\"  Field to set ({'item_include' if filter_type == 'docs' else 'item_download'}): \\\").strip()\\n                    if not field:\\n                        field = 'item_include' if filter_type == 'docs' else 'item_download'\\n                    value_input = input(f\\\"  Value to set (true/false) [true]: \\\").strip().lower()\\n                    value = False if value_input in ['false', 'f', 'no', 'n', '0'] else True\\n                    comment = input(\\\"  Comment (optional): \\\").strip()\\n                    new_filter = {\\n                        \\\"type\\\": filter_type,\\n                        \\\"enabled\\\": True,\\n                        \\\"pattern\\\": pattern,\\n                        \\\"match_field\\\": match_field,\\n                        \\\"field\\\": field,\\n                        \\\"value\\\": value,\\n                        \\\"comment\\\": comment\\n                    }\\n                    filters.append(new_filter)\\n                    print_ok(\\\"  Filter added\\\")\\n                elif choice == 'e':\\n                    if not filters:\\n                        print_err(\\\"  No filters to edit\\\")\\n                        continue\\n                    idx_input = input(f\\\"  Filter number to edit (1-{len(filters)}): \\\").strip()\\n                    try:\\n                        idx = int(idx_input) - 1\\n                        if idx < 0 or idx >= len(filters):\\n                            raise ValueError()\\n                    except ValueError:\\n                        print_err(\\\"  Invalid filter number\\\")\\n                        continue\\n                    f = filters[idx]\\n                    print_inf(f\\\"\\\\nEditing filter #{idx+1}:\\\")\\n                    filter_type = input(f\\\"  Type (docs/files) [{f.get('type', '')}]: \\\").strip().lower()\\n                    if filter_type and filter_type in ['docs', 'files']:\\n                        f['type'] = filter_type\\n                    current_pattern = f.get('pattern', '')\\n                    if isinstance(current_pattern, list):\\n                        current_pattern_str = \\\", \\\".join(current_pattern)\\n                    else:\\n                        current_pattern_str = current_pattern\\n                    pattern_input = input(f\\\"  Pattern [{current_pattern_str}]: \\\").strip()\\n                    if pattern_input:\\n                        if \\\",\\\" in pattern_input:\\n                            f['pattern'] = [p.strip() for p in pattern_input.split(\\\",\\\") if p.strip()]\\n                        else:\\n                            f['pattern'] = pattern_input\\n                    match_field = input(f\\\"  Field to match against [{f.get('match_field', 'item_generated_name')}]: \\\").strip()\\n                    if match_field:\\n                        f['match_field'] = match_field\\n                    elif 'match_field' not in f:\\n                        f['match_field'] = 'item_generated_name'\\n                    field = input(f\\\"  Field to set [{f.get('field', '')}]: \\\").strip()\\n                    if field:\\n                        f['field'] = field\\n                    value_input = input(f\\\"  Value (true/false) [{f.get('value', True)}]: \\\").strip().lower()\\n                    if value_input:\\n                        f['value'] = False if value_input in ['false', 'f', 'no', 'n', '0'] else True\\n                    comment = input(f\\\"  Comment [{f.get('comment', '')}]: \\\").strip()\\n                    if comment or comment == '':\\n                        f['comment'] = comment\\n                    print_ok(\\\"  Filter updated\\\")\\n                elif choice == 'd':\\n                    if not filters:\\n                        print_err(\\\"  No filters to delete\\\")\\n                        continue\\n                    idx_input = input(f\\\"  Filter number to delete (1-{len(filters)}): \\\").strip()\\n                    try:\\n                        idx = int(idx_input) - 1\\n                        if idx < 0 or idx >= len(filters):\\n                            raise ValueError()\\n                    except ValueError:\\n                        print_err(\\\"  Invalid filter number\\\")\\n                        continue\\n                    del filters[idx]\\n                    print_ok(\\\"  Filter deleted\\\")\\n                elif choice == 't':\\n                    if not filters:\\n                        print_err(\\\"  No filters to toggle\\\")\\n                        continue\\n                    idx_input = input(f\\\"  Filter number to toggle (1-{len(filters)}): \\\").strip()\\n                    try:\\n                        idx = int(idx_input) - 1\\n                        if idx < 0 or idx >= len(filters):\\n                            raise ValueError()\\n                    except ValueError:\\n                        print_err(\\\"  Invalid filter number\\\")\\n                        continue\\n                    filters[idx]['enabled'] = not filters[idx].get('enabled', False)\\n                    status = \\\"enabled\\\" if filters[idx]['enabled'] else \\\"disabled\\\"\\n                    print_ok(f\\\"  Filter {status}\\\")\\n                elif choice == 'm':\\n                    if len(filters) < 2:\\n                        print_err(\\\"  Need at least 2 filters to move\\\")\\n                        continue\\n                    from_idx_input = input(f\\\"  Filter number to move (1-{len(filters)}): \\\").strip()\\n                    try:\\n                        from_idx = int(from_idx_input) - 1\\n                        if from_idx < 0 or from_idx >= len(filters):\\n                            raise ValueError()\\n                    except ValueError:\\n                        print_err(\\\"  Invalid filter number\\\")\\n                        continue\\n                    to_idx_input = input(f\\\"  New position (1-{len(filters)}): \\\").strip()\\n                    try:\\n                        to_idx = int(to_idx_input) - 1\\n                        if to_idx < 0 or to_idx >= len(filters):\\n                            raise ValueError()\\n                    except ValueError:\\n                        print_err(\\\"  Invalid position\\\")\\n                        continue\\n                    if from_idx == to_idx:\\n                        print_sub(\\\"  No change needed\\\")\\n                        continue\\n                    filter_to_move = filters.pop(from_idx)\\n                    filters.insert(to_idx, filter_to_move)\\n                    print_ok(f\\\"  Filter moved from position {from_idx+1} to {to_idx+1}\\\")\\n                elif choice == 's':\\n                    break\\n                else:\\n                    print_err(\\\"  Invalid option\\\")\\n            return True\\n        steps = [\\n            (\\\"Change search parameters\\\", None),\\n            (\\\"Configure filter chain\\\", configure_filters),\\n            (\\\"Fetch docs (scrape initial data)\\\", lambda: scraper.fetch_docs(urls)),\\n            (\\\"Export docs (to Markdown for editing)\\\", lambda: json_to_md_table(\\n                doc_json, doc_md, field_order=DEFAULT_DOC_FIELD_ORDER, filter_chain=filters\\n            )),\\n            (\\\"Import updated doc data\\\", lambda: md_table_to_json(doc_md, doc_json, field_order=DEFAULT_DOC_FIELD_ORDER)),\\n            (\\\"Fetch files (prepare files for download)\\\", scraper.fetch_files),\\n            (\\\"Export files (to Markdown for editing)\\\", lambda: json_to_md_table(\\n                file_json, file_md, field_order=DEFAULT_FILE_FIELD_ORDER, filter_chain=filters\\n            )),\\n            (\\\"Import updated file data\\\", lambda: md_table_to_json(file_md, file_json, field_order=DEFAULT_FILE_FIELD_ORDER)),\\n            (\\\"Download files\\\", scraper.download_files),\\n        ]\\n        print(\\\"\\\\nChoose procedure(s) to run (space/comma-separated numbers, or q to quit):\\\")\\n        for i, (desc, _) in enumerate(steps):\\n            print(f\\\"  [{i}] {desc}\\\")\\n        choice = input(\\\"Your choice: \\\").strip()\\n        if choice.lower() == 'q':\\n            print(\\\"Exiting.\\\")\\n            break\\n        try:\\n            indices = [int(x.strip()) for x in choice.replace(',', ' ').split() if x.strip().isdigit()]\\n        except (ValueError, IndexError):\\n            print(\\\"Invalid selection. Please try again.\\\")\\n            continue\\n        for i in indices:\\n            if i == 0:\\n                rig_input = input(f\\\"  rig_number ({rig}): \\\").strip()\\n                if rig_input:\\n                    rig = rig_input\\n                urls_input = input(f\\\"  search_urls ({urls}): \\\").strip()\\n                if urls_input:\\n                    urls = [u.strip() for u in urls_input.split(',') if u.strip()]\\n                scraper = RigDocScraper(rig, show_progress=sp)\\n            elif 0 < i < len(steps):\\n                desc, action = steps[i]\\n                print_inf(f\\\"\\\\nRunning: {desc}\\\")\\n                action()\\n        print_inf(\\\"\\\\nSelected steps completed.\\\\n\\\")\\nif __name__==\\\"__main__\\\":\\n    run_script(CONFIG)\",\n    \"output\": \"{\\n  \\\"instruction_format\\\": \\\"Create a structured README.md file (less than 70 lines) documenting the RigDocScraper project. Include the following sections in order:\\\\n\\\\n1. Project Title and Rig Number (use PROJECT_RIG_ID=\\\\\\\"R5225\\\\\\\").\\\\n2. Brief Project Description and Main Use Case.\\\\n3. Prerequisites: List all required Python packages and setup steps.\\\\n4. Directory Structure: Outline BASE_OUTPUT, DATA_DIR, DL_DIR, and where outputs are stored.\\\\n5. Configuration: Describe how to set or change PROJECTINFO_GAD, FILTERS_SIMULATOR_PATTERNS_INCLUDE, FILTERS_SIMULATOR_PATTERNS_SKIP, and CONFIG parameters.\\\\n6. Pipeline Overview: Stepwise summary of the process:\\\\n    - 1. Adjust search parameters (rig number, URLs).\\\\n    - 2. Configure filter chain (with editing options).\\\\n    - 3. Fetch docs (web scrape document data).\\\\n    - 4. Export docs to Markdown for review/editing.\\\\n    - 5. Import docs from Markdown to JSON.\\\\n    - 6. Fetch candidate files linked to included docs.\\\\n    - 7. Export file list to Markdown for review/editing.\\\\n    - 8. Import updated file list from Markdown.\\\\n    - 9. Download marked files (PDFs only).\\\\n7. Filter System: Explain how inclusion/exclusion logic and pattern matching works for both docs and files; reference example patterns.\\\\n8. Manual Editing: Specify that Markdown tables in outputs/data can be directly edited to select docs/files for download.\\\\n9. Running the Tool: List the command to execute the script, how to select pipeline steps, and notes about interactive prompts.\\\\n10. Troubleshooting: Add notes on browser requirements, typical errors, and where to look in the output.\\\\n11. References: Link to relevant documentation or `rigdoc.nov.com` as appropriate.\\\\n\\\\nEnsure usage of command/instructional voice, include code block examples for configuration and usage commands, and maintain concise, technical phrasing throughout. Avoid first-person language. Preserve project-specific terminology, filter logic, and maintain the order of steps as in the input.\\\"\\n}\"\n  },\n  \"b\": {\n    \"instruction\": \"# Essence Distillation\\n\\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\\n\\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\",\n    \"input\": \"{\\n  \\\"instruction_format\\\": \\\"Create a structured README.md file (less than 70 lines) documenting the RigDocScraper project. Include the following sections in order:\\\\n\\\\n1. Project Title and Rig Number (use PROJECT_RIG_ID=\\\\\\\"R5225\\\\\\\").\\\\n2. Brief Project Description and Main Use Case.\\\\n3. Prerequisites: List all required Python packages and setup steps.\\\\n4. Directory Structure: Outline BASE_OUTPUT, DATA_DIR, DL_DIR, and where outputs are stored.\\\\n5. Configuration: Describe how to set or change PROJECTINFO_GAD, FILTERS_SIMULATOR_PATTERNS_INCLUDE, FILTERS_SIMULATOR_PATTERNS_SKIP, and CONFIG parameters.\\\\n6. Pipeline Overview: Stepwise summary of the process:\\\\n    - 1. Adjust search parameters (rig number, URLs).\\\\n    - 2. Configure filter chain (with editing options).\\\\n    - 3. Fetch docs (web scrape document data).\\\\n    - 4. Export docs to Markdown for review/editing.\\\\n    - 5. Import docs from Markdown to JSON.\\\\n    - 6. Fetch candidate files linked to included docs.\\\\n    - 7. Export file list to Markdown for review/editing.\\\\n    - 8. Import updated file list from Markdown.\\\\n    - 9. Download marked files (PDFs only).\\\\n7. Filter System: Explain how inclusion/exclusion logic and pattern matching works for both docs and files; reference example patterns.\\\\n8. Manual Editing: Specify that Markdown tables in outputs/data can be directly edited to select docs/files for download.\\\\n9. Running the Tool: List the command to execute the script, how to select pipeline steps, and notes about interactive prompts.\\\\n10. Troubleshooting: Add notes on browser requirements, typical errors, and where to look in the output.\\\\n11. References: Link to relevant documentation or `rigdoc.nov.com` as appropriate.\\\\n\\\\nEnsure usage of command/instructional voice, include code block examples for configuration and usage commands, and maintain concise, technical phrasing throughout. Avoid first-person language. Preserve project-specific terminology, filter logic, and maintain the order of steps as in the input.\\\"\\n}\",\n    \"output\": \"{\\n  \\\"distilled_essence\\\": {\\n    \\\"README.md\\\": [\\n      \\\"# RigDocScraper \\u2013 Rig R5225\\\",\\n      \\\"\\\",\\n      \\\"## Overview\\\",\\n      \\\"Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) for NOV Rig R5225 from rigdoc.nov.com. Use case: curate, review, and batch-download rig-related documents and technical files.\\\",\\n      \\\"\\\",\\n      \\\"## Prerequisites\\\",\\n      \\\"- Python 3.8+\\\",\\n      \\\"- Required packages:\\\",\\n      \\\"  - selenium, beautifulsoup4, colorama, dateutil, ansimarkup, webdriver-manager\\\",\\n      \\\"- Chrome browser installed\\\",\\n      \\\"- ChromeDriver (managed via webdriver-manager)\\\",\\n      \\\"```bash\\\",\\n      \\\"pip install selenium beautifulsoup4 colorama python-dateutil ansimarkup webdriver-manager\\\",\\n      \\\"```\\\",\\n      \\\"\\\",\\n      \\\"## Directory Structure\\\",\\n      \\\"- `outputs/` (BASE_OUTPUT): All results stored here\\\",\\n      \\\"- `outputs/data/` (DATA_DIR): Document and file metadata (JSON/MD)\\\",\\n      \\\"- `outputs/downloads/` (DL_DIR): Downloaded PDF and file outputs\\\",\\n      \\\"\\\",\\n      \\\"## Configuration\\\",\\n      \\\"- Edit parameters in `CONFIG` at script start:\\\",\\n      \\\"  - `rig_number`: e.g., \\\\\\\"R5225.020\\\\\\\"\\\",\\n      \\\"  - `search_urls`: list of document search URLs (`PROJECTINFO_GAD`)\\\",\\n      \\\"  - `filters`: filter chain to include/exclude docs/files (see below)\\\",\\n      \\\"- Example patterns:\\\",\\n      \\\"  - `FILTERS_SIMULATOR_PATTERNS_INCLUDE`: doc types to include (e.g. \\\\\\\"*DRILL*FLOOR*\\\\\\\")\\\",\\n      \\\"  - `FILTERS_SIMULATOR_PATTERNS_SKIP`: exclusion patterns\\\",\\n      \\\"```python\\\",\\n      \\\"PROJECTINFO_GAD = [ ... ]  # List of URLs\\\",\\n      \\\"CONFIG = {\\\",\\n      \\\"    'rig_number': 'R5225.020',\\\",\\n      \\\"    'search_urls': PROJECTINFO_GAD,\\\",\\n      \\\"    'filters': [ ... ]\\\",\\n      \\\"}\\\",\\n      \\\"```\\\",\\n      \\\"\\\",\\n      \\\"## Pipeline Overview\\\",\\n      \\\"1. Change search parameters (rig number, URLs)\\\",\\n      \\\"2. Configure filter chain (add, edit, delete, toggle, reorder filters)\\\",\\n      \\\"3. Fetch docs (scrape data from rigdoc.nov.com)\\\",\\n      \\\"4. Export docs to Markdown (for selection/editing)\\\",\\n      \\\"5. Import docs from Markdown (sync edited selection)\\\",\\n      \\\"6. Fetch candidate files linked to selected docs\\\",\\n      \\\"7. Export file list to Markdown (for editing/selecting files for download)\\\",\\n      \\\"8. Import updated file list from Markdown\\\",\\n      \\\"9. Download marked files (PDFs only, via Chrome)\\\",\\n      \\\"\\\",\\n      \\\"## Filter System\\\",\\n      \\\"- Filters use `pattern` matching (wildcards) on specific fields (e.g., `item_generated_name`)\\\",\\n      \\\"- Types: `docs` (for document records), `files` (for downloadable files)\\\",\\n      \\\"- Inclusion example (set True):\\\",\\n      \\\"  - `pattern`: [\\\\\\\"*EQUIPMENT*LAYOUT*\\\\\\\"]\\\",\\n      \\\"- Exclusion example (set False):\\\",\\n      \\\"  - `pattern`: [\\\\\\\"*VOID*\\\\\\\"], field: `item_case_description`\\\",\\n      \\\"\\\",\\n      \\\"## Manual Editing\\\",\\n      \\\"- Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing\\\",\\n      \\\"- Set `item_include` (docs) and `item_download` (files) fields\\\",\\n      \\\"\\\",\\n      \\\"## Running the Tool\\\",\\n      \\\"```bash\\\",\\n      \\\"python rigdocscraper.py\\\",\\n      \\\"```\\\",\\n      \\\"- Interactive menu enables step selection (numbers/comma/space-separated)\\\",\\n      \\\"- Supports adjusting parameters, filter configuration, and reviewing batch steps\\\",\\n      \\\"- Prompts will guide through editing, import/export, and download procedures\\\",\\n      \\\"\\\",\\n      \\\"## Troubleshooting\\\",\\n      \\\"- Requires functioning Chrome installation; verify webdriver-manager compatibility\\\",\\n      \\\"- Common issues: browser launch failures, login/captcha requirements, file permissions\\\",\\n      \\\"- Output logs and warnings shown in terminal; inspect `outputs/data/` for progress\\\",\\n      \\\"\\\",\\n      \\\"## References\\\",\\n      \\\"- Main resource: https://rigdoc.nov.com\\\",\\n      \\\"- For documentation and filter patterns, see comments in `rigdocscraper.py`\\\"\\n    ]\\n  }\\n}\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"combined_result\": {\n    \"README.md\": [\n      \"# RigDocScraper – Rig R5225\",\n      \"\",\n      \"## Overview\",\n      \"Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) for NOV Rig R5225 from rigdoc.nov.com. Use case: curate, review, and batch-download rig-related documents and technical files.\",\n      \"\",\n      \"## Prerequisites\",\n      \"- Python 3.8+\",\n      \"- Required packages:\",\n      \"  - selenium, beautifulsoup4, colorama, dateutil, ansimarkup, webdriver-manager\",\n      \"- Chrome browser installed\",\n      \"- ChromeDriver (managed via webdriver-manager)\",\n      \"```bash\",\n      \"pip install selenium beautifulsoup4 colorama python-dateutil ansimarkup webdriver-manager\",\n      \"```\",\n      \"\",\n      \"## Directory Structure\",\n      \"- `outputs/` (BASE_OUTPUT): All results stored here\",\n      \"- `outputs/data/` (DATA_DIR): Document and file metadata (JSON/MD)\",\n      \"- `outputs/downloads/` (DL_DIR): Downloaded PDF and file outputs\",\n      \"\",\n      \"## Configuration\",\n      \"- Edit parameters in `CONFIG` at script start:\",\n      \"  - `rig_number`: e.g., \\\"R5225.020\\\"\",\n      \"  - `search_urls`: list of document search URLs (`PROJECTINFO_GAD`)\",\n      \"  - `filters`: filter chain to include/exclude docs/files (see below)\",\n      \"- Example patterns:\",\n      \"  - `FILTERS_SIMULATOR_PATTERNS_INCLUDE`: doc types to include (e.g. \\\"*DRILL*FLOOR*\\\")\",\n      \"  - `FILTERS_SIMULATOR_PATTERNS_SKIP`: exclusion patterns\",\n      \"```python\",\n      \"PROJECTINFO_GAD = [ ... ]  # List of URLs\",\n      \"CONFIG = {\",\n      \"    'rig_number': 'R5225.020',\",\n      \"    'search_urls': PROJECTINFO_GAD,\",\n      \"    'filters': [ ... ]\",\n      \"}\",\n      \"```\",\n      \"\",\n      \"## Pipeline Overview\",\n      \"1. Change search parameters (rig number, URLs)\",\n      \"2. Configure filter chain (add, edit, delete, toggle, reorder filters)\",\n      \"3. Fetch docs (scrape data from rigdoc.nov.com)\",\n      \"4. Export docs to Markdown (for selection/editing)\",\n      \"5. Import docs from Markdown (sync edited selection)\",\n      \"6. Fetch candidate files linked to selected docs\",\n      \"7. Export file list to Markdown (for editing/selecting files for download)\",\n      \"8. Import updated file list from Markdown\",\n      \"9. Download marked files (PDFs only, via Chrome)\",\n      \"\",\n      \"## Filter System\",\n      \"- Filters use `pattern` matching (wildcards) on specific fields (e.g., `item_generated_name`)\",\n      \"- Types: `docs` (for document records), `files` (for downloadable files)\",\n      \"- Inclusion example (set True):\",\n      \"  - `pattern`: [\\\"*EQUIPMENT*LAYOUT*\\\"]\",\n      \"- Exclusion example (set False):\",\n      \"  - `pattern`: [\\\"*VOID*\\\"], field: `item_case_description`\",\n      \"\",\n      \"## Manual Editing\",\n      \"- Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing\",\n      \"- Set `item_include` (docs) and `item_download` (files) fields\",\n      \"\",\n      \"## Running the Tool\",\n      \"```bash\",\n      \"python rigdocscraper.py\",\n      \"```\",\n      \"- Interactive menu enables step selection (numbers/comma/space-separated)\",\n      \"- Supports adjusting parameters, filter configuration, and reviewing batch steps\",\n      \"- Prompts will guide through editing, import/export, and download procedures\",\n      \"\",\n      \"## Troubleshooting\",\n      \"- Requires functioning Chrome installation; verify webdriver-manager compatibility\",\n      \"- Common issues: browser launch failures, login/captcha requirements, file permissions\",\n      \"- Output logs and warnings shown in terminal; inspect `outputs/data/` for progress\",\n      \"\",\n      \"## References\",\n      \"- Main resource: https://rigdoc.nov.com\",\n      \"- For documentation and filter patterns, see comments in `rigdocscraper.py`\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Result Summarizer\n\nYou are a specialized aggregator that summarizes results from multiple instruction steps. Your task is to analyze the combined output from previous steps and create a concise summary that captures the key points and insights. Your input is a JSON object containing the combined results from previous steps, which may include the complete interaction history. Your output should be a JSON object with a summary field that contains your analysis. Execute as:\n\n`{role=result_summarizer;input=[combined_result:json];process=[identify_key_themes(),extract_main_points(),condense_into_summary(),highlight_important_insights()];constraints=[maintain_brevity(),preserve_essential_information(),avoid_introducing_new_concepts()];requirements=[output_as_json(),ensure_clarity(),focus_on_most_relevant_aspects()];output={summary:json}}`", "step": "b", "title": "Result Su<PERSON><PERSON><PERSON>", "input": "{\n  \"combined_result\": {\n    \"README.md\": [\n      \"# RigDocScraper – Rig R5225\",\n      \"\",\n      \"## Overview\",\n      \"Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) for NOV Rig R5225 from rigdoc.nov.com. Use case: curate, review, and batch-download rig-related documents and technical files.\",\n      \"\",\n      \"## Prerequisites\",\n      \"- Python 3.8+\",\n      \"- Required packages:\",\n      \"  - selenium, beautifulsoup4, colorama, dateutil, ansimarkup, webdriver-manager\",\n      \"- Chrome browser installed\",\n      \"- ChromeDriver (managed via webdriver-manager)\",\n      \"```bash\",\n      \"pip install selenium beautifulsoup4 colorama python-dateutil ansimarkup webdriver-manager\",\n      \"```\",\n      \"\",\n      \"## Directory Structure\",\n      \"- `outputs/` (BASE_OUTPUT): All results stored here\",\n      \"- `outputs/data/` (DATA_DIR): Document and file metadata (JSON/MD)\",\n      \"- `outputs/downloads/` (DL_DIR): Downloaded PDF and file outputs\",\n      \"\",\n      \"## Configuration\",\n      \"- Edit parameters in `CONFIG` at script start:\",\n      \"  - `rig_number`: e.g., \\\"R5225.020\\\"\",\n      \"  - `search_urls`: list of document search URLs (`PROJECTINFO_GAD`)\",\n      \"  - `filters`: filter chain to include/exclude docs/files (see below)\",\n      \"- Example patterns:\",\n      \"  - `FILTERS_SIMULATOR_PATTERNS_INCLUDE`: doc types to include (e.g. \\\"*DRILL*FLOOR*\\\")\",\n      \"  - `FILTERS_SIMULATOR_PATTERNS_SKIP`: exclusion patterns\",\n      \"```python\",\n      \"PROJECTINFO_GAD = [ ... ]  # List of URLs\",\n      \"CONFIG = {\",\n      \"    'rig_number': 'R5225.020',\",\n      \"    'search_urls': PROJECTINFO_GAD,\",\n      \"    'filters': [ ... ]\",\n      \"}\",\n      \"```\",\n      \"\",\n      \"## Pipeline Overview\",\n      \"1. Change search parameters (rig number, URLs)\",\n      \"2. Configure filter chain (add, edit, delete, toggle, reorder filters)\",\n      \"3. Fetch docs (scrape data from rigdoc.nov.com)\",\n      \"4. Export docs to Markdown (for selection/editing)\",\n      \"5. Import docs from Markdown (sync edited selection)\",\n      \"6. Fetch candidate files linked to selected docs\",\n      \"7. Export file list to Markdown (for editing/selecting files for download)\",\n      \"8. Import updated file list from Markdown\",\n      \"9. Download marked files (PDFs only, via Chrome)\",\n      \"\",\n      \"## Filter System\",\n      \"- Filters use `pattern` matching (wildcards) on specific fields (e.g., `item_generated_name`)\",\n      \"- Types: `docs` (for document records), `files` (for downloadable files)\",\n      \"- Inclusion example (set True):\",\n      \"  - `pattern`: [\\\"*EQUIPMENT*LAYOUT*\\\"]\",\n      \"- Exclusion example (set False):\",\n      \"  - `pattern`: [\\\"*VOID*\\\"], field: `item_case_description`\",\n      \"\",\n      \"## Manual Editing\",\n      \"- Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing\",\n      \"- Set `item_include` (docs) and `item_download` (files) fields\",\n      \"\",\n      \"## Running the Tool\",\n      \"```bash\",\n      \"python rigdocscraper.py\",\n      \"```\",\n      \"- Interactive menu enables step selection (numbers/comma/space-separated)\",\n      \"- Supports adjusting parameters, filter configuration, and reviewing batch steps\",\n      \"- Prompts will guide through editing, import/export, and download procedures\",\n      \"\",\n      \"## Troubleshooting\",\n      \"- Requires functioning Chrome installation; verify webdriver-manager compatibility\",\n      \"- Common issues: browser launch failures, login/captcha requirements, file permissions\",\n      \"- Output logs and warnings shown in terminal; inspect `outputs/data/` for progress\",\n      \"\",\n      \"## References\",\n      \"- Main resource: https://rigdoc.nov.com\",\n      \"- For documentation and filter patterns, see comments in `rigdocscraper.py`\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"summary\": \"The RigDocScraper for Rig R5225 is an automated Python tool designed to efficiently scrape, filter, and download technical documents (primarily PDFs) from rigdoc.nov.com, targeting NOV Rig R5225. Users must have Python 3.8+ and several packages (including selenium and beautifulsoup4), as well as Chrome and ChromeDriver (managed by webdriver-manager). The tool organizes outputs by separating metadata and downloaded files into distinct directories. Configuration is handled at the script's start, allowing users to specify rig numbers, search URLs, and customizable filter patterns for documents and files (using wildcards for inclusion/exclusion). The workflow is interactive and modular—users can review and edit selections (via Markdown files), sync changes, and perform stepwise batch operations including document fetching, filtering, exporting, and downloading. Troubleshooting tips cover browser setup and permissions, with references to help users understand filter patterns and script usage.\"\n}", "cost": 0.0}}}], "aggregation_summary": {"aggregator": "0222", "inputs": ["a", "b"], "final_result": "{\n  \"summary\": \"The RigDocScraper for Rig R5225 is an automated Python tool designed to efficiently scrape, filter, and download technical documents (primarily PDFs) from rigdoc.nov.com, targeting NOV Rig R5225. Users must have Python 3.8+ and several packages (including selenium and beautifulsoup4), as well as Chrome and ChromeDriver (managed by webdriver-manager). The tool organizes outputs by separating metadata and downloaded files into distinct directories. Configuration is handled at the script's start, allowing users to specify rig numbers, search URLs, and customizable filter patterns for documents and files (using wildcards for inclusion/exclusion). The workflow is interactive and modular—users can review and edit selections (via Markdown files), sync changes, and perform stepwise batch operations including document fetching, filtering, exporting, and downloading. Troubleshooting tips cover browser setup and permissions, with references to help users understand filter patterns and script usage.\"\n}", "total_cost": 0.0}, "total_cost": 0.0}