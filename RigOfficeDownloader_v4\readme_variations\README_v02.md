# RigOfficeDownloader

## The Problem
Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:
- Tedious and repetitive
- Error-prone
- A poor use of skilled engineering time

## The Solution
RigOfficeDownloader automates the document retrieval process through a three-stage workflow:
1. **Document Retrieval**: Automatically scrapes document metadata
2. **File Metadata**: Fetches file information for selected documents
3. **Smart Downloads**: Downloads files with intelligent naming and organization

## How It Works
- Uses Selenium to automate web interactions with RigDoc
- Exports data to Markdown for user review and selection
- Applies configurable filters to pre-select relevant documents
- Organizes downloads with consistent naming patterns

## Getting Started
1. Run `py_venv_init.bat` to set up the environment
2. Run `RigOfficeDownloader-v4.bat` to launch the application
3. Follow the interactive menu prompts

## Impact
Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.
