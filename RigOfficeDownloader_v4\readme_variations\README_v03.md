# RigOfficeDownloader

## Technical Overview
RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.

## Architecture
- **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown
- **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads
- **File Organization**: Hierarchical naming system with metadata embedding and subfolder support

## Workflow Steps
1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information
2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing
3. **Selection Import**: `md_table_to_json()` imports user selections
4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents
5. **File Selection**: Export/import cycle for user selection of files
6. **Download Process**: `download_files()` retrieves selected files with smart naming

## Filter Chain System
Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.

## Setup Instructions
1. Run `py_venv_init.bat` to initialize the Python environment
2. Run `RigOfficeDownloader-v4.bat` to execute the application

## Version History
- v1: Basic document retrieval and download
- v2: JSON/Markdown conversion and user selection
- v3: Improved error handling and field organization
- v4: Subfolder support, filter chains, field ordering
