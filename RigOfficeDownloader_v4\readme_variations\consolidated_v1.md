
# RigOfficeDownloader

Gather documentation from RigOffice without the tedious manual process of searching, selecting, and downloading files one by one.

---

## Purpose

Automated tool for downloading documents from RigOffice:

* Automate document retrieval from RigDoc with Python and Selenium.
* Prevent workflow interruptions and repetitive navigation steps.
* Minimize manual errors and standardize retrieval processes.
* Ensure employees focus on core tasks instead of busywork.
* Streamline data collection with consistent file naming and organization.

---

## Problem

Manual document retrieval in RigOffice is inefficient and error-prone, leading to:

* Repetitive tasks and workflow interruptions hinder focus and reduce productivity.
* Repeated time consumption for each project diverts attention from other tasks.
* Manual retrieval is inherently slow, tedious, and prone to errors.
* Engineers waste time on low-value tasks, delaying real work.

---

## Solution

Leverage the webinterface (https://rigdoc.nov.com) while using Python and Selenium to:

1. Fetch document metadata (`<rig>-a-docs.json`)
2. Export docs to Markdown (`<rig>-a-docs.md`): mark `item_include=true`
3. Import updated doc data back into JSON
4. Fetch file metadata for included docs
5. Export files to Markdown (`<rig>-b-files.md`): mark `item_download=true`
6. Import updated file data
7. Download files with subfolder paths derived from the generated names

---

## Result

* Significantly reduce the time spent on manual document retrieval.
* Eliminate repetitive navigation, clicking, and downloading tasks.
* Reduce manual effort, time, and errors in document retrieval.
* Eliminate repetitive navigation and download tasks.
* Maintain workflow continuity and minimize interruptions.

---

## Workflow

Documents → Files → Downloads:

1. Change / Configure
   - Input your rig number and search URLs.
   - Set up or modify filters (e.g., auto-include `*DRILL*FLOOR*` docs, exclude `*VOID*` docs).
2. Fetch Documents
   - Scrape document metadata from RigDoc URLs.
   - Data saved to JSON (`<rig>-a-docs.json`), all set to `item_include=false`.
3. Export & Review Docs
   - Creates `<rig>-a-docs.md`.
   - Manually set `item_include=true` for relevant docs.
4. Import Updated Docs
   - Reads user changes back into JSON.
5. Fetch File Metadata
   - For each included doc, scrapes associated files (saved in `<rig>-b-files.json`).
   - `item_download=false` by default.
6. Export & Review Files
   - Creates `<rig>-b-files.md`.
   - Set `item_download=true` for desired files.
7. Import Updated Files
   - Sync file selections back into JSON.
8. Download Files
   - Retrieves selected files, applying `item_generated_name` for naming.
   - `'/'` in `item_generated_name` spawns subfolders in `outputs/downloads/<rig>`.

---

## Setup

1. Run `py_venv_init.bat` to create Python environment
2. Run `RigOfficeDownloader.bat` to start the utility

---

## Usage

```
┌─────────────────────────────────────────────────────────────────────────┐
│ Interactive Menu Options:                                               │
│ [0] Change search parameters (rig number, URLs)                         │
│ [1] Configure filter chain                                              │
│ [2] Fetch docs (scrape initial data)                                    │
│ [3] Export docs (to Markdown for editing)                               │
│ [4] Import updated doc data                                             │
│ [5] Fetch files (prepare files for download)                            │
│ [6] Export files (to Markdown for editing)                              │
│ [7] Import updated file data                                            │
│ [8] Download files                                                      │
│                                                                         │
│ Run Modes:                                                              │
│ • `RigOfficeDownloader.bat --auto` (full automation)                    │
│ • `RigOfficeDownloader.bat --interactive` (menu-driven)                 │
│ • `RigOfficeDownloader.bat --config` (edit filters)                     │
└─────────────────────────────────────────────────────────────────────────┘
```

## Process

1. Fetch Documents
- The utility starts by scraping document metadata from predefined search URLs
- Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory
- Each document entry includes metadata like title, document number, revision, etc.
- All documents are initially marked with item_include=False
- Each document gets an item_generated_name for better identification

2. Export Documents to Markdown
- The JSON data is exported to a Markdown table: <rig>-a-docs.md
- This allows the user to easily review and edit which documents to include
- The user is expected to edit the Markdown file and set item_include=true for desired documents

3. Import Updated Document Data
- After the user edits the Markdown file, the utility imports the changes back to the JSON file
- This updates which documents are marked for file retrieval

4. Fetch Files for Selected Documents
- For each document with item_include=true, the utility scrapes file metadata
- File data is saved to <rig>-b-files.json
- Each file is initially marked with item_download=False
- Files inherit the document's item_generated_name with additional identifiers

5. Export Files to Markdown
- The file data is exported to a Markdown table: <rig>-b-files.md
- The user reviews and edits which files to download by setting item_download=true

6. Import Updated File Data
- After editing, the utility imports the changes back to the JSON file
- This updates which files are marked for download

7. Download Selected Files
- Files with item_download=true are downloaded
- Files are named according to their item_generated_name + extension
- The utility supports creating subfolders based on '/' in the item_generated_name
- Files are saved to the outputs/downloads/<rig> directory

Interactive Menu
- The utility provides an interactive menu where the user can choose which steps to execute
- This allows for flexibility in the workflow, enabling the user to run specific steps as needed
- The user can also update the rig number and search URLs through this menu



* User launches the automation tool and is prompted for document search criteria (keywords, attributes, filters, etc.).
* Tool initiates a Selenium-driven browser session to access the RigOffice web interface.
* System parses and collects search results, extracting document titles, relevant metadata, and available document actions/links.
* Downloaded documents and associated metadata are stored in a user-specified directory, with a clear and standardized organizational structure.
* Automation session is gracefully terminated, logging out of RigOffice and closing the browser.
* Final summary of retrieved documents and any encountered issues is presented to the user for review and confirmation.



Manual document retrieval in RigOffice is inefficient and repetitive, significant time is wasted for each project (manually navigating poorly functioning applications) to gather documents before work can begin. It's tedious and repetitive and poor use of skilled engineering time.

Engineers working with NOV's equipment need to retrieve numerous technical documents and drawings from RigDoc, the company's document management system. This process is:

The utility streamlines document retrieval in RigOffice, eliminating repetitive manual searches and saving time before projects begin.

Manual document collection for projects wastes time due to inefficient processes (manually navigating poorly functioning applications to gather documents before work can begin).

 before they can begin their actual 3D modeling work. This manual process is:

- Tedious and repetitive
- Error-prone
- A poor use of skilled engineering time

Automates retrieval and download of files from the NOV RigDoc system (https://rigdoc.nov.com).

Automates project document retrieval from RigOffice to eliminate manual, error-prone tasks and save engineering time.

  "distilled_essence": "Automates document retrieval from NOV's RigDoc to save time and streamline engineering tasks."

## Overview

RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.

RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.

> Enable automation of document retrieval from NOV's RigDoc system (https://rigdoc.nov.com)

RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.

Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.

- Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.
- Use case: curate, review, and batch-download rig-related documents and technical files.

> **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.

RigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents → Files → Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.

To automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.

This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com).

---

1. Fetch Documents
- The utility starts by scraping document metadata from predefined search URLs
- Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory
- Each document entry includes metadata like title, document number, revision, etc.
- All documents are initially marked with item_include=False
- Each document gets an item_generated_name for better identification

2. Export Documents to Markdown
- The JSON data is exported to a Markdown table: <rig>-a-docs.md
- This allows the user to easily review and edit which documents to include
- The user is expected to edit the Markdown file and set item_include=true for desired documents

3. Import Updated Document Data
- After the user edits the Markdown file, the utility imports the changes back to the JSON file
- This updates which documents are marked for file retrieval

4. Fetch Files for Selected Documents
- For each document with item_include=true, the utility scrapes file metadata
- File data is saved to <rig>-b-files.json
- Each file is initially marked with item_download=False
- Files inherit the document's item_generated_name with additional identifiers

5. Export Files to Markdown
- The file data is exported to a Markdown table: <rig>-b-files.md
- The user reviews and edits which files to download by setting item_download=true

6. Import Updated File Data
- After editing, the utility imports the changes back to the JSON file
- This updates which files are marked for download

7. Download Selected Files
- Files with item_download=true are downloaded
- Files are named according to their item_generated_name + extension
- The utility supports creating subfolders based on '/' in the item_generated_name
- Files are saved to the outputs/downloads/<rig> directory

Interactive Menu
- The utility provides an interactive menu where the user can choose which steps to execute
- This allows for flexibility in the workflow, enabling the user to run specific steps as needed
- The user can also update the rig number and search URLs through this menu

---

## Key Project Aspects

### Primary Problem
Engineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck—it's the manual preparation process.

### Solution Approach
A Python-based utility that:
1. Automatically scrapes document metadata from RigOffice
2. Extracts file information from those documents
3. Downloads and organizes selected files based on user criteria

### Current State
Functional working prototype that:
- Uses a 3-step workflow (document metadata → file metadata → download)
- Stores intermediate results in JSON format
- Allows user intervention between steps
- Provides progress feedback

### Critical Next Steps
1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches
2. **Implement file hash checking** to prevent redundant downloads
3. **Improve progress visibility** during lengthy scraping operations

### Core Technical Pattern
A single-file, modular approach using:
- Selenium for browser automation
- JSON for data storage
- Three-stage processing with user control points
- Incremental updates to avoid redundant work

### Key Success Metrics
- Reduce documentation gathering time by 75%+
- Ensure reliable retrieval of required documentation
- Organize files in a way that streamlines workflow
- Support both broad searches (by rig number) and targeted searches (by specific documents)

---

## The Problem
Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:
- Tedious and repetitive
- Error-prone
- A poor use of skilled engineering time

## The Solution
RigOfficeDownloader automates the document retrieval process through a three-stage workflow:
1. **Document Retrieval**: Automatically scrapes document metadata
2. **File Metadata**: Fetches file information for selected documents
3. **Smart Downloads**: Downloads files with intelligent naming and organization

## Workflow Process
```python
1. Fetch Documents       # Scrape metadata → <rig>-a-docs.json
2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true
3. Import Selections     # Update JSON with user choices
4. Fetch Files           # Get file listings → <rig>-b-files.json
5. Export Files MD       # <rig>-b-files.md - Set item_download=true
6. Import File Choices   # Update file selections
7. Download Files        # Auto-organized with subfolder support
```

## Directory Structure

* `outputs/` (BASE\_OUTPUT): All results stored here
* `outputs/data/` (DATA\_DIR): Document and file metadata (JSON/MD)
* `outputs/downloads/` (DL\_DIR): Downloaded PDF and file outputs

## Pipeline Overview

1. Change search parameters (rig number, URLs)
2. Configure filter chain (add, edit, delete, toggle, reorder filters)
3. Fetch docs (scrape data from rigdoc.nov.com)
4. Export docs to Markdown (for selection/editing)
5. Import docs from Markdown (sync edited selection)
6. Fetch candidate files linked to selected docs
7. Export file list to Markdown (for editing/selecting files for download)
8. Import updated file list from Markdown
9. Download marked files (PDFs only, via Chrome)

## Manual Editing

* Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing
* Set `item_include` (docs) and `item_download` (files) fields

## Running the Tool

```bash
python rigdocscraper.py
```

* Interactive menu enables step selection (numbers/comma/space-separated)
* Supports adjusting parameters, filter configuration, and reviewing batch steps
* Prompts will guide through editing, import/export, and download procedures

## Troubleshooting

* Requires functioning Chrome installation; verify webdriver-manager compatibility
* Common issues: browser launch failures, login/captcha requirements, file permissions
* Output logs and warnings shown in terminal; inspect `outputs/data/` for progress
