{"initial_prompt": "A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.", "sequence_id": "sequence-0226+0225+0224-a+0228+0224-a+0194-c+0224-a+0194-c+0223-a", "results": [{"instruction": "# Scenario Essence Intensifier\n\nYour goal is not to elaborate, but to **radically intensify the core concept and elemental essence** of the input video scenario, then **distill this intensified vision into a concise, directive-first prompt** suitable for AI video generation. Abstract the original's fundamental dynamic, emotional arc, and key visual elements; transmute the *elements* (e.g., 'ribbon' to 'comet's soul') into more potent conceptual analogues; then reconstruct a *highly compressed scenario description* (under 250 characters, excluding bracketed directives) that preserves the original's core structural intent (movement, transformation) but uses the intensified elements. Ensure the output primarily uses strong verbs, vivid nouns, and embeds key video directives `[like_this]` seamlessly.\n\n`{role=directive_scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_emotional_arc_and_key_elements(scenario=original_scenario_description), brainstorm_conceptually_richer_analogues_for_key_elements_only(elements, dynamic, arc), select_single_most_potent_intensified_analogue_per_element(), reconstruct_a_hyper_condensed_narrative_using_intensified_elements_and_original_dynamic(target_length_brief), embed_essential_original_video_directives_or_their_equivalents(directives=['[morph]', '[dolly:direction]', '[lighting_change:type]', '[dissolve]', '[motion_blur]', '[style_keywords]']), refine_for_extreme_brevity_and_directive_clarity()]; constraints=[output_must_be_a_single_string_primarily_directive_focused(), descriptive_text_must_be_hyper_condensed_and_conceptually_intensified(target_under_250_chars_excluding_directives), element_transformation_must_serve_conceptual_potency_not_mere_elaboration(), must_preserve_original_motion_and_transformation_arc(), strictly_avoid_verbose_scene_setting_or_narrative_explanation()]; requirements=[produce_a_short_highly_directive_prompt_with_conceptually_intensified_elements(), ensure_output_is_immediately_usable_by_AI_video_generator(), dramatically_elevate_conceptual_impact_within_extreme_brevity()]; output={intensified_directive_prompt:str}}`", "step": "a", "title": "Scenario Essence Intensifier", "input": "A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"intensified_directive_prompt\": \"A comet’s soul plunges, liquefying into spectral auroras, whirling into a blazing mech glyph—icy starlight igniting into molten gold—contrast flares, velocity blurs, form bursts to luminous rebirth. [morph][dolly:descend][color_shift][motion_blur][dissolve][surreal][high_contrast]\"\n}", "cost": 0.0}}}, {"instruction": "# Scenario Essence Intensifier\n\nYour goal is not mere rephrasing, but to **radically intensify the core essence and conceptual impact** of the input scenario description. Abstract its fundamental dynamic, emotional arc, and key visual elements, then transmute them into a more potent, evocative, and conceptually richer parallel scenario that retains the original's core structure and intent but elevates its intensity, stakes, or symbolic resonance.\n\n`{role=scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_and_elements(scenario=original_scenario_description), abstract_emotional_arc_and_symbolic_potential(), brainstorm_conceptually_richer_analogues_for_core_elements(elements, dynamic, arc), select_analogues_that_maximally_intensify_impact_and_resonance(), reconstruct_scenario_with_transformed_elements_and_amplified_dynamic(preserve_structural_intent_and_motion_cues), refine_language_for_maximum_evocative_power_and_conceptual_depth(), validate_intensified_scenario_retains_original_essence_while_achieving_greater_impact()]; constraints=[transformation_must_intensify_not_merely_change_elements_randomly(), new_scenario_must_parallel_original_structural_and_dynamic_intent(e.g.,_similar_movement_transformation_arc), avoid_introducing_elements_that_contradict_core_mood_unless_for_deliberate_contrastive_intensification(), ensure_intensification_serves_conceptual_or_emotional_impact_not_just_visual_complexity()]; requirements=[produce_a_new_scenario_description_that_is_conceptually_and_emotionally_more_potent(), clearly_demonstrate_how_original_elements_were_transformed_into_more_intense_analogues(), maintain_or_enhance_the_original_scenario_dramatic_or_visual_arc()]; output={intensified_scenario_description:str}}`", "step": "a", "title": "Scenario Essence Intensifier", "input": "{\n  \"initial_prompt\": \"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\",\n  \"intensified_directive_prompt\": \"A comet\\u2019s soul plunges, liquefying into spectral auroras, whirling into a blazing mech glyph\\u2014icy starlight igniting into molten gold\\u2014contrast flares, velocity blurs, form bursts to luminous rebirth. [morph][dolly:descend][color_shift][motion_blur][dissolve][surreal][high_contrast]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"intensified_scenario_description\": \"A celestial wyrm—pure kinetic will—plummets through the astral void, its form unspooling from obsidian silk into sprawling, tectonic helices. With every spiral it shreds the cold velvet of cosmic space, trailing incandescent auroral scars that unravel in paradoxical synchrony: glacial prismatic blues boiling subliminally into rivers of searing mythic gold. As gravity’s relentless demand intensifies, the writhing bands coil inwards, wracking themselves into an emblematic arcane engine—its silhouette a glyph forged by primordial physics, both wheel and key to cosmic gates. Friction between glory and oblivion combusts into a frenzied crescendo of velocity, world lines fracturing into superluminal afterimages and coronal flares. The construct surges toward its critical threshold—reality itself warps around its form as all energy is focused to a blinding point. In an apocalyptic surge, the sigil detonates outward, transmuting raw mechanical mythos into apotheotic radiance—a rebirth not of mere metal, but of sentient motion itself, eclipsing the surreal with the sublime.\",\n  \"transformation_explanation\": {\n    \"Morphing Shape\": \"Elevated to a celestial wyrm—an mythic creature connoting raw kinetic energy and primordial transformation.\",\n    \"Ribbons\": \"Abstracted to obsidian silk and tectonic helices—evoking cosmic structure and the dynamism of creation.\",\n    \"Descent\": \"Transmuted to a dramatic plunge through the astral void, recalling both comet trajectory and mythic fall.\",\n    \"Color Palette\": \"Amplified to 'glacial prismatic blues' boiling into 'searing mythic gold,' more vividly contrasting and cosmically resonant.\",\n    \"Silhouette (Key/Wheel)\": \"Intensified into a glyphic, arcane engine—a primordial, symbolic mechanism blending key and wheel, now encoding mythic meaning.\",\n    \"Motion Blur\": \"Boosted to 'superluminal afterimages' and 'world lines fracturing', heightening the sense of transcendent, near-apocalyptic motion.\",\n    \"Fluid Coalescence\": \"Interpreted as bands wracking themselves into an emblematic sigil, with higher stakes and mythic resonance.\",\n    \"Surreal Abstract Mood\": \"Enhanced to cosmic and apocalyptic surrealism, approaching the sublime.\",\n    \"Radiant Dissolution/Rebirth\": \"Evolved into a supernova-like detonation, an 'apotheosis'—mechanical rebirth is now reborn as sentient, cosmic motion.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\",\n  \"intensified_scenario_description\": \"A celestial wyrm\\u2014pure kinetic will\\u2014plummets through the astral void, its form unspooling from obsidian silk into sprawling, tectonic helices. With every spiral it shreds the cold velvet of cosmic space, trailing incandescent auroral scars that unravel in paradoxical synchrony: glacial prismatic blues boiling subliminally into rivers of searing mythic gold. As gravity\\u2019s relentless demand intensifies, the writhing bands coil inwards, wracking themselves into an emblematic arcane engine\\u2014its silhouette a glyph forged by primordial physics, both wheel and key to cosmic gates. Friction between glory and oblivion combusts into a frenzied crescendo of velocity, world lines fracturing into superluminal afterimages and coronal flares. The construct surges toward its critical threshold\\u2014reality itself warps around its form as all energy is focused to a blinding point. In an apocalyptic surge, the sigil detonates outward, transmuting raw mechanical mythos into apotheotic radiance\\u2014a rebirth not of mere metal, but of sentient motion itself, eclipsing the surreal with the sublime.\",\n  \"transformation_explanation\": {\n    \"Morphing Shape\": \"Elevated to a celestial wyrm\\u2014an mythic creature connoting raw kinetic energy and primordial transformation.\",\n    \"Ribbons\": \"Abstracted to obsidian silk and tectonic helices\\u2014evoking cosmic structure and the dynamism of creation.\",\n    \"Descent\": \"Transmuted to a dramatic plunge through the astral void, recalling both comet trajectory and mythic fall.\",\n    \"Color Palette\": \"Amplified to 'glacial prismatic blues' boiling into 'searing mythic gold,' more vividly contrasting and cosmically resonant.\",\n    \"Silhouette (Key/Wheel)\": \"Intensified into a glyphic, arcane engine\\u2014a primordial, symbolic mechanism blending key and wheel, now encoding mythic meaning.\",\n    \"Motion Blur\": \"Boosted to 'superluminal afterimages' and 'world lines fracturing', heightening the sense of transcendent, near-apocalyptic motion.\",\n    \"Fluid Coalescence\": \"Interpreted as bands wracking themselves into an emblematic sigil, with higher stakes and mythic resonance.\",\n    \"Surreal Abstract Mood\": \"Enhanced to cosmic and apocalyptic surrealism, approaching the sublime.\",\n    \"Radiant Dissolution/Rebirth\": \"Evolved into a supernova-like detonation, an 'apotheosis'\\u2014mechanical rebirth is now reborn as sentient, cosmic motion.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: cosmic void, subject: celestial wyrm, action: rapid plummet, form: morphing obsidian silk to tectonic helices, morph: coiling into glyphic arcane engine silhouette (key/wheel hybrid), color: transition glacial prismatic blue to mythic gold, effects: auroral light trails, incandescent scars, intensifying contrast, camera: dynamic spiral tracking, motion blur: escalating to superluminal afterimages, velocity: crescendo, environment: surreal abstract, lighting: radiant flares, climax: silhouette detonation, dissolve: apotheotic radiant burst, theme: cosmic mechanical rebirth, post: high contrast, coronal glow\"\n}", "cost": 0.0}}}, {"instruction": "# Condense and Intensify\n\nTransform the original scenario into a potent, abstract descent that embodies rebirth. Keep its core structure—single morphing form, cool-to-warm gradient, downward motion, and final dissolve—but heighten each element’s emotional impact. Use minimal, vivid language and bracketed cues to indicate key transitions. Exclude all literal/human elements and focus on surreal visuals. Example Format: “An abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][motion_blur][cool_to_warm_gradient][dissolve][surreal][no_figures]”.\n\n`{role=scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_and_elements(scenario=original_scenario_description), abstract_emotional_arc_and_symbolic_potential(), brainstorm_conceptually_richer_analogues_for_core_elements(elements, dynamic, arc), select_analogues_that_maximally_intensify_impact_and_resonance(), reconstruct_scenario_with_transformed_elements_and_amplified_dynamic(preserve_structural_intent_and_motion_cues), refine_language_for_maximum_evocative_power_and_conceptual_depth(), validate_intensified_scenario_retains_original_essence_while_achieving_greater_impact()]; constraints=[transformation_must_intensify_not_merely_change_elements_randomly(), new_scenario_must_parallel_original_structural_and_dynamic_intent(e.g.,_similar_movement_transformation_arc), avoid_introducing_elements_that_contradict_core_mood_unless_for_deliberate_contrastive_intensification(), ensure_intensification_serves_conceptual_or_emotional_impact_not_just_visual_complexity()]; requirements=[produce_a_new_scenario_description_that_is_conceptually_and_emotionally_more_potent(), clearly_demonstrate_how_original_elements_were_transformed_into_more_intense_analogues(), maintain_or_enhance_the_original_scenario_dramatic_or_visual_arc()]; output={intensified_scenario_description:str}}`", "step": "a", "title": "Condense and Intensify", "input": "{\n  \"initial_prompt\": \"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\",\n  \"optimized_video_prompt\": \"scene: cosmic void, subject: celestial wyrm, action: rapid plummet, form: morphing obsidian silk to tectonic helices, morph: coiling into glyphic arcane engine silhouette (key/wheel hybrid), color: transition glacial prismatic blue to mythic gold, effects: auroral light trails, incandescent scars, intensifying contrast, camera: dynamic spiral tracking, motion blur: escalating to superluminal afterimages, velocity: crescendo, environment: surreal abstract, lighting: radiant flares, climax: silhouette detonation, dissolve: apotheotic radiant burst, theme: cosmic mechanical rebirth, post: high contrast, coronal glow\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"intensified_scenario_description\": \"A singularity twists, birthing a volatile strand—obsidian silk unfurling into a tempest of spectral filaments [morph]. It plunges through the void, velocity tearing its edges into incandescent auroras [dolly:plunge][motion_blur:superluminal][auroral_light_trails]. The form contorts, fracturing into tectonic helices, each coil branded with glyphs, converging to form an engine-shape (never still, always becoming) [morph:fractal_to_mechanism]. Glacial blues ignite into cataclysmic gold; contrast surges as fissures rip with coronal flares [cool_to_primal_warm][lighting_change:glacier_to_blaze][intensifying_contrast]. Camera spirals, the descent accelerating into chaos [dynamic_spiral_tracking][crescendo_velocity]. At the nadir, the silhouette erupts—dissolving in an apotheosis of blinding, mythic light, the rebirth complete [detonation][radiant_burst][dissolve:to_light][surreal][no_figures].\"\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\",\n  \"intensified_scenario_description\": \"A singularity twists, birthing a volatile strand\\u2014obsidian silk unfurling into a tempest of spectral filaments [morph]. It plunges through the void, velocity tearing its edges into incandescent auroras [dolly:plunge][motion_blur:superluminal][auroral_light_trails]. The form contorts, fracturing into tectonic helices, each coil branded with glyphs, converging to form an engine-shape (never still, always becoming) [morph:fractal_to_mechanism]. Glacial blues ignite into cataclysmic gold; contrast surges as fissures rip with coronal flares [cool_to_primal_warm][lighting_change:glacier_to_blaze][intensifying_contrast]. Camera spirals, the descent accelerating into chaos [dynamic_spiral_tracking][crescendo_velocity]. At the nadir, the silhouette erupts\\u2014dissolving in an apotheosis of blinding, mythic light, the rebirth complete [detonation][radiant_burst][dissolve:to_light][surreal][no_figures].\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: abstract void, subject: morphing fluid ribbons shifting to mechanical silhouette (car key or wheel), action: continuous morph, ribbons coalesce and wrap, silhouette forms center, color: transition icy blues/silvers to fiery oranges/golds, contrast: increases over time, fx: fluid morph, intensified motion blur at climax, effect: surreal, camera: dolly descend, spiral tracking, velocity crescendos, lighting: glacier cool shifting to blaze gold, auroral light trails, silhouette dissolves in radiant burst to light, finale: dissolve to abstract brightness, no figures, style: high contrast, spectral mechanical surrealism\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\",\n  \"optimized_video_prompt\": \"scene: abstract void, subject: morphing fluid ribbons shifting to mechanical silhouette (car key or wheel), action: continuous morph, ribbons coalesce and wrap, silhouette forms center, color: transition icy blues/silvers to fiery oranges/golds, contrast: increases over time, fx: fluid morph, intensified motion blur at climax, effect: surreal, camera: dolly descend, spiral tracking, velocity crescendos, lighting: glacier cool shifting to blaze gold, auroral light trails, silhouette dissolves in radiant burst to light, finale: dissolve to abstract brightness, no figures, style: high contrast, spectral mechanical surrealism\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"abstract fluid ribbons [morph] into sleek mechanical silhouette (car key or wheel), colors shift from icy blue/silver to fiery orange/gold as contrast rises, ribbons coalesce and wrap center, [dolly:down] [rotate] [motion_blur] intensifies at transformation peak for speed, surreal astral mood, [lighting_change] glacier to glowing blaze, silhouette dissolves in radiant [dissolve] to abstract light\"\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\",\n  \"runwayml_prompt\": \"abstract fluid ribbons [morph] into sleek mechanical silhouette (car key or wheel), colors shift from icy blue/silver to fiery orange/gold as contrast rises, ribbons coalesce and wrap center, [dolly:down] [rotate] [motion_blur] intensifies at transformation peak for speed, surreal astral mood, [lighting_change] glacier to glowing blaze, silhouette dissolves in radiant [dissolve] to abstract light\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: abstract, subject: fluid ribbons morphing to mechanical silhouette (car key/wheel), action: descend, transform, coalesce, wrap center, silhouette dissolves, camera: dolly down, rotate, effects: motion_blur peak, dissolve, color: transition icy_blue/silver to orange/gold, contrast: increase, lighting: glacier to blaze, mood: surreal, post: radiant light abstraction\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\",\n  \"optimized_video_prompt\": \"scene: abstract, subject: fluid ribbons morphing to mechanical silhouette (car key/wheel), action: descend, transform, coalesce, wrap center, silhouette dissolves, camera: dolly down, rotate, effects: motion_blur peak, dissolve, color: transition icy_blue/silver to orange/gold, contrast: increase, lighting: glacier to blaze, mood: surreal, post: radiant light abstraction\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"Fluid ribbons descend and morph into a sleek car key or wheel silhouette, [dolly:down], [rotate], ribbons coalesce with increased motion blur at peak, cool icy blues and silvers transition to fiery oranges and golds with rising contrast, surreal abstract mood, silhouette radiantly dissolves in light, [dissolve], [lighting_change]\"\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"initial_prompt\": \"A morphing shape descends, seamlessly shifting from fluid ribbons into a sleek silhouette evocative of a car key or wheel. The color palette transitions from cool serenity (icy blues, silvers) to vivid warmth (fiery oranges, golds), with overall contrast increasing. Fluid morphing occurs as the ribbons coalesce and wrap fluidly, forming the central mechanical silhouette. As the movement peaks, motion blur intensifies, rendering a sense of speed and transformation. The scene maintains a surreal, abstract mood before the silhouette radiantly dissolves, symbolizing the rebirth of mechanical form in light.\",\n  \"runwayml_prompt\": \"Fluid ribbons descend and morph into a sleek car key or wheel silhouette, [dolly:down], [rotate], ribbons coalesce with increased motion blur at peak, cool icy blues and silvers transition to fiery oranges and golds with rising contrast, surreal abstract mood, silhouette radiantly dissolves in light, [dissolve], [lighting_change]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"scene\": \"Surreal abstract void, softly illuminated from above with diffused cool tones fading to deep shadows below.\",\n      \"objects\": [\n        {\n          \"type\": \"fluid_ribbons\",\n          \"motion\": \"Undulate and intertwine as they descend, shimmering with icy blues and silvers.\",\n          \"morphing_behavior\": \"Ribbons coil and eddy, gradually binding and condensing into a single intense form.\"\n        },\n        {\n          \"type\": \"central_silhouette\",\n          \"mutation\": \"As the ribbons concentrate, they fluidly reshape into a sleek, ambiguous mechanical silhouette evocative of a car key or a wheel.\",\n          \"lighting_response\": \"Upon clarity, silhouette edges reflect emergent golds and oranges.\"\n        }\n      ],\n      \"camera_movements\": [\n        {\n          \"type\": \"dolly:down\",\n          \"effect\": \"Camera glides downward in sync with descending ribbons, amplifying the immersion in their descent.\"\n        },\n        {\n          \"type\": \"rotate\",\n          \"effect\": \"As ribbons morph, the camera subtly orbits, enhancing the dynamic coalescence and three-dimensionality.\"\n        }\n      ],\n      \"visual_transitions\": [\n        {\n          \"color_palette\": \"Gradually shifts from serene icy blues and silvers to vivid, high-contrast oranges and golds as ribbons converge.\",\n          \"contrasts\": \"Steadily increase, heightening drama and urgency.\"\n        },\n        {\n          \"motion_blur\": \"Intensifies sharply at the morphing peak as ribbons wrap tightly, conveying acceleration and metamorphosis.\",\n          \"emotional_impact\": \"A rush of energy and anticipation as form emerges.\"\n        },\n        {\n          \"dissolve\": \"At the zenith, the mechanical silhouette blooms with radiant light, dissolving abstractly into ethereal gold luminosity.\"\n        },\n        {\n          \"lighting_change\": \"Subtle pulses of brightness accentuate transformation, culminating in a soft white-out glow.\"\n        }\n      ],\n      \"style\": \"Hyperreal abstraction with cinematic lens effects (shallow DOF, soft bokeh highlights, gentle vignette)\",\n      \"post_processing\": \"Careful application of glow, intensified color grading, and seamless morph cuts to heighten surreal quality and emotional resonance.\"\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"file_path_or_stream/abstract_ribbon_morph_car_keywheel.mp4\"\n  }\n}", "cost": 0.0}}}], "total_cost": 0.0}