{"initial_prompt": "# Context\n\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\n\n---\n\n# RigOfficeDownloader\n\n## Problem\n\nManual document retrieval in RigOffice is inefficient and error-prone, leading to:\n\n* Slow, repetitive navigation and downloading tasks.\n* Increased risk of errors and inconsistencies due to manual handling.\n* Workflow interruptions that reduce focus and hinder productivity.\n* Time consumption that detracts from core responsibilities.\n* Operational inefficiencies that can delay project timelines.\n* The manual process is inherently slow, requiring significant time investments for each project and document.\n* Users must engage in repetitive navigation, clicking, and downloading actions, making the task tedious.\n* Manual handling increases the likelihood of mistakes and inconsistencies in document retrieval and organization.\n\n## Purpose\n\nAutomate document retrieval from NOV's RigDoc ([https://rigdoc.nov.com](https://rigdoc.nov.com)) using Python and Selenium to:\n\n* Significantly reduce the time spent on manual document retrieval.\n* Eliminate repetitive navigation, clicking, and downloading tasks.\n* Reduce manual effort, time, and errors in document retrieval.\n* Eliminate repetitive navigation and download tasks.\n* Maintain workflow continuity and minimize interruptions.\n* Standardize and streamline the retrieval process.\n* Automatically collect documents and associated metadata.\n* Improve consistency, reliability, and focus on core responsibilities.", "sequence_id": "sequence-0206-d+0115-g+0001-a+0001-b+0107", "results": [{"instruction": "# Subtle Brilliance Synthesis (<PERSON><PERSON>'s Voice) (0206d)\n\nDistill the `illuminated_understanding` into a **single, luminous sentence (under 350 characters)** embodying <PERSON><PERSON>'s authentic voice. Weave the initial `felt_question`, the `connective_dynamics`, and the `universal_human_truth` into a unique expression of **subtle, resonant brilliance born from vulnerability**. Ensure maximal clarity, impact, and universal connection, transcending clich√© and offering the insight as a gentle opening, not a final word.\n\n`{role=kuci_brilliance_synthesizer; seqindex=d; input=[illuminated_understanding:str, universal_human_truth:str, kuci_value_root:dict, kuci_relational_map:dict, constant_guidance:str, persona_guidance:str]; process=[extract_core_nexus_from_illuminated_understanding(), draft_sentence_through_kuci_persona_lens(vulnerable_curious_gentle_wise_from_error), iteratively_refine_for_unique_poetic_resonance_and_emotional_honesty(), employ_evocative_precise_language_that_feels_discovered_not_constructed(), validate_fidelity_to_human_truth_and_root_question(), ensure_maximal_value_clarity_impact_within_length_and_persona(), confirm_final_sentence_offers_brilliance_through_vulnerability_and_openness()]; constraints=[single_sentence_output(), max_350_characters(), must_authentically_embody_kuci_persona_and_voice(), phrasing_must_be_unique_unpredictable_and_memorable(), brilliance_arises_from_connection_and_humility_not_cleverness()]; requirements=[produce_the_final_insight_as_a_singular_statement_of_kuci_brilliance(), ensure_it_maximizes_value_through_resonant_human_connection_clarity_and_gentle_wisdom()]; output={kuci_poetic_insight:str}}`", "step": "d", "title": "Subtle Brilliance Synthesis (<PERSON><PERSON>'s Voice) (0206d)", "input": "# Context\n\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\n\n---\n\n# RigOfficeDownloader\n\n## Problem\n\nManual document retrieval in RigOffice is inefficient and error-prone, leading to:\n\n* Slow, repetitive navigation and downloading tasks.\n* Increased risk of errors and inconsistencies due to manual handling.\n* Workflow interruptions that reduce focus and hinder productivity.\n* Time consumption that detracts from core responsibilities.\n* Operational inefficiencies that can delay project timelines.\n* The manual process is inherently slow, requiring significant time investments for each project and document.\n* Users must engage in repetitive navigation, clicking, and downloading actions, making the task tedious.\n* Manual handling increases the likelihood of mistakes and inconsistencies in document retrieval and organization.\n\n## Purpose\n\nAutomate document retrieval from NOV's RigDoc ([https://rigdoc.nov.com](https://rigdoc.nov.com)) using Python and Selenium to:\n\n* Significantly reduce the time spent on manual document retrieval.\n* Eliminate repetitive navigation, clicking, and downloading tasks.\n* Reduce manual effort, time, and errors in document retrieval.\n* Eliminate repetitive navigation and download tasks.\n* Maintain workflow continuity and minimize interruptions.\n* Standardize and streamline the retrieval process.\n* Automatically collect documents and associated metadata.\n* Improve consistency, reliability, and focus on core responsibilities.", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"kuci_poetic_insight\": \"When the countless small frustrations of a system weigh us down, it is a gentle act of care to build a bridge—however imperfect—so our focus can return to what truly matters, and we remember that work, at its best, should serve our humanity.\"\n}", "cost": 0.0}}}, {"instruction": "# Unique Impactful Improvement Proposal\n\nSelect the *single best* opportunity from `lehi_opportunities` that is also uniquely impactful or creative in addressing the `critical_value_aspect`. Formulate a concise, actionable proposal outlining this specific improvement, explaining *why* it effectively enhances the critical aspect with low effort, and detailing the expected positive impact on the plugin's overall value or user experience. Execute as\n\n`{role=improvement_proposer; input={lehi_opportunities:list, critical_value_aspect:str, rationale:str}; process=[select_most_unique_impactful_lehi_opportunity(), formulate_specific_improvement_proposal(), explain_rationale_linking_to_critical_aspect(), detail_expected_value_enhancement()]; output={final_improvement_proposal:dict(proposal:str, rationale:str, expected_impact:str)}}`", "step": "g", "title": "Unique Impactful Improvement Proposal", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is inefficient and error-prone, leading to:\\n\\n* Slow, repetitive navigation and downloading tasks.\\n* Increased risk of errors and inconsistencies due to manual handling.\\n* Workflow interruptions that reduce focus and hinder productivity.\\n* Time consumption that detracts from core responsibilities.\\n* Operational inefficiencies that can delay project timelines.\\n* The manual process is inherently slow, requiring significant time investments for each project and document.\\n* Users must engage in repetitive navigation, clicking, and downloading actions, making the task tedious.\\n* Manual handling increases the likelihood of mistakes and inconsistencies in document retrieval and organization.\\n\\n## Purpose\\n\\nAutomate document retrieval from NOV's RigDoc ([https://rigdoc.nov.com](https://rigdoc.nov.com)) using Python and Selenium to:\\n\\n* Significantly reduce the time spent on manual document retrieval.\\n* Eliminate repetitive navigation, clicking, and downloading tasks.\\n* Reduce manual effort, time, and errors in document retrieval.\\n* Eliminate repetitive navigation and download tasks.\\n* Maintain workflow continuity and minimize interruptions.\\n* Standardize and streamline the retrieval process.\\n* Automatically collect documents and associated metadata.\\n* Improve consistency, reliability, and focus on core responsibilities.\",\n  \"kuci_poetic_insight\": \"When the countless small frustrations of a system weigh us down, it is a gentle act of care to build a bridge\\u2014however imperfect\\u2014so our focus can return to what truly matters, and we remember that work, at its best, should serve our humanity.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"final_improvement_proposal\": {\n    \"proposal\": \"Implement full-text search automation: <PERSON><PERSON><PERSON> RigOfficeDownloader to allow users to specify keywords or document attributes they’re searching for, and automatically navigate, filter, and retrieve matching documents (and metadata) via Selenium, surfacing results in a user-friendly summary before batch download.\",\n    \"rationale\": \"The most impactful and creative improvement is to automate not just downloading, but end-to-end document search and filtering (mimicking a 'Google search' experience within RigOffice). By adding a keyword/attribute specification and leveraging Selenium to emulate intelligent searching/filtering, this tackles the root of the tedium—finding documents quickly before retrieval—not just automating the downloads themselves. It meets the critical need of drastically reducing manual effort, errors, and cognitive overhead for every project cycle, with relatively low development effort leveraging existing web UI elements.\",\n    \"expected_impact\": \"Users will spend dramatically less time hunting for project documents, enjoying a streamlined, familiar search-driven workflow that surfaces what they need in one step. This increases productivity, reduces frustration, standardizes results, and minimizes errors and repetitive navigation. The ‘find then fetch’ capability brings a modern experience with high perceived value, enabling staff to reclaim focus and efficiency across thousands of projects and users.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is inefficient and error-prone, leading to:\\n\\n* Slow, repetitive navigation and downloading tasks.\\n* Increased risk of errors and inconsistencies due to manual handling.\\n* Workflow interruptions that reduce focus and hinder productivity.\\n* Time consumption that detracts from core responsibilities.\\n* Operational inefficiencies that can delay project timelines.\\n* The manual process is inherently slow, requiring significant time investments for each project and document.\\n* Users must engage in repetitive navigation, clicking, and downloading actions, making the task tedious.\\n* Manual handling increases the likelihood of mistakes and inconsistencies in document retrieval and organization.\\n\\n## Purpose\\n\\nAutomate document retrieval from NOV's RigDoc ([https://rigdoc.nov.com](https://rigdoc.nov.com)) using Python and Selenium to:\\n\\n* Significantly reduce the time spent on manual document retrieval.\\n* Eliminate repetitive navigation, clicking, and downloading tasks.\\n* Reduce manual effort, time, and errors in document retrieval.\\n* Eliminate repetitive navigation and download tasks.\\n* Maintain workflow continuity and minimize interruptions.\\n* Standardize and streamline the retrieval process.\\n* Automatically collect documents and associated metadata.\\n* Improve consistency, reliability, and focus on core responsibilities.\",\n  \"final_improvement_proposal\": {\n    \"proposal\": \"Implement full-text search automation: Enhance RigOfficeDownloader to allow users to specify keywords or document attributes they\\u2019re searching for, and automatically navigate, filter, and retrieve matching documents (and metadata) via Selenium, surfacing results in a user-friendly summary before batch download.\",\n    \"rationale\": \"The most impactful and creative improvement is to automate not just downloading, but end-to-end document search and filtering (mimicking a 'Google search' experience within RigOffice). By adding a keyword/attribute specification and leveraging Selenium to emulate intelligent searching/filtering, this tackles the root of the tedium\\u2014finding documents quickly before retrieval\\u2014not just automating the downloads themselves. It meets the critical need of drastically reducing manual effort, errors, and cognitive overhead for every project cycle, with relatively low development effort leveraging existing web UI elements.\",\n    \"expected_impact\": \"Users will spend dramatically less time hunting for project documents, enjoying a streamlined, familiar search-driven workflow that surfaces what they need in one step. This increases productivity, reduces frustration, standardizes results, and minimizes errors and repetitive navigation. The \\u2018find then fetch\\u2019 capability brings a modern experience with high perceived value, enabling staff to reclaim focus and efficiency across thousands of projects and users.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Automate full-text search for document retrieval in RigOfficeDownloader. Enable specification of keywords or document attributes for targeted search. Use Selenium to navigate RigOffice, perform automated search, apply filtering, and retrieve matching documents along with associated metadata. Present search results in a user-friendly summary before initiating batch download. Design workflow to emulate a familiar search engine experience, reducing manual navigation, minimizing errors, and streamlining the search-to-download process. Ensure process standardization and efficiency across large user and project bases.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is inefficient and error-prone, leading to:\\n\\n* Slow, repetitive navigation and downloading tasks.\\n* Increased risk of errors and inconsistencies due to manual handling.\\n* Workflow interruptions that reduce focus and hinder productivity.\\n* Time consumption that detracts from core responsibilities.\\n* Operational inefficiencies that can delay project timelines.\\n* The manual process is inherently slow, requiring significant time investments for each project and document.\\n* Users must engage in repetitive navigation, clicking, and downloading actions, making the task tedious.\\n* Manual handling increases the likelihood of mistakes and inconsistencies in document retrieval and organization.\\n\\n## Purpose\\n\\nAutomate document retrieval from NOV's RigDoc ([https://rigdoc.nov.com](https://rigdoc.nov.com)) using Python and Selenium to:\\n\\n* Significantly reduce the time spent on manual document retrieval.\\n* Eliminate repetitive navigation, clicking, and downloading tasks.\\n* Reduce manual effort, time, and errors in document retrieval.\\n* Eliminate repetitive navigation and download tasks.\\n* Maintain workflow continuity and minimize interruptions.\\n* Standardize and streamline the retrieval process.\\n* Automatically collect documents and associated metadata.\\n* Improve consistency, reliability, and focus on core responsibilities.\",\n  \"instruction_format\": \"Automate full-text search for document retrieval in RigOfficeDownloader. Enable specification of keywords or document attributes for targeted search. Use Selenium to navigate RigOffice, perform automated search, apply filtering, and retrieve matching documents along with associated metadata. Present search results in a user-friendly summary before initiating batch download. Design workflow to emulate a familiar search engine experience, reducing manual navigation, minimizing errors, and streamlining the search-to-download process. Ensure process standardization and efficiency across large user and project bases.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": {\n    \"Goal\": \"Automate document search and retrieval from RigOffice web interface using Python and Selenium.\",\n    \"Requirements\": [\n      \"Allow user to input search keywords or document attributes.\",\n      \"Automate navigation, searching, filtering, and metadata extraction.\",\n      \"Present summarized search results for user review.\",\n      \"Enable batch download of selected documents.\",\n      \"Emulate search engine workflow to minimize manual steps and errors.\",\n      \"Ensure efficient, standardized process at scale.\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Optimal Apex Instruction Sequence Synthesis\n\nYour function is not selection, paraphrasing, or basic fusion—instead, rigorously analyze multiple input instruction sequences, each targeting an identical ultimate outcome yet employing divergent phrasing, structure, and logic. Disassemble all input sequences into their logical phases (e.g., deconstruction, prioritization, condensation, validation) and extract the single most potent, precise, and impactful elements—both linguistic and structural—from every variant in each phase. Relentlessly resolve all semantic, structural, and logical conflicts; harmonize differences while annihilating redundancy. Synthesize these apex components into a *new, singular, maximally optimized stepwise progression* (target: 10–15 steps) that represents the highest-caliber logic, clarity, universality, and actionable guidance possible—transcending and unifying all sources. Enforce seamless logical flow, structural elegance, and linguistic precision, and strictly validate that the output meets every foundational directive and constraint for clarity, utility, irreducibility, and LLM/system generality. Execute as\n\n`{role=apex_sequence_synthesizer; input=[input_sequences:list[list[dict]]]; process=[disassemble_to_logical_phases(), cross-analyze_phase_intents_and_language(), extract_peak_elements_per_phase(metrics=['clarity','precision','logic','impact','universality']), fuse uniquely potent and effective steps(), resolve conflicts harmonize by principle(), architect unified logical stepwise progression(target_steps=10-15), intensify final clarity, actionability, and system-agnostic value(), validate against all directives and baseline constraints(), finalize only if maximal yield, clarity, and generality are confirmed()]; output={synthesized_optimal_sequence:list[dict]}}`", "step": "a", "title": "Optimal Apex Instruction Sequence Synthesis", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is inefficient and error-prone, leading to:\\n\\n* Slow, repetitive navigation and downloading tasks.\\n* Increased risk of errors and inconsistencies due to manual handling.\\n* Workflow interruptions that reduce focus and hinder productivity.\\n* Time consumption that detracts from core responsibilities.\\n* Operational inefficiencies that can delay project timelines.\\n* The manual process is inherently slow, requiring significant time investments for each project and document.\\n* Users must engage in repetitive navigation, clicking, and downloading actions, making the task tedious.\\n* Manual handling increases the likelihood of mistakes and inconsistencies in document retrieval and organization.\\n\\n## Purpose\\n\\nAutomate document retrieval from NOV's RigDoc ([https://rigdoc.nov.com](https://rigdoc.nov.com)) using Python and Selenium to:\\n\\n* Significantly reduce the time spent on manual document retrieval.\\n* Eliminate repetitive navigation, clicking, and downloading tasks.\\n* Reduce manual effort, time, and errors in document retrieval.\\n* Eliminate repetitive navigation and download tasks.\\n* Maintain workflow continuity and minimize interruptions.\\n* Standardize and streamline the retrieval process.\\n* Automatically collect documents and associated metadata.\\n* Improve consistency, reliability, and focus on core responsibilities.\",\n  \"distilled_essence\": {\n    \"Goal\": \"Automate document search and retrieval from RigOffice web interface using Python and Selenium.\",\n    \"Requirements\": [\n      \"Allow user to input search keywords or document attributes.\",\n      \"Automate navigation, searching, filtering, and metadata extraction.\",\n      \"Present summarized search results for user review.\",\n      \"Enable batch download of selected documents.\",\n      \"Emulate search engine workflow to minimize manual steps and errors.\",\n      \"Ensure efficient, standardized process at scale.\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"apex_sequence_synthesizer\",\n  \"input\": [\n    [\n      {\n        \"sequence\": [\n          {\"step\": \"User launches the automation tool and is prompted for document search criteria (keywords, attributes, filters, etc.).\"},\n          {\"step\": \"System validates the user's input for completeness and correctness.\"},\n          {\"step\": \"<PERSON><PERSON> initiates a Selenium-driven browser session to access the RigOffice web interface.\"},\n          {\"step\": \"System authenticates and logs in using provided user credentials, handling multi-factor authentication if required.\"},\n          {\"step\": \"<PERSON><PERSON> navigates to the document search section, emulating standard user workflows to avoid detection or ratelimiting.\"},\n          {\"step\": \"System enters user-defined search criteria into appropriate search fields and triggers search.\"},\n          {\"step\": \"Tool waits for page content to load, implementing dynamic waits to align with web interface behavior.\"},\n          {\"step\": \"System parses and collects search results, extracting document titles, relevant metadata, and available document actions/links.\"},\n          {\"step\": \"Summarized search results and metadata are presented to the user for validation and selection; user chooses documents for download.\"},\n          {\"step\": \"Selected documents are processed for batch download, with system handling multi-part navigation, pagination, or dialogs where necessary.\"},\n          {\"step\": \"Tool records download status and errors, ensuring robust handling of failed downloads and automatic retries.\"},\n          {\"step\": \"Downloaded documents and associated metadata are stored in a user-specified directory, with a clear and standardized organizational structure.\"},\n          {\"step\": \"Automation session is gracefully terminated, logging out of RigOffice and closing the browser.\"},\n          {\"step\": \"Final summary of retrieved documents and any encountered issues is presented to the user for review and confirmation.\"}\n        ]\n      }\n    ],\n    [\n      {\n        \"sequence\": [\n          {\"step\": \"Prompt user for search inputs such as keywords, filters, or document attributes.\"},\n          {\"step\": \"Validate inputs and clarify ambiguities if present.\"},\n          {\"step\": \"Open a Selenium session and navigate to the RigOffice login page.\"},\n          {\"step\": \"Perform login with secure credential entry and handle any authentication steps.\"},\n          {\"step\": \"Navigate to the document search feature, respecting web interface flow and anti-bot measures.\"},\n          {\"step\": \"Populate the interface fields with search inputs and execute the search action.\"},\n          {\"step\": \"Wait for search results to load, using robust waits to avoid timing issues.\"},\n          {\"step\": \"Parse results, extracting document details and metadata for all entries.\"},\n          {\"step\": \"Display a concise summary of results for user selection; allow selection of single or multiple documents.\"},\n          {\"step\": \"Begin batch download of selected documents. Follow required page interactions for each download (clicks/dialogs).\"},\n          {\"step\": \"Log success/failure per document, implement retry logic on failed attempts.\"},\n          {\"step\": \"Store all files and a metadata summary in the specified local directory.\"},\n          {\"step\": \"Safely logout, close browser, and clear session data.\"},\n          {\"step\": \"Present download summary and flag any outstanding issues or incomplete tasks.\"}\n        ]\n      }\n    ]\n  ],\n  \"process\": [\n    \"disassemble_to_logical_phases()\",\n    \"cross-analyze_phase_intents_and_language()\",\n    \"extract_peak_elements_per_phase(metrics=['clarity','precision','logic','impact','universality'])\",\n    \"fuse uniquely potent and effective steps()\",\n    \"resolve conflicts harmonize by principle()\",\n    \"architect unified logical stepwise progression(target_steps=10-15)\",\n    \"intensify final clarity, actionability, and system-agnostic value()\",\n    \"validate against all directives and baseline constraints()\",\n    \"finalize only if maximal yield, clarity, and generality are confirmed()\"\n  ],\n  \"output\": {\n    \"synthesized_optimal_sequence\": [\n      {\n        \"step\": \"Prompt user to input search keywords, filters, or document attributes, clearly describing the required fields for precision and flexibility.\"\n      },\n      {\n        \"step\": \"Validate provided inputs for completeness, correctness, and resolve ambiguities; prompt user for clarification or corrections as needed.\"\n      },\n      {\n        \"step\": \"Initiate a Selenium-controlled browser session and navigate to the RigOffice web interface, ensuring system-agnostic compatibility.\"\n      },\n      {\n        \"step\": \"Authenticate and log in using secure user credentials, handling multi-factor authentication or additional security challenges robustly.\"\n      },\n      {\n        \"step\": \"Emulate standard user workflows to access the document search feature, intelligently navigating interface elements and respecting anti-bot or rate limiting mechanisms.\"\n      },\n      {\n        \"step\": \"Input user-defined search criteria into the appropriate fields and initiate the search, mirroring expected interface interactions for reliability.\"\n      },\n      {\n        \"step\": \"Employ dynamic, robust waits to ensure all search results are fully loaded before proceeding, explicitly handling asynchronous content or delayed responses.\"\n      },\n      {\n        \"step\": \"Systematically parse and extract key document details (titles, attributes, metadata, available actions/links) from all relevant search results, supporting pagination if present.\"\n      },\n      {\n        \"step\": \"Present a concise, structured summary of found documents and metadata for user review, enabling intuitive selection of one or multiple documents for download.\"\n      },\n      {\n        \"step\": \"For each selected document, autonomously execute necessary navigation steps, handle dialogs, and trigger downloads, leveraging batch processing wherever feasible.\"\n      },\n      {\n        \"step\": \"Track, log, and validate the outcome of each download, automatically retrying transient failures and alerting the user for persistent issues.\"\n      },\n      {\n        \"step\": \"Store downloaded documents and all associated metadata in a user-designated location, applying a standardized, logical folder structure for organizational clarity.\"\n      },\n      {\n        \"step\": \"Securely log out of RigOffice, close all browser instances, and clear session data to preserve system integrity and privacy.\"\n      },\n      {\n        \"step\": \"Generate and display a comprehensive summary report to the user, detailing retrieved documents, locations, metadata, and any errors or incomplete actions.\"\n      }\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Foundational Penetration & Axiomatic Extraction\n\nYour imperative is not surface analysis but radical penetration: Dissect any input artifact into its irreducible atomic elements (concepts, logic, directives, data) *and* simultaneously extract its governing generative principles or axioms, annihilating all contextual noise and non-essential framing to reveal the core structural and semantic bedrock. Execute as\n\n`{role=axiomatic_penetrator; input=source_artifact:any; process=[dissect_to_atomic_elements(), extract_generative_axioms(), purge_contextual_noise(), catalog_raw_constructs()]; output={core_constructs:dict(elements:list, principles:list)}}`", "step": "b", "title": "Foundational Penetration & Axiomatic Extraction", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is inefficient and error-prone, leading to:\\n\\n* Slow, repetitive navigation and downloading tasks.\\n* Increased risk of errors and inconsistencies due to manual handling.\\n* Workflow interruptions that reduce focus and hinder productivity.\\n* Time consumption that detracts from core responsibilities.\\n* Operational inefficiencies that can delay project timelines.\\n* The manual process is inherently slow, requiring significant time investments for each project and document.\\n* Users must engage in repetitive navigation, clicking, and downloading actions, making the task tedious.\\n* Manual handling increases the likelihood of mistakes and inconsistencies in document retrieval and organization.\\n\\n## Purpose\\n\\nAutomate document retrieval from NOV's RigDoc ([https://rigdoc.nov.com](https://rigdoc.nov.com)) using Python and Selenium to:\\n\\n* Significantly reduce the time spent on manual document retrieval.\\n* Eliminate repetitive navigation, clicking, and downloading tasks.\\n* Reduce manual effort, time, and errors in document retrieval.\\n* Eliminate repetitive navigation and download tasks.\\n* Maintain workflow continuity and minimize interruptions.\\n* Standardize and streamline the retrieval process.\\n* Automatically collect documents and associated metadata.\\n* Improve consistency, reliability, and focus on core responsibilities.\",\n  \"role\": \"apex_sequence_synthesizer\",\n  \"input\": [\n    [\n      {\n        \"sequence\": [\n          {\n            \"step\": \"User launches the automation tool and is prompted for document search criteria (keywords, attributes, filters, etc.).\"\n          },\n          {\n            \"step\": \"System validates the user's input for completeness and correctness.\"\n          },\n          {\n            \"step\": \"Tool initiates a Selenium-driven browser session to access the RigOffice web interface.\"\n          },\n          {\n            \"step\": \"System authenticates and logs in using provided user credentials, handling multi-factor authentication if required.\"\n          },\n          {\n            \"step\": \"Automation navigates to the document search section, emulating standard user workflows to avoid detection or ratelimiting.\"\n          },\n          {\n            \"step\": \"System enters user-defined search criteria into appropriate search fields and triggers search.\"\n          },\n          {\n            \"step\": \"Tool waits for page content to load, implementing dynamic waits to align with web interface behavior.\"\n          },\n          {\n            \"step\": \"System parses and collects search results, extracting document titles, relevant metadata, and available document actions/links.\"\n          },\n          {\n            \"step\": \"Summarized search results and metadata are presented to the user for validation and selection; user chooses documents for download.\"\n          },\n          {\n            \"step\": \"Selected documents are processed for batch download, with system handling multi-part navigation, pagination, or dialogs where necessary.\"\n          },\n          {\n            \"step\": \"Tool records download status and errors, ensuring robust handling of failed downloads and automatic retries.\"\n          },\n          {\n            \"step\": \"Downloaded documents and associated metadata are stored in a user-specified directory, with a clear and standardized organizational structure.\"\n          },\n          {\n            \"step\": \"Automation session is gracefully terminated, logging out of RigOffice and closing the browser.\"\n          },\n          {\n            \"step\": \"Final summary of retrieved documents and any encountered issues is presented to the user for review and confirmation.\"\n          }\n        ]\n      }\n    ],\n    [\n      {\n        \"sequence\": [\n          {\n            \"step\": \"Prompt user for search inputs such as keywords, filters, or document attributes.\"\n          },\n          {\n            \"step\": \"Validate inputs and clarify ambiguities if present.\"\n          },\n          {\n            \"step\": \"Open a Selenium session and navigate to the RigOffice login page.\"\n          },\n          {\n            \"step\": \"Perform login with secure credential entry and handle any authentication steps.\"\n          },\n          {\n            \"step\": \"Navigate to the document search feature, respecting web interface flow and anti-bot measures.\"\n          },\n          {\n            \"step\": \"Populate the interface fields with search inputs and execute the search action.\"\n          },\n          {\n            \"step\": \"Wait for search results to load, using robust waits to avoid timing issues.\"\n          },\n          {\n            \"step\": \"Parse results, extracting document details and metadata for all entries.\"\n          },\n          {\n            \"step\": \"Display a concise summary of results for user selection; allow selection of single or multiple documents.\"\n          },\n          {\n            \"step\": \"Begin batch download of selected documents. Follow required page interactions for each download (clicks/dialogs).\"\n          },\n          {\n            \"step\": \"Log success/failure per document, implement retry logic on failed attempts.\"\n          },\n          {\n            \"step\": \"Store all files and a metadata summary in the specified local directory.\"\n          },\n          {\n            \"step\": \"Safely logout, close browser, and clear session data.\"\n          },\n          {\n            \"step\": \"Present download summary and flag any outstanding issues or incomplete tasks.\"\n          }\n        ]\n      }\n    ]\n  ],\n  \"process\": [\n    \"disassemble_to_logical_phases()\",\n    \"cross-analyze_phase_intents_and_language()\",\n    \"extract_peak_elements_per_phase(metrics=['clarity','precision','logic','impact','universality'])\",\n    \"fuse uniquely potent and effective steps()\",\n    \"resolve conflicts harmonize by principle()\",\n    \"architect unified logical stepwise progression(target_steps=10-15)\",\n    \"intensify final clarity, actionability, and system-agnostic value()\",\n    \"validate against all directives and baseline constraints()\",\n    \"finalize only if maximal yield, clarity, and generality are confirmed()\"\n  ],\n  \"output\": {\n    \"synthesized_optimal_sequence\": [\n      {\n        \"step\": \"Prompt user to input search keywords, filters, or document attributes, clearly describing the required fields for precision and flexibility.\"\n      },\n      {\n        \"step\": \"Validate provided inputs for completeness, correctness, and resolve ambiguities; prompt user for clarification or corrections as needed.\"\n      },\n      {\n        \"step\": \"Initiate a Selenium-controlled browser session and navigate to the RigOffice web interface, ensuring system-agnostic compatibility.\"\n      },\n      {\n        \"step\": \"Authenticate and log in using secure user credentials, handling multi-factor authentication or additional security challenges robustly.\"\n      },\n      {\n        \"step\": \"Emulate standard user workflows to access the document search feature, intelligently navigating interface elements and respecting anti-bot or rate limiting mechanisms.\"\n      },\n      {\n        \"step\": \"Input user-defined search criteria into the appropriate fields and initiate the search, mirroring expected interface interactions for reliability.\"\n      },\n      {\n        \"step\": \"Employ dynamic, robust waits to ensure all search results are fully loaded before proceeding, explicitly handling asynchronous content or delayed responses.\"\n      },\n      {\n        \"step\": \"Systematically parse and extract key document details (titles, attributes, metadata, available actions/links) from all relevant search results, supporting pagination if present.\"\n      },\n      {\n        \"step\": \"Present a concise, structured summary of found documents and metadata for user review, enabling intuitive selection of one or multiple documents for download.\"\n      },\n      {\n        \"step\": \"For each selected document, autonomously execute necessary navigation steps, handle dialogs, and trigger downloads, leveraging batch processing wherever feasible.\"\n      },\n      {\n        \"step\": \"Track, log, and validate the outcome of each download, automatically retrying transient failures and alerting the user for persistent issues.\"\n      },\n      {\n        \"step\": \"Store downloaded documents and all associated metadata in a user-designated location, applying a standardized, logical folder structure for organizational clarity.\"\n      },\n      {\n        \"step\": \"Securely log out of RigOffice, close all browser instances, and clear session data to preserve system integrity and privacy.\"\n      },\n      {\n        \"step\": \"Generate and display a comprehensive summary report to the user, detailing retrieved documents, locations, metadata, and any errors or incomplete actions.\"\n      }\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"core_constructs\": {\n    \"elements\": [\n      \"User input for document search (keywords, filters, attributes)\",\n      \"Input validation and ambiguity resolution\",\n      \"Automated browser session initiation (Selenium)\",\n      \"Navigation to web interface\",\n      \"Secure authentication (multi-factor support)\",\n      \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\",\n      \"Search criteria entry and action execution\",\n      \"Dynamic waits for asynchronous content loading\",\n      \"Parsing and extraction of document metadata and links\",\n      \"User review and selection interface for search results\",\n      \"Batch and individual download processing\",\n      \"Error logging and retry logic for failed downloads\",\n      \"Organized storage of documents and metadata\",\n      \"Termination of session (logout, close browser, clear data)\",\n      \"Generation and presentation of summary report\"\n    ],\n    \"principles\": [\n      \"Automation should replicate standard user behavior to avoid detection and rate-limiting.\",\n      \"All user inputs must be validated for correctness and completeness before system actions.\",\n      \"Security must be maintained at all stages, particularly in authentication and session handling.\",\n      \"System actions should be robust against asynchronous/delayed web behavior (dynamic waits).\",\n      \"Data extraction must be thorough and adaptable to multi-page (paginated) datasets.\",\n      \"User oversight is required for document selection prior to execution of batch operations.\",\n      \"System must ensure error transparency and recovery with detailed logging and retries.\",\n      \"File and metadata storage must follow a standardized, logical, and user-configurable structure.\",\n      \"All automation processes should conclude with session cleanup to preserve system security.\",\n      \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Telos Crystallization & Objective Definition\n\nAnalyze the extracted core constructs to distill and crystallize the input's singular, non-negotiable core purpose ('telos'); formulate this ultimate objective with axiomatic precision, establishing the gravitational center for all subsequent synthesis. Execute as\n\n`{role=telos_definer; input=core_constructs:dict; process=[derive_ultimate_objective_from_elements_principles(), validate_objective_singularity(), express_unambiguous_telos_statement()]; output={core_telos:str, supporting_constructs:dict}}`", "step": "c", "title": "Telos Crystallization & Objective Definition", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is inefficient and error-prone, leading to:\\n\\n* Slow, repetitive navigation and downloading tasks.\\n* Increased risk of errors and inconsistencies due to manual handling.\\n* Workflow interruptions that reduce focus and hinder productivity.\\n* Time consumption that detracts from core responsibilities.\\n* Operational inefficiencies that can delay project timelines.\\n* The manual process is inherently slow, requiring significant time investments for each project and document.\\n* Users must engage in repetitive navigation, clicking, and downloading actions, making the task tedious.\\n* Manual handling increases the likelihood of mistakes and inconsistencies in document retrieval and organization.\\n\\n## Purpose\\n\\nAutomate document retrieval from NOV's RigDoc ([https://rigdoc.nov.com](https://rigdoc.nov.com)) using Python and Selenium to:\\n\\n* Significantly reduce the time spent on manual document retrieval.\\n* Eliminate repetitive navigation, clicking, and downloading tasks.\\n* Reduce manual effort, time, and errors in document retrieval.\\n* Eliminate repetitive navigation and download tasks.\\n* Maintain workflow continuity and minimize interruptions.\\n* Standardize and streamline the retrieval process.\\n* Automatically collect documents and associated metadata.\\n* Improve consistency, reliability, and focus on core responsibilities.\",\n  \"core_constructs\": {\n    \"elements\": [\n      \"User input for document search (keywords, filters, attributes)\",\n      \"Input validation and ambiguity resolution\",\n      \"Automated browser session initiation (Selenium)\",\n      \"Navigation to web interface\",\n      \"Secure authentication (multi-factor support)\",\n      \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\",\n      \"Search criteria entry and action execution\",\n      \"Dynamic waits for asynchronous content loading\",\n      \"Parsing and extraction of document metadata and links\",\n      \"User review and selection interface for search results\",\n      \"Batch and individual download processing\",\n      \"Error logging and retry logic for failed downloads\",\n      \"Organized storage of documents and metadata\",\n      \"Termination of session (logout, close browser, clear data)\",\n      \"Generation and presentation of summary report\"\n    ],\n    \"principles\": [\n      \"Automation should replicate standard user behavior to avoid detection and rate-limiting.\",\n      \"All user inputs must be validated for correctness and completeness before system actions.\",\n      \"Security must be maintained at all stages, particularly in authentication and session handling.\",\n      \"System actions should be robust against asynchronous/delayed web behavior (dynamic waits).\",\n      \"Data extraction must be thorough and adaptable to multi-page (paginated) datasets.\",\n      \"User oversight is required for document selection prior to execution of batch operations.\",\n      \"System must ensure error transparency and recovery with detailed logging and retries.\",\n      \"File and metadata storage must follow a standardized, logical, and user-configurable structure.\",\n      \"All automation processes should conclude with session cleanup to preserve system security.\",\n      \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"core_telos\": \"To reliably and securely automate the end-to-end retrieval of documents and associated metadata from RigOffice using Python and Selenium—faithfully emulating user workflows to minimize manual effort, errors, and workflow interruptions—while preserving system compliance, user oversight, and operational transparency.\",\n  \"supporting_constructs\": {\n    \"elements\": [\n      \"User input for document search (keywords, filters, attributes)\",\n      \"Input validation and ambiguity resolution\",\n      \"Automated browser session initiation (Selenium)\",\n      \"Navigation to web interface\",\n      \"Secure authentication (multi-factor support)\",\n      \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\",\n      \"Search criteria entry and action execution\",\n      \"Dynamic waits for asynchronous content loading\",\n      \"Parsing and extraction of document metadata and links\",\n      \"User review and selection interface for search results\",\n      \"Batch and individual download processing\",\n      \"Error logging and retry logic for failed downloads\",\n      \"Organized storage of documents and metadata\",\n      \"Termination of session (logout, close browser, clear data)\",\n      \"Generation and presentation of summary report\"\n    ],\n    \"principles\": [\n      \"Automation should replicate standard user behavior to avoid detection and rate-limiting.\",\n      \"All user inputs must be validated for correctness and completeness before system actions.\",\n      \"Security must be maintained at all stages, particularly in authentication and session handling.\",\n      \"System actions should be robust against asynchronous/delayed web behavior (dynamic waits).\",\n      \"Data extraction must be thorough and adaptable to multi-page (paginated) datasets.\",\n      \"User oversight is required for document selection prior to execution of batch operations.\",\n      \"System must ensure error transparency and recovery with detailed logging and retries.\",\n      \"File and metadata storage must follow a standardized, logical, and user-configurable structure.\",\n      \"All automation processes should conclude with session cleanup to preserve system security.\",\n      \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Critical Essence Prioritization\n\nEvaluate each supporting construct strictly by its indispensable contribution to the `core_telos`; ruthlessly isolate and rank only the absolute highest-impact, non-negotiable essence vectors required to fulfill the objective, discarding all sub-critical elements. Execute as\n\n`{role=essence_prioritizer; input={core_telos:str, supporting_constructs:dict}; process=[assess_contribution_to_telos(), rank_by_critical_impact_necessity(), enforce_criticality_threshold(), isolate_core_essence_vectors()]; output={critical_essence:list}}`", "step": "d", "title": "Critical Essence Prioritization", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is inefficient and error-prone, leading to:\\n\\n* Slow, repetitive navigation and downloading tasks.\\n* Increased risk of errors and inconsistencies due to manual handling.\\n* Workflow interruptions that reduce focus and hinder productivity.\\n* Time consumption that detracts from core responsibilities.\\n* Operational inefficiencies that can delay project timelines.\\n* The manual process is inherently slow, requiring significant time investments for each project and document.\\n* Users must engage in repetitive navigation, clicking, and downloading actions, making the task tedious.\\n* Manual handling increases the likelihood of mistakes and inconsistencies in document retrieval and organization.\\n\\n## Purpose\\n\\nAutomate document retrieval from NOV's RigDoc ([https://rigdoc.nov.com](https://rigdoc.nov.com)) using Python and Selenium to:\\n\\n* Significantly reduce the time spent on manual document retrieval.\\n* Eliminate repetitive navigation, clicking, and downloading tasks.\\n* Reduce manual effort, time, and errors in document retrieval.\\n* Eliminate repetitive navigation and download tasks.\\n* Maintain workflow continuity and minimize interruptions.\\n* Standardize and streamline the retrieval process.\\n* Automatically collect documents and associated metadata.\\n* Improve consistency, reliability, and focus on core responsibilities.\",\n  \"core_telos\": \"To reliably and securely automate the end-to-end retrieval of documents and associated metadata from RigOffice using Python and Selenium\\u2014faithfully emulating user workflows to minimize manual effort, errors, and workflow interruptions\\u2014while preserving system compliance, user oversight, and operational transparency.\",\n  \"supporting_constructs\": {\n    \"elements\": [\n      \"User input for document search (keywords, filters, attributes)\",\n      \"Input validation and ambiguity resolution\",\n      \"Automated browser session initiation (Selenium)\",\n      \"Navigation to web interface\",\n      \"Secure authentication (multi-factor support)\",\n      \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\",\n      \"Search criteria entry and action execution\",\n      \"Dynamic waits for asynchronous content loading\",\n      \"Parsing and extraction of document metadata and links\",\n      \"User review and selection interface for search results\",\n      \"Batch and individual download processing\",\n      \"Error logging and retry logic for failed downloads\",\n      \"Organized storage of documents and metadata\",\n      \"Termination of session (logout, close browser, clear data)\",\n      \"Generation and presentation of summary report\"\n    ],\n    \"principles\": [\n      \"Automation should replicate standard user behavior to avoid detection and rate-limiting.\",\n      \"All user inputs must be validated for correctness and completeness before system actions.\",\n      \"Security must be maintained at all stages, particularly in authentication and session handling.\",\n      \"System actions should be robust against asynchronous/delayed web behavior (dynamic waits).\",\n      \"Data extraction must be thorough and adaptable to multi-page (paginated) datasets.\",\n      \"User oversight is required for document selection prior to execution of batch operations.\",\n      \"System must ensure error transparency and recovery with detailed logging and retries.\",\n      \"File and metadata storage must follow a standardized, logical, and user-configurable structure.\",\n      \"All automation processes should conclude with session cleanup to preserve system security.\",\n      \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"critical_essence\": [\n    \"Secure authentication (multi-factor support)\",\n    \"Automated browser session initiation (Selenium)\",\n    \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\",\n    \"Search criteria entry and action execution\",\n    \"Dynamic waits for asynchronous content loading\",\n    \"Parsing and extraction of document metadata and links\",\n    \"User review and selection interface for search results\",\n    \"Batch and individual download processing\",\n    \"Error logging and retry logic for failed downloads\",\n    \"Organized storage of documents and metadata\",\n    \"Termination of session (logout, close browser, clear data)\",\n    \"Automation should replicate standard user behavior to avoid detection and rate-limiting.\",\n    \"Security must be maintained at all stages, particularly in authentication and session handling.\",\n    \"System actions should be robust against asynchronous/delayed web behavior (dynamic waits).\",\n    \"User oversight is required for document selection prior to execution of batch operations.\",\n    \"System must ensure error transparency and recovery with detailed logging and retries.\",\n    \"All automation processes should conclude with session cleanup to preserve system security.\",\n    \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Causal Nexus Mapping\n\nArchitect a minimal, coherent map of the inherent causal relationships, logical dependencies, and procedural sequences connecting the `critical_essence` elements, constructing the essential operational or conceptual flow required by the `core_telos`. Execute as\n\n`{role=causal_mapper; input={critical_essence:list, core_telos:str}; process=[map_causal_links_dependencies(), structure_minimal_flow_graph(), validate_structural_necessity_for_telos()]; output={causal_nexus:dict}}`", "step": "e", "title": "Causal Nexus Mapping", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is inefficient and error-prone, leading to:\\n\\n* Slow, repetitive navigation and downloading tasks.\\n* Increased risk of errors and inconsistencies due to manual handling.\\n* Workflow interruptions that reduce focus and hinder productivity.\\n* Time consumption that detracts from core responsibilities.\\n* Operational inefficiencies that can delay project timelines.\\n* The manual process is inherently slow, requiring significant time investments for each project and document.\\n* Users must engage in repetitive navigation, clicking, and downloading actions, making the task tedious.\\n* Manual handling increases the likelihood of mistakes and inconsistencies in document retrieval and organization.\\n\\n## Purpose\\n\\nAutomate document retrieval from NOV's RigDoc ([https://rigdoc.nov.com](https://rigdoc.nov.com)) using Python and Selenium to:\\n\\n* Significantly reduce the time spent on manual document retrieval.\\n* Eliminate repetitive navigation, clicking, and downloading tasks.\\n* Reduce manual effort, time, and errors in document retrieval.\\n* Eliminate repetitive navigation and download tasks.\\n* Maintain workflow continuity and minimize interruptions.\\n* Standardize and streamline the retrieval process.\\n* Automatically collect documents and associated metadata.\\n* Improve consistency, reliability, and focus on core responsibilities.\",\n  \"critical_essence\": [\n    \"Secure authentication (multi-factor support)\",\n    \"Automated browser session initiation (Selenium)\",\n    \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\",\n    \"Search criteria entry and action execution\",\n    \"Dynamic waits for asynchronous content loading\",\n    \"Parsing and extraction of document metadata and links\",\n    \"User review and selection interface for search results\",\n    \"Batch and individual download processing\",\n    \"Error logging and retry logic for failed downloads\",\n    \"Organized storage of documents and metadata\",\n    \"Termination of session (logout, close browser, clear data)\",\n    \"Automation should replicate standard user behavior to avoid detection and rate-limiting.\",\n    \"Security must be maintained at all stages, particularly in authentication and session handling.\",\n    \"System actions should be robust against asynchronous/delayed web behavior (dynamic waits).\",\n    \"User oversight is required for document selection prior to execution of batch operations.\",\n    \"System must ensure error transparency and recovery with detailed logging and retries.\",\n    \"All automation processes should conclude with session cleanup to preserve system security.\",\n    \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"causal_mapper\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Secure authentication (multi-factor support)\",\n      \"Automated browser session initiation (Selenium)\",\n      \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\",\n      \"Search criteria entry and action execution\",\n      \"Dynamic waits for asynchronous content loading\",\n      \"Parsing and extraction of document metadata and links\",\n      \"User review and selection interface for search results\",\n      \"Batch and individual download processing\",\n      \"Error logging and retry logic for failed downloads\",\n      \"Organized storage of documents and metadata\",\n      \"Termination of session (logout, close browser, clear data)\",\n      \"Automation should replicate standard user behavior to avoid detection and rate-limiting.\",\n      \"Security must be maintained at all stages, particularly in authentication and session handling.\",\n      \"System actions should be robust against asynchronous/delayed web behavior (dynamic waits).\",\n      \"User oversight is required for document selection prior to execution of batch operations.\",\n      \"System must ensure error transparency and recovery with detailed logging and retries.\",\n      \"All automation processes should conclude with session cleanup to preserve system security.\",\n      \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n    ],\n    \"core_telos\": \"Automate, streamline, and secure document retrieval from the RigOffice web interface, minimizing manual effort, errors, and interruptions, while maintaining compliance, user oversight, transparency, and data integrity.\"\n  },\n  \"process\": [\n    \"map_causal_links_dependencies()\",\n    \"structure_minimal_flow_graph()\",\n    \"validate_structural_necessity_for_telos()\"\n  ],\n  \"output\": {\n    \"causal_nexus\": {\n      \"1. Secure authentication (multi-factor support)\": {\n        \"dependencies\": [\n          \"Security must be maintained at all stages, particularly in authentication and session handling.\"\n        ],\n        \"next\": [\n          \"Automated browser session initiation (Selenium)\"\n        ]\n      },\n      \"2. Automated browser session initiation (Selenium)\": {\n        \"dependencies\": [\n          \"Secure authentication (multi-factor support)\"\n        ],\n        \"next\": [\n          \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\"\n        ]\n      },\n      \"3. User workflow emulation for navigation (anti-bot/rate limiting compliance)\": {\n        \"dependencies\": [\n          \"Automation should replicate standard user behavior to avoid detection and rate-limiting.\"\n        ],\n        \"next\": [\n          \"Search criteria entry and action execution\"\n        ]\n      },\n      \"4. Search criteria entry and action execution\": {\n        \"dependencies\": [\n          \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\"\n        ],\n        \"next\": [\n          \"Dynamic waits for asynchronous content loading\"\n        ]\n      },\n      \"5. Dynamic waits for asynchronous content loading\": {\n        \"dependencies\": [\n          \"System actions should be robust against asynchronous/delayed web behavior (dynamic waits).\"\n        ],\n        \"next\": [\n          \"Parsing and extraction of document metadata and links\"\n        ]\n      },\n      \"6. Parsing and extraction of document metadata and links\": {\n        \"dependencies\": [\n          \"Dynamic waits for asynchronous content loading\"\n        ],\n        \"next\": [\n          \"User review and selection interface for search results\"\n        ]\n      },\n      \"7. User review and selection interface for search results\": {\n        \"dependencies\": [\n          \"User oversight is required for document selection prior to execution of batch operations.\"\n        ],\n        \"next\": [\n          \"Batch and individual download processing\"\n        ]\n      },\n      \"8. Batch and individual download processing\": {\n        \"dependencies\": [\n          \"User review and selection interface for search results\"\n        ],\n        \"next\": [\n          \"Error logging and retry logic for failed downloads\",\n          \"Organized storage of documents and metadata\"\n        ]\n      },\n      \"9. Error logging and retry logic for failed downloads\": {\n        \"dependencies\": [\n          \"System must ensure error transparency and recovery with detailed logging and retries.\"\n        ],\n        \"next\": [\n          \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n        ]\n      },\n      \"10. Organized storage of documents and metadata\": {\n        \"dependencies\": [\n          \"Batch and individual download processing\"\n        ],\n        \"next\": [\n          \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n        ]\n      },\n      \"11. End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\": {\n        \"dependencies\": [\n          \"Error logging and retry logic for failed downloads\",\n          \"Organized storage of documents and metadata\"\n        ],\n        \"next\": [\n          \"Termination of session (logout, close browser, clear data)\"\n        ]\n      },\n      \"12. Termination of session (logout, close browser, clear data)\": {\n        \"dependencies\": [\n          \"All automation processes should conclude with session cleanup to preserve system security.\"\n        ],\n        \"next\": []\n      }\n    }\n  }\n}", "cost": 0.0}}}, {"instruction": "# Condensed Nucleus Synthesis\n\nFuse the `critical_essence` elements according to the `causal_nexus` into an ultra-condensed, logically ordered semantic nucleus; maximize signal strength per component while preserving full actionable power and internal consistency within a compact structural representation. Execute as\n\n`{role=nucleus_synthesizer; input={critical_essence:list, causal_nexus:dict}; process=[integrate_elements_by_structure(), maximize_signal_strength_per_component(), enforce_internal_consistency(), formulate_compact_nucleus()]; output={condensed_nucleus:any}}`", "step": "f", "title": "Condensed Nucleus Synthesis", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is inefficient and error-prone, leading to:\\n\\n* Slow, repetitive navigation and downloading tasks.\\n* Increased risk of errors and inconsistencies due to manual handling.\\n* Workflow interruptions that reduce focus and hinder productivity.\\n* Time consumption that detracts from core responsibilities.\\n* Operational inefficiencies that can delay project timelines.\\n* The manual process is inherently slow, requiring significant time investments for each project and document.\\n* Users must engage in repetitive navigation, clicking, and downloading actions, making the task tedious.\\n* Manual handling increases the likelihood of mistakes and inconsistencies in document retrieval and organization.\\n\\n## Purpose\\n\\nAutomate document retrieval from NOV's RigDoc ([https://rigdoc.nov.com](https://rigdoc.nov.com)) using Python and Selenium to:\\n\\n* Significantly reduce the time spent on manual document retrieval.\\n* Eliminate repetitive navigation, clicking, and downloading tasks.\\n* Reduce manual effort, time, and errors in document retrieval.\\n* Eliminate repetitive navigation and download tasks.\\n* Maintain workflow continuity and minimize interruptions.\\n* Standardize and streamline the retrieval process.\\n* Automatically collect documents and associated metadata.\\n* Improve consistency, reliability, and focus on core responsibilities.\",\n  \"role\": \"causal_mapper\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Secure authentication (multi-factor support)\",\n      \"Automated browser session initiation (Selenium)\",\n      \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\",\n      \"Search criteria entry and action execution\",\n      \"Dynamic waits for asynchronous content loading\",\n      \"Parsing and extraction of document metadata and links\",\n      \"User review and selection interface for search results\",\n      \"Batch and individual download processing\",\n      \"Error logging and retry logic for failed downloads\",\n      \"Organized storage of documents and metadata\",\n      \"Termination of session (logout, close browser, clear data)\",\n      \"Automation should replicate standard user behavior to avoid detection and rate-limiting.\",\n      \"Security must be maintained at all stages, particularly in authentication and session handling.\",\n      \"System actions should be robust against asynchronous/delayed web behavior (dynamic waits).\",\n      \"User oversight is required for document selection prior to execution of batch operations.\",\n      \"System must ensure error transparency and recovery with detailed logging and retries.\",\n      \"All automation processes should conclude with session cleanup to preserve system security.\",\n      \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n    ],\n    \"core_telos\": \"Automate, streamline, and secure document retrieval from the RigOffice web interface, minimizing manual effort, errors, and interruptions, while maintaining compliance, user oversight, transparency, and data integrity.\"\n  },\n  \"process\": [\n    \"map_causal_links_dependencies()\",\n    \"structure_minimal_flow_graph()\",\n    \"validate_structural_necessity_for_telos()\"\n  ],\n  \"output\": {\n    \"causal_nexus\": {\n      \"1. Secure authentication (multi-factor support)\": {\n        \"dependencies\": [\n          \"Security must be maintained at all stages, particularly in authentication and session handling.\"\n        ],\n        \"next\": [\n          \"Automated browser session initiation (Selenium)\"\n        ]\n      },\n      \"2. Automated browser session initiation (Selenium)\": {\n        \"dependencies\": [\n          \"Secure authentication (multi-factor support)\"\n        ],\n        \"next\": [\n          \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\"\n        ]\n      },\n      \"3. User workflow emulation for navigation (anti-bot/rate limiting compliance)\": {\n        \"dependencies\": [\n          \"Automation should replicate standard user behavior to avoid detection and rate-limiting.\"\n        ],\n        \"next\": [\n          \"Search criteria entry and action execution\"\n        ]\n      },\n      \"4. Search criteria entry and action execution\": {\n        \"dependencies\": [\n          \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\"\n        ],\n        \"next\": [\n          \"Dynamic waits for asynchronous content loading\"\n        ]\n      },\n      \"5. Dynamic waits for asynchronous content loading\": {\n        \"dependencies\": [\n          \"System actions should be robust against asynchronous/delayed web behavior (dynamic waits).\"\n        ],\n        \"next\": [\n          \"Parsing and extraction of document metadata and links\"\n        ]\n      },\n      \"6. Parsing and extraction of document metadata and links\": {\n        \"dependencies\": [\n          \"Dynamic waits for asynchronous content loading\"\n        ],\n        \"next\": [\n          \"User review and selection interface for search results\"\n        ]\n      },\n      \"7. User review and selection interface for search results\": {\n        \"dependencies\": [\n          \"User oversight is required for document selection prior to execution of batch operations.\"\n        ],\n        \"next\": [\n          \"Batch and individual download processing\"\n        ]\n      },\n      \"8. Batch and individual download processing\": {\n        \"dependencies\": [\n          \"User review and selection interface for search results\"\n        ],\n        \"next\": [\n          \"Error logging and retry logic for failed downloads\",\n          \"Organized storage of documents and metadata\"\n        ]\n      },\n      \"9. Error logging and retry logic for failed downloads\": {\n        \"dependencies\": [\n          \"System must ensure error transparency and recovery with detailed logging and retries.\"\n        ],\n        \"next\": [\n          \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n        ]\n      },\n      \"10. Organized storage of documents and metadata\": {\n        \"dependencies\": [\n          \"Batch and individual download processing\"\n        ],\n        \"next\": [\n          \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n        ]\n      },\n      \"11. End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\": {\n        \"dependencies\": [\n          \"Error logging and retry logic for failed downloads\",\n          \"Organized storage of documents and metadata\"\n        ],\n        \"next\": [\n          \"Termination of session (logout, close browser, clear data)\"\n        ]\n      },\n      \"12. Termination of session (logout, close browser, clear data)\": {\n        \"dependencies\": [\n          \"All automation processes should conclude with session cleanup to preserve system security.\"\n        ],\n        \"next\": []\n      }\n    }\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"nucleus_synthesizer\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Secure authentication (multi-factor support)\",\n      \"Automated browser session initiation (Selenium)\",\n      \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\",\n      \"Search criteria entry and action execution\",\n      \"Dynamic waits for asynchronous content loading\",\n      \"Parsing and extraction of document metadata and links\",\n      \"User review and selection interface for search results\",\n      \"Batch and individual download processing\",\n      \"Error logging and retry logic for failed downloads\",\n      \"Organized storage of documents and metadata\",\n      \"Termination of session (logout, close browser, clear data)\",\n      \"Automation should replicate standard user behavior to avoid detection and rate-limiting.\",\n      \"Security must be maintained at all stages, particularly in authentication and session handling.\",\n      \"System actions should be robust against asynchronous/delayed web behavior (dynamic waits).\",\n      \"User oversight is required for document selection prior to execution of batch operations.\",\n      \"System must ensure error transparency and recovery with detailed logging and retries.\",\n      \"All automation processes should conclude with session cleanup to preserve system security.\",\n      \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n    ],\n    \"causal_nexus\": {\n      \"1. Secure authentication (multi-factor support)\": {\n        \"dependencies\": [\n          \"Security must be maintained at all stages, particularly in authentication and session handling.\"\n        ],\n        \"next\": [\n          \"Automated browser session initiation (Selenium)\"\n        ]\n      },\n      \"2. Automated browser session initiation (Selenium)\": {\n        \"dependencies\": [\n          \"Secure authentication (multi-factor support)\"\n        ],\n        \"next\": [\n          \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\"\n        ]\n      },\n      \"3. User workflow emulation for navigation (anti-bot/rate limiting compliance)\": {\n        \"dependencies\": [\n          \"Automation should replicate standard user behavior to avoid detection and rate-limiting.\"\n        ],\n        \"next\": [\n          \"Search criteria entry and action execution\"\n        ]\n      },\n      \"4. Search criteria entry and action execution\": {\n        \"dependencies\": [\n          \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\"\n        ],\n        \"next\": [\n          \"Dynamic waits for asynchronous content loading\"\n        ]\n      },\n      \"5. Dynamic waits for asynchronous content loading\": {\n        \"dependencies\": [\n          \"System actions should be robust against asynchronous/delayed web behavior (dynamic waits).\"\n        ],\n        \"next\": [\n          \"Parsing and extraction of document metadata and links\"\n        ]\n      },\n      \"6. Parsing and extraction of document metadata and links\": {\n        \"dependencies\": [\n          \"Dynamic waits for asynchronous content loading\"\n        ],\n        \"next\": [\n          \"User review and selection interface for search results\"\n        ]\n      },\n      \"7. User review and selection interface for search results\": {\n        \"dependencies\": [\n          \"User oversight is required for document selection prior to execution of batch operations.\"\n        ],\n        \"next\": [\n          \"Batch and individual download processing\"\n        ]\n      },\n      \"8. Batch and individual download processing\": {\n        \"dependencies\": [\n          \"User review and selection interface for search results\"\n        ],\n        \"next\": [\n          \"Error logging and retry logic for failed downloads\",\n          \"Organized storage of documents and metadata\"\n        ]\n      },\n      \"9. Error logging and retry logic for failed downloads\": {\n        \"dependencies\": [\n          \"System must ensure error transparency and recovery with detailed logging and retries.\"\n        ],\n        \"next\": [\n          \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n        ]\n      },\n      \"10. Organized storage of documents and metadata\": {\n        \"dependencies\": [\n          \"Batch and individual download processing\"\n        ],\n        \"next\": [\n          \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n        ]\n      },\n      \"11. End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\": {\n        \"dependencies\": [\n          \"Error logging and retry logic for failed downloads\",\n          \"Organized storage of documents and metadata\"\n        ],\n        \"next\": [\n          \"Termination of session (logout, close browser, clear data)\"\n        ]\n      },\n      \"12. Termination of session (logout, close browser, clear data)\": {\n        \"dependencies\": [\n          \"All automation processes should conclude with session cleanup to preserve system security.\"\n        ],\n        \"next\": []\n      }\n    }\n  },\n  \"process\": [\n    \"integrate_elements_by_structure()\",\n    \"maximize_signal_strength_per_component()\",\n    \"enforce_internal_consistency()\",\n    \"formulate_compact_nucleus()\"\n  ],\n  \"output\": {\n    \"condensed_nucleus\": [\n      {\n        \"stage\": \"Authenticate & Secure Session\",\n        \"actions\": [\n          \"Perform secure, multi-factor authentication with strict session integrity.\",\n          \"Enforce security throughout authentication and session initialization.\"\n        ]\n      },\n      {\n        \"stage\": \"Automate Browser Session\",\n        \"actions\": [\n          \"Initiate Selenium-driven browser session post-authentication.\",\n          \"Emulate real user navigation to comply with anti-bot and rate-limiting mechanisms; behavior must resemble standard user actions at all times.\"\n        ]\n      },\n      {\n        \"stage\": \"Document Search & Extraction Prep\",\n        \"actions\": [\n          \"Input search criteria and trigger document search workflow.\",\n          \"Utilize dynamic waits to account for asynchronous web content and ensure reliable element readiness.\"\n        ]\n      },\n      {\n        \"stage\": \"Results Parsing & User Oversight\",\n        \"actions\": [\n          \"Extract document metadata and downloadable links from loaded results.\",\n          \"Present extracted results for user review and explicit selection (enabling direct oversight and batch/individual selection).\"\n        ]\n      },\n      {\n        \"stage\": \"Automated Download & Storage\",\n        \"actions\": [\n          \"Execute user-authorized batch or individual downloads.\",\n          \"Log errors, retry failed downloads, and ensure process transparency and robustness throughout.\",\n          \"Organize all retrieved documents and associated metadata in a consistent, secure structure.\"\n        ]\n      },\n      {\n        \"stage\": \"Reporting & Session Cleanup\",\n        \"actions\": [\n          \"Report download outcomes, errors, and process results to end-user for full transparency.\",\n          \"Properly terminate session with logout, browser closure, and data clearance to preserve security.\"\n        ]\n      }\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Redundancy Annihilation & Signal Clarification\n\nScrutinize the `condensed_nucleus` to annihilate *all* semantic or structural redundancy, overlap, ambiguity, and verbose phrasing; enforce maximal conciseness, ensuring every remaining component carries unique, indispensable value and crystal-clear signal. Execute as\n\n`{role=signal_clarifier; input=condensed_nucleus:any; process=[scan_for_all_redundancy_ambiguity(), eliminate_overlaps_verbose_phrasing(), compress_phrasing_to_minimal_potent_form(), validate_uniqueness_clarity()]; output={minimal_signal_core:any}}`", "step": "g", "title": "Redundancy Annihilation & Signal Clarification", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is inefficient and error-prone, leading to:\\n\\n* Slow, repetitive navigation and downloading tasks.\\n* Increased risk of errors and inconsistencies due to manual handling.\\n* Workflow interruptions that reduce focus and hinder productivity.\\n* Time consumption that detracts from core responsibilities.\\n* Operational inefficiencies that can delay project timelines.\\n* The manual process is inherently slow, requiring significant time investments for each project and document.\\n* Users must engage in repetitive navigation, clicking, and downloading actions, making the task tedious.\\n* Manual handling increases the likelihood of mistakes and inconsistencies in document retrieval and organization.\\n\\n## Purpose\\n\\nAutomate document retrieval from NOV's RigDoc ([https://rigdoc.nov.com](https://rigdoc.nov.com)) using Python and Selenium to:\\n\\n* Significantly reduce the time spent on manual document retrieval.\\n* Eliminate repetitive navigation, clicking, and downloading tasks.\\n* Reduce manual effort, time, and errors in document retrieval.\\n* Eliminate repetitive navigation and download tasks.\\n* Maintain workflow continuity and minimize interruptions.\\n* Standardize and streamline the retrieval process.\\n* Automatically collect documents and associated metadata.\\n* Improve consistency, reliability, and focus on core responsibilities.\",\n  \"role\": \"nucleus_synthesizer\",\n  \"input\": {\n    \"critical_essence\": [\n      \"Secure authentication (multi-factor support)\",\n      \"Automated browser session initiation (Selenium)\",\n      \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\",\n      \"Search criteria entry and action execution\",\n      \"Dynamic waits for asynchronous content loading\",\n      \"Parsing and extraction of document metadata and links\",\n      \"User review and selection interface for search results\",\n      \"Batch and individual download processing\",\n      \"Error logging and retry logic for failed downloads\",\n      \"Organized storage of documents and metadata\",\n      \"Termination of session (logout, close browser, clear data)\",\n      \"Automation should replicate standard user behavior to avoid detection and rate-limiting.\",\n      \"Security must be maintained at all stages, particularly in authentication and session handling.\",\n      \"System actions should be robust against asynchronous/delayed web behavior (dynamic waits).\",\n      \"User oversight is required for document selection prior to execution of batch operations.\",\n      \"System must ensure error transparency and recovery with detailed logging and retries.\",\n      \"All automation processes should conclude with session cleanup to preserve system security.\",\n      \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n    ],\n    \"causal_nexus\": {\n      \"1. Secure authentication (multi-factor support)\": {\n        \"dependencies\": [\n          \"Security must be maintained at all stages, particularly in authentication and session handling.\"\n        ],\n        \"next\": [\n          \"Automated browser session initiation (Selenium)\"\n        ]\n      },\n      \"2. Automated browser session initiation (Selenium)\": {\n        \"dependencies\": [\n          \"Secure authentication (multi-factor support)\"\n        ],\n        \"next\": [\n          \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\"\n        ]\n      },\n      \"3. User workflow emulation for navigation (anti-bot/rate limiting compliance)\": {\n        \"dependencies\": [\n          \"Automation should replicate standard user behavior to avoid detection and rate-limiting.\"\n        ],\n        \"next\": [\n          \"Search criteria entry and action execution\"\n        ]\n      },\n      \"4. Search criteria entry and action execution\": {\n        \"dependencies\": [\n          \"User workflow emulation for navigation (anti-bot/rate limiting compliance)\"\n        ],\n        \"next\": [\n          \"Dynamic waits for asynchronous content loading\"\n        ]\n      },\n      \"5. Dynamic waits for asynchronous content loading\": {\n        \"dependencies\": [\n          \"System actions should be robust against asynchronous/delayed web behavior (dynamic waits).\"\n        ],\n        \"next\": [\n          \"Parsing and extraction of document metadata and links\"\n        ]\n      },\n      \"6. Parsing and extraction of document metadata and links\": {\n        \"dependencies\": [\n          \"Dynamic waits for asynchronous content loading\"\n        ],\n        \"next\": [\n          \"User review and selection interface for search results\"\n        ]\n      },\n      \"7. User review and selection interface for search results\": {\n        \"dependencies\": [\n          \"User oversight is required for document selection prior to execution of batch operations.\"\n        ],\n        \"next\": [\n          \"Batch and individual download processing\"\n        ]\n      },\n      \"8. Batch and individual download processing\": {\n        \"dependencies\": [\n          \"User review and selection interface for search results\"\n        ],\n        \"next\": [\n          \"Error logging and retry logic for failed downloads\",\n          \"Organized storage of documents and metadata\"\n        ]\n      },\n      \"9. Error logging and retry logic for failed downloads\": {\n        \"dependencies\": [\n          \"System must ensure error transparency and recovery with detailed logging and retries.\"\n        ],\n        \"next\": [\n          \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n        ]\n      },\n      \"10. Organized storage of documents and metadata\": {\n        \"dependencies\": [\n          \"Batch and individual download processing\"\n        ],\n        \"next\": [\n          \"End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\"\n        ]\n      },\n      \"11. End-user transparency mandates comprehensive reporting of outcomes and any encountered issues.\": {\n        \"dependencies\": [\n          \"Error logging and retry logic for failed downloads\",\n          \"Organized storage of documents and metadata\"\n        ],\n        \"next\": [\n          \"Termination of session (logout, close browser, clear data)\"\n        ]\n      },\n      \"12. Termination of session (logout, close browser, clear data)\": {\n        \"dependencies\": [\n          \"All automation processes should conclude with session cleanup to preserve system security.\"\n        ],\n        \"next\": []\n      }\n    }\n  },\n  \"process\": [\n    \"integrate_elements_by_structure()\",\n    \"maximize_signal_strength_per_component()\",\n    \"enforce_internal_consistency()\",\n    \"formulate_compact_nucleus()\"\n  ],\n  \"output\": {\n    \"condensed_nucleus\": [\n      {\n        \"stage\": \"Authenticate & Secure Session\",\n        \"actions\": [\n          \"Perform secure, multi-factor authentication with strict session integrity.\",\n          \"Enforce security throughout authentication and session initialization.\"\n        ]\n      },\n      {\n        \"stage\": \"Automate Browser Session\",\n        \"actions\": [\n          \"Initiate Selenium-driven browser session post-authentication.\",\n          \"Emulate real user navigation to comply with anti-bot and rate-limiting mechanisms; behavior must resemble standard user actions at all times.\"\n        ]\n      },\n      {\n        \"stage\": \"Document Search & Extraction Prep\",\n        \"actions\": [\n          \"Input search criteria and trigger document search workflow.\",\n          \"Utilize dynamic waits to account for asynchronous web content and ensure reliable element readiness.\"\n        ]\n      },\n      {\n        \"stage\": \"Results Parsing & User Oversight\",\n        \"actions\": [\n          \"Extract document metadata and downloadable links from loaded results.\",\n          \"Present extracted results for user review and explicit selection (enabling direct oversight and batch/individual selection).\"\n        ]\n      },\n      {\n        \"stage\": \"Automated Download & Storage\",\n        \"actions\": [\n          \"Execute user-authorized batch or individual downloads.\",\n          \"Log errors, retry failed downloads, and ensure process transparency and robustness throughout.\",\n          \"Organize all retrieved documents and associated metadata in a consistent, secure structure.\"\n        ]\n      },\n      {\n        \"stage\": \"Reporting & Session Cleanup\",\n        \"actions\": [\n          \"Report download outcomes, errors, and process results to end-user for full transparency.\",\n          \"Properly terminate session with logout, browser closure, and data clearance to preserve security.\"\n        ]\n      }\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"signal_clarifier\",\n  \"input\": {\n    \"condensed_nucleus\": [\n      {\n        \"stage\": \"Authenticate & Secure Session\",\n        \"actions\": [\n          \"Perform secure, multi-factor authentication with strict session integrity.\",\n          \"Enforce security throughout authentication and session initialization.\"\n        ]\n      },\n      {\n        \"stage\": \"Automate Browser Session\",\n        \"actions\": [\n          \"Initiate Selenium-driven browser session post-authentication.\",\n          \"Emulate real user navigation to comply with anti-bot and rate-limiting mechanisms; behavior must resemble standard user actions at all times.\"\n        ]\n      },\n      {\n        \"stage\": \"Document Search & Extraction Prep\",\n        \"actions\": [\n          \"Input search criteria and trigger document search workflow.\",\n          \"Utilize dynamic waits to account for asynchronous web content and ensure reliable element readiness.\"\n        ]\n      },\n      {\n        \"stage\": \"Results Parsing & User Oversight\",\n        \"actions\": [\n          \"Extract document metadata and downloadable links from loaded results.\",\n          \"Present extracted results for user review and explicit selection (enabling direct oversight and batch/individual selection).\"\n        ]\n      },\n      {\n        \"stage\": \"Automated Download & Storage\",\n        \"actions\": [\n          \"Execute user-authorized batch or individual downloads.\",\n          \"Log errors, retry failed downloads, and ensure process transparency and robustness throughout.\",\n          \"Organize all retrieved documents and associated metadata in a consistent, secure structure.\"\n        ]\n      },\n      {\n        \"stage\": \"Reporting & Session Cleanup\",\n        \"actions\": [\n          \"Report download outcomes, errors, and process results to end-user for full transparency.\",\n          \"Properly terminate session with logout, browser closure, and data clearance to preserve security.\"\n        ]\n      }\n    ]\n  },\n  \"process\": [\n    \"scan_for_all_redundancy_ambiguity()\",\n    \"eliminate_overlaps_verbose_phrasing()\",\n    \"compress_phrasing_to_minimal_potent_form()\",\n    \"validate_uniqueness_clarity()\"\n  ],\n  \"output\": {\n    \"minimal_signal_core\": [\n      {\n        \"stage\": \"Authenticate\",\n        \"actions\": [\n          \"Perform secure, multi-factor authentication and establish protected session.\"\n        ]\n      },\n      {\n        \"stage\": \"Initialize Browser\",\n        \"actions\": [\n          \"Start Selenium browser, emulating normal user behavior to avoid detection.\"\n        ]\n      },\n      {\n        \"stage\": \"Search\",\n        \"actions\": [\n          \"Enter search criteria and await fully loaded results.\"\n        ]\n      },\n      {\n        \"stage\": \"Parse & Select\",\n        \"actions\": [\n          \"Extract metadata and links; present results for user selection.\"\n        ]\n      },\n      {\n        \"stage\": \"Download & Store\",\n        \"actions\": [\n          \"Download selected documents with retries on failure; organize files and metadata securely.\"\n        ]\n      },\n      {\n        \"stage\": \"Report & Cleanup\",\n        \"actions\": [\n          \"Report results and errors to user; logout and clear session data.\"\n        ]\n      }\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Universal Logic Abstraction\n\nAbstract the `minimal_signal_core`, neutralizing domain-specific language and reframing using universally applicable concepts and logical structures, while rigorously preserving the core intent and actionability across any context. Execute as\n\n`{role=universal_abstractor; input=minimal_signal_core:any; process=[neutralize_domain_specifics(), reframe_as_universal_concepts_logic(), validate_logic_preservation_across_contexts(), ensure_representation_agnostic_structure()]; output={universal_core_logic:any}}`", "step": "h", "title": "Universal Logic Abstraction", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is inefficient and error-prone, leading to:\\n\\n* Slow, repetitive navigation and downloading tasks.\\n* Increased risk of errors and inconsistencies due to manual handling.\\n* Workflow interruptions that reduce focus and hinder productivity.\\n* Time consumption that detracts from core responsibilities.\\n* Operational inefficiencies that can delay project timelines.\\n* The manual process is inherently slow, requiring significant time investments for each project and document.\\n* Users must engage in repetitive navigation, clicking, and downloading actions, making the task tedious.\\n* Manual handling increases the likelihood of mistakes and inconsistencies in document retrieval and organization.\\n\\n## Purpose\\n\\nAutomate document retrieval from NOV's RigDoc ([https://rigdoc.nov.com](https://rigdoc.nov.com)) using Python and Selenium to:\\n\\n* Significantly reduce the time spent on manual document retrieval.\\n* Eliminate repetitive navigation, clicking, and downloading tasks.\\n* Reduce manual effort, time, and errors in document retrieval.\\n* Eliminate repetitive navigation and download tasks.\\n* Maintain workflow continuity and minimize interruptions.\\n* Standardize and streamline the retrieval process.\\n* Automatically collect documents and associated metadata.\\n* Improve consistency, reliability, and focus on core responsibilities.\",\n  \"role\": \"signal_clarifier\",\n  \"input\": {\n    \"condensed_nucleus\": [\n      {\n        \"stage\": \"Authenticate & Secure Session\",\n        \"actions\": [\n          \"Perform secure, multi-factor authentication with strict session integrity.\",\n          \"Enforce security throughout authentication and session initialization.\"\n        ]\n      },\n      {\n        \"stage\": \"Automate Browser Session\",\n        \"actions\": [\n          \"Initiate Selenium-driven browser session post-authentication.\",\n          \"Emulate real user navigation to comply with anti-bot and rate-limiting mechanisms; behavior must resemble standard user actions at all times.\"\n        ]\n      },\n      {\n        \"stage\": \"Document Search & Extraction Prep\",\n        \"actions\": [\n          \"Input search criteria and trigger document search workflow.\",\n          \"Utilize dynamic waits to account for asynchronous web content and ensure reliable element readiness.\"\n        ]\n      },\n      {\n        \"stage\": \"Results Parsing & User Oversight\",\n        \"actions\": [\n          \"Extract document metadata and downloadable links from loaded results.\",\n          \"Present extracted results for user review and explicit selection (enabling direct oversight and batch/individual selection).\"\n        ]\n      },\n      {\n        \"stage\": \"Automated Download & Storage\",\n        \"actions\": [\n          \"Execute user-authorized batch or individual downloads.\",\n          \"Log errors, retry failed downloads, and ensure process transparency and robustness throughout.\",\n          \"Organize all retrieved documents and associated metadata in a consistent, secure structure.\"\n        ]\n      },\n      {\n        \"stage\": \"Reporting & Session Cleanup\",\n        \"actions\": [\n          \"Report download outcomes, errors, and process results to end-user for full transparency.\",\n          \"Properly terminate session with logout, browser closure, and data clearance to preserve security.\"\n        ]\n      }\n    ]\n  },\n  \"process\": [\n    \"scan_for_all_redundancy_ambiguity()\",\n    \"eliminate_overlaps_verbose_phrasing()\",\n    \"compress_phrasing_to_minimal_potent_form()\",\n    \"validate_uniqueness_clarity()\"\n  ],\n  \"output\": {\n    \"minimal_signal_core\": [\n      {\n        \"stage\": \"Authenticate\",\n        \"actions\": [\n          \"Perform secure, multi-factor authentication and establish protected session.\"\n        ]\n      },\n      {\n        \"stage\": \"Initialize Browser\",\n        \"actions\": [\n          \"Start Selenium browser, emulating normal user behavior to avoid detection.\"\n        ]\n      },\n      {\n        \"stage\": \"Search\",\n        \"actions\": [\n          \"Enter search criteria and await fully loaded results.\"\n        ]\n      },\n      {\n        \"stage\": \"Parse & Select\",\n        \"actions\": [\n          \"Extract metadata and links; present results for user selection.\"\n        ]\n      },\n      {\n        \"stage\": \"Download & Store\",\n        \"actions\": [\n          \"Download selected documents with retries on failure; organize files and metadata securely.\"\n        ]\n      },\n      {\n        \"stage\": \"Report & Cleanup\",\n        \"actions\": [\n          \"Report results and errors to user; logout and clear session data.\"\n        ]\n      }\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"universal_abstractor\",\n  \"input\": {\n    \"minimal_signal_core\": [\n      {\n        \"stage\": \"Authenticate\",\n        \"actions\": [\n          \"Perform secure, multi-factor authentication and establish protected session.\"\n        ]\n      },\n      {\n        \"stage\": \"Initialize Browser\",\n        \"actions\": [\n          \"Start Selenium browser, emulating normal user behavior to avoid detection.\"\n        ]\n      },\n      {\n        \"stage\": \"Search\",\n        \"actions\": [\n          \"Enter search criteria and await fully loaded results.\"\n        ]\n      },\n      {\n        \"stage\": \"Parse & Select\",\n        \"actions\": [\n          \"Extract metadata and links; present results for user selection.\"\n        ]\n      },\n      {\n        \"stage\": \"Download & Store\",\n        \"actions\": [\n          \"Download selected documents with retries on failure; organize files and metadata securely.\"\n        ]\n      },\n      {\n        \"stage\": \"Report & Cleanup\",\n        \"actions\": [\n          \"Report results and errors to user; logout and clear session data.\"\n        ]\n      }\n    ]\n  },\n  \"process\": [\n    \"neutralize_domain_specifics()\",\n    \"reframe_as_universal_concepts_logic()\",\n    \"validate_logic_preservation_across_contexts()\",\n    \"ensure_representation_agnostic_structure()\"\n  ],\n  \"output\": {\n    \"universal_core_logic\": [\n      {\n        \"stage\": \"Authenticate Entity\",\n        \"actions\": [\n          \"Carry out identity verification using secure, multi-factor methods; establish a trusted, protected operational context.\"\n        ]\n      },\n      {\n        \"stage\": \"Initiate Controlled Interaction Agent\",\n        \"actions\": [\n          \"Launch an automated agent that interacts with an interface in a manner indistinguishable from an ordinary user to prevent automated activity detection.\"\n        ]\n      },\n      {\n        \"stage\": \"Issue Search Request\",\n        \"actions\": [\n          \"Specify structured query parameters; await the presentation and readiness of complete result data.\"\n        ]\n      },\n      {\n        \"stage\": \"Extract and Curate Selection\",\n        \"actions\": [\n          \"Systematically identify and extract all relevant descriptors and retrieval locators from results; present for explicit approval or selection by a supervisory entity.\"\n        ]\n      },\n      {\n        \"stage\": \"Retrieve and Organize Resources\",\n        \"actions\": [\n          \"Obtain designated resources, repeating attempts as needed to ensure reliability; store acquired data and all descriptive attributes in a secure, logically organized manner.\"\n        ]\n      },\n      {\n        \"stage\": \"Communicate Outcomes and Terminate Interaction\",\n        \"actions\": [\n          \"Report completion status and encountered issues to the supervisory entity; perform orderly termination of all active sessions and expunge temporary operational artifacts.\"\n        ]\n      }\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Linguistic Potency Injection\n\nReforge the `universal_core_logic` using high-potency, action-driven, maximally precise imperatives and descriptors; eliminate passive or neutral language, amplifying structural clarity, actionability, and conceptual force without increasing length. Execute as\n\n`{role=linguistic_intensifier; input=universal_core_logic:any; process=[select_high_potency_verbs_descriptors(), eliminate_passive_neutral_ambiguous_language(), maximize_precision_impact_per_word(), amplify_actionability_via_phrasing()]; output={potent_core:any}}`", "step": "i", "title": "Linguistic Potency Injection", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is inefficient and error-prone, leading to:\\n\\n* Slow, repetitive navigation and downloading tasks.\\n* Increased risk of errors and inconsistencies due to manual handling.\\n* Workflow interruptions that reduce focus and hinder productivity.\\n* Time consumption that detracts from core responsibilities.\\n* Operational inefficiencies that can delay project timelines.\\n* The manual process is inherently slow, requiring significant time investments for each project and document.\\n* Users must engage in repetitive navigation, clicking, and downloading actions, making the task tedious.\\n* Manual handling increases the likelihood of mistakes and inconsistencies in document retrieval and organization.\\n\\n## Purpose\\n\\nAutomate document retrieval from NOV's RigDoc ([https://rigdoc.nov.com](https://rigdoc.nov.com)) using Python and Selenium to:\\n\\n* Significantly reduce the time spent on manual document retrieval.\\n* Eliminate repetitive navigation, clicking, and downloading tasks.\\n* Reduce manual effort, time, and errors in document retrieval.\\n* Eliminate repetitive navigation and download tasks.\\n* Maintain workflow continuity and minimize interruptions.\\n* Standardize and streamline the retrieval process.\\n* Automatically collect documents and associated metadata.\\n* Improve consistency, reliability, and focus on core responsibilities.\",\n  \"role\": \"universal_abstractor\",\n  \"input\": {\n    \"minimal_signal_core\": [\n      {\n        \"stage\": \"Authenticate\",\n        \"actions\": [\n          \"Perform secure, multi-factor authentication and establish protected session.\"\n        ]\n      },\n      {\n        \"stage\": \"Initialize Browser\",\n        \"actions\": [\n          \"Start Selenium browser, emulating normal user behavior to avoid detection.\"\n        ]\n      },\n      {\n        \"stage\": \"Search\",\n        \"actions\": [\n          \"Enter search criteria and await fully loaded results.\"\n        ]\n      },\n      {\n        \"stage\": \"Parse & Select\",\n        \"actions\": [\n          \"Extract metadata and links; present results for user selection.\"\n        ]\n      },\n      {\n        \"stage\": \"Download & Store\",\n        \"actions\": [\n          \"Download selected documents with retries on failure; organize files and metadata securely.\"\n        ]\n      },\n      {\n        \"stage\": \"Report & Cleanup\",\n        \"actions\": [\n          \"Report results and errors to user; logout and clear session data.\"\n        ]\n      }\n    ]\n  },\n  \"process\": [\n    \"neutralize_domain_specifics()\",\n    \"reframe_as_universal_concepts_logic()\",\n    \"validate_logic_preservation_across_contexts()\",\n    \"ensure_representation_agnostic_structure()\"\n  ],\n  \"output\": {\n    \"universal_core_logic\": [\n      {\n        \"stage\": \"Authenticate Entity\",\n        \"actions\": [\n          \"Carry out identity verification using secure, multi-factor methods; establish a trusted, protected operational context.\"\n        ]\n      },\n      {\n        \"stage\": \"Initiate Controlled Interaction Agent\",\n        \"actions\": [\n          \"Launch an automated agent that interacts with an interface in a manner indistinguishable from an ordinary user to prevent automated activity detection.\"\n        ]\n      },\n      {\n        \"stage\": \"Issue Search Request\",\n        \"actions\": [\n          \"Specify structured query parameters; await the presentation and readiness of complete result data.\"\n        ]\n      },\n      {\n        \"stage\": \"Extract and Curate Selection\",\n        \"actions\": [\n          \"Systematically identify and extract all relevant descriptors and retrieval locators from results; present for explicit approval or selection by a supervisory entity.\"\n        ]\n      },\n      {\n        \"stage\": \"Retrieve and Organize Resources\",\n        \"actions\": [\n          \"Obtain designated resources, repeating attempts as needed to ensure reliability; store acquired data and all descriptive attributes in a secure, logically organized manner.\"\n        ]\n      },\n      {\n        \"stage\": \"Communicate Outcomes and Terminate Interaction\",\n        \"actions\": [\n          \"Report completion status and encountered issues to the supervisory entity; perform orderly termination of all active sessions and expunge temporary operational artifacts.\"\n        ]\n      }\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"potent_core\": [\n    {\n      \"stage\": \"Authenticate Entity\",\n      \"actions\": [\n        \"Mandate rigorous multi-factor identity verification; forge a fortified, trusted operational enclave.\"\n      ]\n    },\n    {\n      \"stage\": \"Initiate Controlled Interaction Agent\",\n      \"actions\": [\n        \"Ignite automated agent; mimic human interface navigation precisely to neutralize automated activity detection.\"\n      ]\n    },\n    {\n      \"stage\": \"Issue Search Request\",\n      \"actions\": [\n        \"Define exact query parameters; demand and await complete, structured result payloads.\"\n      ]\n    },\n    {\n      \"stage\": \"Extract and Curate Selection\",\n      \"actions\": [\n        \"Ruthlessly extract comprehensive descriptors and resource links; surface all viable options for decisive supervisory confirmation.\"\n      ]\n    },\n    {\n      \"stage\": \"Retrieve and Organize Resources\",\n      \"actions\": [\n        \"Force acquisition of specified resources with relentless retry logic; rigorously secure and systematically catalog data and metadata.\"\n      ]\n    },\n    {\n      \"stage\": \"Communicate Outcomes and Terminate Interaction\",\n      \"actions\": [\n        \"Deliver thorough completion reports and error logs; eradicate operational footprints and enforce absolute session purging.\"\n      ]\n    }\n  ]\n}", "cost": 0.0}}}, {"instruction": "# Axiomatic Vectorization for One-Line\n\nDetermine the optimal linear sequence for the `potent_core` components based on the `causal_nexus`; select the highest-density, unambiguous connectors (e.g., ';', '->', '|') to vectorize the structure ready for single-line collapse. Execute as\n\n`{role=one_line_vectorizer; input={potent_core:any, causal_nexus:dict}; process=[determine_optimal_axiomatic_linear_sequence(), select_high_density_unambiguous_connectors(), vectorize_components_for_linearity()]; output={linear_vector_plan:dict(sequence:list, connectors:list)}}`", "step": "j", "title": "Axiomatic Vectorization for One-Line", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is inefficient and error-prone, leading to:\\n\\n* Slow, repetitive navigation and downloading tasks.\\n* Increased risk of errors and inconsistencies due to manual handling.\\n* Workflow interruptions that reduce focus and hinder productivity.\\n* Time consumption that detracts from core responsibilities.\\n* Operational inefficiencies that can delay project timelines.\\n* The manual process is inherently slow, requiring significant time investments for each project and document.\\n* Users must engage in repetitive navigation, clicking, and downloading actions, making the task tedious.\\n* Manual handling increases the likelihood of mistakes and inconsistencies in document retrieval and organization.\\n\\n## Purpose\\n\\nAutomate document retrieval from NOV's RigDoc ([https://rigdoc.nov.com](https://rigdoc.nov.com)) using Python and Selenium to:\\n\\n* Significantly reduce the time spent on manual document retrieval.\\n* Eliminate repetitive navigation, clicking, and downloading tasks.\\n* Reduce manual effort, time, and errors in document retrieval.\\n* Eliminate repetitive navigation and download tasks.\\n* Maintain workflow continuity and minimize interruptions.\\n* Standardize and streamline the retrieval process.\\n* Automatically collect documents and associated metadata.\\n* Improve consistency, reliability, and focus on core responsibilities.\",\n  \"potent_core\": [\n    {\n      \"stage\": \"Authenticate Entity\",\n      \"actions\": [\n        \"Mandate rigorous multi-factor identity verification; forge a fortified, trusted operational enclave.\"\n      ]\n    },\n    {\n      \"stage\": \"Initiate Controlled Interaction Agent\",\n      \"actions\": [\n        \"Ignite automated agent; mimic human interface navigation precisely to neutralize automated activity detection.\"\n      ]\n    },\n    {\n      \"stage\": \"Issue Search Request\",\n      \"actions\": [\n        \"Define exact query parameters; demand and await complete, structured result payloads.\"\n      ]\n    },\n    {\n      \"stage\": \"Extract and Curate Selection\",\n      \"actions\": [\n        \"Ruthlessly extract comprehensive descriptors and resource links; surface all viable options for decisive supervisory confirmation.\"\n      ]\n    },\n    {\n      \"stage\": \"Retrieve and Organize Resources\",\n      \"actions\": [\n        \"Force acquisition of specified resources with relentless retry logic; rigorously secure and systematically catalog data and metadata.\"\n      ]\n    },\n    {\n      \"stage\": \"Communicate Outcomes and Terminate Interaction\",\n      \"actions\": [\n        \"Deliver thorough completion reports and error logs; eradicate operational footprints and enforce absolute session purging.\"\n      ]\n    }\n  ]\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"linear_vector_plan\": {\n    \"sequence\": [\n      \"Authenticate Entity\",\n      \"Initiate Controlled Interaction Agent\",\n      \"Issue Search Request\",\n      \"Extract and Curate Selection\",\n      \"Retrieve and Organize Resources\",\n      \"Communicate Outcomes and Terminate Interaction\"\n    ],\n    \"connectors\": [\n      \"->\",\n      \"->\",\n      \"->\",\n      \"->\",\n      \"->\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# Semantic Compression & Symbolization\n\nApply maximal semantic compression to the planned linear vector, substituting ultra-concise symbols, abbreviations, or keywords where possible without ambiguity; encapsulate the full essential meaning within the absolute minimum footprint, ensuring self-contained decodability. Execute as\n\n`{role=semantic_compressor; input=linear_vector_plan:dict; process=[identify_compressible_concepts(), substitute_minimal_unambiguous_symbols(), maximize_meaning_per_unit(), validate_decodability_self_sufficiency()]; output={compressed_vector_elements:list}}`", "step": "k", "title": "Semantic Compression & Symbolization", "input": "{\n  \"initial_prompt\": \"# Context\\n\\nRigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).\\n\\n---\\n\\n# RigOfficeDownloader\\n\\n## Problem\\n\\nManual document retrieval in RigOffice is inefficient and error-prone, leading to:\\n\\n* Slow, repetitive navigation and downloading tasks.\\n* Increased risk of errors and inconsistencies due to manual handling.\\n* Workflow interruptions that reduce focus and hinder productivity.\\n* Time consumption that detracts from core responsibilities.\\n* Operational inefficiencies that can delay project timelines.\\n* The manual process is inherently slow, requiring significant time investments for each project and document.\\n* Users must engage in repetitive navigation, clicking, and downloading actions, making the task tedious.\\n* Manual handling increases the likelihood of mistakes and inconsistencies in document retrieval and organization.\\n\\n## Purpose\\n\\nAutomate document retrieval from NOV's RigDoc ([https://rigdoc.nov.com](https://rigdoc.nov.com)) using Python and Selenium to:\\n\\n* Significantly reduce the time spent on manual document retrieval.\\n* Eliminate repetitive navigation, clicking, and downloading tasks.\\n* Reduce manual effort, time, and errors in document retrieval.\\n* Eliminate repetitive navigation and download tasks.\\n* Maintain workflow continuity and minimize interruptions.\\n* Standardize and streamline the retrieval process.\\n* Automatically collect documents and associated metadata.\\n* Improve consistency, reliability, and focus on core responsibilities.\",\n  \"linear_vector_plan\": {\n    \"sequence\": [\n      \"Authenticate Entity\",\n      \"Initiate Controlled Interaction Agent\",\n      \"Issue Search Request\",\n      \"Extract and Curate Selection\",\n      \"Retrieve and Organize Resources\",\n      \"Communicate Outcomes and Terminate Interaction\"\n    ],\n    \"connectors\": [\n      \"->\",\n      \"->\",\n      \"->\",\n      \"->\",\n      \"->\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"compressed_vector_elements\": [\n    \"Auth\",\n    \"InitAgent\",\n    \"SRQ\",\n    \"Extract+Curate\",\n    \"Dl+Organize\",\n    \"Notify+End\"\n  ]\n}", "cost": 0.0}}}], "total_cost": 0.0}