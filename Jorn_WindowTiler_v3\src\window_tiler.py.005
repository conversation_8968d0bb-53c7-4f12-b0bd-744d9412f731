#!/usr/bin/env python3
"""
Enhanced Window Tiler with optional minimized-window restoration.

Key points:
- Enumerates *all* top-level windows (including minimized).
- Groups them by process name OR by simple "window type".
- Lets you pick which group to tile.
- Optionally tries to unminimize these windows (SW_SHOWNORMAL + SetWindowPos).

Requirements:
    pip install pywin32
"""

import win32api
import win32gui
import win32con
import win32process
import ctypes
import os
import sys

from enum import Enum, auto

# -------------------------------------------------------------------------
# 1) Enumerations & Basic Classes
# -------------------------------------------------------------------------
class WindowType(Enum):
    BROWSER = auto()
    TERMINAL = auto()
    EXPLORER = auto()
    EDITOR = auto()
    IDE = auto()
    NORMAL = auto()
    UNKNOWN = auto()


class Monitor:
    """Represents a physical display monitor."""
    def __init__(self, handle, info):
        self.handle = handle
        self.is_primary = bool(info["Flags"] == 1)
        self.device = info["Device"]
        mon_rect = info["Monitor"]  # (left, top, right, bottom)
        work_rect = info["Work"]    # (left, top, right, bottom)
        self.monitor_area = mon_rect
        self.work_area = work_rect

    def get_dimensions(self):
        left, top, right, bottom = self.monitor_area
        return {"width": right - left, "height": bottom - top}


class Window:
    """
    Represents a single top-level window:
      - We skip child or tool windows (by style).
      - We can forcibly restore if minimized.
    """
    def __init__(self, hwnd):
        self.hwnd = hwnd
        self.title = win32gui.GetWindowText(hwnd)
        self.class_name = win32gui.GetClassName(hwnd)
        self.process_name = None
        self.window_type = WindowType.UNKNOWN

        self._update_process_info()
        self._classify()

    def _update_process_info(self):
        """Get the process name from the window handle."""
        try:
            _, pid = win32process.GetWindowThreadProcessId(self.hwnd)
            proc_access = win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ
            handle = ctypes.windll.kernel32.OpenProcess(proc_access, False, pid)
            if handle:
                exe_path = win32process.GetModuleFileNameEx(handle, 0)
                self.process_name = os.path.basename(exe_path).lower()
                ctypes.windll.kernel32.CloseHandle(handle)
        except:
            pass

    def _classify(self):
        """Rough classification of the window based on process name."""
        pname = self.process_name or ""
        cname = self.class_name.lower()

        if any(b in pname for b in ["chrome.exe", "msedge.exe", "firefox.exe", "iexplore.exe"]):
            self.window_type = WindowType.BROWSER
        elif any(t in pname for t in ["cmd.exe", "powershell.exe", "windowsterminal.exe"]):
            self.window_type = WindowType.TERMINAL
        elif "explorer.exe" in pname or "cabinetwclass" in cname:
            self.window_type = WindowType.EXPLORER
        elif any(e in pname for e in ["notepad.exe", "code.exe", "sublime_text.exe"]):
            self.window_type = WindowType.EDITOR
        elif any(i in pname for i in ["devenv.exe", "pycharm", "idea64"]):
            self.window_type = WindowType.IDE
        else:
            # Default
            self.window_type = WindowType.NORMAL

    def move_and_resize(self, x, y, width, height, restore_minimized=True):
        """
        Position this window and optionally restore if minimized.

        Steps:
        1) If restore_minimized is True and the window is iconic,
           call ShowWindow(SW_SHOWNORMAL).
        2) Move/resize with MoveWindow.
        3) SetWindowPos to top to ensure we see it.

        This won't always override *all* OS rules (some windows might not restore).
        """
        if restore_minimized and win32gui.IsIconic(self.hwnd):
            # SW_SHOWNORMAL is more reliable for typical apps than SW_RESTORE
            win32gui.ShowWindow(self.hwnd, win32con.SW_SHOWNORMAL)

        # Move the window
        win32gui.MoveWindow(self.hwnd, x, y, width, height, True)

        # Force window to top, just so we can see it
        # (No activation, to avoid messing with user focus)
        win32gui.SetWindowPos(
            self.hwnd,
            win32con.HWND_TOP,
            x, y, width, height,
            win32con.SWP_NOSENDCHANGING | win32con.SWP_SHOWWINDOW
        )

    def __repr__(self):
        return (f"<Window hwnd={self.hwnd} title='{self.title}' "
                f"proc='{self.process_name}' type={self.window_type.name}>")

# -------------------------------------------------------------------------
# 2) Window Gathering, but skip non-top-level
# -------------------------------------------------------------------------
def is_top_level_window(hwnd):
    """
    Check if a window is top-level & not a tool/child window.
    We'll check the WS_CHILD and WS_POPUP styles, etc.
    """
    # Window styles
    style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)

    # If WS_CHILD is set, it's not top-level
    # If the title is empty or some special case, we skip it
    # We also skip WS_DISABLED windows if you prefer
    # But let's keep it simpler: just check for child style
    if style & win32con.WS_CHILD:
        return False

    return True

def get_all_windows():
    """
    Enumerate all top-level windows, including minimized, ignoring
    known system placeholders or child windows.
    """
    results = []

    # Skip certain system classes
    system_classes = {
        'Default IME', 'MSCTFIME UI', 'CiceroUIWndFrame',
        'GDI+ Window', 'MediaContextNotificationWindow',
        'SystemResourceNotifyWindow', '#32770',
        'DesktopWindowXamlSource', 'DDE Server Window',
        'Windows.UI.Core.CoreWindow'
    }
    # Skip certain system "titles"
    skip_titles = {"Task Switching", "Program Manager", "Windows Input Experience"}

    def enum_callback(hwnd, _):
        # Basic checks
        if not is_top_level_window(hwnd):
            return True
        title = win32gui.GetWindowText(hwnd)
        if not title or title in skip_titles:
            return True
        class_name = win32gui.GetClassName(hwnd)
        if class_name in system_classes:
            return True

        # We'll include it
        results.append(Window(hwnd))
        return True

    win32gui.EnumWindows(enum_callback, None)
    return results

# -------------------------------------------------------------------------
# 3) Grouping
# -------------------------------------------------------------------------
def group_by_process_name(windows):
    """Group windows by their .process_name."""
    groups = {}
    for w in windows:
        pname = w.process_name or "unknown"
        groups.setdefault(pname, []).append(w)
    return groups

def group_by_window_type(windows):
    """Group windows by their .window_type enum."""
    groups = {}
    for w in windows:
        wtype = w.window_type
        groups.setdefault(wtype, []).append(w)
    return groups

# -------------------------------------------------------------------------
# 4) Monitor Handling
# -------------------------------------------------------------------------
class WrappedMonitor:
    """Helper for user selection in console."""
    def __init__(self, monitor_obj, index):
        self.monitor_obj = monitor_obj
        self.index = index

    def __str__(self):
        dims = self.monitor_obj.get_dimensions()
        primary_txt = " [PRIMARY]" if self.monitor_obj.is_primary else ""
        return f"{self.index}) {self.monitor_obj.device} - {dims['width']}x{dims['height']}{primary_txt}"

def get_all_monitors():
    """Return a list of Monitor objects."""
    monitors = []
    for (handle, _, info) in win32api.EnumDisplayMonitors(None, None):
        mon_info = win32api.GetMonitorInfo(handle)
        monitors.append(Monitor(handle, mon_info))
    return monitors

def choose_monitor():
    """Prompt user to choose a monitor (or pick primary by default)."""
    monitor_objs = get_all_monitors()
    if not monitor_objs:
        print("No monitors found!")
        sys.exit(1)

    wrapped = []
    for i, m in enumerate(monitor_objs):
        wrapped.append(WrappedMonitor(m, i))

    print("\nMonitors Detected:")
    for w in wrapped:
        print(w)

    choice = input("Select a monitor index [blank=choose primary]: ").strip()
    if not choice:
        # Attempt to pick the primary
        for m in monitor_objs:
            if m.is_primary:
                return m
        return monitor_objs[0]  # fallback

    try:
        idx = int(choice)
        if 0 <= idx < len(monitor_objs):
            return monitor_objs[idx]
        else:
            print("Invalid monitor index, defaulting to first.")
            return monitor_objs[0]
    except:
        print("Invalid input, defaulting to first monitor.")
        return monitor_objs[0]

# -------------------------------------------------------------------------
# 5) Tiling
# -------------------------------------------------------------------------
def tile_in_grid(monitor, windows, rows=2, cols=2, restore_minimized=True):
    """
    Simple grid tiling on the chosen monitor.

    monitor: Monitor object
    windows: list[Window]
    rows, cols: grid layout
    restore_minimized: bool controlling SW_SHOWNORMAL on minimized windows
    """
    if not windows:
        print("No windows to tile.")
        return

    left, top, right, bottom = monitor.monitor_area
    total_width = right - left
    total_height = bottom - top

    n = min(len(windows), rows * cols)

    for i, w in enumerate(windows[:n]):
        row = i // cols
        col = i % cols

        x = left + int(col * (total_width / cols))
        y = top + int(row * (total_height / rows))
        wth = int(total_width / cols)
        hth = int(total_height / rows)

        w.move_and_resize(
            x, y, wth, hth,
            restore_minimized=restore_minimized
        )

    print(f"Tiled {n} windows in a {rows}x{cols} grid on {monitor.device}.")

# -------------------------------------------------------------------------
# 6) Main CLI
# -------------------------------------------------------------------------
def main():
    print("Gathering top-level windows (including minimized).")
    windows = get_all_windows()
    if not windows:
        print("No windows found!")
        return

    print(f"Total windows found: {len(windows)}")

    print("\nChoose grouping style:")
    print("1) By process name (e.g. 'chrome.exe')")
    print("2) By window type (BROWSER, TERMINAL, EDITOR, etc.)")
    choice = input("Enter [1/2]: ").strip()

    if choice == "1":
        groups = group_by_process_name(windows)
    else:
        groups = group_by_window_type(windows)

    # Show groups
    print("\nGroups Detected:")
    group_keys = sorted(groups.keys(), key=lambda k: str(k))
    for idx, key in enumerate(group_keys):
        label = str(key)
        # If it's a WindowType, label might look like 'WindowType.BROWSER'
        if isinstance(key, WindowType):
            label = key.name
        print(f"{idx}) {label} -> {len(groups[key])} windows")

    if not group_keys:
        print("No groups found!")
        return

    chosen = input("\nWhich group do you want to tile? Enter index: ").strip()
    try:
        chosen_idx = int(chosen)
        group_key = group_keys[chosen_idx]
    except:
        print("Invalid choice, quitting.")
        return

    r = input("Number of rows [default=2]: ").strip()
    c = input("Number of columns [default=2]: ").strip()
    rows = int(r) if r.isdigit() else 2
    cols = int(c) if c.isdigit() else 2

    # Prompt whether we restore minimized windows or not
    restore_prompt = input("Restore minimized windows? [Y/n]: ").strip().lower()
    restore_minimized = (restore_prompt != 'n')

    monitor = choose_monitor()
    tile_in_grid(monitor, groups[group_key], rows, cols, restore_minimized=restore_minimized)

    print("\nDone.")

if __name__ == "__main__":
    main()
