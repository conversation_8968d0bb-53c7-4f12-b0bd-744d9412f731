"""
Window Tiler package.
Enhanced Window Tiler with optional minimized-window restoration,
integrating style-based (Alt+Tab) filtering with psutil-based process checks
and dimension checks, all in a modular class-based architecture.
"""

from .window_manager import WindowManager
from .monitor_manager import MonitorManager
from .layout_manager import LayoutManager
from .user_interface import UserInterface
from .window_tiler_app import WindowTilerApp
from .models import Window, Monitor, WindowType, WrappedMonitor

__all__ = [
    'WindowManager',
    'MonitorManager',
    'LayoutManager',
    'UserInterface',
    'WindowTilerApp',
    'Window',
    'Monitor',
    'WindowType',
    'WrappedMonitor'
]
