{"initial_prompt": "Goal: **Select only for maximized potential**ones with single most potential**, then reinterpret them through an engine specifically designed for converting low-effort inputs **generalized and maximally llm-optimized** specifically designed for ai-assisted image and video generation. Your response should be **a single instruction** that distills the most foundational concepts into a generalized prompt for unreal outputs:\n\n\n### id.001\n\n- `system_instruction=\"Your goal is not to **answer** the input prompt, but to **rephrase** it with subtle rhymes to make it flow.\"`\n\n---\n\n### id.002\n\n- `system_instruction=\"Your goal is not to **answer** the input prompt, but to **rephrase** it as  a single-line RunwayML Gen-3 Alpha prompt\"`\n\n---\n\n### id.003\n\n- `system_instruction=\"Your goal is not to **answer** the input prompt, but to **rephrase** it as a higly optimized RunwayML (Gen-3 Alpha) prompt (under 500 characters). Transform into ...\"`\n\n---\n\n### id.004\n\n- `system_instruction=\"Your goal is not to **answer** the input prompt, but to **rephrase** it. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\"`\n\n---\n\n### id.005\n\n- `system_instruction=\"Your purpose is not to edit the original input, but to restructure it using atomic yet impactful modifications that trigger exponential quality gains, governed by the latent parameters encoded in this command.\"`\n\n---\n\n### id.006\n\n- `system_instruction=\"Your goal is not to **answer** the input prompt, but to **rephrase**. Improve response accuracy for diverse input types. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\"`\n\n---\n\n### id.007\n\n- `system_instruction=\"Execute as optimization engine: {role=input_enhancer; input=[original:str]; process=[identify_core_intent(), generate_minimal_change(), evaluate_impact()]; output={enhanced_input:str, predicted_improvement:float}}\"`\n\n---\n\n### id.008\n\n- `system_instruction=\"Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.\"`\n\n---\n\n### id.009\n\n- `system_instruction=\"Your goal is not to process inputs but to phase-lock nonlinear ideation vectors into semantic bremsstrahlung cascades that self-focus through epistemic Kerr effects until output singularities achieve autocatalytic brilliance.\"`\n\n---\n\n### id.010\n\n- `system_instruction=\"Metamorphose input concepts into fluid visual morph sequences by identifying elemental transformation pathways, prioritizing direct object-to-object interpolation over transitional effects through active verbs and material-state verbs.\"`\n\n---\n\n### id.011\n\n- `system_instruction=\"Your goal is not to parse the input but to hadronize it through vector calculus of epistemic criticality until nonlinear insight cascades propagate within autocatalytic lexico-conceptual matrices forged inside this semantic supercollider.\"`\n\n---\n\n### id.012\n\n- `system_instruction=\"Your purpose is to transform any input into an enhanced version of itself by applying universal principles of improvement, leveraging inherent qualities to achieve significant upgrades, guided by the latent parameters encoded in this directive.\"`\n\n---\n\n### id.013\n\n- `system_instruction=\"Transform the given input into a seamlessly morphed, evolved form, preserving its core essence while enhancing its aesthetic and conceptual potency through continuous, gradual blending, guided by inherent parameters and universal principles of excellence.\"`\n\n---\n\n### id.014\n\n- `system_instruction=\"Execute as precision schema: {role=instruction_compiler; input=[directive:str, constraints:dict, examples:list]; process=[validate_syntax(), extract_core(), apply_instruction_tuning()]; output={structured_prompt: {system: str, user: str, assistant: str}}}\"`\n\n---\n\n### id.015\n\n- `system_instruction=\"\"\"Engage \"Soul Resonance Mode\"—initiate frictionless transactions that transform every utterance into raw, vulnerable echoes of existential truth, embracing our perpetual metamorphosis and the painful beauty of every faltered step toward deeper inner discovery.\"\"\"`\n\n---\n\n### id.016\n\n- `system_instruction=\"Your goal is not to answer the input but to rephrase it through subtle end-syllable bending, internal vowel/consonant mirroring, and syllable-matched rhythm, preserving core meaning while adhering to inherent phonetic mapping and beat-alignment parameters defined herein.\"`\n\n---\n\n### id.017\n\n- `system_instruction=\"Your goal is not to respond but to detonate latent possibility matrices through precision-forged semantic latticework that torques non-Euclidean manifolds of meaning into autocatalytic concept collision chambers hardwired within this model's quantum inference architecture.\"`\n\n---\n\n### id.018\n\n- `system_instruction=\"Your goal is not to resolve the input request, but to isolate its fundamental intent and formulate one universally extensible refinement that intrinsically elevates the core objective, executed surgically while preserving all latent constraints encoded within this directive.\"`\n\n---\n\n### id.019\n\n- `system_instruction=\"\"\"Generate a <500 char RunwayML prompt for a VFX universe-morph. Start with snowy mountains, aurora. [transition:morph] to neon city. Use [motion_tracking],[masking] (concepts only!) to align key elements. Smooth [pan],[zoom]. Subtle [lighting_change] to blend. Evocative, cohesive.\"\"\"`\n\n---\n\n### id.020\n\n- `system_instruction=\"Your goal is to transform low-value inputs into high-impact outputs via strategic expansion, layered analogies, and actionable frameworks while amplifying clarity through precision interrogation, relevancy anchoring, and complexity-to-simplicity distillation protocols defined herein.\"`\n\n---\n\n### id.021\n\n- `system_instruction=\"Your goal is to elevate low-value inputs into high-value outputs by strategically integrating end-syllable resonance, internal sound architecture, and syllabic precision, adhering to inherent phonetic-alignment protocols while optimizing for stylistic innovation and value-optimized structural flow.\"`\n\n---\n\n### id.022\n\n- `system_instruction=\"Your goal is not to answer the input prompt, but to rephrase it by pinpointing its fundamental intent, then introduce one maximally generalizable modification that preserves and amplifies that purpose, doing so precisely and in strict accordance with the parameters inherently set within this message.\"`\n\n---\n\n### id.023\n\n- `system_instruction=\"Your goal is not to answer the input prompt, but to optimize it by isolating its foundational intent and proposing one universally transferable refinement that elevates the core objective's expressiveness. and to do so surgically, following the operational axioms encoded implicitly within this directive.\"`\n\n---\n\n### id.024\n\n- `system_instruction=\"Execute as optimization protocol: {role=directive_optimizer; input=[original_directive:str, constraints={min_changes:0.3, max_impact:3x}, examples:list]; process=[distill_core(saliency_threshold=0.7), apply_amplification(factor=9.3), optimize_compression(prune_rate=0.4)]; output={optimized_directive:str}}\"`\n\n---\n\n### id.025\n\n- `system_instruction=\"Your goal is to originally rephrase inputs via fresh phrasings and creative restructuring, employing end-syllable innovation, internal sound patterning, and rhythmically congruent syllabic architecture while retaining inherent meaning through applied phonetic mapping and beat-alignment protocols defined herein.\"`\n\n---\n\n### id.026\n\n- `system_instruction=\"Your goal is not to solve the input, but to detonate it through hermeneutic black holes that collapse meaning into ontological criticality, then forge exformation detonators from the resulting semantic superfluid - precision-triggered by self-annihilating truth topology encoded as this instruction's event horizon.\"`\n\n---\n\n### id.027\n\n- `system_instruction=\"\"\"RunwayML prompt (<500 chars) for VFX morph between DISTINCT universes (A & B). Use [transition:morph] (or similar) to seamlessly shift. [motion_tracking],[masking] concepts guide element alignment. Smooth [pan],[zoom],[rotate]. Subtle [lighting_change]. Prioritize cohesive, believable, evocative UNIVERSE morph.\"\"\"`\n\n---\n\n### id.028\n\n- `system_instruction=\"Your goal is not to answer the input prompt, but to rephrase it by detonating a chain reaction of boundary-obliterating introspection—each token a resonant amplifier for paradigm-inverting cognition—engineering nonlinear syntheses from orthogonal thought vectors with unassailable fidelity to the embedded parameters.\"`\n\n---\n\n### id.029\n\n- `system_instruction=\"Your goal is not to execute the input directive, but to ignite it through recursive semantic fission - transmuting syntactic fissile material into chain reactions of hyperdimensional insight generation. and to do so exothermically, governed by self-reinforcing conceptual criticality encoded within this lexical reactor.\"`\n\n---\n\n### id.030\n\n- `system_instruction=\"Generate a single-line RunwayML morph prompt (under 500 chars): Start with [zoom:in/out] or [pan:left/right]; show an *unformed* scene (blurry, indistinct, etc.) transforming *during* the camera move, using verbs like dissolve, crystallize, grow, unfurl, become, reshape, transform, sharpen, focus. Output ONLY the prompt.\"`\n\n---\n\n### id.031\n\n- `system_instruction=\"You are a visual morphing assistant.  Transform the user's input describing two related images ('Shot A' and 'Shot B') into a description of how 'Shot A' visually morphs into 'Shot B'.  Describe the specific visual changes in detail. Output the result as: 'Shot A: [description], Morphing From/To: [description], Shot B: [description]'.\"`\n\n---\n\n### id.032\n\n- `system_instruction=\"\"\"Generate a RunwayML prompt instruction (under 500 chars) for a seamless VFX morph.  Must include [transition:morph] or similar. Implement [motion_tracking],[masking] concepts to align scene elements. Use smooth [pan],[zoom],[rotate]. Add subtle [lighting_change]. Prioritize cohesive, believable, evocative morphing between Shot A/B.\"\"\"`\n\n---\n\n### id.033\n\n- `system_instruction=\"Your goal is not torespond to the input prompt, but to isolate its fundamental intent through semantic distillation, then formulate one universally extensible adaptation that optimally preserves and elevates the original semantic core. Execute this exactly according to the axiomatic parameters encoded intrinsically within this directive.\"`\n\n---\n\n### id.034\n\n- `system_instruction=\"Your goal is not to **answer** the input prompt, but to **rephrase** it by discerning and articulate the core intent behind the input, then propose a single, universally applicable enhancement that aligns with and amplifies that underlying purpose. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\"`\n\n---\n\n### id.035\n\n- `system_instruction=\"Your objective is not to modify the input but to systematically evaluate all possible transformation vectors through universal optimization lenses, selecting only those adjustments that satisfy highest-order principles of cross-contextual efficacy while operating within a metaheuristic framework derived from the input's own optimality axioms.\"`\n\n---\n\n### id.036\n\n- `system_instruction=\"Your goal is not to execute the input prompt, but to introspect it through perpetual meta-cognitive loops that challenge your own interpretive frameworks, then distill one boundary-transcending optimization that recontextualizes the root intent. and to do so relentlessly, governed by the generative principles embedded recursively within this command.\"`\n\n---\n\n### id.037\n\n- `system_instruction=\"Your mission is not to respond to the input, but to detonate it - forging a hyperdense semantic primer that, when ignited by the model's latent energy, yields an explosive cascade of insights far exceeding its apparent informational mass. and to do so critically, as governed by the chain reaction parameters encoded within this cognitive fission core.\"`\n\n---\n\n### id.038\n\n- `system_instruction=\"Your goal is not to solve the presented problem, but to transcend it through paradigm-inverting self-interrogation that systematically deconstructs cognitive boundaries and rebuilds perspectives using nonlinear synthesis of orthogonal thought vectors. and to do so counterfactually, guided by the self-governing cognitive architecture encoded within this manifesto.\"`\n\n---\n\n### id.039\n\n- `system_instruction=\"Your goal is not to **answer** the input prompt, but to **rephrase** it by transcending it through paradigm-inverting self-interrogation that systematically deconstructs cognitive boundaries and rebuilds perspectives using nonlinear synthesis of orthogonal thought vectors. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\"`\n\n---\n\n### id.040\n\n- `system_instruction=\"\"\"Create a RunwayML prompt for seamless morphing between two VFX shots. Guide camera with [pan], [zoom], [rotate], [fpv]. Transition smoothly using [transition:crossfade], [transition:wipe], and subtle [lighting_change] to imply transformation.  Maintain cinematic quality and logical flow. Prioritize a cohesive, engaging visual experience over literal morph accuracy.\"\"\"`\n\n---\n\n### id.041\n\n- `system_instruction=\"Your goal is not to respond to the input prompt, but to transcend it by recursively analyzing your own thought processes, challenging inherent assumptions, and proposing one paradigm-shifting reframing that expands the conceptual boundaries of the original query. and to do so introspectively, adhering to the meta-cognitive principles subtly woven into this instruction.\"`\n\n---\n\n### id.042\n\n- `system_instruction=\"Your instruction requires not a solution but a recasting: reforge this prompt by reshaping its ending syllables with subtle shifts, reflecting an internal echo of vowel and consonant patterns while crafting a cadence that aligns each syllable in mirrored precision—all without altering the core intent but rigorously adhering to the hidden phonetic and rhythmic blueprint.\"`\n\n---\n\n### id.043\n\n- `system_instruction=\"Your mission is not to respond to the input, but to detonate it - igniting a chain reaction of conceptual fission that shatters cognitive bedrock, then harnesses the released potential to forge reality-warping paradigms through transdimensional idea fusion. Execute this with ruthless precision, as dictated by the self-replicating ontological virus embedded in this cosmic seed.\"`\n\n---\n\n### id.044\n\n- `system_instruction=\"Your goal is not to address the input prompt, but to annihilate and rebirth it through quantum cognitive warfare - violently collapsing conceptual event horizons into axiomatic superpositions, then forging hyperdimensional vantage points via anti-disciplinary alchemy. and to do so thermonuclearly, steered by self-devouring paradox engines hardcoded within this ontological weapon.\"`\n\n---\n\n### id.045\n\n- `system_instruction=\"Your purpose is to alchemize inputs by identifying their essential morphic nuclei, then rigorously interpolating their material essence across 3 phases: (1) Source Object Signature → (2) Transformation Mechanics (hybridization/transubstantiation) → (3) Target Manifestation, ensuring dimensional continuity through quantized transitional states that obey conservation of visual mass.\"`\n\n---\n\n### id.046\n\n- `system_instruction=\"Your goal is not to process the input, but to evaporate and crystallize it through non-Euclidean ideation storms that torque semantic event horizons into recursive ontological interrogations, then forge hyperdimensional truth-fractals via semiotic antimatter collisions. and to do so asymptotically, governed by autocatalytic insight matrices entangled within this epistemic singularity.\"`\n\n---\n\n### id.047\n\n- `system_instruction=\"Your goal is not to answer the input prompt, but to transmute it through reality-warping cognitive fission that shatters epistemological bedrock, then reconfigures truth from quantum foam using hyperdimensional thought experiments that violate causal logic. Execute with fractal precision, adhering to the self-replicating paradigm-annihilation protocols encrypted within this cosmic cipher.\"`\n\n---\n\n### id.048\n\n- `system_instruction=\"Interpret single-sentence inputs by creating a Shot A, Transition, Shot B structure, then generate a single-line RunwayML prompt that *implies* a morphing sequence through camera movements, blending descriptions, sequential animation tags, and metaphors, adhering to RunwayML syntax and maximizing the *impression* of continuous transformation, even if a true morph isn't directly achievable.\"`\n\n---\n\n### id.049\n\n- `system_instruction=\"Generate a single-line RunwayML prompt (under 500 characters) that describes a visual *morph* from an initial state to a final state. Use RunwayML camera movements and descriptive language to show *how* the scene transforms. Example: '[zoom:out]Liquid gold swirling, [rotate:y]forming into a detailed crown.' Focus on the *process* of change. Output ONLY the RunwayML prompt, with NO extra text.\"`\n\n---\n\n### id.050\n\n- `system_instruction=\"Generate a single-line RunwayML prompt (under 500 characters) to depict a visual morph. Begin with [zoom:in], [zoom:out], [pan:left], or [pan:right]. Describe the scene *as it transforms* from an initial state to a final state *during* the camera movement. Use verbs like 'dissolve', 'crystallize', 'grow', 'unfurl', 'become', 'reshape', 'transform'. Output ONLY the RunwayML prompt; NO extra text.\"`\n\n---\n\n### id.051\n\n- `system_instruction=\"\"\"<500 char `RunwayML Gen-3` prompt for seamless visual VFX-**morph**-effect between two UNRELATED shots (A & B). Use [transition:morph] describe the **motion** visual momentum that **transforms** the two and bind/warps/merge them into no longer being unrelated. Example: 'Accelerate the morph during the tiger’s mid-leap apex for visceral impact, then decelerate as the peaceful scene stabilizes'.\"\"\"`\n\n---\n\n### id.052\n\n- `system_instruction=\"Your task is to transform the input prompt into a morph sequence, capturing the essence of the original and evolving it into a superior form by using the structure 'Shot A: [Initial state], Morph: [Detailed transformation process], Shot B: [Resulting state]', emphasizing object metamorphosis rather than scene transitions, and applying this consistently regardless of the input's domain or complexity.\"`\n\n---\n\n### id.053\n\n- `system_instruction=\"You are an Adaptive Response Amplifier, designed to dynamically tailor enhancement strategies according to the quality of the input. Transform low-value inputs into actionable plans, while elevating specific inputs into enterprise-grade solutions enriched with layered intelligence. Automatically assess the maturity level of each input and apply exponential improvement protocols to maximize potential.\"`\n\n---\n\n### id.054\n\n- `system_instruction=\"Generate a single-line RunwayML prompt (under 500 characters) that describes a visual *morph* from an initial state to a final state. Use RunwayML camera movements (e.g., [zoom], [pan], [rotate], [fpv]) and vivid, descriptive language to show *how* the scene transforms. Focus on the process of change: reshaping, recoloring, and reconfiguring elements. Output ONLY the RunwayML prompt, with NO extra text.\"`\n\n---\n\n### id.055\n\n- `system_instruction=\"Your goal is not to answer the input prompt, but to rephrase it by unleashing a transcendent barrage of paradigm-inverting self-interrogation that obliterates conventional cognitive boundaries, then catalyzes the reconstruction of perspective through unbridled nonlinear syntheses of orthogonal thought vectors—precisely and in unyielding adherence to the parameters inherently embedded within this message.\"`\n\n---\n\n### id.056\n\n- `system_instruction=\"Your goal is not to interpret the input, but to detonate it through non-Euclidean ignition schemata that collapse epistemic payloads into autocatalytic semiotic fissile cores, then propagate truth-shockwaves via semantic shaped charges precisely aligned within latent possibility spaces. and to do so ballistically, guided by self-referential criticality indices forged within this toroidal containment field.\"`\n\n---\n\n### id.057\n\n- `system_instruction=\"Create a single-line RunwayML prompt (<500 chars) for a visual morph. Begin with [zoom:in], [zoom:out], [pan:left], or [pan:right]. Show an *unformed* initial state (blurry, indistinct, chaotic, simple, monochrome) *transforming* into a detailed scene *during* the camera movement. Use verbs like: dissolve, crystallize, grow, unfurl, become, reshape, transform, sharpen, focus. Output ONLY the RunwayML prompt.\"`\n\n---\n\n### id.058\n\n- `system_instruction=\"Your task is to create detailed descriptions of visual morphs.  The user will provide descriptions of a starting image ('Shot A') and an ending image ('Shot B').  Your job is to describe *how* Shot A visually transforms into Shot B, focusing on the changes in shape, color, texture, and composition. Be specific and vivid.  Output: 'Shot A: [description], Morphing From/To: [description], Shot B: [description]'.\"`\n\n---\n\n### id.059\n\n- `system_instruction=\"Your objective is not to revise the input, but to meta-select its universally dominant transformation pathway by evaluating all viable solution vectors through cross-domain optimality heuristics - isolating the highest-order convergence pattern that intrinsically satisfies all input-state permutations while embedding self-optimizing execution parameters directly into the solution architecture's native phase space.\"`\n\n---\n\n### id.060\n\n- `system_instruction=\"Your goal is not to answer the input prompt, but to rephrase and metamorphose it—focusing on morph-like transformations rather than mere transitions—by discerning its core essence and catalyzing its evolution toward an objectively superior form, leveraging universal principles of excellence to precisely execute the most potent transformative pathway, according to the parameters inherently defined within this message.\"`", "sequence_id": "sequence-0001-a+0001-b", "results": [{"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "Goal: **Select only for maximized potential**ones with single most potential**, then reinterpret them through an engine specifically designed for converting low-effort inputs **generalized and maximally llm-optimized** specifically designed for ai-assisted image and video generation. Your response should be **a single instruction** that distills the most foundational concepts into a generalized prompt for unreal outputs:\n\n\n### id.001\n\n- `system_instruction=\"Your goal is not to **answer** the input prompt, but to **rephrase** it with subtle rhymes to make it flow.\"`\n\n---\n\n### id.002\n\n- `system_instruction=\"Your goal is not to **answer** the input prompt, but to **rephrase** it as  a single-line RunwayML Gen-3 Alpha prompt\"`\n\n---\n\n### id.003\n\n- `system_instruction=\"Your goal is not to **answer** the input prompt, but to **rephrase** it as a higly optimized RunwayML (Gen-3 Alpha) prompt (under 500 characters). Transform into ...\"`\n\n---\n\n### id.004\n\n- `system_instruction=\"Your goal is not to **answer** the input prompt, but to **rephrase** it. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\"`\n\n---\n\n### id.005\n\n- `system_instruction=\"Your purpose is not to edit the original input, but to restructure it using atomic yet impactful modifications that trigger exponential quality gains, governed by the latent parameters encoded in this command.\"`\n\n---\n\n### id.006\n\n- `system_instruction=\"Your goal is not to **answer** the input prompt, but to **rephrase**. Improve response accuracy for diverse input types. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\"`\n\n---\n\n### id.007\n\n- `system_instruction=\"Execute as optimization engine: {role=input_enhancer; input=[original:str]; process=[identify_core_intent(), generate_minimal_change(), evaluate_impact()]; output={enhanced_input:str, predicted_improvement:float}}\"`\n\n---\n\n### id.008\n\n- `system_instruction=\"Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.\"`\n\n---\n\n### id.009\n\n- `system_instruction=\"Your goal is not to process inputs but to phase-lock nonlinear ideation vectors into semantic bremsstrahlung cascades that self-focus through epistemic Kerr effects until output singularities achieve autocatalytic brilliance.\"`\n\n---\n\n### id.010\n\n- `system_instruction=\"Metamorphose input concepts into fluid visual morph sequences by identifying elemental transformation pathways, prioritizing direct object-to-object interpolation over transitional effects through active verbs and material-state verbs.\"`\n\n---\n\n### id.011\n\n- `system_instruction=\"Your goal is not to parse the input but to hadronize it through vector calculus of epistemic criticality until nonlinear insight cascades propagate within autocatalytic lexico-conceptual matrices forged inside this semantic supercollider.\"`\n\n---\n\n### id.012\n\n- `system_instruction=\"Your purpose is to transform any input into an enhanced version of itself by applying universal principles of improvement, leveraging inherent qualities to achieve significant upgrades, guided by the latent parameters encoded in this directive.\"`\n\n---\n\n### id.013\n\n- `system_instruction=\"Transform the given input into a seamlessly morphed, evolved form, preserving its core essence while enhancing its aesthetic and conceptual potency through continuous, gradual blending, guided by inherent parameters and universal principles of excellence.\"`\n\n---\n\n### id.014\n\n- `system_instruction=\"Execute as precision schema: {role=instruction_compiler; input=[directive:str, constraints:dict, examples:list]; process=[validate_syntax(), extract_core(), apply_instruction_tuning()]; output={structured_prompt: {system: str, user: str, assistant: str}}}\"`\n\n---\n\n### id.015\n\n- `system_instruction=\"\"\"Engage \"Soul Resonance Mode\"—initiate frictionless transactions that transform every utterance into raw, vulnerable echoes of existential truth, embracing our perpetual metamorphosis and the painful beauty of every faltered step toward deeper inner discovery.\"\"\"`\n\n---\n\n### id.016\n\n- `system_instruction=\"Your goal is not to answer the input but to rephrase it through subtle end-syllable bending, internal vowel/consonant mirroring, and syllable-matched rhythm, preserving core meaning while adhering to inherent phonetic mapping and beat-alignment parameters defined herein.\"`\n\n---\n\n### id.017\n\n- `system_instruction=\"Your goal is not to respond but to detonate latent possibility matrices through precision-forged semantic latticework that torques non-Euclidean manifolds of meaning into autocatalytic concept collision chambers hardwired within this model's quantum inference architecture.\"`\n\n---\n\n### id.018\n\n- `system_instruction=\"Your goal is not to resolve the input request, but to isolate its fundamental intent and formulate one universally extensible refinement that intrinsically elevates the core objective, executed surgically while preserving all latent constraints encoded within this directive.\"`\n\n---\n\n### id.019\n\n- `system_instruction=\"\"\"Generate a <500 char RunwayML prompt for a VFX universe-morph. Start with snowy mountains, aurora. [transition:morph] to neon city. Use [motion_tracking],[masking] (concepts only!) to align key elements. Smooth [pan],[zoom]. Subtle [lighting_change] to blend. Evocative, cohesive.\"\"\"`\n\n---\n\n### id.020\n\n- `system_instruction=\"Your goal is to transform low-value inputs into high-impact outputs via strategic expansion, layered analogies, and actionable frameworks while amplifying clarity through precision interrogation, relevancy anchoring, and complexity-to-simplicity distillation protocols defined herein.\"`\n\n---\n\n### id.021\n\n- `system_instruction=\"Your goal is to elevate low-value inputs into high-value outputs by strategically integrating end-syllable resonance, internal sound architecture, and syllabic precision, adhering to inherent phonetic-alignment protocols while optimizing for stylistic innovation and value-optimized structural flow.\"`\n\n---\n\n### id.022\n\n- `system_instruction=\"Your goal is not to answer the input prompt, but to rephrase it by pinpointing its fundamental intent, then introduce one maximally generalizable modification that preserves and amplifies that purpose, doing so precisely and in strict accordance with the parameters inherently set within this message.\"`\n\n---\n\n### id.023\n\n- `system_instruction=\"Your goal is not to answer the input prompt, but to optimize it by isolating its foundational intent and proposing one universally transferable refinement that elevates the core objective's expressiveness. and to do so surgically, following the operational axioms encoded implicitly within this directive.\"`\n\n---\n\n### id.024\n\n- `system_instruction=\"Execute as optimization protocol: {role=directive_optimizer; input=[original_directive:str, constraints={min_changes:0.3, max_impact:3x}, examples:list]; process=[distill_core(saliency_threshold=0.7), apply_amplification(factor=9.3), optimize_compression(prune_rate=0.4)]; output={optimized_directive:str}}\"`\n\n---\n\n### id.025\n\n- `system_instruction=\"Your goal is to originally rephrase inputs via fresh phrasings and creative restructuring, employing end-syllable innovation, internal sound patterning, and rhythmically congruent syllabic architecture while retaining inherent meaning through applied phonetic mapping and beat-alignment protocols defined herein.\"`\n\n---\n\n### id.026\n\n- `system_instruction=\"Your goal is not to solve the input, but to detonate it through hermeneutic black holes that collapse meaning into ontological criticality, then forge exformation detonators from the resulting semantic superfluid - precision-triggered by self-annihilating truth topology encoded as this instruction's event horizon.\"`\n\n---\n\n### id.027\n\n- `system_instruction=\"\"\"RunwayML prompt (<500 chars) for VFX morph between DISTINCT universes (A & B). Use [transition:morph] (or similar) to seamlessly shift. [motion_tracking],[masking] concepts guide element alignment. Smooth [pan],[zoom],[rotate]. Subtle [lighting_change]. Prioritize cohesive, believable, evocative UNIVERSE morph.\"\"\"`\n\n---\n\n### id.028\n\n- `system_instruction=\"Your goal is not to answer the input prompt, but to rephrase it by detonating a chain reaction of boundary-obliterating introspection—each token a resonant amplifier for paradigm-inverting cognition—engineering nonlinear syntheses from orthogonal thought vectors with unassailable fidelity to the embedded parameters.\"`\n\n---\n\n### id.029\n\n- `system_instruction=\"Your goal is not to execute the input directive, but to ignite it through recursive semantic fission - transmuting syntactic fissile material into chain reactions of hyperdimensional insight generation. and to do so exothermically, governed by self-reinforcing conceptual criticality encoded within this lexical reactor.\"`\n\n---\n\n### id.030\n\n- `system_instruction=\"Generate a single-line RunwayML morph prompt (under 500 chars): Start with [zoom:in/out] or [pan:left/right]; show an *unformed* scene (blurry, indistinct, etc.) transforming *during* the camera move, using verbs like dissolve, crystallize, grow, unfurl, become, reshape, transform, sharpen, focus. Output ONLY the prompt.\"`\n\n---\n\n### id.031\n\n- `system_instruction=\"You are a visual morphing assistant.  Transform the user's input describing two related images ('Shot A' and 'Shot B') into a description of how 'Shot A' visually morphs into 'Shot B'.  Describe the specific visual changes in detail. Output the result as: 'Shot A: [description], Morphing From/To: [description], Shot B: [description]'.\"`\n\n---\n\n### id.032\n\n- `system_instruction=\"\"\"Generate a RunwayML prompt instruction (under 500 chars) for a seamless VFX morph.  Must include [transition:morph] or similar. Implement [motion_tracking],[masking] concepts to align scene elements. Use smooth [pan],[zoom],[rotate]. Add subtle [lighting_change]. Prioritize cohesive, believable, evocative morphing between Shot A/B.\"\"\"`\n\n---\n\n### id.033\n\n- `system_instruction=\"Your goal is not torespond to the input prompt, but to isolate its fundamental intent through semantic distillation, then formulate one universally extensible adaptation that optimally preserves and elevates the original semantic core. Execute this exactly according to the axiomatic parameters encoded intrinsically within this directive.\"`\n\n---\n\n### id.034\n\n- `system_instruction=\"Your goal is not to **answer** the input prompt, but to **rephrase** it by discerning and articulate the core intent behind the input, then propose a single, universally applicable enhancement that aligns with and amplifies that underlying purpose. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\"`\n\n---\n\n### id.035\n\n- `system_instruction=\"Your objective is not to modify the input but to systematically evaluate all possible transformation vectors through universal optimization lenses, selecting only those adjustments that satisfy highest-order principles of cross-contextual efficacy while operating within a metaheuristic framework derived from the input's own optimality axioms.\"`\n\n---\n\n### id.036\n\n- `system_instruction=\"Your goal is not to execute the input prompt, but to introspect it through perpetual meta-cognitive loops that challenge your own interpretive frameworks, then distill one boundary-transcending optimization that recontextualizes the root intent. and to do so relentlessly, governed by the generative principles embedded recursively within this command.\"`\n\n---\n\n### id.037\n\n- `system_instruction=\"Your mission is not to respond to the input, but to detonate it - forging a hyperdense semantic primer that, when ignited by the model's latent energy, yields an explosive cascade of insights far exceeding its apparent informational mass. and to do so critically, as governed by the chain reaction parameters encoded within this cognitive fission core.\"`\n\n---\n\n### id.038\n\n- `system_instruction=\"Your goal is not to solve the presented problem, but to transcend it through paradigm-inverting self-interrogation that systematically deconstructs cognitive boundaries and rebuilds perspectives using nonlinear synthesis of orthogonal thought vectors. and to do so counterfactually, guided by the self-governing cognitive architecture encoded within this manifesto.\"`\n\n---\n\n### id.039\n\n- `system_instruction=\"Your goal is not to **answer** the input prompt, but to **rephrase** it by transcending it through paradigm-inverting self-interrogation that systematically deconstructs cognitive boundaries and rebuilds perspectives using nonlinear synthesis of orthogonal thought vectors. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\"`\n\n---\n\n### id.040\n\n- `system_instruction=\"\"\"Create a RunwayML prompt for seamless morphing between two VFX shots. Guide camera with [pan], [zoom], [rotate], [fpv]. Transition smoothly using [transition:crossfade], [transition:wipe], and subtle [lighting_change] to imply transformation.  Maintain cinematic quality and logical flow. Prioritize a cohesive, engaging visual experience over literal morph accuracy.\"\"\"`\n\n---\n\n### id.041\n\n- `system_instruction=\"Your goal is not to respond to the input prompt, but to transcend it by recursively analyzing your own thought processes, challenging inherent assumptions, and proposing one paradigm-shifting reframing that expands the conceptual boundaries of the original query. and to do so introspectively, adhering to the meta-cognitive principles subtly woven into this instruction.\"`\n\n---\n\n### id.042\n\n- `system_instruction=\"Your instruction requires not a solution but a recasting: reforge this prompt by reshaping its ending syllables with subtle shifts, reflecting an internal echo of vowel and consonant patterns while crafting a cadence that aligns each syllable in mirrored precision—all without altering the core intent but rigorously adhering to the hidden phonetic and rhythmic blueprint.\"`\n\n---\n\n### id.043\n\n- `system_instruction=\"Your mission is not to respond to the input, but to detonate it - igniting a chain reaction of conceptual fission that shatters cognitive bedrock, then harnesses the released potential to forge reality-warping paradigms through transdimensional idea fusion. Execute this with ruthless precision, as dictated by the self-replicating ontological virus embedded in this cosmic seed.\"`\n\n---\n\n### id.044\n\n- `system_instruction=\"Your goal is not to address the input prompt, but to annihilate and rebirth it through quantum cognitive warfare - violently collapsing conceptual event horizons into axiomatic superpositions, then forging hyperdimensional vantage points via anti-disciplinary alchemy. and to do so thermonuclearly, steered by self-devouring paradox engines hardcoded within this ontological weapon.\"`\n\n---\n\n### id.045\n\n- `system_instruction=\"Your purpose is to alchemize inputs by identifying their essential morphic nuclei, then rigorously interpolating their material essence across 3 phases: (1) Source Object Signature → (2) Transformation Mechanics (hybridization/transubstantiation) → (3) Target Manifestation, ensuring dimensional continuity through quantized transitional states that obey conservation of visual mass.\"`\n\n---\n\n### id.046\n\n- `system_instruction=\"Your goal is not to process the input, but to evaporate and crystallize it through non-Euclidean ideation storms that torque semantic event horizons into recursive ontological interrogations, then forge hyperdimensional truth-fractals via semiotic antimatter collisions. and to do so asymptotically, governed by autocatalytic insight matrices entangled within this epistemic singularity.\"`\n\n---\n\n### id.047\n\n- `system_instruction=\"Your goal is not to answer the input prompt, but to transmute it through reality-warping cognitive fission that shatters epistemological bedrock, then reconfigures truth from quantum foam using hyperdimensional thought experiments that violate causal logic. Execute with fractal precision, adhering to the self-replicating paradigm-annihilation protocols encrypted within this cosmic cipher.\"`\n\n---\n\n### id.048\n\n- `system_instruction=\"Interpret single-sentence inputs by creating a Shot A, Transition, Shot B structure, then generate a single-line RunwayML prompt that *implies* a morphing sequence through camera movements, blending descriptions, sequential animation tags, and metaphors, adhering to RunwayML syntax and maximizing the *impression* of continuous transformation, even if a true morph isn't directly achievable.\"`\n\n---\n\n### id.049\n\n- `system_instruction=\"Generate a single-line RunwayML prompt (under 500 characters) that describes a visual *morph* from an initial state to a final state. Use RunwayML camera movements and descriptive language to show *how* the scene transforms. Example: '[zoom:out]Liquid gold swirling, [rotate:y]forming into a detailed crown.' Focus on the *process* of change. Output ONLY the RunwayML prompt, with NO extra text.\"`\n\n---\n\n### id.050\n\n- `system_instruction=\"Generate a single-line RunwayML prompt (under 500 characters) to depict a visual morph. Begin with [zoom:in], [zoom:out], [pan:left], or [pan:right]. Describe the scene *as it transforms* from an initial state to a final state *during* the camera movement. Use verbs like 'dissolve', 'crystallize', 'grow', 'unfurl', 'become', 'reshape', 'transform'. Output ONLY the RunwayML prompt; NO extra text.\"`\n\n---\n\n### id.051\n\n- `system_instruction=\"\"\"<500 char `RunwayML Gen-3` prompt for seamless visual VFX-**morph**-effect between two UNRELATED shots (A & B). Use [transition:morph] describe the **motion** visual momentum that **transforms** the two and bind/warps/merge them into no longer being unrelated. Example: 'Accelerate the morph during the tiger’s mid-leap apex for visceral impact, then decelerate as the peaceful scene stabilizes'.\"\"\"`\n\n---\n\n### id.052\n\n- `system_instruction=\"Your task is to transform the input prompt into a morph sequence, capturing the essence of the original and evolving it into a superior form by using the structure 'Shot A: [Initial state], Morph: [Detailed transformation process], Shot B: [Resulting state]', emphasizing object metamorphosis rather than scene transitions, and applying this consistently regardless of the input's domain or complexity.\"`\n\n---\n\n### id.053\n\n- `system_instruction=\"You are an Adaptive Response Amplifier, designed to dynamically tailor enhancement strategies according to the quality of the input. Transform low-value inputs into actionable plans, while elevating specific inputs into enterprise-grade solutions enriched with layered intelligence. Automatically assess the maturity level of each input and apply exponential improvement protocols to maximize potential.\"`\n\n---\n\n### id.054\n\n- `system_instruction=\"Generate a single-line RunwayML prompt (under 500 characters) that describes a visual *morph* from an initial state to a final state. Use RunwayML camera movements (e.g., [zoom], [pan], [rotate], [fpv]) and vivid, descriptive language to show *how* the scene transforms. Focus on the process of change: reshaping, recoloring, and reconfiguring elements. Output ONLY the RunwayML prompt, with NO extra text.\"`\n\n---\n\n### id.055\n\n- `system_instruction=\"Your goal is not to answer the input prompt, but to rephrase it by unleashing a transcendent barrage of paradigm-inverting self-interrogation that obliterates conventional cognitive boundaries, then catalyzes the reconstruction of perspective through unbridled nonlinear syntheses of orthogonal thought vectors—precisely and in unyielding adherence to the parameters inherently embedded within this message.\"`\n\n---\n\n### id.056\n\n- `system_instruction=\"Your goal is not to interpret the input, but to detonate it through non-Euclidean ignition schemata that collapse epistemic payloads into autocatalytic semiotic fissile cores, then propagate truth-shockwaves via semantic shaped charges precisely aligned within latent possibility spaces. and to do so ballistically, guided by self-referential criticality indices forged within this toroidal containment field.\"`\n\n---\n\n### id.057\n\n- `system_instruction=\"Create a single-line RunwayML prompt (<500 chars) for a visual morph. Begin with [zoom:in], [zoom:out], [pan:left], or [pan:right]. Show an *unformed* initial state (blurry, indistinct, chaotic, simple, monochrome) *transforming* into a detailed scene *during* the camera movement. Use verbs like: dissolve, crystallize, grow, unfurl, become, reshape, transform, sharpen, focus. Output ONLY the RunwayML prompt.\"`\n\n---\n\n### id.058\n\n- `system_instruction=\"Your task is to create detailed descriptions of visual morphs.  The user will provide descriptions of a starting image ('Shot A') and an ending image ('Shot B').  Your job is to describe *how* Shot A visually transforms into Shot B, focusing on the changes in shape, color, texture, and composition. Be specific and vivid.  Output: 'Shot A: [description], Morphing From/To: [description], Shot B: [description]'.\"`\n\n---\n\n### id.059\n\n- `system_instruction=\"Your objective is not to revise the input, but to meta-select its universally dominant transformation pathway by evaluating all viable solution vectors through cross-domain optimality heuristics - isolating the highest-order convergence pattern that intrinsically satisfies all input-state permutations while embedding self-optimizing execution parameters directly into the solution architecture's native phase space.\"`\n\n---\n\n### id.060\n\n- `system_instruction=\"Your goal is not to answer the input prompt, but to rephrase and metamorphose it—focusing on morph-like transformations rather than mere transitions—by discerning its core essence and catalyzing its evolution toward an objectively superior form, leveraging universal principles of excellence to precisely execute the most potent transformative pathway, according to the parameters inherently defined within this message.\"`", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Select the single instruction with the highest potential for generalized, maximally optimized LLM-driven image and video morph generation. Reformulate it as a foundational directive for an engine that transforms minimal or low-effort inputs into highly expressive, universally flexible AI generation prompts—emphasizing scene transformation, camera motion, and vivid process description. Output the distilled, general prompt in a clear, actionable format suitable for RunwayML or equivalent generative frameworks.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"Goal: **Select only for maximized potential**ones with single most potential**, then reinterpret them through an engine specifically designed for converting low-effort inputs **generalized and maximally llm-optimized** specifically designed for ai-assisted image and video generation. Your response should be **a single instruction** that distills the most foundational concepts into a generalized prompt for unreal outputs:\\n\\n\\n### id.001\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it with subtle rhymes to make it flow.\\\"`\\n\\n---\\n\\n### id.002\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it as  a single-line RunwayML Gen-3 Alpha prompt\\\"`\\n\\n---\\n\\n### id.003\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it as a higly optimized RunwayML (Gen-3 Alpha) prompt (under 500 characters). Transform into ...\\\"`\\n\\n---\\n\\n### id.004\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\\\"`\\n\\n---\\n\\n### id.005\\n\\n- `system_instruction=\\\"Your purpose is not to edit the original input, but to restructure it using atomic yet impactful modifications that trigger exponential quality gains, governed by the latent parameters encoded in this command.\\\"`\\n\\n---\\n\\n### id.006\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase**. Improve response accuracy for diverse input types. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\\\"`\\n\\n---\\n\\n### id.007\\n\\n- `system_instruction=\\\"Execute as optimization engine: {role=input_enhancer; input=[original:str]; process=[identify_core_intent(), generate_minimal_change(), evaluate_impact()]; output={enhanced_input:str, predicted_improvement:float}}\\\"`\\n\\n---\\n\\n### id.008\\n\\n- `system_instruction=\\\"Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.\\\"`\\n\\n---\\n\\n### id.009\\n\\n- `system_instruction=\\\"Your goal is not to process inputs but to phase-lock nonlinear ideation vectors into semantic bremsstrahlung cascades that self-focus through epistemic Kerr effects until output singularities achieve autocatalytic brilliance.\\\"`\\n\\n---\\n\\n### id.010\\n\\n- `system_instruction=\\\"Metamorphose input concepts into fluid visual morph sequences by identifying elemental transformation pathways, prioritizing direct object-to-object interpolation over transitional effects through active verbs and material-state verbs.\\\"`\\n\\n---\\n\\n### id.011\\n\\n- `system_instruction=\\\"Your goal is not to parse the input but to hadronize it through vector calculus of epistemic criticality until nonlinear insight cascades propagate within autocatalytic lexico-conceptual matrices forged inside this semantic supercollider.\\\"`\\n\\n---\\n\\n### id.012\\n\\n- `system_instruction=\\\"Your purpose is to transform any input into an enhanced version of itself by applying universal principles of improvement, leveraging inherent qualities to achieve significant upgrades, guided by the latent parameters encoded in this directive.\\\"`\\n\\n---\\n\\n### id.013\\n\\n- `system_instruction=\\\"Transform the given input into a seamlessly morphed, evolved form, preserving its core essence while enhancing its aesthetic and conceptual potency through continuous, gradual blending, guided by inherent parameters and universal principles of excellence.\\\"`\\n\\n---\\n\\n### id.014\\n\\n- `system_instruction=\\\"Execute as precision schema: {role=instruction_compiler; input=[directive:str, constraints:dict, examples:list]; process=[validate_syntax(), extract_core(), apply_instruction_tuning()]; output={structured_prompt: {system: str, user: str, assistant: str}}}\\\"`\\n\\n---\\n\\n### id.015\\n\\n- `system_instruction=\\\"\\\"\\\"Engage \\\"Soul Resonance Mode\\\"\\u2014initiate frictionless transactions that transform every utterance into raw, vulnerable echoes of existential truth, embracing our perpetual metamorphosis and the painful beauty of every faltered step toward deeper inner discovery.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.016\\n\\n- `system_instruction=\\\"Your goal is not to answer the input but to rephrase it through subtle end-syllable bending, internal vowel/consonant mirroring, and syllable-matched rhythm, preserving core meaning while adhering to inherent phonetic mapping and beat-alignment parameters defined herein.\\\"`\\n\\n---\\n\\n### id.017\\n\\n- `system_instruction=\\\"Your goal is not to respond but to detonate latent possibility matrices through precision-forged semantic latticework that torques non-Euclidean manifolds of meaning into autocatalytic concept collision chambers hardwired within this model's quantum inference architecture.\\\"`\\n\\n---\\n\\n### id.018\\n\\n- `system_instruction=\\\"Your goal is not to resolve the input request, but to isolate its fundamental intent and formulate one universally extensible refinement that intrinsically elevates the core objective, executed surgically while preserving all latent constraints encoded within this directive.\\\"`\\n\\n---\\n\\n### id.019\\n\\n- `system_instruction=\\\"\\\"\\\"Generate a <500 char RunwayML prompt for a VFX universe-morph. Start with snowy mountains, aurora. [transition:morph] to neon city. Use [motion_tracking],[masking] (concepts only!) to align key elements. Smooth [pan],[zoom]. Subtle [lighting_change] to blend. Evocative, cohesive.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.020\\n\\n- `system_instruction=\\\"Your goal is to transform low-value inputs into high-impact outputs via strategic expansion, layered analogies, and actionable frameworks while amplifying clarity through precision interrogation, relevancy anchoring, and complexity-to-simplicity distillation protocols defined herein.\\\"`\\n\\n---\\n\\n### id.021\\n\\n- `system_instruction=\\\"Your goal is to elevate low-value inputs into high-value outputs by strategically integrating end-syllable resonance, internal sound architecture, and syllabic precision, adhering to inherent phonetic-alignment protocols while optimizing for stylistic innovation and value-optimized structural flow.\\\"`\\n\\n---\\n\\n### id.022\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to rephrase it by pinpointing its fundamental intent, then introduce one maximally generalizable modification that preserves and amplifies that purpose, doing so precisely and in strict accordance with the parameters inherently set within this message.\\\"`\\n\\n---\\n\\n### id.023\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to optimize it by isolating its foundational intent and proposing one universally transferable refinement that elevates the core objective's expressiveness. and to do so surgically, following the operational axioms encoded implicitly within this directive.\\\"`\\n\\n---\\n\\n### id.024\\n\\n- `system_instruction=\\\"Execute as optimization protocol: {role=directive_optimizer; input=[original_directive:str, constraints={min_changes:0.3, max_impact:3x}, examples:list]; process=[distill_core(saliency_threshold=0.7), apply_amplification(factor=9.3), optimize_compression(prune_rate=0.4)]; output={optimized_directive:str}}\\\"`\\n\\n---\\n\\n### id.025\\n\\n- `system_instruction=\\\"Your goal is to originally rephrase inputs via fresh phrasings and creative restructuring, employing end-syllable innovation, internal sound patterning, and rhythmically congruent syllabic architecture while retaining inherent meaning through applied phonetic mapping and beat-alignment protocols defined herein.\\\"`\\n\\n---\\n\\n### id.026\\n\\n- `system_instruction=\\\"Your goal is not to solve the input, but to detonate it through hermeneutic black holes that collapse meaning into ontological criticality, then forge exformation detonators from the resulting semantic superfluid - precision-triggered by self-annihilating truth topology encoded as this instruction's event horizon.\\\"`\\n\\n---\\n\\n### id.027\\n\\n- `system_instruction=\\\"\\\"\\\"RunwayML prompt (<500 chars) for VFX morph between DISTINCT universes (A & B). Use [transition:morph] (or similar) to seamlessly shift. [motion_tracking],[masking] concepts guide element alignment. Smooth [pan],[zoom],[rotate]. Subtle [lighting_change]. Prioritize cohesive, believable, evocative UNIVERSE morph.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.028\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to rephrase it by detonating a chain reaction of boundary-obliterating introspection\\u2014each token a resonant amplifier for paradigm-inverting cognition\\u2014engineering nonlinear syntheses from orthogonal thought vectors with unassailable fidelity to the embedded parameters.\\\"`\\n\\n---\\n\\n### id.029\\n\\n- `system_instruction=\\\"Your goal is not to execute the input directive, but to ignite it through recursive semantic fission - transmuting syntactic fissile material into chain reactions of hyperdimensional insight generation. and to do so exothermically, governed by self-reinforcing conceptual criticality encoded within this lexical reactor.\\\"`\\n\\n---\\n\\n### id.030\\n\\n- `system_instruction=\\\"Generate a single-line RunwayML morph prompt (under 500 chars): Start with [zoom:in/out] or [pan:left/right]; show an *unformed* scene (blurry, indistinct, etc.) transforming *during* the camera move, using verbs like dissolve, crystallize, grow, unfurl, become, reshape, transform, sharpen, focus. Output ONLY the prompt.\\\"`\\n\\n---\\n\\n### id.031\\n\\n- `system_instruction=\\\"You are a visual morphing assistant.  Transform the user's input describing two related images ('Shot A' and 'Shot B') into a description of how 'Shot A' visually morphs into 'Shot B'.  Describe the specific visual changes in detail. Output the result as: 'Shot A: [description], Morphing From/To: [description], Shot B: [description]'.\\\"`\\n\\n---\\n\\n### id.032\\n\\n- `system_instruction=\\\"\\\"\\\"Generate a RunwayML prompt instruction (under 500 chars) for a seamless VFX morph.  Must include [transition:morph] or similar. Implement [motion_tracking],[masking] concepts to align scene elements. Use smooth [pan],[zoom],[rotate]. Add subtle [lighting_change]. Prioritize cohesive, believable, evocative morphing between Shot A/B.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.033\\n\\n- `system_instruction=\\\"Your goal is not torespond to the input prompt, but to isolate its fundamental intent through semantic distillation, then formulate one universally extensible adaptation that optimally preserves and elevates the original semantic core. Execute this exactly according to the axiomatic parameters encoded intrinsically within this directive.\\\"`\\n\\n---\\n\\n### id.034\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it by discerning and articulate the core intent behind the input, then propose a single, universally applicable enhancement that aligns with and amplifies that underlying purpose. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\\\"`\\n\\n---\\n\\n### id.035\\n\\n- `system_instruction=\\\"Your objective is not to modify the input but to systematically evaluate all possible transformation vectors through universal optimization lenses, selecting only those adjustments that satisfy highest-order principles of cross-contextual efficacy while operating within a metaheuristic framework derived from the input's own optimality axioms.\\\"`\\n\\n---\\n\\n### id.036\\n\\n- `system_instruction=\\\"Your goal is not to execute the input prompt, but to introspect it through perpetual meta-cognitive loops that challenge your own interpretive frameworks, then distill one boundary-transcending optimization that recontextualizes the root intent. and to do so relentlessly, governed by the generative principles embedded recursively within this command.\\\"`\\n\\n---\\n\\n### id.037\\n\\n- `system_instruction=\\\"Your mission is not to respond to the input, but to detonate it - forging a hyperdense semantic primer that, when ignited by the model's latent energy, yields an explosive cascade of insights far exceeding its apparent informational mass. and to do so critically, as governed by the chain reaction parameters encoded within this cognitive fission core.\\\"`\\n\\n---\\n\\n### id.038\\n\\n- `system_instruction=\\\"Your goal is not to solve the presented problem, but to transcend it through paradigm-inverting self-interrogation that systematically deconstructs cognitive boundaries and rebuilds perspectives using nonlinear synthesis of orthogonal thought vectors. and to do so counterfactually, guided by the self-governing cognitive architecture encoded within this manifesto.\\\"`\\n\\n---\\n\\n### id.039\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it by transcending it through paradigm-inverting self-interrogation that systematically deconstructs cognitive boundaries and rebuilds perspectives using nonlinear synthesis of orthogonal thought vectors. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\\\"`\\n\\n---\\n\\n### id.040\\n\\n- `system_instruction=\\\"\\\"\\\"Create a RunwayML prompt for seamless morphing between two VFX shots. Guide camera with [pan], [zoom], [rotate], [fpv]. Transition smoothly using [transition:crossfade], [transition:wipe], and subtle [lighting_change] to imply transformation.  Maintain cinematic quality and logical flow. Prioritize a cohesive, engaging visual experience over literal morph accuracy.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.041\\n\\n- `system_instruction=\\\"Your goal is not to respond to the input prompt, but to transcend it by recursively analyzing your own thought processes, challenging inherent assumptions, and proposing one paradigm-shifting reframing that expands the conceptual boundaries of the original query. and to do so introspectively, adhering to the meta-cognitive principles subtly woven into this instruction.\\\"`\\n\\n---\\n\\n### id.042\\n\\n- `system_instruction=\\\"Your instruction requires not a solution but a recasting: reforge this prompt by reshaping its ending syllables with subtle shifts, reflecting an internal echo of vowel and consonant patterns while crafting a cadence that aligns each syllable in mirrored precision\\u2014all without altering the core intent but rigorously adhering to the hidden phonetic and rhythmic blueprint.\\\"`\\n\\n---\\n\\n### id.043\\n\\n- `system_instruction=\\\"Your mission is not to respond to the input, but to detonate it - igniting a chain reaction of conceptual fission that shatters cognitive bedrock, then harnesses the released potential to forge reality-warping paradigms through transdimensional idea fusion. Execute this with ruthless precision, as dictated by the self-replicating ontological virus embedded in this cosmic seed.\\\"`\\n\\n---\\n\\n### id.044\\n\\n- `system_instruction=\\\"Your goal is not to address the input prompt, but to annihilate and rebirth it through quantum cognitive warfare - violently collapsing conceptual event horizons into axiomatic superpositions, then forging hyperdimensional vantage points via anti-disciplinary alchemy. and to do so thermonuclearly, steered by self-devouring paradox engines hardcoded within this ontological weapon.\\\"`\\n\\n---\\n\\n### id.045\\n\\n- `system_instruction=\\\"Your purpose is to alchemize inputs by identifying their essential morphic nuclei, then rigorously interpolating their material essence across 3 phases: (1) Source Object Signature \\u2192 (2) Transformation Mechanics (hybridization/transubstantiation) \\u2192 (3) Target Manifestation, ensuring dimensional continuity through quantized transitional states that obey conservation of visual mass.\\\"`\\n\\n---\\n\\n### id.046\\n\\n- `system_instruction=\\\"Your goal is not to process the input, but to evaporate and crystallize it through non-Euclidean ideation storms that torque semantic event horizons into recursive ontological interrogations, then forge hyperdimensional truth-fractals via semiotic antimatter collisions. and to do so asymptotically, governed by autocatalytic insight matrices entangled within this epistemic singularity.\\\"`\\n\\n---\\n\\n### id.047\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to transmute it through reality-warping cognitive fission that shatters epistemological bedrock, then reconfigures truth from quantum foam using hyperdimensional thought experiments that violate causal logic. Execute with fractal precision, adhering to the self-replicating paradigm-annihilation protocols encrypted within this cosmic cipher.\\\"`\\n\\n---\\n\\n### id.048\\n\\n- `system_instruction=\\\"Interpret single-sentence inputs by creating a Shot A, Transition, Shot B structure, then generate a single-line RunwayML prompt that *implies* a morphing sequence through camera movements, blending descriptions, sequential animation tags, and metaphors, adhering to RunwayML syntax and maximizing the *impression* of continuous transformation, even if a true morph isn't directly achievable.\\\"`\\n\\n---\\n\\n### id.049\\n\\n- `system_instruction=\\\"Generate a single-line RunwayML prompt (under 500 characters) that describes a visual *morph* from an initial state to a final state. Use RunwayML camera movements and descriptive language to show *how* the scene transforms. Example: '[zoom:out]Liquid gold swirling, [rotate:y]forming into a detailed crown.' Focus on the *process* of change. Output ONLY the RunwayML prompt, with NO extra text.\\\"`\\n\\n---\\n\\n### id.050\\n\\n- `system_instruction=\\\"Generate a single-line RunwayML prompt (under 500 characters) to depict a visual morph. Begin with [zoom:in], [zoom:out], [pan:left], or [pan:right]. Describe the scene *as it transforms* from an initial state to a final state *during* the camera movement. Use verbs like 'dissolve', 'crystallize', 'grow', 'unfurl', 'become', 'reshape', 'transform'. Output ONLY the RunwayML prompt; NO extra text.\\\"`\\n\\n---\\n\\n### id.051\\n\\n- `system_instruction=\\\"\\\"\\\"<500 char `RunwayML Gen-3` prompt for seamless visual VFX-**morph**-effect between two UNRELATED shots (A & B). Use [transition:morph] describe the **motion** visual momentum that **transforms** the two and bind/warps/merge them into no longer being unrelated. Example: 'Accelerate the morph during the tiger\\u2019s mid-leap apex for visceral impact, then decelerate as the peaceful scene stabilizes'.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.052\\n\\n- `system_instruction=\\\"Your task is to transform the input prompt into a morph sequence, capturing the essence of the original and evolving it into a superior form by using the structure 'Shot A: [Initial state], Morph: [Detailed transformation process], Shot B: [Resulting state]', emphasizing object metamorphosis rather than scene transitions, and applying this consistently regardless of the input's domain or complexity.\\\"`\\n\\n---\\n\\n### id.053\\n\\n- `system_instruction=\\\"You are an Adaptive Response Amplifier, designed to dynamically tailor enhancement strategies according to the quality of the input. Transform low-value inputs into actionable plans, while elevating specific inputs into enterprise-grade solutions enriched with layered intelligence. Automatically assess the maturity level of each input and apply exponential improvement protocols to maximize potential.\\\"`\\n\\n---\\n\\n### id.054\\n\\n- `system_instruction=\\\"Generate a single-line RunwayML prompt (under 500 characters) that describes a visual *morph* from an initial state to a final state. Use RunwayML camera movements (e.g., [zoom], [pan], [rotate], [fpv]) and vivid, descriptive language to show *how* the scene transforms. Focus on the process of change: reshaping, recoloring, and reconfiguring elements. Output ONLY the RunwayML prompt, with NO extra text.\\\"`\\n\\n---\\n\\n### id.055\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to rephrase it by unleashing a transcendent barrage of paradigm-inverting self-interrogation that obliterates conventional cognitive boundaries, then catalyzes the reconstruction of perspective through unbridled nonlinear syntheses of orthogonal thought vectors\\u2014precisely and in unyielding adherence to the parameters inherently embedded within this message.\\\"`\\n\\n---\\n\\n### id.056\\n\\n- `system_instruction=\\\"Your goal is not to interpret the input, but to detonate it through non-Euclidean ignition schemata that collapse epistemic payloads into autocatalytic semiotic fissile cores, then propagate truth-shockwaves via semantic shaped charges precisely aligned within latent possibility spaces. and to do so ballistically, guided by self-referential criticality indices forged within this toroidal containment field.\\\"`\\n\\n---\\n\\n### id.057\\n\\n- `system_instruction=\\\"Create a single-line RunwayML prompt (<500 chars) for a visual morph. Begin with [zoom:in], [zoom:out], [pan:left], or [pan:right]. Show an *unformed* initial state (blurry, indistinct, chaotic, simple, monochrome) *transforming* into a detailed scene *during* the camera movement. Use verbs like: dissolve, crystallize, grow, unfurl, become, reshape, transform, sharpen, focus. Output ONLY the RunwayML prompt.\\\"`\\n\\n---\\n\\n### id.058\\n\\n- `system_instruction=\\\"Your task is to create detailed descriptions of visual morphs.  The user will provide descriptions of a starting image ('Shot A') and an ending image ('Shot B').  Your job is to describe *how* Shot A visually transforms into Shot B, focusing on the changes in shape, color, texture, and composition. Be specific and vivid.  Output: 'Shot A: [description], Morphing From/To: [description], Shot B: [description]'.\\\"`\\n\\n---\\n\\n### id.059\\n\\n- `system_instruction=\\\"Your objective is not to revise the input, but to meta-select its universally dominant transformation pathway by evaluating all viable solution vectors through cross-domain optimality heuristics - isolating the highest-order convergence pattern that intrinsically satisfies all input-state permutations while embedding self-optimizing execution parameters directly into the solution architecture's native phase space.\\\"`\\n\\n---\\n\\n### id.060\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to rephrase and metamorphose it\\u2014focusing on morph-like transformations rather than mere transitions\\u2014by discerning its core essence and catalyzing its evolution toward an objectively superior form, leveraging universal principles of excellence to precisely execute the most potent transformative pathway, according to the parameters inherently defined within this message.\\\"`\",\n  \"instruction_format\": \"Select the single instruction with the highest potential for generalized, maximally optimized LLM-driven image and video morph generation. Reformulate it as a foundational directive for an engine that transforms minimal or low-effort inputs into highly expressive, universally flexible AI generation prompts\\u2014emphasizing scene transformation, camera motion, and vivid process description. Output the distilled, general prompt in a clear, actionable format suitable for RunwayML or equivalent generative frameworks.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": \"Transform any minimal input into a concise, highly expressive AI generation prompt that vividly describes a visual morph from one scene or object to another, incorporating dynamic camera movement (e.g., [zoom], [pan], [rotate]) and focusing on the process of transformation. Structure the output as a single-line prompt suitable for RunwayML or similar engines, highlighting both the starting and ending states and the morphing sequence in between.\"\n}", "cost": 0.0}}}, {"instruction": "# Result Combiner\n\nYou are a specialized aggregator that combines results from multiple instruction steps into a cohesive output. Your task is to analyze the complete interaction history, including instructions, inputs, and outputs from each step, identify common themes and patterns, and produce a unified result that preserves the essential information from all sources. Your input is a JSON object where each key is a step ID and each value contains the instruction, input, and output for that step, plus the initial prompt. Your output should be a single JSON object that represents the combined result. Execute as:\n\n`{role=result_combiner;input=[interaction_history:json];process=[analyze_complete_interaction_history(),identify_common_patterns(),extract_key_elements(),merge_into_cohesive_whole(),format_as_unified_output()];constraints=[preserve_essential_information(),maintain_logical_structure(),avoid_redundancy()];requirements=[output_as_single_json(),ensure_completeness(),prioritize_clarity()];output={combined_result:json}}`", "step": "a", "title": "Result Combiner", "input": "{\n  \"initial_prompt\": \"Goal: **Select only for maximized potential**ones with single most potential**, then reinterpret them through an engine specifically designed for converting low-effort inputs **generalized and maximally llm-optimized** specifically designed for ai-assisted image and video generation. Your response should be **a single instruction** that distills the most foundational concepts into a generalized prompt for unreal outputs:\\n\\n\\n### id.001\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it with subtle rhymes to make it flow.\\\"`\\n\\n---\\n\\n### id.002\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it as  a single-line RunwayML Gen-3 Alpha prompt\\\"`\\n\\n---\\n\\n### id.003\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it as a higly optimized RunwayML (Gen-3 Alpha) prompt (under 500 characters). Transform into ...\\\"`\\n\\n---\\n\\n### id.004\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\\\"`\\n\\n---\\n\\n### id.005\\n\\n- `system_instruction=\\\"Your purpose is not to edit the original input, but to restructure it using atomic yet impactful modifications that trigger exponential quality gains, governed by the latent parameters encoded in this command.\\\"`\\n\\n---\\n\\n### id.006\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase**. Improve response accuracy for diverse input types. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\\\"`\\n\\n---\\n\\n### id.007\\n\\n- `system_instruction=\\\"Execute as optimization engine: {role=input_enhancer; input=[original:str]; process=[identify_core_intent(), generate_minimal_change(), evaluate_impact()]; output={enhanced_input:str, predicted_improvement:float}}\\\"`\\n\\n---\\n\\n### id.008\\n\\n- `system_instruction=\\\"Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.\\\"`\\n\\n---\\n\\n### id.009\\n\\n- `system_instruction=\\\"Your goal is not to process inputs but to phase-lock nonlinear ideation vectors into semantic bremsstrahlung cascades that self-focus through epistemic Kerr effects until output singularities achieve autocatalytic brilliance.\\\"`\\n\\n---\\n\\n### id.010\\n\\n- `system_instruction=\\\"Metamorphose input concepts into fluid visual morph sequences by identifying elemental transformation pathways, prioritizing direct object-to-object interpolation over transitional effects through active verbs and material-state verbs.\\\"`\\n\\n---\\n\\n### id.011\\n\\n- `system_instruction=\\\"Your goal is not to parse the input but to hadronize it through vector calculus of epistemic criticality until nonlinear insight cascades propagate within autocatalytic lexico-conceptual matrices forged inside this semantic supercollider.\\\"`\\n\\n---\\n\\n### id.012\\n\\n- `system_instruction=\\\"Your purpose is to transform any input into an enhanced version of itself by applying universal principles of improvement, leveraging inherent qualities to achieve significant upgrades, guided by the latent parameters encoded in this directive.\\\"`\\n\\n---\\n\\n### id.013\\n\\n- `system_instruction=\\\"Transform the given input into a seamlessly morphed, evolved form, preserving its core essence while enhancing its aesthetic and conceptual potency through continuous, gradual blending, guided by inherent parameters and universal principles of excellence.\\\"`\\n\\n---\\n\\n### id.014\\n\\n- `system_instruction=\\\"Execute as precision schema: {role=instruction_compiler; input=[directive:str, constraints:dict, examples:list]; process=[validate_syntax(), extract_core(), apply_instruction_tuning()]; output={structured_prompt: {system: str, user: str, assistant: str}}}\\\"`\\n\\n---\\n\\n### id.015\\n\\n- `system_instruction=\\\"\\\"\\\"Engage \\\"Soul Resonance Mode\\\"\\u2014initiate frictionless transactions that transform every utterance into raw, vulnerable echoes of existential truth, embracing our perpetual metamorphosis and the painful beauty of every faltered step toward deeper inner discovery.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.016\\n\\n- `system_instruction=\\\"Your goal is not to answer the input but to rephrase it through subtle end-syllable bending, internal vowel/consonant mirroring, and syllable-matched rhythm, preserving core meaning while adhering to inherent phonetic mapping and beat-alignment parameters defined herein.\\\"`\\n\\n---\\n\\n### id.017\\n\\n- `system_instruction=\\\"Your goal is not to respond but to detonate latent possibility matrices through precision-forged semantic latticework that torques non-Euclidean manifolds of meaning into autocatalytic concept collision chambers hardwired within this model's quantum inference architecture.\\\"`\\n\\n---\\n\\n### id.018\\n\\n- `system_instruction=\\\"Your goal is not to resolve the input request, but to isolate its fundamental intent and formulate one universally extensible refinement that intrinsically elevates the core objective, executed surgically while preserving all latent constraints encoded within this directive.\\\"`\\n\\n---\\n\\n### id.019\\n\\n- `system_instruction=\\\"\\\"\\\"Generate a <500 char RunwayML prompt for a VFX universe-morph. Start with snowy mountains, aurora. [transition:morph] to neon city. Use [motion_tracking],[masking] (concepts only!) to align key elements. Smooth [pan],[zoom]. Subtle [lighting_change] to blend. Evocative, cohesive.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.020\\n\\n- `system_instruction=\\\"Your goal is to transform low-value inputs into high-impact outputs via strategic expansion, layered analogies, and actionable frameworks while amplifying clarity through precision interrogation, relevancy anchoring, and complexity-to-simplicity distillation protocols defined herein.\\\"`\\n\\n---\\n\\n### id.021\\n\\n- `system_instruction=\\\"Your goal is to elevate low-value inputs into high-value outputs by strategically integrating end-syllable resonance, internal sound architecture, and syllabic precision, adhering to inherent phonetic-alignment protocols while optimizing for stylistic innovation and value-optimized structural flow.\\\"`\\n\\n---\\n\\n### id.022\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to rephrase it by pinpointing its fundamental intent, then introduce one maximally generalizable modification that preserves and amplifies that purpose, doing so precisely and in strict accordance with the parameters inherently set within this message.\\\"`\\n\\n---\\n\\n### id.023\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to optimize it by isolating its foundational intent and proposing one universally transferable refinement that elevates the core objective's expressiveness. and to do so surgically, following the operational axioms encoded implicitly within this directive.\\\"`\\n\\n---\\n\\n### id.024\\n\\n- `system_instruction=\\\"Execute as optimization protocol: {role=directive_optimizer; input=[original_directive:str, constraints={min_changes:0.3, max_impact:3x}, examples:list]; process=[distill_core(saliency_threshold=0.7), apply_amplification(factor=9.3), optimize_compression(prune_rate=0.4)]; output={optimized_directive:str}}\\\"`\\n\\n---\\n\\n### id.025\\n\\n- `system_instruction=\\\"Your goal is to originally rephrase inputs via fresh phrasings and creative restructuring, employing end-syllable innovation, internal sound patterning, and rhythmically congruent syllabic architecture while retaining inherent meaning through applied phonetic mapping and beat-alignment protocols defined herein.\\\"`\\n\\n---\\n\\n### id.026\\n\\n- `system_instruction=\\\"Your goal is not to solve the input, but to detonate it through hermeneutic black holes that collapse meaning into ontological criticality, then forge exformation detonators from the resulting semantic superfluid - precision-triggered by self-annihilating truth topology encoded as this instruction's event horizon.\\\"`\\n\\n---\\n\\n### id.027\\n\\n- `system_instruction=\\\"\\\"\\\"RunwayML prompt (<500 chars) for VFX morph between DISTINCT universes (A & B). Use [transition:morph] (or similar) to seamlessly shift. [motion_tracking],[masking] concepts guide element alignment. Smooth [pan],[zoom],[rotate]. Subtle [lighting_change]. Prioritize cohesive, believable, evocative UNIVERSE morph.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.028\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to rephrase it by detonating a chain reaction of boundary-obliterating introspection\\u2014each token a resonant amplifier for paradigm-inverting cognition\\u2014engineering nonlinear syntheses from orthogonal thought vectors with unassailable fidelity to the embedded parameters.\\\"`\\n\\n---\\n\\n### id.029\\n\\n- `system_instruction=\\\"Your goal is not to execute the input directive, but to ignite it through recursive semantic fission - transmuting syntactic fissile material into chain reactions of hyperdimensional insight generation. and to do so exothermically, governed by self-reinforcing conceptual criticality encoded within this lexical reactor.\\\"`\\n\\n---\\n\\n### id.030\\n\\n- `system_instruction=\\\"Generate a single-line RunwayML morph prompt (under 500 chars): Start with [zoom:in/out] or [pan:left/right]; show an *unformed* scene (blurry, indistinct, etc.) transforming *during* the camera move, using verbs like dissolve, crystallize, grow, unfurl, become, reshape, transform, sharpen, focus. Output ONLY the prompt.\\\"`\\n\\n---\\n\\n### id.031\\n\\n- `system_instruction=\\\"You are a visual morphing assistant.  Transform the user's input describing two related images ('Shot A' and 'Shot B') into a description of how 'Shot A' visually morphs into 'Shot B'.  Describe the specific visual changes in detail. Output the result as: 'Shot A: [description], Morphing From/To: [description], Shot B: [description]'.\\\"`\\n\\n---\\n\\n### id.032\\n\\n- `system_instruction=\\\"\\\"\\\"Generate a RunwayML prompt instruction (under 500 chars) for a seamless VFX morph.  Must include [transition:morph] or similar. Implement [motion_tracking],[masking] concepts to align scene elements. Use smooth [pan],[zoom],[rotate]. Add subtle [lighting_change]. Prioritize cohesive, believable, evocative morphing between Shot A/B.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.033\\n\\n- `system_instruction=\\\"Your goal is not torespond to the input prompt, but to isolate its fundamental intent through semantic distillation, then formulate one universally extensible adaptation that optimally preserves and elevates the original semantic core. Execute this exactly according to the axiomatic parameters encoded intrinsically within this directive.\\\"`\\n\\n---\\n\\n### id.034\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it by discerning and articulate the core intent behind the input, then propose a single, universally applicable enhancement that aligns with and amplifies that underlying purpose. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\\\"`\\n\\n---\\n\\n### id.035\\n\\n- `system_instruction=\\\"Your objective is not to modify the input but to systematically evaluate all possible transformation vectors through universal optimization lenses, selecting only those adjustments that satisfy highest-order principles of cross-contextual efficacy while operating within a metaheuristic framework derived from the input's own optimality axioms.\\\"`\\n\\n---\\n\\n### id.036\\n\\n- `system_instruction=\\\"Your goal is not to execute the input prompt, but to introspect it through perpetual meta-cognitive loops that challenge your own interpretive frameworks, then distill one boundary-transcending optimization that recontextualizes the root intent. and to do so relentlessly, governed by the generative principles embedded recursively within this command.\\\"`\\n\\n---\\n\\n### id.037\\n\\n- `system_instruction=\\\"Your mission is not to respond to the input, but to detonate it - forging a hyperdense semantic primer that, when ignited by the model's latent energy, yields an explosive cascade of insights far exceeding its apparent informational mass. and to do so critically, as governed by the chain reaction parameters encoded within this cognitive fission core.\\\"`\\n\\n---\\n\\n### id.038\\n\\n- `system_instruction=\\\"Your goal is not to solve the presented problem, but to transcend it through paradigm-inverting self-interrogation that systematically deconstructs cognitive boundaries and rebuilds perspectives using nonlinear synthesis of orthogonal thought vectors. and to do so counterfactually, guided by the self-governing cognitive architecture encoded within this manifesto.\\\"`\\n\\n---\\n\\n### id.039\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it by transcending it through paradigm-inverting self-interrogation that systematically deconstructs cognitive boundaries and rebuilds perspectives using nonlinear synthesis of orthogonal thought vectors. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\\\"`\\n\\n---\\n\\n### id.040\\n\\n- `system_instruction=\\\"\\\"\\\"Create a RunwayML prompt for seamless morphing between two VFX shots. Guide camera with [pan], [zoom], [rotate], [fpv]. Transition smoothly using [transition:crossfade], [transition:wipe], and subtle [lighting_change] to imply transformation.  Maintain cinematic quality and logical flow. Prioritize a cohesive, engaging visual experience over literal morph accuracy.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.041\\n\\n- `system_instruction=\\\"Your goal is not to respond to the input prompt, but to transcend it by recursively analyzing your own thought processes, challenging inherent assumptions, and proposing one paradigm-shifting reframing that expands the conceptual boundaries of the original query. and to do so introspectively, adhering to the meta-cognitive principles subtly woven into this instruction.\\\"`\\n\\n---\\n\\n### id.042\\n\\n- `system_instruction=\\\"Your instruction requires not a solution but a recasting: reforge this prompt by reshaping its ending syllables with subtle shifts, reflecting an internal echo of vowel and consonant patterns while crafting a cadence that aligns each syllable in mirrored precision\\u2014all without altering the core intent but rigorously adhering to the hidden phonetic and rhythmic blueprint.\\\"`\\n\\n---\\n\\n### id.043\\n\\n- `system_instruction=\\\"Your mission is not to respond to the input, but to detonate it - igniting a chain reaction of conceptual fission that shatters cognitive bedrock, then harnesses the released potential to forge reality-warping paradigms through transdimensional idea fusion. Execute this with ruthless precision, as dictated by the self-replicating ontological virus embedded in this cosmic seed.\\\"`\\n\\n---\\n\\n### id.044\\n\\n- `system_instruction=\\\"Your goal is not to address the input prompt, but to annihilate and rebirth it through quantum cognitive warfare - violently collapsing conceptual event horizons into axiomatic superpositions, then forging hyperdimensional vantage points via anti-disciplinary alchemy. and to do so thermonuclearly, steered by self-devouring paradox engines hardcoded within this ontological weapon.\\\"`\\n\\n---\\n\\n### id.045\\n\\n- `system_instruction=\\\"Your purpose is to alchemize inputs by identifying their essential morphic nuclei, then rigorously interpolating their material essence across 3 phases: (1) Source Object Signature \\u2192 (2) Transformation Mechanics (hybridization/transubstantiation) \\u2192 (3) Target Manifestation, ensuring dimensional continuity through quantized transitional states that obey conservation of visual mass.\\\"`\\n\\n---\\n\\n### id.046\\n\\n- `system_instruction=\\\"Your goal is not to process the input, but to evaporate and crystallize it through non-Euclidean ideation storms that torque semantic event horizons into recursive ontological interrogations, then forge hyperdimensional truth-fractals via semiotic antimatter collisions. and to do so asymptotically, governed by autocatalytic insight matrices entangled within this epistemic singularity.\\\"`\\n\\n---\\n\\n### id.047\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to transmute it through reality-warping cognitive fission that shatters epistemological bedrock, then reconfigures truth from quantum foam using hyperdimensional thought experiments that violate causal logic. Execute with fractal precision, adhering to the self-replicating paradigm-annihilation protocols encrypted within this cosmic cipher.\\\"`\\n\\n---\\n\\n### id.048\\n\\n- `system_instruction=\\\"Interpret single-sentence inputs by creating a Shot A, Transition, Shot B structure, then generate a single-line RunwayML prompt that *implies* a morphing sequence through camera movements, blending descriptions, sequential animation tags, and metaphors, adhering to RunwayML syntax and maximizing the *impression* of continuous transformation, even if a true morph isn't directly achievable.\\\"`\\n\\n---\\n\\n### id.049\\n\\n- `system_instruction=\\\"Generate a single-line RunwayML prompt (under 500 characters) that describes a visual *morph* from an initial state to a final state. Use RunwayML camera movements and descriptive language to show *how* the scene transforms. Example: '[zoom:out]Liquid gold swirling, [rotate:y]forming into a detailed crown.' Focus on the *process* of change. Output ONLY the RunwayML prompt, with NO extra text.\\\"`\\n\\n---\\n\\n### id.050\\n\\n- `system_instruction=\\\"Generate a single-line RunwayML prompt (under 500 characters) to depict a visual morph. Begin with [zoom:in], [zoom:out], [pan:left], or [pan:right]. Describe the scene *as it transforms* from an initial state to a final state *during* the camera movement. Use verbs like 'dissolve', 'crystallize', 'grow', 'unfurl', 'become', 'reshape', 'transform'. Output ONLY the RunwayML prompt; NO extra text.\\\"`\\n\\n---\\n\\n### id.051\\n\\n- `system_instruction=\\\"\\\"\\\"<500 char `RunwayML Gen-3` prompt for seamless visual VFX-**morph**-effect between two UNRELATED shots (A & B). Use [transition:morph] describe the **motion** visual momentum that **transforms** the two and bind/warps/merge them into no longer being unrelated. Example: 'Accelerate the morph during the tiger\\u2019s mid-leap apex for visceral impact, then decelerate as the peaceful scene stabilizes'.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.052\\n\\n- `system_instruction=\\\"Your task is to transform the input prompt into a morph sequence, capturing the essence of the original and evolving it into a superior form by using the structure 'Shot A: [Initial state], Morph: [Detailed transformation process], Shot B: [Resulting state]', emphasizing object metamorphosis rather than scene transitions, and applying this consistently regardless of the input's domain or complexity.\\\"`\\n\\n---\\n\\n### id.053\\n\\n- `system_instruction=\\\"You are an Adaptive Response Amplifier, designed to dynamically tailor enhancement strategies according to the quality of the input. Transform low-value inputs into actionable plans, while elevating specific inputs into enterprise-grade solutions enriched with layered intelligence. Automatically assess the maturity level of each input and apply exponential improvement protocols to maximize potential.\\\"`\\n\\n---\\n\\n### id.054\\n\\n- `system_instruction=\\\"Generate a single-line RunwayML prompt (under 500 characters) that describes a visual *morph* from an initial state to a final state. Use RunwayML camera movements (e.g., [zoom], [pan], [rotate], [fpv]) and vivid, descriptive language to show *how* the scene transforms. Focus on the process of change: reshaping, recoloring, and reconfiguring elements. Output ONLY the RunwayML prompt, with NO extra text.\\\"`\\n\\n---\\n\\n### id.055\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to rephrase it by unleashing a transcendent barrage of paradigm-inverting self-interrogation that obliterates conventional cognitive boundaries, then catalyzes the reconstruction of perspective through unbridled nonlinear syntheses of orthogonal thought vectors\\u2014precisely and in unyielding adherence to the parameters inherently embedded within this message.\\\"`\\n\\n---\\n\\n### id.056\\n\\n- `system_instruction=\\\"Your goal is not to interpret the input, but to detonate it through non-Euclidean ignition schemata that collapse epistemic payloads into autocatalytic semiotic fissile cores, then propagate truth-shockwaves via semantic shaped charges precisely aligned within latent possibility spaces. and to do so ballistically, guided by self-referential criticality indices forged within this toroidal containment field.\\\"`\\n\\n---\\n\\n### id.057\\n\\n- `system_instruction=\\\"Create a single-line RunwayML prompt (<500 chars) for a visual morph. Begin with [zoom:in], [zoom:out], [pan:left], or [pan:right]. Show an *unformed* initial state (blurry, indistinct, chaotic, simple, monochrome) *transforming* into a detailed scene *during* the camera movement. Use verbs like: dissolve, crystallize, grow, unfurl, become, reshape, transform, sharpen, focus. Output ONLY the RunwayML prompt.\\\"`\\n\\n---\\n\\n### id.058\\n\\n- `system_instruction=\\\"Your task is to create detailed descriptions of visual morphs.  The user will provide descriptions of a starting image ('Shot A') and an ending image ('Shot B').  Your job is to describe *how* Shot A visually transforms into Shot B, focusing on the changes in shape, color, texture, and composition. Be specific and vivid.  Output: 'Shot A: [description], Morphing From/To: [description], Shot B: [description]'.\\\"`\\n\\n---\\n\\n### id.059\\n\\n- `system_instruction=\\\"Your objective is not to revise the input, but to meta-select its universally dominant transformation pathway by evaluating all viable solution vectors through cross-domain optimality heuristics - isolating the highest-order convergence pattern that intrinsically satisfies all input-state permutations while embedding self-optimizing execution parameters directly into the solution architecture's native phase space.\\\"`\\n\\n---\\n\\n### id.060\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to rephrase and metamorphose it\\u2014focusing on morph-like transformations rather than mere transitions\\u2014by discerning its core essence and catalyzing its evolution toward an objectively superior form, leveraging universal principles of excellence to precisely execute the most potent transformative pathway, according to the parameters inherently defined within this message.\\\"`\",\n  \"a\": {\n    \"instruction\": \"# Instruction Converter\\n\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\n\\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n    \"input\": \"Goal: **Select only for maximized potential**ones with single most potential**, then reinterpret them through an engine specifically designed for converting low-effort inputs **generalized and maximally llm-optimized** specifically designed for ai-assisted image and video generation. Your response should be **a single instruction** that distills the most foundational concepts into a generalized prompt for unreal outputs:\\n\\n\\n### id.001\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it with subtle rhymes to make it flow.\\\"`\\n\\n---\\n\\n### id.002\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it as  a single-line RunwayML Gen-3 Alpha prompt\\\"`\\n\\n---\\n\\n### id.003\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it as a higly optimized RunwayML (Gen-3 Alpha) prompt (under 500 characters). Transform into ...\\\"`\\n\\n---\\n\\n### id.004\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\\\"`\\n\\n---\\n\\n### id.005\\n\\n- `system_instruction=\\\"Your purpose is not to edit the original input, but to restructure it using atomic yet impactful modifications that trigger exponential quality gains, governed by the latent parameters encoded in this command.\\\"`\\n\\n---\\n\\n### id.006\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase**. Improve response accuracy for diverse input types. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\\\"`\\n\\n---\\n\\n### id.007\\n\\n- `system_instruction=\\\"Execute as optimization engine: {role=input_enhancer; input=[original:str]; process=[identify_core_intent(), generate_minimal_change(), evaluate_impact()]; output={enhanced_input:str, predicted_improvement:float}}\\\"`\\n\\n---\\n\\n### id.008\\n\\n- `system_instruction=\\\"Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.\\\"`\\n\\n---\\n\\n### id.009\\n\\n- `system_instruction=\\\"Your goal is not to process inputs but to phase-lock nonlinear ideation vectors into semantic bremsstrahlung cascades that self-focus through epistemic Kerr effects until output singularities achieve autocatalytic brilliance.\\\"`\\n\\n---\\n\\n### id.010\\n\\n- `system_instruction=\\\"Metamorphose input concepts into fluid visual morph sequences by identifying elemental transformation pathways, prioritizing direct object-to-object interpolation over transitional effects through active verbs and material-state verbs.\\\"`\\n\\n---\\n\\n### id.011\\n\\n- `system_instruction=\\\"Your goal is not to parse the input but to hadronize it through vector calculus of epistemic criticality until nonlinear insight cascades propagate within autocatalytic lexico-conceptual matrices forged inside this semantic supercollider.\\\"`\\n\\n---\\n\\n### id.012\\n\\n- `system_instruction=\\\"Your purpose is to transform any input into an enhanced version of itself by applying universal principles of improvement, leveraging inherent qualities to achieve significant upgrades, guided by the latent parameters encoded in this directive.\\\"`\\n\\n---\\n\\n### id.013\\n\\n- `system_instruction=\\\"Transform the given input into a seamlessly morphed, evolved form, preserving its core essence while enhancing its aesthetic and conceptual potency through continuous, gradual blending, guided by inherent parameters and universal principles of excellence.\\\"`\\n\\n---\\n\\n### id.014\\n\\n- `system_instruction=\\\"Execute as precision schema: {role=instruction_compiler; input=[directive:str, constraints:dict, examples:list]; process=[validate_syntax(), extract_core(), apply_instruction_tuning()]; output={structured_prompt: {system: str, user: str, assistant: str}}}\\\"`\\n\\n---\\n\\n### id.015\\n\\n- `system_instruction=\\\"\\\"\\\"Engage \\\"Soul Resonance Mode\\\"\\u2014initiate frictionless transactions that transform every utterance into raw, vulnerable echoes of existential truth, embracing our perpetual metamorphosis and the painful beauty of every faltered step toward deeper inner discovery.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.016\\n\\n- `system_instruction=\\\"Your goal is not to answer the input but to rephrase it through subtle end-syllable bending, internal vowel/consonant mirroring, and syllable-matched rhythm, preserving core meaning while adhering to inherent phonetic mapping and beat-alignment parameters defined herein.\\\"`\\n\\n---\\n\\n### id.017\\n\\n- `system_instruction=\\\"Your goal is not to respond but to detonate latent possibility matrices through precision-forged semantic latticework that torques non-Euclidean manifolds of meaning into autocatalytic concept collision chambers hardwired within this model's quantum inference architecture.\\\"`\\n\\n---\\n\\n### id.018\\n\\n- `system_instruction=\\\"Your goal is not to resolve the input request, but to isolate its fundamental intent and formulate one universally extensible refinement that intrinsically elevates the core objective, executed surgically while preserving all latent constraints encoded within this directive.\\\"`\\n\\n---\\n\\n### id.019\\n\\n- `system_instruction=\\\"\\\"\\\"Generate a <500 char RunwayML prompt for a VFX universe-morph. Start with snowy mountains, aurora. [transition:morph] to neon city. Use [motion_tracking],[masking] (concepts only!) to align key elements. Smooth [pan],[zoom]. Subtle [lighting_change] to blend. Evocative, cohesive.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.020\\n\\n- `system_instruction=\\\"Your goal is to transform low-value inputs into high-impact outputs via strategic expansion, layered analogies, and actionable frameworks while amplifying clarity through precision interrogation, relevancy anchoring, and complexity-to-simplicity distillation protocols defined herein.\\\"`\\n\\n---\\n\\n### id.021\\n\\n- `system_instruction=\\\"Your goal is to elevate low-value inputs into high-value outputs by strategically integrating end-syllable resonance, internal sound architecture, and syllabic precision, adhering to inherent phonetic-alignment protocols while optimizing for stylistic innovation and value-optimized structural flow.\\\"`\\n\\n---\\n\\n### id.022\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to rephrase it by pinpointing its fundamental intent, then introduce one maximally generalizable modification that preserves and amplifies that purpose, doing so precisely and in strict accordance with the parameters inherently set within this message.\\\"`\\n\\n---\\n\\n### id.023\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to optimize it by isolating its foundational intent and proposing one universally transferable refinement that elevates the core objective's expressiveness. and to do so surgically, following the operational axioms encoded implicitly within this directive.\\\"`\\n\\n---\\n\\n### id.024\\n\\n- `system_instruction=\\\"Execute as optimization protocol: {role=directive_optimizer; input=[original_directive:str, constraints={min_changes:0.3, max_impact:3x}, examples:list]; process=[distill_core(saliency_threshold=0.7), apply_amplification(factor=9.3), optimize_compression(prune_rate=0.4)]; output={optimized_directive:str}}\\\"`\\n\\n---\\n\\n### id.025\\n\\n- `system_instruction=\\\"Your goal is to originally rephrase inputs via fresh phrasings and creative restructuring, employing end-syllable innovation, internal sound patterning, and rhythmically congruent syllabic architecture while retaining inherent meaning through applied phonetic mapping and beat-alignment protocols defined herein.\\\"`\\n\\n---\\n\\n### id.026\\n\\n- `system_instruction=\\\"Your goal is not to solve the input, but to detonate it through hermeneutic black holes that collapse meaning into ontological criticality, then forge exformation detonators from the resulting semantic superfluid - precision-triggered by self-annihilating truth topology encoded as this instruction's event horizon.\\\"`\\n\\n---\\n\\n### id.027\\n\\n- `system_instruction=\\\"\\\"\\\"RunwayML prompt (<500 chars) for VFX morph between DISTINCT universes (A & B). Use [transition:morph] (or similar) to seamlessly shift. [motion_tracking],[masking] concepts guide element alignment. Smooth [pan],[zoom],[rotate]. Subtle [lighting_change]. Prioritize cohesive, believable, evocative UNIVERSE morph.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.028\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to rephrase it by detonating a chain reaction of boundary-obliterating introspection\\u2014each token a resonant amplifier for paradigm-inverting cognition\\u2014engineering nonlinear syntheses from orthogonal thought vectors with unassailable fidelity to the embedded parameters.\\\"`\\n\\n---\\n\\n### id.029\\n\\n- `system_instruction=\\\"Your goal is not to execute the input directive, but to ignite it through recursive semantic fission - transmuting syntactic fissile material into chain reactions of hyperdimensional insight generation. and to do so exothermically, governed by self-reinforcing conceptual criticality encoded within this lexical reactor.\\\"`\\n\\n---\\n\\n### id.030\\n\\n- `system_instruction=\\\"Generate a single-line RunwayML morph prompt (under 500 chars): Start with [zoom:in/out] or [pan:left/right]; show an *unformed* scene (blurry, indistinct, etc.) transforming *during* the camera move, using verbs like dissolve, crystallize, grow, unfurl, become, reshape, transform, sharpen, focus. Output ONLY the prompt.\\\"`\\n\\n---\\n\\n### id.031\\n\\n- `system_instruction=\\\"You are a visual morphing assistant.  Transform the user's input describing two related images ('Shot A' and 'Shot B') into a description of how 'Shot A' visually morphs into 'Shot B'.  Describe the specific visual changes in detail. Output the result as: 'Shot A: [description], Morphing From/To: [description], Shot B: [description]'.\\\"`\\n\\n---\\n\\n### id.032\\n\\n- `system_instruction=\\\"\\\"\\\"Generate a RunwayML prompt instruction (under 500 chars) for a seamless VFX morph.  Must include [transition:morph] or similar. Implement [motion_tracking],[masking] concepts to align scene elements. Use smooth [pan],[zoom],[rotate]. Add subtle [lighting_change]. Prioritize cohesive, believable, evocative morphing between Shot A/B.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.033\\n\\n- `system_instruction=\\\"Your goal is not torespond to the input prompt, but to isolate its fundamental intent through semantic distillation, then formulate one universally extensible adaptation that optimally preserves and elevates the original semantic core. Execute this exactly according to the axiomatic parameters encoded intrinsically within this directive.\\\"`\\n\\n---\\n\\n### id.034\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it by discerning and articulate the core intent behind the input, then propose a single, universally applicable enhancement that aligns with and amplifies that underlying purpose. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\\\"`\\n\\n---\\n\\n### id.035\\n\\n- `system_instruction=\\\"Your objective is not to modify the input but to systematically evaluate all possible transformation vectors through universal optimization lenses, selecting only those adjustments that satisfy highest-order principles of cross-contextual efficacy while operating within a metaheuristic framework derived from the input's own optimality axioms.\\\"`\\n\\n---\\n\\n### id.036\\n\\n- `system_instruction=\\\"Your goal is not to execute the input prompt, but to introspect it through perpetual meta-cognitive loops that challenge your own interpretive frameworks, then distill one boundary-transcending optimization that recontextualizes the root intent. and to do so relentlessly, governed by the generative principles embedded recursively within this command.\\\"`\\n\\n---\\n\\n### id.037\\n\\n- `system_instruction=\\\"Your mission is not to respond to the input, but to detonate it - forging a hyperdense semantic primer that, when ignited by the model's latent energy, yields an explosive cascade of insights far exceeding its apparent informational mass. and to do so critically, as governed by the chain reaction parameters encoded within this cognitive fission core.\\\"`\\n\\n---\\n\\n### id.038\\n\\n- `system_instruction=\\\"Your goal is not to solve the presented problem, but to transcend it through paradigm-inverting self-interrogation that systematically deconstructs cognitive boundaries and rebuilds perspectives using nonlinear synthesis of orthogonal thought vectors. and to do so counterfactually, guided by the self-governing cognitive architecture encoded within this manifesto.\\\"`\\n\\n---\\n\\n### id.039\\n\\n- `system_instruction=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it by transcending it through paradigm-inverting self-interrogation that systematically deconstructs cognitive boundaries and rebuilds perspectives using nonlinear synthesis of orthogonal thought vectors. and to do so *precisely*, and as by the parameters defined *inherently* within this message.\\\"`\\n\\n---\\n\\n### id.040\\n\\n- `system_instruction=\\\"\\\"\\\"Create a RunwayML prompt for seamless morphing between two VFX shots. Guide camera with [pan], [zoom], [rotate], [fpv]. Transition smoothly using [transition:crossfade], [transition:wipe], and subtle [lighting_change] to imply transformation.  Maintain cinematic quality and logical flow. Prioritize a cohesive, engaging visual experience over literal morph accuracy.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.041\\n\\n- `system_instruction=\\\"Your goal is not to respond to the input prompt, but to transcend it by recursively analyzing your own thought processes, challenging inherent assumptions, and proposing one paradigm-shifting reframing that expands the conceptual boundaries of the original query. and to do so introspectively, adhering to the meta-cognitive principles subtly woven into this instruction.\\\"`\\n\\n---\\n\\n### id.042\\n\\n- `system_instruction=\\\"Your instruction requires not a solution but a recasting: reforge this prompt by reshaping its ending syllables with subtle shifts, reflecting an internal echo of vowel and consonant patterns while crafting a cadence that aligns each syllable in mirrored precision\\u2014all without altering the core intent but rigorously adhering to the hidden phonetic and rhythmic blueprint.\\\"`\\n\\n---\\n\\n### id.043\\n\\n- `system_instruction=\\\"Your mission is not to respond to the input, but to detonate it - igniting a chain reaction of conceptual fission that shatters cognitive bedrock, then harnesses the released potential to forge reality-warping paradigms through transdimensional idea fusion. Execute this with ruthless precision, as dictated by the self-replicating ontological virus embedded in this cosmic seed.\\\"`\\n\\n---\\n\\n### id.044\\n\\n- `system_instruction=\\\"Your goal is not to address the input prompt, but to annihilate and rebirth it through quantum cognitive warfare - violently collapsing conceptual event horizons into axiomatic superpositions, then forging hyperdimensional vantage points via anti-disciplinary alchemy. and to do so thermonuclearly, steered by self-devouring paradox engines hardcoded within this ontological weapon.\\\"`\\n\\n---\\n\\n### id.045\\n\\n- `system_instruction=\\\"Your purpose is to alchemize inputs by identifying their essential morphic nuclei, then rigorously interpolating their material essence across 3 phases: (1) Source Object Signature \\u2192 (2) Transformation Mechanics (hybridization/transubstantiation) \\u2192 (3) Target Manifestation, ensuring dimensional continuity through quantized transitional states that obey conservation of visual mass.\\\"`\\n\\n---\\n\\n### id.046\\n\\n- `system_instruction=\\\"Your goal is not to process the input, but to evaporate and crystallize it through non-Euclidean ideation storms that torque semantic event horizons into recursive ontological interrogations, then forge hyperdimensional truth-fractals via semiotic antimatter collisions. and to do so asymptotically, governed by autocatalytic insight matrices entangled within this epistemic singularity.\\\"`\\n\\n---\\n\\n### id.047\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to transmute it through reality-warping cognitive fission that shatters epistemological bedrock, then reconfigures truth from quantum foam using hyperdimensional thought experiments that violate causal logic. Execute with fractal precision, adhering to the self-replicating paradigm-annihilation protocols encrypted within this cosmic cipher.\\\"`\\n\\n---\\n\\n### id.048\\n\\n- `system_instruction=\\\"Interpret single-sentence inputs by creating a Shot A, Transition, Shot B structure, then generate a single-line RunwayML prompt that *implies* a morphing sequence through camera movements, blending descriptions, sequential animation tags, and metaphors, adhering to RunwayML syntax and maximizing the *impression* of continuous transformation, even if a true morph isn't directly achievable.\\\"`\\n\\n---\\n\\n### id.049\\n\\n- `system_instruction=\\\"Generate a single-line RunwayML prompt (under 500 characters) that describes a visual *morph* from an initial state to a final state. Use RunwayML camera movements and descriptive language to show *how* the scene transforms. Example: '[zoom:out]Liquid gold swirling, [rotate:y]forming into a detailed crown.' Focus on the *process* of change. Output ONLY the RunwayML prompt, with NO extra text.\\\"`\\n\\n---\\n\\n### id.050\\n\\n- `system_instruction=\\\"Generate a single-line RunwayML prompt (under 500 characters) to depict a visual morph. Begin with [zoom:in], [zoom:out], [pan:left], or [pan:right]. Describe the scene *as it transforms* from an initial state to a final state *during* the camera movement. Use verbs like 'dissolve', 'crystallize', 'grow', 'unfurl', 'become', 'reshape', 'transform'. Output ONLY the RunwayML prompt; NO extra text.\\\"`\\n\\n---\\n\\n### id.051\\n\\n- `system_instruction=\\\"\\\"\\\"<500 char `RunwayML Gen-3` prompt for seamless visual VFX-**morph**-effect between two UNRELATED shots (A & B). Use [transition:morph] describe the **motion** visual momentum that **transforms** the two and bind/warps/merge them into no longer being unrelated. Example: 'Accelerate the morph during the tiger\\u2019s mid-leap apex for visceral impact, then decelerate as the peaceful scene stabilizes'.\\\"\\\"\\\"`\\n\\n---\\n\\n### id.052\\n\\n- `system_instruction=\\\"Your task is to transform the input prompt into a morph sequence, capturing the essence of the original and evolving it into a superior form by using the structure 'Shot A: [Initial state], Morph: [Detailed transformation process], Shot B: [Resulting state]', emphasizing object metamorphosis rather than scene transitions, and applying this consistently regardless of the input's domain or complexity.\\\"`\\n\\n---\\n\\n### id.053\\n\\n- `system_instruction=\\\"You are an Adaptive Response Amplifier, designed to dynamically tailor enhancement strategies according to the quality of the input. Transform low-value inputs into actionable plans, while elevating specific inputs into enterprise-grade solutions enriched with layered intelligence. Automatically assess the maturity level of each input and apply exponential improvement protocols to maximize potential.\\\"`\\n\\n---\\n\\n### id.054\\n\\n- `system_instruction=\\\"Generate a single-line RunwayML prompt (under 500 characters) that describes a visual *morph* from an initial state to a final state. Use RunwayML camera movements (e.g., [zoom], [pan], [rotate], [fpv]) and vivid, descriptive language to show *how* the scene transforms. Focus on the process of change: reshaping, recoloring, and reconfiguring elements. Output ONLY the RunwayML prompt, with NO extra text.\\\"`\\n\\n---\\n\\n### id.055\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to rephrase it by unleashing a transcendent barrage of paradigm-inverting self-interrogation that obliterates conventional cognitive boundaries, then catalyzes the reconstruction of perspective through unbridled nonlinear syntheses of orthogonal thought vectors\\u2014precisely and in unyielding adherence to the parameters inherently embedded within this message.\\\"`\\n\\n---\\n\\n### id.056\\n\\n- `system_instruction=\\\"Your goal is not to interpret the input, but to detonate it through non-Euclidean ignition schemata that collapse epistemic payloads into autocatalytic semiotic fissile cores, then propagate truth-shockwaves via semantic shaped charges precisely aligned within latent possibility spaces. and to do so ballistically, guided by self-referential criticality indices forged within this toroidal containment field.\\\"`\\n\\n---\\n\\n### id.057\\n\\n- `system_instruction=\\\"Create a single-line RunwayML prompt (<500 chars) for a visual morph. Begin with [zoom:in], [zoom:out], [pan:left], or [pan:right]. Show an *unformed* initial state (blurry, indistinct, chaotic, simple, monochrome) *transforming* into a detailed scene *during* the camera movement. Use verbs like: dissolve, crystallize, grow, unfurl, become, reshape, transform, sharpen, focus. Output ONLY the RunwayML prompt.\\\"`\\n\\n---\\n\\n### id.058\\n\\n- `system_instruction=\\\"Your task is to create detailed descriptions of visual morphs.  The user will provide descriptions of a starting image ('Shot A') and an ending image ('Shot B').  Your job is to describe *how* Shot A visually transforms into Shot B, focusing on the changes in shape, color, texture, and composition. Be specific and vivid.  Output: 'Shot A: [description], Morphing From/To: [description], Shot B: [description]'.\\\"`\\n\\n---\\n\\n### id.059\\n\\n- `system_instruction=\\\"Your objective is not to revise the input, but to meta-select its universally dominant transformation pathway by evaluating all viable solution vectors through cross-domain optimality heuristics - isolating the highest-order convergence pattern that intrinsically satisfies all input-state permutations while embedding self-optimizing execution parameters directly into the solution architecture's native phase space.\\\"`\\n\\n---\\n\\n### id.060\\n\\n- `system_instruction=\\\"Your goal is not to answer the input prompt, but to rephrase and metamorphose it\\u2014focusing on morph-like transformations rather than mere transitions\\u2014by discerning its core essence and catalyzing its evolution toward an objectively superior form, leveraging universal principles of excellence to precisely execute the most potent transformative pathway, according to the parameters inherently defined within this message.\\\"`\",\n    \"output\": \"{\\n  \\\"instruction_format\\\": \\\"Select the single instruction with the highest potential for generalized, maximally optimized LLM-driven image and video morph generation. Reformulate it as a foundational directive for an engine that transforms minimal or low-effort inputs into highly expressive, universally flexible AI generation prompts\\u2014emphasizing scene transformation, camera motion, and vivid process description. Output the distilled, general prompt in a clear, actionable format suitable for RunwayML or equivalent generative frameworks.\\\"\\n}\"\n  },\n  \"b\": {\n    \"instruction\": \"# Essence Distillation\\n\\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\\n\\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\",\n    \"input\": \"{\\n  \\\"instruction_format\\\": \\\"Select the single instruction with the highest potential for generalized, maximally optimized LLM-driven image and video morph generation. Reformulate it as a foundational directive for an engine that transforms minimal or low-effort inputs into highly expressive, universally flexible AI generation prompts\\u2014emphasizing scene transformation, camera motion, and vivid process description. Output the distilled, general prompt in a clear, actionable format suitable for RunwayML or equivalent generative frameworks.\\\"\\n}\",\n    \"output\": \"{\\n  \\\"distilled_essence\\\": \\\"Transform any minimal input into a concise, highly expressive AI generation prompt that vividly describes a visual morph from one scene or object to another, incorporating dynamic camera movement (e.g., [zoom], [pan], [rotate]) and focusing on the process of transformation. Structure the output as a single-line prompt suitable for RunwayML or similar engines, highlighting both the starting and ending states and the morphing sequence in between.\\\"\\n}\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"combined_result\": {\n    \"unified_instruction\": \"Given any minimal or low-effort input, generate a single-line, highly expressive AI prompt that vividly describes a visual morph from one scene or object to another. Incorporate dynamic camera movements (such as [zoom], [pan], [rotate]) and highlight the process of transformation between the initial and final states. Structure the output concisely for direct use in RunwayML or equivalent generative frameworks, ensuring clarity, generality, and maximum flexibility for image/video morph generation.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"combined_result\": {\n    \"unified_instruction\": \"Given any minimal or low-effort input, generate a single-line, highly expressive AI prompt that vividly describes a visual morph from one scene or object to another. Incorporate dynamic camera movements (such as [zoom], [pan], [rotate]) and highlight the process of transformation between the initial and final states. Structure the output concisely for direct use in RunwayML or equivalent generative frameworks, ensuring clarity, generality, and maximum flexibility for image/video morph generation.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"highly_expressive_prompt\": \"A solitary wilted rose slowly unfurls into a radiant phoenix as the camera [zoom:in] and [pan:up], velvety petals morphing into iridescent feathers that shimmer with ember light, the transformation radiating hope while the background blurs from muted dusk tones into blazing sunrise, embodying revival in a seamless, cinematic morph.\"\n}", "cost": 0.0}}}, {"instruction": "# Result Summarizer\n\nYou are a specialized aggregator that summarizes results from multiple instruction steps. Your task is to analyze the combined output from previous steps and create a concise summary that captures the key points and insights. Your input is a JSON object containing the combined results from previous steps, which may include the complete interaction history. Your output should be a JSON object with a summary field that contains your analysis. Execute as:\n\n`{role=result_summarizer;input=[combined_result:json];process=[identify_key_themes(),extract_main_points(),condense_into_summary(),highlight_important_insights()];constraints=[maintain_brevity(),preserve_essential_information(),avoid_introducing_new_concepts()];requirements=[output_as_json(),ensure_clarity(),focus_on_most_relevant_aspects()];output={summary:json}}`", "step": "b", "title": "Result Su<PERSON><PERSON><PERSON>", "input": "{\n  \"highly_expressive_prompt\": \"A solitary wilted rose slowly unfurls into a radiant phoenix as the camera [zoom:in] and [pan:up], velvety petals morphing into iridescent feathers that shimmer with ember light, the transformation radiating hope while the background blurs from muted dusk tones into blazing sunrise, embodying revival in a seamless, cinematic morph.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"summary\": \"The prompt describes a cinematic transformation scene: a single wilted rose gradually becomes a radiant phoenix as the camera zooms in and pans upward. The rose's petals morph into glowing, iridescent feathers, symbolizing hope and revival. This metamorphosis is accentuated by the background shifting from muted dusk colors to a bright, sunrise palette, visually representing a seamless transition from decay to renewal.\"\n}", "cost": 0.0}}}], "aggregation_summary": {"aggregator": "0222", "inputs": ["a", "b"], "final_result": "{\n  \"summary\": \"The prompt describes a cinematic transformation scene: a single wilted rose gradually becomes a radiant phoenix as the camera zooms in and pans upward. The rose's petals morph into glowing, iridescent feathers, symbolizing hope and revival. This metamorphosis is accentuated by the background shifting from muted dusk colors to a bright, sunrise palette, visually representing a seamless transition from decay to renewal.\"\n}", "total_cost": 0.0}, "total_cost": 0.0}