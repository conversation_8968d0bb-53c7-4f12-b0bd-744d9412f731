import win32api
import win32gui
import win32con

class Monitor:
    def __init__(self, monitor_handle, monitor_info):
        self.handle = monitor_handle
        self.flags = monitor_info['Flags']
        self.device = monitor_info['Device']
        self._set_areas(monitor_info['Monitor'], monitor_info['Work'])

    def _set_areas(self, monitor_area, work_area):
        self.monitor_area = monitor_area
        self.work_area = work_area
        self.monitor_area_dict = self._convert_to_dict(monitor_area)
        self.work_area_dict = self._convert_to_dict(work_area)

    def _convert_to_dict(self, area):
        return {
            'x': area[0],
            'y': area[1],
            'width': area[2] - area[0],
            'height': area[3] - area[1]
        }

    def is_primary(self):
        return self.flags == 1

    def get_dimensions(self):
        return {
            'width': self.monitor_area[2] - self.monitor_area[0],
            'height': self.monitor_area[3] - self.monitor_area[1]
        }

    def get_work_area(self):
        return self._convert_to_dict(self.work_area)

def get_all_monitors():
    monitors_info = {}
    for monitor in win32api.EnumDisplayMonitors(None, None):
        monitor_handle = monitor[0]
        monitor_info = win32api.GetMonitorInfo(monitor_handle)
        monitor_obj = Monitor(monitor_handle, monitor_info)
        monitors_info[monitor_handle] = monitor_obj

    return monitors_info

class Window:
    def __init__(self, hwnd):
        self.hwnd = hwnd
        self.update_dimensions()

    def update_dimensions(self):
        rect = win32gui.GetWindowRect(self.hwnd)
        self.dimensions = {
            'x': rect[0],
            'y': rect[1],
            'width': rect[2] - rect[0],
            'height': rect[3] - rect[1]
        }


    def tile(self, monitor: Monitor, position: tuple, size: tuple):
        monitor_dimensions = monitor.get_dimensions()
        new_x = monitor.monitor_area[0] + int(position[0] * monitor_dimensions['width'])
        new_y = monitor.monitor_area[1] + int(position[1] * monitor_dimensions['height'])
        new_width = int(size[0] * monitor_dimensions['width'])
        new_height = int(size[1] * monitor_dimensions['height'])

        win32gui.MoveWindow(self.hwnd, new_x, new_y, new_width, new_height, True)

    def get_rect(self):
        return win32gui.GetWindowRect(self.hwnd)

    def maximize_within_monitor(self, monitor: Monitor):
        work_area = monitor.get_work_area()
        self.tile(monitor, (0, 0), (1, 1))

    def center_within_monitor(self, monitor: Monitor):
        monitor_dimensions = monitor.get_dimensions()
        window_width = self.dimensions['width']
        window_height = self.dimensions['height']
        x_position = (monitor_dimensions['width'] - window_width) // 2
        y_position = (monitor_dimensions['height'] - window_height) // 2
        self.tile(monitor, (x_position / monitor_dimensions['width'], y_position / monitor_dimensions['height']), (window_width / monitor_dimensions['width'], window_height / monitor_dimensions['height']))


    def is_enabled(self):
        return win32gui.IsWindowEnabled(self.hwnd)

    def is_visible(self):
        return win32gui.IsWindowVisible(self.hwnd)

    def is_minimized(self):
        return win32gui.IsIconic(self.hwnd)

    def get_text(self):
        return win32gui.GetWindowText(self.hwnd)

    def get_class_name(self):
        return win32gui.GetClassName(self.hwnd)

    def get_style(self):
        return win32gui.GetWindowLong(self.hwnd, win32con.GWL_STYLE)

    def toggle_visibility(self):
        if self.is_visible():
            win32gui.ShowWindow(self.hwnd, win32con.SW_HIDE)
        else:
            win32gui.ShowWindow(self.hwnd, win32con.SW_SHOW)

    def is_focused(self):
        return self.hwnd == win32gui.GetForegroundWindow()

    def bring_to_front(self):
        win32gui.SetWindowPos(self.hwnd, win32con.HWND_TOP,
                              self.dimensions['x'], self.dimensions['y'],
                              self.dimensions['width'], self.dimensions['height'],
                              win32con.SWP_NOACTIVATE)

    def resize_and_move(self, x, y, width, height):
        win32gui.MoveWindow(self.hwnd, x, y, width, height, True)

    def is_full_screen(self):
        screen_width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
        screen_height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)
        rect = self.get_rect()
        return (rect[0] == 0 and rect[1] == 0 and
                rect[2] == screen_width and rect[3] == screen_height)

def get_all_windows():
    windows = {}
    def enum_windows(hwnd, result):
        if win32gui.IsWindowVisible(hwnd) and win32gui.GetWindowText(hwnd):
            windows[hwnd] = Window(hwnd)
    win32gui.EnumWindows(enum_windows, [])
    return windows

def tile_windows(monitor: Monitor, windows, rows, columns, column_ratios=None, row_ratios=None):
    if rows <= 0 or columns <= 0:
        raise ValueError("Rows and columns must be greater than 0")

    monitor_dimensions = monitor.get_dimensions()
    default_width_ratio = 1 / columns
    default_height_ratio = 1 / rows

    for i, window in enumerate(windows):
        col = i % columns
        row = i // columns

        x_ratio = sum(column_ratios[:col]) if column_ratios else col * default_width_ratio
        y_ratio = sum(row_ratios[:row]) if row_ratios else row * default_height_ratio
        width_ratio = column_ratios[col] if column_ratios else default_width_ratio
        height_ratio = row_ratios[row] if row_ratios else default_height_ratio

        window.tile(monitor, (x_ratio, y_ratio), (width_ratio, height_ratio))

# # Example usage
# primary_monitor = next((mon for mon in get_all_monitors().values() if mon.is_primary()), None)
# all_windows = [Window(hwnd) for hwnd, title, class_name in get_all_windows() if class_name == "CabinetWClass"]  # Example class filter
# print(primary_monitor)

# tile_windows(primary_monitor, all_windows, 3, 3, column_ratios=[0.5, 0.2, 0.3])



# Set up monitors and windows
monitors = get_all_monitors()
primary_monitor = next((mon for mon in monitors.values() if mon.is_primary()), None)
windows = get_all_windows()
test_window = next(iter(windows.values()))  # Get the first window for testing

# Test Monitor methods
print(f"Primary Monitor: {primary_monitor.is_primary()}")
print(f"Monitor Dimensions: {primary_monitor.get_dimensions()}")
print(f"Monitor Work Area: {primary_monitor.get_work_area()}")

# Test Window methods
test_window.update_dimensions()
print(f"Window Dimensions: {test_window.dimensions}")

# Tile test
test_window.tile(primary_monitor, (0.5, 0.5), (0.25, 0.25))
print(f"Tiled Window Rect: {test_window.get_rect()}")

# Maximizing within monitor
test_window.maximize_within_monitor(primary_monitor)
print(f"Maximized Window Rect: {test_window.get_rect()}")

# Centering within monitor
test_window.center_within_monitor(primary_monitor)
print(f"Centered Window Rect: {test_window.get_rect()}")

# Toggle visibility
test_window.toggle_visibility()
print(f"Window Visible: {test_window.is_visible()}")
test_window.toggle_visibility()  # Toggle back

# Window focus
print(f"Window Focused: {test_window.is_focused()}")

# Bring to front
test_window.bring_to_front()
print("Window brought to front (visual confirmation required)")

# Resize and move
test_window.resize_and_move(100, 100, 400, 300)
print(f"Resized and Moved Window Rect: {test_window.get_rect()}")

# Full-screen check
print(f"Window Full Screen: {test_window.is_full_screen()}")
