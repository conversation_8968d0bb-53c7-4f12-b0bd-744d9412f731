"""
Core data models for Window Tiler application.
Contains classes for representing windows, monitors, and related enumerations.
"""

import os
import win32gui
import win32con
import win32process
from enum import Enum, auto


class WindowType(Enum):
    """Enumeration of window types for classification purposes."""
    BROWSER = auto()
    TERMINAL = auto()
    EXPLORER = auto()
    EDITOR = auto()
    IDE = auto()
    NORMAL = auto()
    UNKNOWN = auto()


class Monitor:
    """Represents a physical display monitor."""
    def __init__(self, handle, info):
        self.handle = handle
        self.is_primary = bool(info["Flags"] == 1)
        self.device = info["Device"]
        # Monitor area (left, top, right, bottom)
        mon_rect = info["Monitor"]
        work_rect = info["Work"]
        self.monitor_area = mon_rect
        self.work_area = work_rect

    def get_dimensions(self):
        """Return the width and height of the monitor area."""
        left, top, right, bottom = self.monitor_area
        return {"width": right - left, "height": bottom - top}


class WrappedMonitor:
    """UI-friendly wrapper for Monitor objects with display formatting."""
    def __init__(self, monitor_obj, index):
        self.monitor_obj = monitor_obj
        self.index = index

    def __str__(self):
        dims = self.monitor_obj.get_dimensions()
        p_txt = " [PRIMARY]" if self.monitor_obj.is_primary else ""
        return f"{self.index}) {self.monitor_obj.device} - {dims['width']}x{dims['height']}{p_txt}"


class Window:
    """
    Represents a single top-level window that passes style-based filters.
    Retrieves process info via psutil for skipping certain exe's if desired.
    """
    def __init__(self, hwnd, exe_path, rect):
        self.hwnd = hwnd
        self.title = win32gui.GetWindowText(hwnd)
        self.class_name = win32gui.GetClassName(hwnd)
        self.exe_path = exe_path  # full path from psutil
        self.process_name = os.path.basename(exe_path).lower() if exe_path else ""
        self.window_type = WindowType.UNKNOWN

        self.rect = rect  # (left, top, right, bottom)

        self._classify()

    def _classify(self):
        """Classify window based on process name and class name."""
        pname = self.process_name
        cname = self.class_name.lower()

        if any(b in pname for b in ["chrome.exe", "msedge.exe", "firefox.exe", "iexplore.exe"]):
            self.window_type = WindowType.BROWSER
        elif any(t in pname for t in ["cmd.exe", "powershell.exe", "windowsterminal.exe"]):
            self.window_type = WindowType.TERMINAL
        elif "explorer.exe" in pname or "cabinetwclass" in cname:
            self.window_type = WindowType.EXPLORER
        elif any(e in pname for e in ["notepad.exe", "code.exe", "sublime_text.exe"]):
            self.window_type = WindowType.EDITOR
        elif any(i in pname for i in ["devenv.exe", "pycharm", "idea64"]):
            self.window_type = WindowType.IDE
        else:
            self.window_type = WindowType.NORMAL

    def move_and_resize(self, x, y, width, height, restore_minimized=True):
        """Move and resize the window to the specified coordinates and dimensions."""
        if restore_minimized and win32gui.IsIconic(self.hwnd):
            win32gui.ShowWindow(self.hwnd, win32con.SW_SHOWNORMAL)

        win32gui.MoveWindow(self.hwnd, x, y, width, height, True)

        win32gui.SetWindowPos(
            self.hwnd,
            win32con.HWND_TOP,
            x, y, width, height,
            win32con.SWP_NOSENDCHANGING | win32con.SWP_SHOWWINDOW
        )

    def __repr__(self):
        return (f"<Window hwnd={self.hwnd} title='{self.title}' "
                f"exe='{self.exe_path}' type={self.window_type.name}>")
