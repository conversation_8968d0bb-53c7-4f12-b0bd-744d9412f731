{"version": "13.0.1", "description": "Professional text and hex editor with Binary Templates technology.", "homepage": "https://www.sweetscape.com/010editor/", "license": {"identifier": "Proprietary", "url": "https://www.sweetscape.com/010editor/manual/License.htm"}, "notes": "This is a free 30-day trial version. Please buy a copy of it for furthur evaluation.", "architecture": {"64bit": {"url": "https://download.sweetscape.com/010EditorWin64Portable13.0.1.zip", "hash": "4158136519a7f3bbc7e3f2f8b471884704b04edd37e8f73487b4e4ae63a63baa", "extract_dir": "010EditorWin64Portable"}, "32bit": {"url": "https://download.sweetscape.com/010EditorWin32Portable13.0.1.zip", "hash": "2564cd82e4eaea34671698cb36c635146a450de501e8f223bab59ab12a082b15", "extract_dir": "010EditorWin32Portable"}}, "bin": [["AppData\\010Editor.exe", "010editor"]], "shortcuts": [["AppData\\010Editor.exe", "010 Editor"]], "persist": ["AppData\\Config", "AppData\\Data", "AppData\\Plugins", "010 Scripts", "010 Templates", "AppData\\Temp"], "checkver": {"url": "https://www.sweetscape.com/download/previous/", "regex": "Download 010 Editor v([\\d.]+)"}, "autoupdate": {"architecture": {"64bit": {"url": "https://download.sweetscape.com/010EditorWin64Portable$version.zip"}, "32bit": {"url": "https://download.sweetscape.com/010EditorWin32Portable$version.zip"}}}}