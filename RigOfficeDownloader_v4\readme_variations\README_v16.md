# RigOfficeDownloader

> Automate document retrieval from NOV's RigDoc system, saving engineering time and streamlining 3D modeling preparation.

## Purpose

```
┌─────────────────────────────────────────────────────────────────────────┐
│ Eliminate tedious manual document retrieval from NOV's RigDoc system    │
│ through intelligent automation, preserving 75%+ engineering time for    │
│ high-value tasks.                                                       │
└─────────────────────────────────────────────────────────────────────────┘
```

## Workflow

```
┌─────────────────────────────────────────────────────────────────────────┐
│                                                                         │
│  Documents → Files → Downloads                                          │
│                                                                         │
│  1. Fetch document metadata                                             │
│  2. Select relevant documents                                           │
│  3. Fetch file metadata for selected docs                               │
│  4. Select files to download                                            │
│  5. Download and organize files                                         │
│                                                                         │
└─────────────────────────────────────────────────────────────────────────┘
```

## Features

```
┌─────────────────────────────────────────────────────────────────────────┐
│ • Three-stage workflow with user checkpoints                            │
│ • Interactive menu for flexible execution                               │
│ • Markdown interfaces for document/file review                          │
│ • Configurable filter chains                                            │
│ • Smart file organization with subfolder support based on naming        │
│   patterns                                                              │
│ • Metadata-driven file naming                                           │
│ • Error handling and deduplication                                      │
└─────────────────────────────────────────────────────────────────────────┘
```

## Setup

```
┌─────────────────────────────────────────────────────────────────────────┐
│ 1. Run `py_venv_init.bat` to create Python environment                  │
│ 2. Run `RigOfficeDownloader-v4.bat` to start the application            │
└─────────────────────────────────────────────────────────────────────────┘
```

## Usage

```
┌─────────────────────────────────────────────────────────────────────────┐
│ Interactive Menu Options:                                               │
│ [0] Change search parameters (rig number, URLs)                         │
│ [1] Configure filter chain                                              │
│ [2] Fetch docs (scrape initial data)                                    │
│ [3] Export docs (to Markdown for editing)                               │
│ [4] Import updated doc data                                             │
│ [5] Fetch files (prepare files for download)                            │
│ [6] Export files (to Markdown for editing)                              │
│ [7] Import updated file data                                            │
│ [8] Download files                                                      │
│                                                                         │
│ Run Modes:                                                              │
│ • `RigOfficeDownloader-v4.bat --auto` (full automation)                 │
│ • `RigOfficeDownloader-v4.bat --interactive` (menu-driven)              │
│ • `RigOfficeDownloader-v4.bat --config` (edit filters)                  │
└─────────────────────────────────────────────────────────────────────────┘
```

## Directory Structure

```
┌─────────────────────────────────────────────────────────────────────────┐
│ outputs/                                                                │
│ ├── data/               # Metadata control files                        │
│ │   ├── <rig>-a-docs.json                                               │
│ │   ├── <rig>-a-docs.md                                                 │
│ │   ├── <rig>-b-files.json                                              │
│ │   └── <rig>-b-files.md                                                │
│ └── downloads/          # Final organized files                         │
│     └── <rig>/                                                          │
│         ├── <subfolder>/                                                │
│         │   └── <item_generated_name>.<ext>                             │
│         └── ...                                                         │
└─────────────────────────────────────────────────────────────────────────┘
```

## Benefits

```
┌─────────────────────────────────────────────────────────────────────────┐
│                                                                         │
│ ┌─────────────────┬────────────────┬─────────────────────────────────┐  │
│ │                 │ Manual         │ Automated                       │  │
│ ├─────────────────┼────────────────┼─────────────────────────────────┤  │
│ │ Time per project│ 4-6 hours      │ <1 hour (75%+ saved)            │  │
│ ├─────────────────┼────────────────┼─────────────────────────────────┤  │
│ │ Organization    │ Error-prone    │ Metadata-driven                 │  │
│ ├─────────────────┼────────────────┼─────────────────────────────────┤  │
│ │ User review     │ Tedious        │ Markdown checkpoints            │  │
│ ├─────────────────┼────────────────┼─────────────────────────────────┤  │
│ │ Auditability    │ None           │ JSON/MD traceability            │  │
│ └─────────────────┴────────────────┴─────────────────────────────────┘  │
│                                                                         │
└─────────────────────────────────────────────────────────────────────────┘
```

## Technical Details

```
┌─────────────────────────────────────────────────────────────────────────┐
│ • Core Technologies: Python, Selenium, BeautifulSoup, JSON, Markdown     │
│                                                                         │
│ • Data Flow: Web scraping → JSON storage → Markdown interface →         │
│   User selection → Automated downloads                                  │
│                                                                         │
│ • Filter System: Pattern-based matching (inclusion/exclusion) on fields │
│                                                                         │
│ • File Organization: Subfolder creation from '/' in item_generated_name │
└─────────────────────────────────────────────────────────────────────────┘
```

## Version History

```
┌─────────────────────────────────────────────────────────────────────────┐
│ • v1: Basic document retrieval and download                             │
│ • v2: JSON/Markdown conversion and user selection                       │
│ • v3: Improved error handling and field organization                    │
│ • v4: Subfolder support, filter chains, field ordering                  │
└─────────────────────────────────────────────────────────────────────────┘
```

> "Complexity is the enemy of execution — Optimized for engineers, by engineers."
