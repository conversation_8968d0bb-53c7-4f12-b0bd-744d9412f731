"""
Monitor Manager module for detecting and managing monitors.
"""

import win32api
from .models import Monitor, WrappedMonitor


class MonitorManager:
    """
    Manages monitor detection and selection operations.
    """

    def __init__(self):
        self.monitors = []
        self.detect_monitors()

    def detect_monitors(self):
        """
        Detect all connected monitors.
        Returns a reference to self for method chaining.
        """
        self.monitors = []
        for (handle, _, info) in win32api.EnumDisplayMonitors(None, None):
            mon_info = win32api.GetMonitorInfo(handle)
            self.monitors.append(Monitor(handle, mon_info))
        return self

    def get_primary_monitor(self):
        """
        Get the primary monitor.
        Returns the primary monitor, or the first monitor if no primary is found.
        """
        for monitor in self.monitors:
            if monitor.is_primary:
                return monitor
        return self.monitors[0] if self.monitors else None

    def get_monitor_by_index(self, index):
        """
        Get a monitor by its index.
        Returns the monitor at the specified index, or None if the index is out of range.
        """
        if 0 <= index < len(self.monitors):
            return self.monitors[index]
        return None

    def get_wrapped_monitors(self):
        """
        Get a list of wrapped monitors with display-friendly formatting.
        Returns a list of WrappedMonitor objects.
        """
        return [WrappedMonitor(m, i) for i, m in enumerate(self.monitors)]

    def get_monitor_count(self):
        """
        Get the number of detected monitors.
        """
        return len(self.monitors)
