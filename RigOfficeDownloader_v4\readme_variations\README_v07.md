
## Overview
- Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.
- Use case: curate, review, and batch-download rig-related documents and technical files.

## Directory Structure

* `outputs/` (BASE\_OUTPUT): All results stored here
* `outputs/data/` (DATA\_DIR): Document and file metadata (JSON/MD)
* `outputs/downloads/` (DL\_DIR): Downloaded PDF and file outputs

## Pipeline Overview

1. Change search parameters (rig number, URLs)
2. Configure filter chain (add, edit, delete, toggle, reorder filters)
3. Fetch docs (scrape data from rigdoc.nov.com)
4. Export docs to Markdown (for selection/editing)
5. Import docs from Markdown (sync edited selection)
6. Fetch candidate files linked to selected docs
7. Export file list to Markdown (for editing/selecting files for download)
8. Import updated file list from Markdown
9. Download marked files (PDFs only, via Chrome)

## Manual Editing

* Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing
* Set `item_include` (docs) and `item_download` (files) fields

## Running the Tool

```bash
python rigdocscraper.py
```

* Interactive menu enables step selection (numbers/comma/space-separated)
* Supports adjusting parameters, filter configuration, and reviewing batch steps
* Prompts will guide through editing, import/export, and download procedures

## Troubleshooting

* Requires functioning Chrome installation; verify webdriver-manager compatibility
* Common issues: browser launch failures, login/captcha requirements, file permissions
* Output logs and warnings shown in terminal; inspect `outputs/data/` for progress
