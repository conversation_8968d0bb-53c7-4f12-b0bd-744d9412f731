├── .cmd
│   ├── py_venv_pip_install.bat
│   ├── py_venv_run_script.bat
│   ├── py_venv_terminal.bat
│   ├── py_venv_upgrade_requirements.bat
│   └── py_venv_write_requirements.bat
├── outputs
│   ├── data
│   │   ├── R5385.004.2-a-docs.json
│   │   ├── R5385.004.2-a-docs.md
│   │   ├── R5385.004.2-b-files.json
│   │   ├── R5385.004.2-b-files.md
│   │   ├── R5385.010-a-docs.json
│   │   ├── R5385.010-a-docs.md
│   │   ├── R5385.010-b-files.json
│   │   └── R5385.010-b-files.md
│   └── downloads
│       └── R5385.dirtree.md
├── readme_variations
│   ├── memory-bank
│   │   ├── 00_memory_bank_guide.md
│   │   ├── 01_foundation.md
│   │   ├── 02_context.md
│   │   ├── 03_patterns.md
│   │   ├── 04_tech.md
│   │   ├── 05_activity.md
│   │   ├── 06_progress.md
│   │   ├── 07_tasks.md
│   │   └── 08_objective.md
│   ├── README_v01.md
│   ├── README_v02.md
│   ├── README_v03.md
│   ├── README_v04.md
│   ├── README_v05.md
│   ├── README_v06.md
│   ├── README_v07.md
│   ├── README_v08.md
│   ├── README_v09.md
│   ├── README_v10.md
│   ├── README_v11.md
│   ├── README_v12.md
│   ├── README_v13.md
│   ├── README_v14.md
│   ├── README_v15.md
│   ├── README_v16.md
│   ├── README_v17.md
│   ├── consolidated_v1.md
│   ├── consolidated_v1.md.001
│   └── readme_variations.md
├── src
│   ├── .gitignore
│   ├── RigOfficeDownloader-v4.bat
│   ├── RigOfficeDownloader-v4.py
│   └── code_guidelines.md
├── .gitignore
├── GOAL.md
├── README.md
├── README.md.001
├── README_variation1.md
├── RigOfficeDownloader-v4.sublime-project
├── RigOfficeDownloader-v4.sublime-workspace
├── RigOfficeDownloader.sublime-workspace
├── generate_readme_variations.py
├── notes.md
├── py_venv_init.bat
├── requirements.txt
└── understanding_the_environment.md