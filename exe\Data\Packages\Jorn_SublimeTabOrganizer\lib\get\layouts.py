import sublime
import math

def current_layout(window):
    return window.layout()

def group_count(window):
    return window.num_groups()

def active_group(window):
    return window.active_group()

def create_row_layout_data(count):
    if count <= 0:
        return {"cols": [0.0, 1.0], "rows": [0.0, 1.0], "cells": [[0, 0, 1, 1]]}
    
    rows = [float(i) / count for i in range(count + 1)]
    cells = [[0, i, 1, i + 1] for i in range(count)]
    
    return {
        "cols": [0.0, 1.0],
        "rows": rows,
        "cells": cells
    }

def create_column_layout_data(count):
    if count <= 0:
        return {"cols": [0.0, 1.0], "rows": [0.0, 1.0], "cells": [[0, 0, 1, 1]]}
    
    cols = [float(i) / count for i in range(count + 1)]
    cells = [[i, 0, i + 1, 1] for i in range(count)]
    
    return {
        "cols": cols,
        "rows": [0.0, 1.0],
        "cells": cells
    }

def create_grid_layout_data(total):
    if total <= 0:
        return {"cols": [0.0, 1.0], "rows": [0.0, 1.0], "cells": [[0, 0, 1, 1]]}
    
    cols_count = math.ceil(math.sqrt(total))
    rows_count = math.ceil(total / cols_count)
    
    cols = [float(i) / cols_count for i in range(cols_count + 1)]
    rows = [float(i) / rows_count for i in range(rows_count + 1)]
    
    cells = []
    for i in range(total):
        row = i // cols_count
        col = i % cols_count
        cells.append([col, row, col + 1, row + 1])
    
    return {
        "cols": cols,
        "rows": rows,
        "cells": cells
    }
