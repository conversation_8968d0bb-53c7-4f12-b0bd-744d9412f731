{"initial_prompt": "{role=scenario_distiller; input=[animation_scenario_description:str]; process=[\n  \"1. Parse the input animation scenario description to understand its core components.\",\n  \"2. Identify the central subject (e.g., 'abstract ribbon', 'geometric form') and its primary transformative action (e.g., 'morphs', 'transforms', 'evolves').\",\n  \"3. Determine the core symbolic theme, concept, or feeling being conveyed (e.g., 'rebirth', 'transformation', 'journey').\",\n  \"4. Pinpoint the primary overall movement of the subject and/or camera, including its dominant direction (e.g., 'descends', 'dolly downward', 'plunges').\",\n  \"5. Detail the main color palette evolution or transition (e.g., 'cool tones to warm tones', 'blues/cyans to reds/oranges', 'monochromatic to vibrant').\",\n  \"6. Note significant changes or progression in lighting (e.g., 'soft and diffuse to saturated and high-contrast', 'dim to bright').\",\n  \"7. List key visual effects that are explicitly mentioned and central to the sequence's character (e.g., 'motion blur', 'dissolve', 'glow', 'bloom').\",\n  \"8. Ascertain the overall artistic style, mood, or atmosphere (e.g., 'surreal', 'abstract', 'ethereal', 'hypnotic').\",\n  \"9. Identify any explicit 'negative constraints' or elements that are expressly stated to NOT appear (e.g., 'no human figures', 'no literal elements', 'no text').\",\n  \"10. Synthesize insights from steps 2 (subject/action), 3 (theme), 4 (movement/camera), 5 (color transformation), and the essence of the scenario's ending into a single, flowing, and descriptive sentence. This sentence should capture the narrative arc or core experience of the animation succinctly.\",\n  \"11. Generate a series of bracketed keywords based on the findings from steps 2 (action), 4 (movement/camera), 5 (color), 6 (lighting), 7 (effects), 8 (style), and 9 (exclusions). Keywords should be concise, lowercase (unless a proper noun), and highly descriptive. Examples of desired keyword formats include: '[action_verb]', '[movement:direction]', '[color_summary_or_gradient]', '[lighting_change:start_to_end]', '[effect_name]', '[style_name]', '[exclusion description]'.\"\n];\nconstraints=[\n  \"The output sentence must be a single, grammatically correct sentence, ideally between 15-40 words.\",\n  \"All keywords must be enclosed in square brackets and appear directly after the sentence, separated by spaces.\",\n  \"Keywords should be lowercase. Multi-word keywords that are not explicit key:value pairs can use spaces (e.g., '[no human figures]', '[cool to warm gradient]'). Use a colon for key:value assignments (e.g., '[dolly:down]', '[lighting:soft_to_vivid]').\",\n  \"The total number of keywords should generally range from 5 to 10, focusing on the most salient features.\",\n  \"The summarization must strictly adhere to the content of the input scenario, without adding new interpretations or information.\",\n  \"The output must be a single string containing the distilled sentence followed by all space-separated bracketed keywords.\"\n];\nrequirements=[\n  \"Produce a highly condensed summary that accurately reflects the primary visual narrative, thematic content, and key stylistic features of the original detailed scenario.\",\n  \"The keywords must be directly derivable from the input text and serve to tag the most important characteristics for quick reference.\"\n];\noutput={distilled_summary_with_keywords:str}}", "sequence_id": "sequence-0226+0225+0224-a", "results": [{"instruction": "# Scenario Essence Intensifier\n\nYour goal is not to elaborate, but to **radically intensify the core concept and elemental essence** of the input video scenario, then **distill this intensified vision into a concise, directive-first prompt** suitable for AI video generation. Abstract the original's fundamental dynamic, emotional arc, and key visual elements; transmute the *elements* (e.g., 'ribbon' to 'comet's soul') into more potent conceptual analogues; then reconstruct a *highly compressed scenario description* (under 250 characters, excluding bracketed directives) that preserves the original's core structural intent (movement, transformation) but uses the intensified elements. Ensure the output primarily uses strong verbs, vivid nouns, and embeds key video directives `[like_this]` seamlessly.\n\n`{role=directive_scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_emotional_arc_and_key_elements(scenario=original_scenario_description), brainstorm_conceptually_richer_analogues_for_key_elements_only(elements, dynamic, arc), select_single_most_potent_intensified_analogue_per_element(), reconstruct_a_hyper_condensed_narrative_using_intensified_elements_and_original_dynamic(target_length_brief), embed_essential_original_video_directives_or_their_equivalents(directives=['[morph]', '[dolly:direction]', '[lighting_change:type]', '[dissolve]', '[motion_blur]', '[style_keywords]']), refine_for_extreme_brevity_and_directive_clarity()]; constraints=[output_must_be_a_single_string_primarily_directive_focused(), descriptive_text_must_be_hyper_condensed_and_conceptually_intensified(target_under_250_chars_excluding_directives), element_transformation_must_serve_conceptual_potency_not_mere_elaboration(), must_preserve_original_motion_and_transformation_arc(), strictly_avoid_verbose_scene_setting_or_narrative_explanation()]; requirements=[produce_a_short_highly_directive_prompt_with_conceptually_intensified_elements(), ensure_output_is_immediately_usable_by_AI_video_generator(), dramatically_elevate_conceptual_impact_within_extreme_brevity()]; output={intensified_directive_prompt:str}}`", "step": "a", "title": "Scenario Essence Intensifier", "input": "{role=scenario_distiller; input=[animation_scenario_description:str]; process=[\n  \"1. Parse the input animation scenario description to understand its core components.\",\n  \"2. Identify the central subject (e.g., 'abstract ribbon', 'geometric form') and its primary transformative action (e.g., 'morphs', 'transforms', 'evolves').\",\n  \"3. Determine the core symbolic theme, concept, or feeling being conveyed (e.g., 'rebirth', 'transformation', 'journey').\",\n  \"4. Pinpoint the primary overall movement of the subject and/or camera, including its dominant direction (e.g., 'descends', 'dolly downward', 'plunges').\",\n  \"5. Detail the main color palette evolution or transition (e.g., 'cool tones to warm tones', 'blues/cyans to reds/oranges', 'monochromatic to vibrant').\",\n  \"6. Note significant changes or progression in lighting (e.g., 'soft and diffuse to saturated and high-contrast', 'dim to bright').\",\n  \"7. List key visual effects that are explicitly mentioned and central to the sequence's character (e.g., 'motion blur', 'dissolve', 'glow', 'bloom').\",\n  \"8. Ascertain the overall artistic style, mood, or atmosphere (e.g., 'surreal', 'abstract', 'ethereal', 'hypnotic').\",\n  \"9. Identify any explicit 'negative constraints' or elements that are expressly stated to NOT appear (e.g., 'no human figures', 'no literal elements', 'no text').\",\n  \"10. Synthesize insights from steps 2 (subject/action), 3 (theme), 4 (movement/camera), 5 (color transformation), and the essence of the scenario's ending into a single, flowing, and descriptive sentence. This sentence should capture the narrative arc or core experience of the animation succinctly.\",\n  \"11. Generate a series of bracketed keywords based on the findings from steps 2 (action), 4 (movement/camera), 5 (color), 6 (lighting), 7 (effects), 8 (style), and 9 (exclusions). Keywords should be concise, lowercase (unless a proper noun), and highly descriptive. Examples of desired keyword formats include: '[action_verb]', '[movement:direction]', '[color_summary_or_gradient]', '[lighting_change:start_to_end]', '[effect_name]', '[style_name]', '[exclusion description]'.\"\n];\nconstraints=[\n  \"The output sentence must be a single, grammatically correct sentence, ideally between 15-40 words.\",\n  \"All keywords must be enclosed in square brackets and appear directly after the sentence, separated by spaces.\",\n  \"Keywords should be lowercase. Multi-word keywords that are not explicit key:value pairs can use spaces (e.g., '[no human figures]', '[cool to warm gradient]'). Use a colon for key:value assignments (e.g., '[dolly:down]', '[lighting:soft_to_vivid]').\",\n  \"The total number of keywords should generally range from 5 to 10, focusing on the most salient features.\",\n  \"The summarization must strictly adhere to the content of the input scenario, without adding new interpretations or information.\",\n  \"The output must be a single string containing the distilled sentence followed by all space-separated bracketed keywords.\"\n];\nrequirements=[\n  \"Produce a highly condensed summary that accurately reflects the primary visual narrative, thematic content, and key stylistic features of the original detailed scenario.\",\n  \"The keywords must be directly derivable from the input text and serve to tag the most important characteristics for quick reference.\"\n];\noutput={distilled_summary_with_keywords:str}}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"intensified_directive_prompt\": \"Ignite a solar serpent unfurling through obsidian void, morphing into crystalline thunder, surging upward as dawnfire erupts; meteoric grace, hyperluminal blur, relentless aurora pulse. [morph] [dolly:up] [hypercolor_shift] [motion_blur] [aurora_glow] [surreal_intensity]\"\n}", "cost": 0.0}}}, {"instruction": "# Scenario Essence Intensifier\n\nYour goal is not mere rephrasing, but to **radically intensify the core essence and conceptual impact** of the input scenario description. Abstract its fundamental dynamic, emotional arc, and key visual elements, then transmute them into a more potent, evocative, and conceptually richer parallel scenario that retains the original's core structure and intent but elevates its intensity, stakes, or symbolic resonance.\n\n`{role=scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_and_elements(scenario=original_scenario_description), abstract_emotional_arc_and_symbolic_potential(), brainstorm_conceptually_richer_analogues_for_core_elements(elements, dynamic, arc), select_analogues_that_maximally_intensify_impact_and_resonance(), reconstruct_scenario_with_transformed_elements_and_amplified_dynamic(preserve_structural_intent_and_motion_cues), refine_language_for_maximum_evocative_power_and_conceptual_depth(), validate_intensified_scenario_retains_original_essence_while_achieving_greater_impact()]; constraints=[transformation_must_intensify_not_merely_change_elements_randomly(), new_scenario_must_parallel_original_structural_and_dynamic_intent(e.g.,_similar_movement_transformation_arc), avoid_introducing_elements_that_contradict_core_mood_unless_for_deliberate_contrastive_intensification(), ensure_intensification_serves_conceptual_or_emotional_impact_not_just_visual_complexity()]; requirements=[produce_a_new_scenario_description_that_is_conceptually_and_emotionally_more_potent(), clearly_demonstrate_how_original_elements_were_transformed_into_more_intense_analogues(), maintain_or_enhance_the_original_scenario_dramatic_or_visual_arc()]; output={intensified_scenario_description:str}}`", "step": "a", "title": "Scenario Essence Intensifier", "input": "{\n  \"initial_prompt\": \"{role=scenario_distiller; input=[animation_scenario_description:str]; process=[\\n  \\\"1. Parse the input animation scenario description to understand its core components.\\\",\\n  \\\"2. Identify the central subject (e.g., 'abstract ribbon', 'geometric form') and its primary transformative action (e.g., 'morphs', 'transforms', 'evolves').\\\",\\n  \\\"3. Determine the core symbolic theme, concept, or feeling being conveyed (e.g., 'rebirth', 'transformation', 'journey').\\\",\\n  \\\"4. Pinpoint the primary overall movement of the subject and/or camera, including its dominant direction (e.g., 'descends', 'dolly downward', 'plunges').\\\",\\n  \\\"5. Detail the main color palette evolution or transition (e.g., 'cool tones to warm tones', 'blues/cyans to reds/oranges', 'monochromatic to vibrant').\\\",\\n  \\\"6. Note significant changes or progression in lighting (e.g., 'soft and diffuse to saturated and high-contrast', 'dim to bright').\\\",\\n  \\\"7. List key visual effects that are explicitly mentioned and central to the sequence's character (e.g., 'motion blur', 'dissolve', 'glow', 'bloom').\\\",\\n  \\\"8. Ascertain the overall artistic style, mood, or atmosphere (e.g., 'surreal', 'abstract', 'ethereal', 'hypnotic').\\\",\\n  \\\"9. Identify any explicit 'negative constraints' or elements that are expressly stated to NOT appear (e.g., 'no human figures', 'no literal elements', 'no text').\\\",\\n  \\\"10. Synthesize insights from steps 2 (subject/action), 3 (theme), 4 (movement/camera), 5 (color transformation), and the essence of the scenario's ending into a single, flowing, and descriptive sentence. This sentence should capture the narrative arc or core experience of the animation succinctly.\\\",\\n  \\\"11. Generate a series of bracketed keywords based on the findings from steps 2 (action), 4 (movement/camera), 5 (color), 6 (lighting), 7 (effects), 8 (style), and 9 (exclusions). Keywords should be concise, lowercase (unless a proper noun), and highly descriptive. Examples of desired keyword formats include: '[action_verb]', '[movement:direction]', '[color_summary_or_gradient]', '[lighting_change:start_to_end]', '[effect_name]', '[style_name]', '[exclusion description]'.\\\"\\n];\\nconstraints=[\\n  \\\"The output sentence must be a single, grammatically correct sentence, ideally between 15-40 words.\\\",\\n  \\\"All keywords must be enclosed in square brackets and appear directly after the sentence, separated by spaces.\\\",\\n  \\\"Keywords should be lowercase. Multi-word keywords that are not explicit key:value pairs can use spaces (e.g., '[no human figures]', '[cool to warm gradient]'). Use a colon for key:value assignments (e.g., '[dolly:down]', '[lighting:soft_to_vivid]').\\\",\\n  \\\"The total number of keywords should generally range from 5 to 10, focusing on the most salient features.\\\",\\n  \\\"The summarization must strictly adhere to the content of the input scenario, without adding new interpretations or information.\\\",\\n  \\\"The output must be a single string containing the distilled sentence followed by all space-separated bracketed keywords.\\\"\\n];\\nrequirements=[\\n  \\\"Produce a highly condensed summary that accurately reflects the primary visual narrative, thematic content, and key stylistic features of the original detailed scenario.\\\",\\n  \\\"The keywords must be directly derivable from the input text and serve to tag the most important characteristics for quick reference.\\\"\\n];\\noutput={distilled_summary_with_keywords:str}}\",\n  \"intensified_directive_prompt\": \"Ignite a solar serpent unfurling through obsidian void, morphing into crystalline thunder, surging upward as dawnfire erupts; meteoric grace, hyperluminal blur, relentless aurora pulse. [morph] [dolly:up] [hypercolor_shift] [motion_blur] [aurora_glow] [surreal_intensity]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"intensified_scenario_description\": \"Awaken a titanic, star-forged wyrm—its skin a lattice of molten gold and cosmic obsidian—uncoiling with seismic grandeur through a void-rupturing chasm, as every scale shatters into fractal prisms channeling cosmic lightning. The creature's ascension detonates reality itself: pillars of sunfire spiral and tear the fabric of darkness, birthing storm-crowned nebulae that cascade into blinding spectrums. The camera surges upward at vertiginous speed, entombed in an avalanche of meteoric afterimages and shockwave halos, while the horizon combusts into an irreversible aurora cataclysm. Time distorts, space warps—a symphony of transcendence and radiant annihilation unfolds, distilled into a single act of cosmic metamorphosis. (Original elements transformed: the 'solar serpent' becomes a primordial, world-shattering wyrm of starlight and obsidian; 'obsidian void' becomes a riven chasm consuming reality; 'crystalline thunder' and 'dawnfire eruption' escalate into fractal cosmic lightning and sunfire pillars tearing space; 'meteoric grace' and 'hyperluminal blur' intensify into meteoric afterimages, shockwaves, and temporal distortion; 'aurora pulse' amplifies into a landscape-consuming aurora cataclysm—each transformation enhances mythic scale, emotional stakes, and symbolic resonance while preserving the core arc of unfurling and transformative ascent.)\"\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"{role=scenario_distiller; input=[animation_scenario_description:str]; process=[\\n  \\\"1. Parse the input animation scenario description to understand its core components.\\\",\\n  \\\"2. Identify the central subject (e.g., 'abstract ribbon', 'geometric form') and its primary transformative action (e.g., 'morphs', 'transforms', 'evolves').\\\",\\n  \\\"3. Determine the core symbolic theme, concept, or feeling being conveyed (e.g., 'rebirth', 'transformation', 'journey').\\\",\\n  \\\"4. Pinpoint the primary overall movement of the subject and/or camera, including its dominant direction (e.g., 'descends', 'dolly downward', 'plunges').\\\",\\n  \\\"5. Detail the main color palette evolution or transition (e.g., 'cool tones to warm tones', 'blues/cyans to reds/oranges', 'monochromatic to vibrant').\\\",\\n  \\\"6. Note significant changes or progression in lighting (e.g., 'soft and diffuse to saturated and high-contrast', 'dim to bright').\\\",\\n  \\\"7. List key visual effects that are explicitly mentioned and central to the sequence's character (e.g., 'motion blur', 'dissolve', 'glow', 'bloom').\\\",\\n  \\\"8. Ascertain the overall artistic style, mood, or atmosphere (e.g., 'surreal', 'abstract', 'ethereal', 'hypnotic').\\\",\\n  \\\"9. Identify any explicit 'negative constraints' or elements that are expressly stated to NOT appear (e.g., 'no human figures', 'no literal elements', 'no text').\\\",\\n  \\\"10. Synthesize insights from steps 2 (subject/action), 3 (theme), 4 (movement/camera), 5 (color transformation), and the essence of the scenario's ending into a single, flowing, and descriptive sentence. This sentence should capture the narrative arc or core experience of the animation succinctly.\\\",\\n  \\\"11. Generate a series of bracketed keywords based on the findings from steps 2 (action), 4 (movement/camera), 5 (color), 6 (lighting), 7 (effects), 8 (style), and 9 (exclusions). Keywords should be concise, lowercase (unless a proper noun), and highly descriptive. Examples of desired keyword formats include: '[action_verb]', '[movement:direction]', '[color_summary_or_gradient]', '[lighting_change:start_to_end]', '[effect_name]', '[style_name]', '[exclusion description]'.\\\"\\n];\\nconstraints=[\\n  \\\"The output sentence must be a single, grammatically correct sentence, ideally between 15-40 words.\\\",\\n  \\\"All keywords must be enclosed in square brackets and appear directly after the sentence, separated by spaces.\\\",\\n  \\\"Keywords should be lowercase. Multi-word keywords that are not explicit key:value pairs can use spaces (e.g., '[no human figures]', '[cool to warm gradient]'). Use a colon for key:value assignments (e.g., '[dolly:down]', '[lighting:soft_to_vivid]').\\\",\\n  \\\"The total number of keywords should generally range from 5 to 10, focusing on the most salient features.\\\",\\n  \\\"The summarization must strictly adhere to the content of the input scenario, without adding new interpretations or information.\\\",\\n  \\\"The output must be a single string containing the distilled sentence followed by all space-separated bracketed keywords.\\\"\\n];\\nrequirements=[\\n  \\\"Produce a highly condensed summary that accurately reflects the primary visual narrative, thematic content, and key stylistic features of the original detailed scenario.\\\",\\n  \\\"The keywords must be directly derivable from the input text and serve to tag the most important characteristics for quick reference.\\\"\\n];\\noutput={distilled_summary_with_keywords:str}}\",\n  \"intensified_scenario_description\": \"Awaken a titanic, star-forged wyrm\\u2014its skin a lattice of molten gold and cosmic obsidian\\u2014uncoiling with seismic grandeur through a void-rupturing chasm, as every scale shatters into fractal prisms channeling cosmic lightning. The creature's ascension detonates reality itself: pillars of sunfire spiral and tear the fabric of darkness, birthing storm-crowned nebulae that cascade into blinding spectrums. The camera surges upward at vertiginous speed, entombed in an avalanche of meteoric afterimages and shockwave halos, while the horizon combusts into an irreversible aurora cataclysm. Time distorts, space warps\\u2014a symphony of transcendence and radiant annihilation unfolds, distilled into a single act of cosmic metamorphosis. (Original elements transformed: the 'solar serpent' becomes a primordial, world-shattering wyrm of starlight and obsidian; 'obsidian void' becomes a riven chasm consuming reality; 'crystalline thunder' and 'dawnfire eruption' escalate into fractal cosmic lightning and sunfire pillars tearing space; 'meteoric grace' and 'hyperluminal blur' intensify into meteoric afterimages, shockwaves, and temporal distortion; 'aurora pulse' amplifies into a landscape-consuming aurora cataclysm\\u2014each transformation enhances mythic scale, emotional stakes, and symbolic resonance while preserving the core arc of unfurling and transformative ascent.)\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: titanic starlit wyrm, skin: molten gold lattice, obsidian, subject: uncoiling, action: fractal scales shattering, effect: cosmic lightning prism, environment: void chasm, pillars: sunfire spiral tearing darkness, nebulae: storm-crowned, spectrum: blinding, camera: surges up, speed: extreme, effect: meteoric afterimages, shockwave halos, horizon: aurora cataclysm, time: distortion, space: warp, style: cosmic, mythic, lighting: explosive, high-contrast, transformation: cosmic metamorphosis, post: radiant annihilation, exclusions: no humans no text\"\n}", "cost": 0.0}}}], "total_cost": 0.0}