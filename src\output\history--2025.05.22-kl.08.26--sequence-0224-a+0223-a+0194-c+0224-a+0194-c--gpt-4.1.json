{"initial_prompt": "An abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][dissolve][motion_blur][surreal][cool_to_warm_gradient][no human figures]\n\nA single abstract, morphing geometric shape (such as a smooth, flowing ribbon) is centered against an undefined, subtly gradient background. The camera glides directly downward alongside the shape throughout the entire 5 seconds. The shape continuously morphs as it descends, symbolizing transformation. At the top, colors are exclusively cool blues and cyans with soft, diffuse lighting. As the descent progresses, both the shape and background gradually shift toward intense warm reds and oranges, with lighting increasing in saturation and contrast. Dynamic motion blur trails accentuate the movement, intensifying as the downward motion speeds up in the latter half. The animation ends as the shape reaches maximum vividness and warmth at the bottom.\n\nA single morphing geometric ribbon floats centered on a soft gradient background, [dolly:down] alongside the ribbon as it transforms and descends. Gradually shift colors from cool blues and cyans to vivid reds and oranges, [lighting_change] increases saturation and contrast, [morph] enhances ribbon transformation, dynamic motion blur trails intensify as speed increases toward the bottom.\n\nAs the ribbon and invisible camera move downward, both ribbon and background colors transition gradually: cool blue-cyan at the top to vivid red-orange at the bottom. Lighting grows noticeably more saturated and higher in contrast by the final second. A subtle motion blur trail appears behind the ribbon, low at first, increasing in intensity as downward speed increases, peaking during the final second. Video ends with the ribbon and background at maximum color warmth, saturation, and contrast.", "sequence_id": "sequence-0224-a+0223-a+0194-c+0224-a+0194-c", "results": [{"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "An abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][dissolve][motion_blur][surreal][cool_to_warm_gradient][no human figures]\n\nA single abstract, morphing geometric shape (such as a smooth, flowing ribbon) is centered against an undefined, subtly gradient background. The camera glides directly downward alongside the shape throughout the entire 5 seconds. The shape continuously morphs as it descends, symbolizing transformation. At the top, colors are exclusively cool blues and cyans with soft, diffuse lighting. As the descent progresses, both the shape and background gradually shift toward intense warm reds and oranges, with lighting increasing in saturation and contrast. Dynamic motion blur trails accentuate the movement, intensifying as the downward motion speeds up in the latter half. The animation ends as the shape reaches maximum vividness and warmth at the bottom.\n\nA single morphing geometric ribbon floats centered on a soft gradient background, [dolly:down] alongside the ribbon as it transforms and descends. Gradually shift colors from cool blues and cyans to vivid reds and oranges, [lighting_change] increases saturation and contrast, [morph] enhances ribbon transformation, dynamic motion blur trails intensify as speed increases toward the bottom.\n\nAs the ribbon and invisible camera move downward, both ribbon and background colors transition gradually: cool blue-cyan at the top to vivid red-orange at the bottom. Lighting grows noticeably more saturated and higher in contrast by the final second. A subtle motion blur trail appears behind the ribbon, low at first, increasing in intensity as downward speed increases, peaking during the final second. Video ends with the ribbon and background at maximum color warmth, saturation, and contrast.", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: abstract, background: smooth_gradient, subject: morphing geometric ribbon, color: cool_blue_cyan_top_to_vivid_red_orange_bottom, action: continuous morph, movement: ribbon and camera dolly_down, lighting: soft_diffuse_top_to_high_saturation_contrast_bottom, motion: blur trails intensifying with speed, camera: center follow, duration: 5s, effects: dissolve, surreal, no_human_figures\"\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"initial_prompt\": \"An abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][dissolve][motion_blur][surreal][cool_to_warm_gradient][no human figures]\\n\\nA single abstract, morphing geometric shape (such as a smooth, flowing ribbon) is centered against an undefined, subtly gradient background. The camera glides directly downward alongside the shape throughout the entire 5 seconds. The shape continuously morphs as it descends, symbolizing transformation. At the top, colors are exclusively cool blues and cyans with soft, diffuse lighting. As the descent progresses, both the shape and background gradually shift toward intense warm reds and oranges, with lighting increasing in saturation and contrast. Dynamic motion blur trails accentuate the movement, intensifying as the downward motion speeds up in the latter half. The animation ends as the shape reaches maximum vividness and warmth at the bottom.\\n\\nA single morphing geometric ribbon floats centered on a soft gradient background, [dolly:down] alongside the ribbon as it transforms and descends. Gradually shift colors from cool blues and cyans to vivid reds and oranges, [lighting_change] increases saturation and contrast, [morph] enhances ribbon transformation, dynamic motion blur trails intensify as speed increases toward the bottom.\\n\\nAs the ribbon and invisible camera move downward, both ribbon and background colors transition gradually: cool blue-cyan at the top to vivid red-orange at the bottom. Lighting grows noticeably more saturated and higher in contrast by the final second. A subtle motion blur trail appears behind the ribbon, low at first, increasing in intensity as downward speed increases, peaking during the final second. Video ends with the ribbon and background at maximum color warmth, saturation, and contrast.\",\n  \"optimized_video_prompt\": \"scene: abstract, background: smooth_gradient, subject: morphing geometric ribbon, color: cool_blue_cyan_top_to_vivid_red_orange_bottom, action: continuous morph, movement: ribbon and camera dolly_down, lighting: soft_diffuse_top_to_high_saturation_contrast_bottom, motion: blur trails intensifying with speed, camera: center follow, duration: 5s, effects: dissolve, surreal, no_human_figures\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"generated_video_sequence\": \"file_path_or_stream/abstract_ribbon_rebirth_sequence.mp4\",\n  \"video_synthesis_director_execution\": {\n    \"parse_all_parameters\": {\n      \"scene\": \"abstract\",\n      \"background\": \"smooth gradient\",\n      \"subject\": \"single morphing geometric ribbon\",\n      \"colors\": \"cool blue/cyan at top, transitioning to vivid red/orange at bottom\",\n      \"action\": \"continuous morph of ribbon form and color\",\n      \"movement\": \"ribbon and camera dolly downward together\",\n      \"lighting\": \"soft and diffuse at top, increasing to high saturation and contrast at bottom\",\n      \"motion_blur\": \"dynamic, increases as downward speed increases toward video end\",\n      \"camera\": \"centered, glide follows ribbon downward\",\n      \"duration\": \"5 seconds\",\n      \"effects\": [\"dissolve transitions within morphs\", \"surreal style\", \"strictly no human figures\"]\n    },\n    \"visualize_scene_and_subject_composition\": [\n      \"Frame 1s: A soft, ethereal, smooth gradient background in cool blues and cyans. Centered is a luminous, glass-like ribbon with gentle curves, semi-transparent, faint reflections. The ribbon hovers, loosely tethered to the visual midline.\",\n      \"No distinct horizon; the background dissolves seamlessly above and below, subtly pulsing with faint grain for surreal texture.\"\n    ],\n    \"choreograph_action_and_camera_movements\": [\n      \"From the first frame, the camera glides downward in parallel with the ribbon—always keeping it perfectly centered. Both ribbon and camera accelerate slowly at first, then gather pace after the midpoint.\",\n      \"As the descent accelerates, the morphing of the ribbon becomes more pronounced: the ribbon twists, loops, and literally unfurls into increasingly complex forms, sometimes echoing organic motifs, sometimes geometric.\",\n      \"Camera dolly is seamless, without shake or abrupt jumps; the motion is dreamlike and continuous, paced to crescendo in the final second.\"\n    ],\n    \"apply_lighting_and_atmospheric_effects\": [\n      \"Lighting at the start is ultra-soft and diffuse, casting gentle highlights and feathery shadows within the curves of the ribbon.\",\n      \"As the descent continues, invisible light sources grow sharper and hotter—rim lighting ignites along the ribbon’s edges, reflections intensify, color and luminosity pulse synchronously with the morphing action.\",\n      \"By the last second, the scene is bathed in saturated, high-contrast warmth; the background radiates vivid reds and oranges, the ribbon is aglow with internal light.\"\n    ],\n    \"render_in_specified_visual_style\": [\n      \"Overall mood is surreal—physics and texture are heightened but stylized: the ribbon shimmers, its surface subtly rippling, as if made of living liquid glass.\",\n      \"Dissolve effects blend the morphing stages, making the transformation hypnotic and continuous.\",\n      \"Motion blur trails stretch elegantly behind the ribbon: low intensity and tight at start, gradually smearing wider and wilder as the speed and passion of the plunge build.\"\n    ],\n    \"apply_post_processing_enhancements\": [\n      \"Color grading ensures flawless, fluid progression in hue and brightness—cool to warm, soft to vivid.\",\n      \"Glow filters and selective sharpening reinforce the central ribbon, helping it pop as its form and color climax at video end.\",\n      \"Final dissolve as the sequence ends: ribbon and background both surge to peak warmth and saturation, then softly blur, symbolizing transcendent rebirth.\"\n    ],\n    \"ensure_sequence_coherence_and_emotional_impact\": [\n      \"Visual and literal descent mirrors symbolic ‘rebirth’—from serene, cold anonymity to passionate transformation and emergence.\",\n      \"The morphing and color crescendo, tightly synched to the downward sweep and intensifying motion blur, deliver a visceral, stirring journey culminating in radiant catharsis as the screen fills with fire-like warmth.\"\n    ]\n  }\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"An abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][dissolve][motion_blur][surreal][cool_to_warm_gradient][no human figures]\\n\\nA single abstract, morphing geometric shape (such as a smooth, flowing ribbon) is centered against an undefined, subtly gradient background. The camera glides directly downward alongside the shape throughout the entire 5 seconds. The shape continuously morphs as it descends, symbolizing transformation. At the top, colors are exclusively cool blues and cyans with soft, diffuse lighting. As the descent progresses, both the shape and background gradually shift toward intense warm reds and oranges, with lighting increasing in saturation and contrast. Dynamic motion blur trails accentuate the movement, intensifying as the downward motion speeds up in the latter half. The animation ends as the shape reaches maximum vividness and warmth at the bottom.\\n\\nA single morphing geometric ribbon floats centered on a soft gradient background, [dolly:down] alongside the ribbon as it transforms and descends. Gradually shift colors from cool blues and cyans to vivid reds and oranges, [lighting_change] increases saturation and contrast, [morph] enhances ribbon transformation, dynamic motion blur trails intensify as speed increases toward the bottom.\\n\\nAs the ribbon and invisible camera move downward, both ribbon and background colors transition gradually: cool blue-cyan at the top to vivid red-orange at the bottom. Lighting grows noticeably more saturated and higher in contrast by the final second. A subtle motion blur trail appears behind the ribbon, low at first, increasing in intensity as downward speed increases, peaking during the final second. Video ends with the ribbon and background at maximum color warmth, saturation, and contrast.\",\n  \"generated_video_sequence\": \"file_path_or_stream/abstract_ribbon_rebirth_sequence.mp4\",\n  \"video_synthesis_director_execution\": {\n    \"parse_all_parameters\": {\n      \"scene\": \"abstract\",\n      \"background\": \"smooth gradient\",\n      \"subject\": \"single morphing geometric ribbon\",\n      \"colors\": \"cool blue/cyan at top, transitioning to vivid red/orange at bottom\",\n      \"action\": \"continuous morph of ribbon form and color\",\n      \"movement\": \"ribbon and camera dolly downward together\",\n      \"lighting\": \"soft and diffuse at top, increasing to high saturation and contrast at bottom\",\n      \"motion_blur\": \"dynamic, increases as downward speed increases toward video end\",\n      \"camera\": \"centered, glide follows ribbon downward\",\n      \"duration\": \"5 seconds\",\n      \"effects\": [\n        \"dissolve transitions within morphs\",\n        \"surreal style\",\n        \"strictly no human figures\"\n      ]\n    },\n    \"visualize_scene_and_subject_composition\": [\n      \"Frame 1s: A soft, ethereal, smooth gradient background in cool blues and cyans. Centered is a luminous, glass-like ribbon with gentle curves, semi-transparent, faint reflections. The ribbon hovers, loosely tethered to the visual midline.\",\n      \"No distinct horizon; the background dissolves seamlessly above and below, subtly pulsing with faint grain for surreal texture.\"\n    ],\n    \"choreograph_action_and_camera_movements\": [\n      \"From the first frame, the camera glides downward in parallel with the ribbon\\u2014always keeping it perfectly centered. Both ribbon and camera accelerate slowly at first, then gather pace after the midpoint.\",\n      \"As the descent accelerates, the morphing of the ribbon becomes more pronounced: the ribbon twists, loops, and literally unfurls into increasingly complex forms, sometimes echoing organic motifs, sometimes geometric.\",\n      \"Camera dolly is seamless, without shake or abrupt jumps; the motion is dreamlike and continuous, paced to crescendo in the final second.\"\n    ],\n    \"apply_lighting_and_atmospheric_effects\": [\n      \"Lighting at the start is ultra-soft and diffuse, casting gentle highlights and feathery shadows within the curves of the ribbon.\",\n      \"As the descent continues, invisible light sources grow sharper and hotter\\u2014rim lighting ignites along the ribbon\\u2019s edges, reflections intensify, color and luminosity pulse synchronously with the morphing action.\",\n      \"By the last second, the scene is bathed in saturated, high-contrast warmth; the background radiates vivid reds and oranges, the ribbon is aglow with internal light.\"\n    ],\n    \"render_in_specified_visual_style\": [\n      \"Overall mood is surreal\\u2014physics and texture are heightened but stylized: the ribbon shimmers, its surface subtly rippling, as if made of living liquid glass.\",\n      \"Dissolve effects blend the morphing stages, making the transformation hypnotic and continuous.\",\n      \"Motion blur trails stretch elegantly behind the ribbon: low intensity and tight at start, gradually smearing wider and wilder as the speed and passion of the plunge build.\"\n    ],\n    \"apply_post_processing_enhancements\": [\n      \"Color grading ensures flawless, fluid progression in hue and brightness\\u2014cool to warm, soft to vivid.\",\n      \"Glow filters and selective sharpening reinforce the central ribbon, helping it pop as its form and color climax at video end.\",\n      \"Final dissolve as the sequence ends: ribbon and background both surge to peak warmth and saturation, then softly blur, symbolizing transcendent rebirth.\"\n    ],\n    \"ensure_sequence_coherence_and_emotional_impact\": [\n      \"Visual and literal descent mirrors symbolic \\u2018rebirth\\u2019\\u2014from serene, cold anonymity to passionate transformation and emergence.\",\n      \"The morphing and color crescendo, tightly synched to the downward sweep and intensifying motion blur, deliver a visceral, stirring journey culminating in radiant catharsis as the screen fills with fire-like warmth.\"\n    ]\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"A surreal, morphing geometric ribbon floats centered over a smooth gradient background. [dolly:down] follows the ribbon as it descends and transforms. Background and ribbon shift from cool blue/cyan at the top to vivid red/orange at the bottom, [lighting_change:soft_to_vivid], [morph] enhances the transformation, [motion_blur] intensifies with speed, [dissolve] blends morphs, no human figures.\"\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"An abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][dissolve][motion_blur][surreal][cool_to_warm_gradient][no human figures]\\n\\nA single abstract, morphing geometric shape (such as a smooth, flowing ribbon) is centered against an undefined, subtly gradient background. The camera glides directly downward alongside the shape throughout the entire 5 seconds. The shape continuously morphs as it descends, symbolizing transformation. At the top, colors are exclusively cool blues and cyans with soft, diffuse lighting. As the descent progresses, both the shape and background gradually shift toward intense warm reds and oranges, with lighting increasing in saturation and contrast. Dynamic motion blur trails accentuate the movement, intensifying as the downward motion speeds up in the latter half. The animation ends as the shape reaches maximum vividness and warmth at the bottom.\\n\\nA single morphing geometric ribbon floats centered on a soft gradient background, [dolly:down] alongside the ribbon as it transforms and descends. Gradually shift colors from cool blues and cyans to vivid reds and oranges, [lighting_change] increases saturation and contrast, [morph] enhances ribbon transformation, dynamic motion blur trails intensify as speed increases toward the bottom.\\n\\nAs the ribbon and invisible camera move downward, both ribbon and background colors transition gradually: cool blue-cyan at the top to vivid red-orange at the bottom. Lighting grows noticeably more saturated and higher in contrast by the final second. A subtle motion blur trail appears behind the ribbon, low at first, increasing in intensity as downward speed increases, peaking during the final second. Video ends with the ribbon and background at maximum color warmth, saturation, and contrast.\",\n  \"runwayml_prompt\": \"A surreal, morphing geometric ribbon floats centered over a smooth gradient background. [dolly:down] follows the ribbon as it descends and transforms. Background and ribbon shift from cool blue/cyan at the top to vivid red/orange at the bottom, [lighting_change:soft_to_vivid], [morph] enhances the transformation, [motion_blur] intensifies with speed, [dissolve] blends morphs, no human figures.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: abstract gradient, subject: morphing geometric ribbon (centered), action: continuous downward descent, camera: dolly down tracking subject, color: top cool blues/cyans, bottom vivid reds/oranges, lighting: soft diffuse shifting to high saturation/contrast, morph: persistent transformation, effect: dynamic motion blur increases with speed, transition: dissolve blends morphs, style: surreal, no humans, duration: 5s\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"An abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][dissolve][motion_blur][surreal][cool_to_warm_gradient][no human figures]\\n\\nA single abstract, morphing geometric shape (such as a smooth, flowing ribbon) is centered against an undefined, subtly gradient background. The camera glides directly downward alongside the shape throughout the entire 5 seconds. The shape continuously morphs as it descends, symbolizing transformation. At the top, colors are exclusively cool blues and cyans with soft, diffuse lighting. As the descent progresses, both the shape and background gradually shift toward intense warm reds and oranges, with lighting increasing in saturation and contrast. Dynamic motion blur trails accentuate the movement, intensifying as the downward motion speeds up in the latter half. The animation ends as the shape reaches maximum vividness and warmth at the bottom.\\n\\nA single morphing geometric ribbon floats centered on a soft gradient background, [dolly:down] alongside the ribbon as it transforms and descends. Gradually shift colors from cool blues and cyans to vivid reds and oranges, [lighting_change] increases saturation and contrast, [morph] enhances ribbon transformation, dynamic motion blur trails intensify as speed increases toward the bottom.\\n\\nAs the ribbon and invisible camera move downward, both ribbon and background colors transition gradually: cool blue-cyan at the top to vivid red-orange at the bottom. Lighting grows noticeably more saturated and higher in contrast by the final second. A subtle motion blur trail appears behind the ribbon, low at first, increasing in intensity as downward speed increases, peaking during the final second. Video ends with the ribbon and background at maximum color warmth, saturation, and contrast.\",\n  \"optimized_video_prompt\": \"scene: abstract gradient, subject: morphing geometric ribbon (centered), action: continuous downward descent, camera: dolly down tracking subject, color: top cool blues/cyans, bottom vivid reds/oranges, lighting: soft diffuse shifting to high saturation/contrast, morph: persistent transformation, effect: dynamic motion blur increases with speed, transition: dissolve blends morphs, style: surreal, no humans, duration: 5s\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"a surreal, abstract geometric ribbon morphs continuously as it descends, [dolly:down] tracking downward movement, [morph] intensifies ribbon transformation, background shifts from cool blues and cyans to vivid reds and oranges, [lighting_change:soft_to_vivid], dynamic motion blur trails increase with speed, [dissolve] blends transitions, no humans\"\n}", "cost": 0.0}}}], "total_cost": 0.0}