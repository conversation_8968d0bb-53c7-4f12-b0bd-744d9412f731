"""
Utility Functions for Window Tiler

This module provides essential utility functions for:
1. Shell window access and special folder handling
2. Process detection and information retrieval
3. Window type detection
"""

import os
import re
import ctypes
import urllib.parse

import win32com.client
import win32com.shell.shellcon as shellcon
import win32gui
import win32api
import win32process
import win32con

from window_tiler.core.types import WindowType


def get_shell_windows():
    """
    Get shell window instances and special folder mappings.
    
    Returns:
        tuple: (shell_window_mapping, special_folders_mapping) where:
            - shell_window_mapping: Dict mapping window handles to shell instances
            - special_folders_mapping: Dict mapping special folder names to paths
    """
    # Create Shell.Application instance
    shell = win32com.client.Dispatch('Shell.Application')
    shell_windows = shell.Windows()
    
    # Map window handles to shell instances
    shell_mapping = {win.HWND: win for win in shell_windows}

    # Get special folders
    csidl_constants = [
        getattr(shellcon, name) for name in dir(shellcon) 
        if name.startswith("CSIDL_")
    ]
    
    # Create namespace objects for each constant
    namespaces = [shell.Namespace(constant) for constant in csidl_constants]
    
    # Filter valid namespaces and extract info
    valid_namespaces = [ns for ns in namespaces if hasattr(ns, 'Application')]
    special_folders = [[ns.Self.Name, ns.Self.Path] for ns in valid_namespaces]
    special_folders_mapping = {item[0]: item[1] for item in special_folders}

    return shell_mapping, special_folders_mapping


def get_process_info(hwnd):
    """
    Get process information for a window.
    
    Args:
        hwnd: Window handle
        
    Returns:
        dict: Process information or None if not available
    """
    try:
        # Get process ID
        _, process_id = win32process.GetWindowThreadProcessId(hwnd)
        
        # Open process handle
        process_access = win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ
        process_handle = ctypes.windll.kernel32.OpenProcess(process_access, False, process_id)
        
        if not process_handle:
            return None
            
        # Get process executable path
        try:
            exe_path = win32process.GetModuleFileNameEx(process_handle, 0)
            exe_name = os.path.basename(exe_path)
            
            return {
                'id': process_id,
                'handle': process_handle,
                'path': exe_path,
                'name': exe_name
            }
        except:
            ctypes.windll.kernel32.CloseHandle(process_handle)
            
    except Exception as e:
        pass
        
    return None


def get_explorer_info(hwnd, shell_mapping=None, special_folders=None):
    """
    Get information about Explorer windows.
    
    Args:
        hwnd: Window handle
        shell_mapping: Optional shell window mapping
        special_folders: Optional special folders mapping
        
    Returns:
        dict: Explorer window information or None if not applicable
    """
    # Check if it's an Explorer window
    if win32gui.GetClassName(hwnd) != 'CabinetWClass':
        return None
        
    # Get shell mappings if not provided
    if shell_mapping is None or special_folders is None:
        shell_mapping, special_folders = get_shell_windows()
    
    # Get window title
    title = win32gui.GetWindowText(hwnd)
    
    result = {
        'path': None,
        'type': None,
        'is_special': False
    }
    
    # Get shell instance
    shell_win = shell_mapping.get(hwnd)
    if not shell_win:
        return result
    
    try:
        # Handle special folders
        if title in special_folders:
            result['is_special'] = True
            result['path'] = special_folders[title]
            
            # Check if path is a GUID
            is_guid = bool(re.search(r'::{[\w-]*}', result['path']))
            result['is_guid'] = is_guid
            
        # Handle normal folders with LocationURL
        elif hasattr(shell_win, 'LocationURL') and shell_win.LocationURL:
            result['path'] = shell_win.LocationURL.replace('file:///', '')
            result['path'] = os.path.normpath(urllib.parse.unquote(result['path']))
            result['is_special'] = False
            
            # Get folder view info if available
            try:
                doc = shell_win.Document
                result['view_info'] = {
                    'icon_size': doc.IconSize,
                    'view_mode': doc.CurrentViewMode
                }
            except:
                pass
    
    except Exception as e:
        pass
        
    return result


def determine_window_type(hwnd):
    """
    Determine the type of a window based on its properties.
    
    Args:
        hwnd: Window handle
        
    Returns:
        WindowType: The determined window type
    """
    # Skip invalid windows
    if not hwnd or not win32gui.IsWindow(hwnd):
        return WindowType.UNKNOWN
    
    # Skip invisible windows
    if not win32gui.IsWindowVisible(hwnd):
        return WindowType.UNKNOWN
        
    # Get basic window properties
    window_class = win32gui.GetClassName(hwnd)
    window_title = win32gui.GetWindowText(hwnd)
    
    # Skip windows without titles
    if not window_title:
        return WindowType.UNKNOWN
    
    # Get process information
    process_info = get_process_info(hwnd)
    
    # Handle Explorer windows
    if window_class == 'CabinetWClass':
        explorer_info = get_explorer_info(hwnd)
        if explorer_info and explorer_info.get('is_special'):
            return WindowType.EXPLORER_SPECIAL
        else:
            return WindowType.EXPLORER_NORMAL
    
    # Handle windows based on process name
    if process_info and 'name' in process_info:
        process_name = process_info['name'].lower()
        
        # Browsers
        if 'chrome.exe' in process_name:
            return WindowType.BROWSER_CHROME
        elif 'msedge.exe' in process_name:
            return WindowType.BROWSER_EDGE
        elif 'firefox.exe' in process_name:
            return WindowType.BROWSER_FIREFOX
        elif any(name in process_name for name in ['iexplore.exe', 'safari.exe', 'opera.exe']):
            return WindowType.BROWSER
            
        # Terminals
        if any(term in process_name for term in ['cmd.exe', 'powershell.exe', 'windowsterminal.exe']):
            return WindowType.TERMINAL
            
        # Editors
        if any(editor in process_name for name in ['notepad.exe', 'code.exe', 'sublime_text.exe']):
            return WindowType.EDITOR
            
        # IDEs
        if any(ide in process_name for ide in ['devenv.exe', 'pycharm64.exe', 'idea64.exe']):
            return WindowType.IDE
    
    # No process info available
    if not process_info:
        return WindowType.UNLINKED
    
    # Default case
    return WindowType.NORMAL
