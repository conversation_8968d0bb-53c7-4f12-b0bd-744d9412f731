# Window Tiler

Simple, reliable window tiling for Windows.

## Quick Start

**Default usage:**
```python
from window_tiler import WindowTilerApp
WindowTilerApp().run()
```

**Custom configuration:**
```python
from window_tiler import Config, WindowTilerApp

config = Config()
config.types['brave.exe'] = 'Browser'
config.skip.add('myapp.exe')
WindowTilerApp(config).run()
```

## Configuration

```python
config = Config()

# Add applications
config.types.update({
    'brave.exe': 'Browser',
    'alacritty.exe': 'Terminal',
    'vlc.exe': 'Media'
})

# Skip processes
config.skip.update({'steam.exe', 'nvidia-share.exe'})
```

## Default Types

- **Browser:** chrome.exe, firefox.exe, msedge.exe
- **Terminal:** cmd.exe, powershell.exe, windowsterminal.exe
- **Editor:** notepad.exe, code.exe, sublime_text.exe
- **IDE:** devenv.exe, pycharm64.exe, idea64.exe
- **Explorer:** explorer.exe

See `example_custom_config.py` for more examples.
