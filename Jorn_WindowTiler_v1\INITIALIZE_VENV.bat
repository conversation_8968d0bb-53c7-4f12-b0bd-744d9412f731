@ECHO OFF
SETLOCAL ENABLEDELAYEDEXPANSION


:: =============================================================================
:: Main Script Logic
:: =============================================================================
CALL :InitBatchScript
CALL :SpecifyOptions
CALL :LocateProjectFolder
CALL :CheckAndInstallPython
CALL :DisplayProjectInfo
CALL :InitEverythingFile
CALL :InitPythonEnvironment
CALL :InstallPythonPackages
CALL :InitPyProjectCreator
CALL :ActivateVenv
EXIT /B 0


:: =============================================================================
:: Subroutines
:: =============================================================================

:: Init script variables.
:InitBatchScript
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    SET "CmdStartDirectory=%CD%"
    SET "CmdFullPath=%~dp0%~nx0"
    SET "CmdDirectory=%~dp0"
    SET "CmdFileName=%~nx0"
    SET "CmdBaseName=%~n0"
    SET "CmdExtension=%~x0"
GOTO :EOF

:: Specify script options.
:SpecifyOptions
    SET "VenvPyVersion=310"
    SET "VenvStorageLocation=%APPDATA%"
    SET "InstallRequirementsTxt=true"
    SET "InstallCustomPackages=true"
    SET "UpdateRequirementsTxt=true"
    SET "RunInitPyProjectCreator=false"
GOTO :EOF

:: Locate project folder.
:LocateProjectFolder
    SET "Dir_Identifier_1=.cache"
    SET "Dir_Identifier_2=.venv"
    :SearchProjectFolder
        IF EXIST "%CD%\%Dir_Identifier_1%" (GOTO FoundProject)
        IF EXIST "%CD%\%Dir_Identifier_2%" (GOTO FoundProject)
        SET "TempDir=%CD%" & CD ..
        IF "%CD%"=="%TempDir%" (
            CD /D "%CmdStartDirectory%"
            ECHO Could not find a project directory. You're on your own.
            PING 127.0.0.1 -n 10 > NUL & EXIT
        )
    GOTO SearchProjectFolder
    :FoundProject
        SET "ProjectPath=%CD%"
        FOR %%I IN ("%ProjectPath%") DO (SET "ProjectName=%%~nI")
        CD /D "%CmdStartDirectory%"
GOTO :EOF

:: Install Python if missing.
:CheckAndInstallPython
    SET "VenvPyInstallers=%ProjectPath%\.venv\.py.installers"
    SET "Python_308_Executable=C:\Python308\python.exe"
    SET "Python_309_Executable=C:\Python309\python.exe"
    SET "Python_310_Executable=C:\Python310\python.exe"
    SET "Python_311_Executable=C:\Python311\python.exe"
    SET "Python_308_Installer=%VenvPyInstallers%\python-3.8.0-amd64.exe"
    SET "Python_309_Installer=%VenvPyInstallers%\python-3.9.0-amd64.exe"
    SET "Python_310_Installer=%VenvPyInstallers%\python-3.10.8-amd64.exe"
    SET "Python_311_Installer=%VenvPyInstallers%\python-3.11.3-amd64.exe"
    SET "LocalPyInstaller=!Python_%VenvPyVersion%_Installer!"
    SET "LocalPyExe=!Python_%VenvPyVersion%_Executable!"
    SET "LocalPyDir=!LocalPyExe:\python.exe=!"
    IF NOT EXIST "%LocalPyExe%" (
        ECHO Installing Python
        ECHO - To:   !LocalPyDir!\
        ECHO - From: .!LocalPyInstaller:%ProjectPath%=!
        ECHO.
        ECHO Add "%LocalPyExe%" to ^%%PATH^%%?
        SET /P AddPyToPath="> Enter 0 or 1 for PrependPath (default is 0): "
        ECHO.
        ECHO -----------------------------------------------------------------------
        IF NOT "!AddPyToPath!"=="1" SET "AddPyToPath=0"
        "%LocalPyInstaller%" /QUIET InstallAllUsers=1 PrependPath=!AddPyToPath! TargetDir="!LocalPyDir!"
    )
GOTO :EOF

:: Display project info.
:DisplayProjectInfo
    ECHO -----------------------------------------------------------------------
    ECHO You're working on the project: %ProjectName%
    ECHO.
    ECHO - Project Directory: %ProjectPath%
    ECHO - Current Directory: .!CD:%ProjectPath%=!
    ECHO.
    ECHO -----------------------------------------------------------------------
GOTO :EOF

:: Initialize everything file.
:InitEverythingFile
    SET "EverythingBatPath=%ProjectPath%\.venv\.py.utils\_OPEN_PRJ_IN_EVERYTHING.BAT"
    :: ECHO "%%PROGRAMFILES%%\Everything 1.5a\Everything64.exe" -basename "%EverythingBatPath%"
GOTO :EOF

:: Run project init script.
:InitPyProjectCreator
    IF "%RunInitPyProjectCreator%" == "true" (
        SET "PyScriptName=_PyProjectCreator.py"
        SET "PyScriptPath=%ProjectPath%\.venv\.py.utils\!PyScriptName!"
        IF EXIST "!PyScriptPath!" (
            ECHO ^(venv^) Executing Script
            ECHO - Script Path: ^\.venv^\.py.utils^\!PyScriptName!
            ECHO.
            SET "VenvActivateCmd="%VenvActivate%" & CD /D %ProjectPath%"
            CMD /K "!VenvActivateCmd! & python "!PyScriptPath!" "%ProjectPath%" "%VenvDirPath%""
        )
    )
GOTO :EOF

:: # =============================================================================

:: Create a new venv or initialize an existing one.
:InitPythonEnvironment
    SET "VenvDirName=%ProjectName%__Python%VenvPyVersion%"
    SET "VenvDirPath=%VenvStorageLocation%\_py_venvs\%VenvDirName%\venv"
    SET "VenvPyExe=%VenvDirPath%\Scripts\python.exe"
    SET "VenvPipExe=%VenvDirPath%\Scripts\pip.exe"
    SET "VenvActivate=%VenvDirPath%\Scripts\activate"
    IF NOT EXIST "%VenvDirPath%\Scripts\python.exe" (
        ECHO ^(venv^) Creating New Virtual Environment
        ECHO - Path: %VenvDirPath%
        FOR /F "tokens=*" %%F IN ('"%LocalPyExe%" -V') DO ECHO - Version: %%F
        ECHO.
        IF NOT EXIST "%VenvDirPath%" MKDIR "%VenvDirPath%"
        "%LocalPyExe%" -m venv "%VenvDirPath%"
    ) ELSE (
        ECHO ^(venv^) Initializing Virtual Environment
        ECHO - Directory: %VenvDirPath%
        ECHO.
    )
    IF NOT EXIST "%ProjectPath%\.cache" MKDIR "%ProjectPath%\.cache"
    ECHO %ProjectName%>"%ProjectPath%\.cache\.PROJECT-NAME.%COMPUTERNAME%"
    ECHO %ProjectPath%>"%ProjectPath%\.cache\.PROJECT-PATH.%COMPUTERNAME%"
    ECHO %VenvDirPath%>"%ProjectPath%\.cache\.venv-PATH.%COMPUTERNAME%"
    ECHO %VenvPyVersion%>"%ProjectPath%\.cache\.PYTHON-VERSION.%COMPUTERNAME%"
GOTO :EOF

:: Install Python packages.
:InstallPythonPackages
    ECHO ^(venv^) Handling Python Dependencies / Packages
    ECHO.
    SET "VenvRequirementsTxt=%ProjectPath%\requirements.txt"
    IF "%InstallRequirementsTxt%" == "true" (
        IF EXIST "%VenvRequirementsTxt%" (
            "%VenvPipExe%" install --upgrade -r "%VenvRequirementsTxt%" > NUL
        )
    )
    IF "%InstallCustomPackages%" == "true" (
        "%VenvPyExe%" -m pip install --upgrade pip
        "%VenvPyExe%" -m pip install --upgrade requests
        "%VenvPyExe%" -m pip install --upgrade rich
        "%VenvPyExe%" -m pip install --upgrade isort
        "%VenvPyExe%" -m pip install --upgrade pyglet
        "%VenvPyExe%" -m pip install --upgrade python-dateutil
    )
    IF "%UpdateRequirementsTxt%" == "true" (
        "%VenvPipExe%" freeze > "%VenvRequirementsTxt%"
    )

:: Run project init script.
:InitPyProjectCreator
    IF "%RunInitPyProjectCreator%" == "true" (
        SET "PyScriptName=_PyProjectCreator.py"
        SET "PyScriptPath=%ProjectPath%\.venv\.py.utils\!PyScriptName!"
        IF EXIST "!PyScriptPath!" (
            ECHO ^(venv^) Executing Script
            ECHO - Script Path: ^\.venv^\.py.utils^\!PyScriptName!
            ECHO.
            SET "VenvActivateCmd="%VenvActivate%" & CD /D %ProjectPath%"
            CMD /K "!VenvActivateCmd! & python "!PyScriptPath!" "%ProjectPath%" "%VenvDirPath%""
        )
    )
GOTO :EOF

:: Activate utils.
:ActivateVenv
    ECHO ^(venv^) Activating Virtual Environment
    ECHO - Directory: %VenvDirPath%
    ECHO.
    SET "VenvActivateCmd="%VenvActivate%" & CD /D %CD% & TITLE ^(venv^) %CD%"
    CMD /K "%VenvActivateCmd%"
GOTO :EOF