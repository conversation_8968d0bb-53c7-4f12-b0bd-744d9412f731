# Import modules
import os
import sys
import re
import time
import ctypes
import urllib.parse
from enum import Enum
# Import pywin32 modules
import win32api
import win32com.client
import win32com.shell.shellcon as shellcon
import win32con
import win32gui
import win32process
import ctypes
import pythoncom

# Import ppretty (for displaying the structure of objects/classes)
from ppretty import ppretty
# Create an instance of the command
# clsid = "f4e8bc67-9616-4a28-a332-cf27a5ca6736"
# command = pythoncom.CoCreateInstance(clsid, None, pythoncom.CLSCTX_INPROC_SERVER, pythoncom.IID_IUnknown)

# Execute the command

def register_shell_hook_window(hwnd):
    ctypes.windll.user32.RegisterShellHookWindow(hwnd)

def register_window_message(message):
    return ctypes.windll.user32.RegisterWindowMessageW(message)

def get_foreground_window():
    return ctypes.windll.user32.GetForegroundWindow()

def get_class_name(hwnd):
    class_name = ctypes.create_unicode_buffer(256)
    ctypes.windll.user32.GetClassNameW(hwnd, class_name, 256)
    return class_name.value

def set_column_width(hwnd, column_index, width):
    ctypes.windll.user32.SendMessageW(hwnd, 0x101D, column_index, width)

def shell_message(wparam, lparam):
    if wparam == 6:
        hwnd = lparam
        class_name = get_class_name(hwnd)
        if class_name == "CabinetWClass":
            # print(dir(ctypes.windll.user32))
            ctypes.windll.user32.SetFocus(hwnd)
            ctypes.windll.user32.keybd_event(0x11, 0, 0, 0) # Control down
            ctypes.windll.user32.keybd_event(0x6D, 0, 0, 0) # NumpadAdd
            ctypes.windll.user32.keybd_event(0x11, 0, 0x0002, 0) # Control up

# def main():
#     hwnd = get_foreground_window()
#     register_shell_hook_window(hwnd)
#     msg_num = register_window_message("SHELLHOOK")
#     shell_message_c = ctypes.WINFUNCTYPE(None, ctypes.c_int, ctypes.c_int)(shell_message)
#     ctypes.windll.user32.SetWindowsHookExW(14, shell_message_c, None, 0)


# if __name__ == '__main__':
#     main()

"""
IMPORT CONSTANTS:
Constants are not complete memory addresses, they are constant offset values
that are used in conjunction with functions like win32gui.GetWindowLong to
access specific information stored in memory, by providing the offset at which
the information is stored."
"""
from win32con import CW_USEDEFAULT
from win32con import IDI_APPLICATION
from win32con import IMAGE_ICON
from win32con import LR_DEFAULTSIZE
from win32con import LR_LOADFROMFILE
from win32con import WM_DESTROY
from win32con import WM_USER
from win32con import WS_OVERLAPPED
from win32con import WS_BORDER
from win32con import GWL_STYLE # Window style
from win32con import GWL_EXSTYLE # Extended window style
import pythoncom

CLSID_ExplorerSizeAllColumnsCommand = "{f4e8bc67-9616-4a28-a332-cf27a5ca6736}"
import pythoncom

# guid = pythoncom.CreateGuid()
# print((guid))
# Get the CLSID of the command
# clsid = guid


import inspect
import commctrl
import win32com.client
import pythoncom
import win32com.shell.shellcon as shellcon
# shell = win32com.client.Dispatch("Shell.Application")
# folder = shell.NameSpace("C:\\Windows\\System32\\cmd.exe")
# print(folder)
# folder.InvokeVerbEx("{f4e8bc67-9616-4a28-a332-cf27a5ca6736}")
# time.sleep(9999)
# print(dir(pythoncom.IID_IDispatch))
import pythoncom
import win32com.client
# guid = pythoncom.CreateGuid()
# window_object = pythoncom.CoCreateInstance(CLSID_ExplorerSizeAllColumnsCommand, None, pythoncom.CLSCTX_LOCAL_SERVER, pythoncom.IID_IDispatch)
# guid = "{f4e8bc67-9616-4a28-a332-cf27a5ca6736}"
# window = pythoncom.CoCreateInstance(guid, None, pythoncom.CLSCTX_SERVER, pythoncom.IID_IDispatch)
# window = win32com.client.Dispatch(window)

# # Execute the command
# window.InvokeVerbEx("Size all columns to fit")

# print(dir(guid))
# def get_window_style(hwnd):
#     # returms memory addresses
#     # When using GetWindowLong to retrieve window styles, the data returned
#     # are a DWORD values (bit flags), which must be used in conjunction with
#     # bitwise operators in order to determine the names of the window style
#     # constants  by name require us to  of theseTo enable access to the window styles by name of the constant.
#     # This extracts the window style constants in order to access the window styles by the name of the constant.

#     # With this, you can access the window styles by the name of the constant.
#     # - Use 'win32con' to list the style constants and retrieve their names.
#     # - Use 'win32con' to retrieve the corresponding value of each constant.
#     GWL_STYLE_NAMES = [const for const in dir(win32con) if const.startswith("WS_")]
#     GWL_STYLE_VALUES = [getattr(win32con,const) for const in GWL_STYLE_NAMES]



def get_window_style(hwnd):
    # create two lists, the constants and its corresponding values.
    # style_names: strings
    # style_values: hex verdier / int (0x00800000L)

    # getLong: dette er window styles DWORD (list flags)
    # window style is a DWORD value that contains bit flags that
# specify the appearance and behavior of the window. These flags
# can be manipulated using bitwise operators.

    # one with style constants, and one with the values.
    #
     # (which
    # is used in conjunction with

    # When using GetWindowLong to retrieve window styles it returns DWORD values
    # that represents bit flags (used in conjunction with bitwise operators).
    # In order to retrieve the names of the window style constants  by name require us to  of theseTo enable access to the window styles by name of the constant.
    # This extracts the window style constants in order to access the window styles by the name of the constant.

    # With this, you can access the window styles by the name of the constant.
    # - Use 'win32con' to list the style constants and retrieve their names.
    # - Use 'win32con' to retrieve the corresponding value of each constant.
    GWL_STYLE_NAMES = [const for const in dir(win32con) if const.startswith("WS_")]
    GWL_STYLE_VALUES = [getattr(win32con,const) for const in GWL_STYLE_NAMES]


    # Retrieves the DWORD values of the window styles (GWL_STYLE & GWL_EXSTYLE).
    window_style_dword = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
    window_ex_style_dword = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)

    window_styles = []
    window_extended_styles = []

    for const_index in range(len(GWL_STYLE_NAMES)):
        # Bitwise operator to check if current style is match
        if GWL_STYLE_VALUES[const_index] & window_style_dword:
            window_styles.append(GWL_STYLE_NAMES[const_index])
        # Bitwise operator to check if current extended style is match
        if GWL_STYLE_VALUES[const_index] & window_ex_style_dword:
            window_extended_styles.append(GWL_STYLE_NAMES[const_index])

    return window_styles, window_extended_styles

    print("--------------------")
    print(hwnd)
    print(window_styles)
    print(window_extended_styles)
            # windows_by_style[style_name].append(hwnd)
    # return windows_by_style


# style = 885981184

# for name, value in inspect.getmembers(win32con):
#     # if name.startswith("WS_") and value == style:
#     print('%s:%s' % (name, value))
def get_window_data(hwnd):
    # dersom ett vindu ikke har attributter som indikerer at det kan endre
    # posisjon eller størrelse så skal denne returnere false, fordi siden
    # dette er et layout-tool så kan vi ikke gjøre noe med de vinduene.
    # kan kanskje sjekke dette med style.
    w_enabled   = (win32gui.IsWindowEnabled(hwnd))
    w_visible   = (win32gui.IsWindowVisible(hwnd))
    w_title     = (win32gui.GetWindowText(hwnd))
    w_class     = (win32gui.GetClassName(hwnd))

    w_placement = (win32gui.GetWindowPlacement(hwnd))
    w_state     = (w_placement[1]) # Min/Max/Normal/TOPMOST

    w_rect      = (win32gui.GetWindowRect(hwnd))
    w_position  = (w_rect[0], w_rect[1])
    w_size      = (w_rect[2] - w_rect[0], w_rect[3] - w_rect[1])

    w_process_id  = None
    w_process_exe = None

    w_monitor_index = None
    w_monitor_rect  = None

    # PROCESS
    thread_id, process_id = win32process.GetWindowThreadProcessId(hwnd)
    process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
    process_handle = ctypes.windll.kernel32.OpenProcess(process_query, False, process_id)
    if process_handle:
        w_process_id = process_id
        w_process_exe = win32process.GetModuleFileNameEx(process_handle, 0)

    # MONITOR
    monitor_handle = win32api.MonitorFromWindow(hwnd, win32con.MONITOR_DEFAULTTONEAREST)
    monitor_info = win32api.GetMonitorInfo(monitor_handle)
    w_monitor_index = int("".join(filter(str.isdigit, monitor_info["Device"])))
    w_monitor_rect = (monitor_info["Monitor"])
    print('title: %s | class: %s | monitor: %s' % (w_title, w_class, w_monitor_index))
    print(win32gui.GetWindowPos(hwnd))

    # Retrieves the window styles.
    # https://learn.microsoft.com/en-us/windows/win32/winmsg/window-styles
    # print(win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE))

    # Retrieves the extended window styles.
    # https://learn.microsoft.com/en-us/windows/win32/winmsg/extended-window-styles
    # print(win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE))



# FILE-EXPLORER: WINDOWS
shell_object_instance = win32com.client.Dispatch('Shell.Application')
shell_window_instances = shell_object_instance.Windows()
shell_window_instances = win32com.client.Dispatch('{9BA05972-F6A8-11CF-A442-00A0C90A8F39}')

# FILE-EXPLORER: FOLDERS (IShellFolder)
shell_folder_instances = [shell.Document for shell in shell_window_instances]

shell_windows_data = []

for window in shell_window_instances:
    w_hwnd      = (window.HWND)
    w_enabled   = (win32gui.IsWindowEnabled(window.HWND))
    w_visible   = (win32gui.IsWindowVisible(window.HWND))
    w_title     = (win32gui.GetWindowText(window.HWND))
    w_class     = (win32gui.GetClassName(window.HWND))
    w_rect      = (win32gui.GetWindowRect(window.HWND))
    w_placement = (win32gui.GetWindowPlacement(window.HWND))

    w_state     = (w_placement[1]) # Min/Max/Normal/TOPMOST
    w_position  = (w_rect[0], w_rect[1])
    w_size      = (w_rect[2] - w_rect[0], w_rect[3] - w_rect[1])

    w_process_id  = None
    w_process_exe = None

    w_monitor_index = None
    w_monitor_rect  = None

    # PROCESS
    thread_id, process_id = win32process.GetWindowThreadProcessId(window.HWND)
    process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
    process_handle = ctypes.windll.kernel32.OpenProcess(process_query, False, process_id)
    # if process_handle:
    w_process_id = process_id
    w_process_exe = win32process.GetModuleFileNameEx(process_handle, 0)

    # MONITOR
    monitor_handle = win32api.MonitorFromWindow(window.HWND, win32con.MONITOR_DEFAULTTONEAREST)
    monitor_info = win32api.GetMonitorInfo(monitor_handle)
    w_monitor_index = int("".join(filter(str.isdigit, monitor_info["Device"])))
    w_monitor_rect = (monitor_info["Monitor"])

    if w_title == "SaveWindowLayout":
        window_object_structure = ppretty(
            # Object to represent
            window,
            # (type:str)  | Indentation level of the output.
            indent='  ',
            # (type:int)  | Maximum width of the output string.
            width=100,
            # (type:int)  | Maximum depth of introspecion.
            depth=5,
            # (type:int)  | Maximum length of sequences (lists, tuples, etc.).
            seq_length=500,
            # (type:bool) | Show or hide "protected" attributes/members (i.e. those that start with "_").
            show_protected=True,
            # (type:bool) | Show or hide "private" attributes/members (i.e. those that start with "__").
            show_private=False,
            # (type:bool) | Show or hide static attributes/members.
            show_static=True,
            # (type:bool) | Show or hide properties (i.e. those defined with the @property decorator).
            show_properties=True,
            # (type:bool) | Show or hide the memory address of the object.
            show_address=False,
            # (type:int)  | Maximum string length.
            str_length=50
        )

        # Write the output to a text file
        script_name = os.path.splitext(os.path.basename(__file__))[0]
        output_name = (script_name + '_output_result_WINDOW.py')
        with open(output_name, 'w') as file:
            file.write(window_object_structure)

        win32gui.SetForegroundWindow(w_hwnd)

        # # Create an instance of the command
        # folder_item = shell.Namespace(os.path.normpath(window.LocationURL))
        # ilink = pythoncom.CoCreateInstance(CLSID_ExplorerSizeAllColumnsCommand, None,
        #                                    pythoncom.CLSCTX_INPROC_SERVER,
        #                                    shell.IID_IShellLink)
        # command = pythoncom.CoCreateInstance(CLSID_ExplorerSizeAllColumnsCommand, None, pythoncom.CLSCTX_INPROC_SERVER, pythoncom.IID_IDispatch)
        # print(dir(command.QueryInterface))
        # # Execute the command
        # command.QueryInterface(shell)



        # import pythoncom
        # # import win32com.shell.shell as shell
        # import win32com.client

        # # Get an instance of the Windows Explorer application
        # explorer = win32com.client.Dispatch("Shell.Explorer.2")

        # # Send the command to the active window
        # print(dir(shell))
        # # shell.Namespace
        # folder_item = shell.Namespace(os.path.normpath(window.LocationURL))
        # explorer.ExecWB(shell.CLSID_ExplorerSizeAllColumnsCommand, shell.OLECMDEXECOPT_DODEFAULT)

        # # Get the hwnd of the active window
        # active_window = ctypes.windll.user32.GetForegroundWindow()
        # shell_browser = win32com.client.Dispatch(win32com.client.gencache.EnsureDispatch("Shell.Explorer.2"))
        # print(type(shell_browser))
        # print(dir(shell_browser))

        # # Get the IShellBrowser interface from the active window
        # shell_browser = shell.IShellBrowser(pythoncom.ObjectFromLresult(
        #     active_window, pythoncom.IID_IShellBrowser, 0))

        # # Send the command to the active window
        # shell_browser.SendControlMsg(FCW_STATUS, SB_COMMAND,
        #                              shell.CLSID_ExplorerSizeAllColumnsCommand, 0, None)

        # ctypes.windll.user32.SendMessageW(w_hwnd, 273, 0x7FFF, 0x37466)

        # column_index = 1
        # width = 100
        # set_column_width(w_hwnd, column_index, width)
        # register_shell_hook_window(w_hwnd)
        # msg_num = register_window_message("SHELLHOOK")
        # shell_message_c = ctypes.WINFUNCTYPE(None, ctypes.c_int, ctypes.c_int)(shell_message)
        # ctypes.windll.user32.SetWindowsHookExW(14, shell_message_c, None, 0)
        # import commctrl
        # import ctypes

        # print(dir(commctrl))
        # user32 = ctypes.windll.user32
        # user32.ListView_SetColumnWidth.argtypes = (ctypes.c_void_p, ctypes.c_int, ctypes.c_int)
        # user32.ListView_SetColumnWidth(w_hwnd, column_index, width)




        # from pywintypes import IID
        # import pythoncom

        # shell_folder = win32com.client.Dispatch(pythoncom.CoCreateInstance(shell.CLSID_ShellWindows, None, pythoncom.CLSCTX_ALL, shell.IID_IShellWindows))

        # shell_folder.InvokeVerbEx("SizeAllColumns")

        # Specify the CLSID (Class Identifier) for COM-Object: ShellWindowsPermalink (alternatively 'Shell.Application' could be used)
        # shellWindows_CLSID = '{9BA05972-F6A8-11CF-A442-00A0C90A8F39}'
        # shell_folder = win32com.client.Dispatch(pythoncom.CoCreateInstance(shell, None, pythoncom.CLSCTX_ALL, window))
        # print(shell_folder)
        # print(w_title)
        # folder_item = shell_object_instance.Namespace(os.path.normpath(window.LocationURL))
        # # obj_shell = win32com.client.Dispatch("Shell.Application")
        # # folder_item = obj_shell.NameSpace(folder)
        # # Get the folder's PIDL
        # print(dir(folder_item.ParentFolder))
        # print(dir(folder_item))
        nspace = shell_object_instance.Namespace(window.LocationURL)
        # print(dir(nspace))
        # nparse = nspace.ParseName("SaveWindowLayout")
        print(dir(nspace))
        nspace.InvokeVerb("{f4e8bc67-9616-4a28-a332-cf27a5ca6736}")
        print(dir(window))
        win32com.client.dynamic.function("{f4e8bc67-9616-4a28-a332-cf27a5ca6736}")
        window.InvokeVerbEx("{f4e8bc67-9616-4a28-a332-cf27a5ca6736}")
        folder_item_pidl = folder_item.ParseName("").AbsolutePidl
        print(folder_item_pidl)
        print(window.Document)
# folder = shell.NameSpace("C:\\Windows\\System32\\cmd.exe")
# print(folder)
        # window.Document.Folder.InvokeVerbEx("{f4e8bc67-9616-4a28-a332-cf27a5ca6736}")
    # get_window_style(window.HWND)
    # time.sleep(9999)
    # Retrieves the window styles.
    # https://learn.microsoft.com/en-us/windows/win32/winmsg/window-styles
    # win_style = (win32gui.GetWindowLong(window.HWND, win32con.GWL_STYLE))
    # print(win_style)
    # print(bin(win_style))
    # STYLE_CONSTANTS = [getattr(win32con, name) for name in dir(win32con) if name.startswith("WS_") or name.startswith("DS_")]
    # STYLE_NAMES = [name for name in dir(win32con)]
    # csidl_namespaces = [shell_object_instance.Namespace(constant) for constant in CSIDL_CONSTANTS]
    # print(str(STYLE_CONSTANTS))
    # for x in range(len(STYLE_CONSTANTS)):
        # print('%s:%s' % (STYLE_CONSTANTS[x][0],STYLE_NAMES[x]))
    # print(WS_BORDER)
    # print(STYLE_CONSTANTS)
    # print([getattr(win32con, name) for name in dir(win32con)])
    # style_names = {x:x for x in dir(win32con) if (x.startswith("WS_") or x.startswith("DS_"))}
    # print(STYLE_CONSTANTS)
    # print((win_style))
    # if win32gui.GetWindowLong(window.HWND, win32con.GWL_STYLE) & win32con.WS_OVERLAPPEDWINDOW:
        # print("This window has the WS_OVERLAPPEDWINDOW style.")

    # if win_style & win32con.WS_TILED:
    # if win_style in STYLE_CONSTANTS:
        # print('title: %s | class: %s | monitor: %s' % (w_title, w_class, w_monitor_index))
    # print(STYLE_CONSTANTS)
    # print(style_names)
# time.sleep(9999)
# def get_windows_by_style():
#     windows_by_style = {}
#     style_names = {x:x for x in dir(win32con) if (x.startswith("WS_") or x.startswith("DS_"))}

    # def callback(hwnd, _):
    #     style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
    #     for x in style_names:
    #         if getattr(win32con,x) & style:
    #             style_name = style_names[x]
    #             if style_name not in windows_by_style:
    #                 windows_by_style[style_name] = []
    #             windows_by_style[style_name].append(hwnd)
    # win32gui.EnumWindows(callback, None)
    # return windows_by_style

    # Retrieves the extended window styles.
    # https://learn.microsoft.com/en-us/windows/win32/winmsg/extended-window-styles
    # print(win32gui.GetWindowLong(window.HWND, win32con.GWL_EXSTYLE))

# time.sleep(9999)
# TOP-LEVEL: [ALL] hwnd's
WINDOWS_ALL_HWNDS = []
current_hwnd = win32gui.GetTopWindow(None)
while current_hwnd:
    WINDOWS_ALL_HWNDS.append(current_hwnd)
    current_hwnd = win32gui.GetWindow(current_hwnd, win32con.GW_HWNDNEXT)

for HWND in WINDOWS_ALL_HWNDS:
    w_enabled   = (win32gui.IsWindowEnabled(HWND))
    w_visible   = (win32gui.IsWindowVisible(HWND))
    w_title     = (win32gui.GetWindowText(HWND))
    w_class     = (win32gui.GetClassName(HWND))
    w_rect      = (win32gui.GetWindowRect(HWND))
    w_placement = (win32gui.GetWindowPlacement(HWND))

    w_state     = (w_placement[1]) # Min/Max/Normal/TOPMOST
    w_position  = (w_rect[0], w_rect[1])
    w_size      = (w_rect[2] - w_rect[0], w_rect[3] - w_rect[1])

    w_process_id  = None
    w_process_exe = None

    w_monitor_index = None
    w_monitor_rect  = None

    # PROCESS
    thread_id, process_id = win32process.GetWindowThreadProcessId(HWND)
    process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
    process_handle = ctypes.windll.kernel32.OpenProcess(process_query, False, process_id)
    if process_handle:
        w_process_id = process_id
        w_process_exe = win32process.GetModuleFileNameEx(process_handle, 0)

    # MONITOR
    monitor_handle = win32api.MonitorFromWindow(HWND, win32con.MONITOR_DEFAULTTONEAREST)
    monitor_info = win32api.GetMonitorInfo(monitor_handle)
    w_monitor_index = int("".join(filter(str.isdigit, monitor_info["Device"])))
    w_monitor_rect = (monitor_info["Monitor"])
    # print('title: %s | class: %s | monitor: %s' % (w_title, w_class, w_monitor_index))

    # print(win32gui.GetWindowRect(HWND))

    # Retrieves the window styles.
    # https://learn.microsoft.com/en-us/windows/win32/winmsg/window-styles
    # print(win32gui.GetWindowLong(HWND, win32con.GWL_STYLE))

    # Retrieves the extended window styles.
    # https://learn.microsoft.com/en-us/windows/win32/winmsg/extended-window-styles
    # print(win32gui.GetWindowLong(HWND, win32con.GWL_EXSTYLE))

    get_window_style(HWND)

    # win_style=(win32gui.GetWindowLong(HWND, win32con.GWL_STYLE))
    # STYLE_CONSTANTS = [getattr(win32con, name) for name in dir(win32con) if name.startswith("WS_") or name.startswith("DS_")]
    # STYLE_NAMES = [name for name in dir(win32con)]
    # csidl_namespaces = [shell_object_instance.Namespace(constant) for constant in CSIDL_CONSTANTS]
    # print(str(STYLE_CONSTANTS))
    # for x in range(len(STYLE_CONSTANTS)):
        # print('%s:%s' % (STYLE_CONSTANTS[x][0],STYLE_NAMES[x]))
    # print(WS_BORDER)
    # print(STYLE_CONSTANTS)
    # print([getattr(win32con, name) for name in dir(win32con)])
    # style_names = {x:x for x in dir(win32con) if (x.startswith("WS_") or x.startswith("DS_"))}
    # print(STYLE_CONSTANTS)
    # print((win_style))
    # print(win_style)
    # if win_style in STYLE_CONSTANTS:
        # print('title: %s | class: %s | monitor: %s' % (w_title, w_class, w_monitor_index))
    # print(STYLE_CONSTANTS)


    # print(w_monitor_index)

# for x in shell_window_instances:
#     print(win32gui.GetWindowText(x.HWND))
#     print(win32gui.GetClassName(x.HWND))
time.sleep(9999)
# # WINDOW:[OBJECTS]
# window.focus.get() :: true / false
# window.focus.set() :: true / false
# window.state.get() :: min / max / (normal/original/unchanged)
# window.state.set() :: min / max / (normal/original/unchanged)
# window.visible.get() :: true / false
# window.visible.set() :: true / false
# window.position.set() :: (x, y)
# window.position.get() :: (x, y)
# window.size.set() :: (width, height)
# window.size.get() :: (width, height)
# window.monitor.get() :: 0/1/2/etc
# window.monitor.set() :: 0/1/2/etc
# window.topmost.set() :: true / false
# window.topmost.get() :: true / false
# 0: [BASE-WINDOW]:     # ANY-WINDOW
#  .. 1: "window_title" # Any   - Get
#  .. 1: "window_class" # Any   - Get
#  .. 1: "_placement"   # Any   - Get (Intermediate)
#  .. 1: "position"     # Any   - Get/Set
#  .. 1: "size"         # Any   - Get/Set
#  .. 1: "window_type"  # Any   - Get
#  .. 1: "process_id"   # Type1 - Get
#  .. 1: "process_exe"  # Type1 - Get

# 0: [EXPLORER-WINDOW(BASE)]:
#  .. 1: [FOLDER]
#  .. 1: "process_exe" # Get
#  .. 1: "window_type"
#  ...... 2: [FOLDER_VIEW]
#  ......... 3: "icon_size"   # Get/Set
#  ......... 3: "view_mode"   # Get/Set
#  ......... 3: "sort_column" # Get/Set
#  ......... 3: "group_by"    # Get/Set
#  ...... 2: [FOLDER_SELECTION]
#  ......... 3: "focused_file"   # Get/Set
#  ......... 3: "selected_files" # Get/Set


# Update the instance variables for folder view options.
folder_obj = hwnd_shell_instance.Document
self.folder_icon_size = folder_obj.IconSize
self.folder_view_mode = folder_obj.CurrentViewMode
self.folder_sort_column = folder_obj.SortColumns
self.folder_group_by = folder_obj.GroupBy
print(self.folder_group_by)
# Update the instance variables for files in folder.
self.folder_selected_files = [file.Name for file in folder_obj.SelectedItems()]
self.folder_all_files = [file.Name for file in folder_obj.Folder.Items()]
self.folder_focused_file = folder_obj.FocusedItem.Name if folder_obj.FocusedItem else None


# # In Windows, a 'special folder' refers to a folder represented by an interface
# # rather than a specific path (e.g. 'Desktop', 'Control Panel', etc). They are
# # identified by unique constants called 'CSIDL' (Constant Special Item ID List).
# #
# # The following steps are taken:
# # - Use 'shellcon' to list the 'CSIDL' identifiers and retrieve their constants.
# # - Use 'shell_object_instance' to create shell object namespaces for each constant.
# # - Filter out any invalid namespaces (without a shell Application property).
# # - Retrieve identifier ('Name') and path ('Path') from each namespace.
# # - Dictionary map {'Name':'Path'} to enable path to be reached from 'title'.
# CSIDL_CONSTANTS = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]
# csidl_namespaces = [shell_object_instance.Namespace(constant) for constant in CSIDL_CONSTANTS]
# valid_namespaces = [namespace for namespace in csidl_namespaces if hasattr(namespace, 'Application')]
# special_folders = [[namespace.Self.Name, namespace.Self.Path] for namespace in valid_namespaces]
# special_folders_mapping = {item[0]: item[1] for item in special_folders}


# --------------------------------------------------------------------------------------------------------
# --------------------------------------------------------------------------------------------------------
# --------------------------------------------------------------------------------------------------------
# NY TANKE, DET JEG SKAL GJØRE MED VINDUENE ER JO EGENTLIG BARE Å SETTE STØRRE/POSISJON OSV.
# INGEN AV OPERASJONENE HAR NOE Å GJØRE MED KNAPPER OSV, SÅ JEG TRENGER ANTAKELIGVIS
# IKKE Å SPESIFISERE AT DET ER POSITIONAL WINDOW / TOP-WINDOW ?

# TROR DE TYPENE JEG TRENGER ER:
# - APPLICATION WINDOW (EVENTUELT GENERIC WINDOW?)
#   - Optional: Må matche 'ExStyle' # [win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)]
#   - Optional: Må matche title
#   - Optional: Må matche class
#   - Optional: Må matche screen
#   - Optional: Må matche ...
#   - Default: Må ha prosess
#   - Default: Må ha placement (ikke  (0, 1, (-1, -1), (-1, -1), (0, 0, 0, 0)))
# - FILE EXPLORER WINDOW (FOLDER)
#   - Denne er sikker
# - SPECIAL FOLDER WINDOW
#   - Denne er sikker

# Alt annet er egentlig unødvendig, fordi jeg kan heller legge inn flags
# som lar deg spesifisere eksklusjoner fra APPLICATION WINDOW. Sånn at man
# kun skal lagre informasjon om vinduer med prosess f.eks.
# Må muligens tilrettelegge for Parent også, dersom jeg trenger å legge inn
# ting som f.eks. trykke på en knapp i et vindu.

# --------------------------------------------------------
# HOW CAN I CHECK IF A WINDOW HAS A WS_VSCROLL ?
# import win32gui
# import win32con

# def has_ws_vscroll(hwnd):
#     style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
#     if style & win32con.WS_VSCROLL:
#         return True
#     return False

# # Example usage:
# print(has_ws_vscroll(hwnd))
# --------------------------------------------------------


# def get_windows_by_style():
#     windows_by_style = {}
#     def callback(hwnd, _):
#         style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
#         if style not in windows_by_style:
#             windows_by_style[style] = []
#         windows_by_style[style].append(hwnd)
#     win32gui.EnumWindows(callback, None)
#     print(windows_by_style.items())
#     print(windows_by_style.keys())
#     return windows_by_style

# import win32gui
# import win32con

# def get_windows_by_style():
#     windows_by_style = {}
#     style_names = {x:x for x in dir(win32con) if (x.startswith("WS_") or x.startswith("DS_"))}

#     def callback(hwnd, _):
#         style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
#         for x in style_names:
#             if getattr(win32con,x) & style:
#                 style_name = style_names[x]
#                 if style_name not in windows_by_style:
#                     windows_by_style[style_name] = []
#                 windows_by_style[style_name].append(hwnd)
#     win32gui.EnumWindows(callback, None)
#     return windows_by_style



# print(get_windows_by_style())
# import time
# time.sleep(9999)
# # All top-level windows in Windows share the following attributes:

# # hwnd (handle to the window)
# # title (title of the window)
# # class_name (name of the window class)
# # Windows can be grouped by their attributes as follows:

# # Windows with the attribute "placement": These windows have a placement attribute that can be used to get and set the position and size of the window, as well as its state (minimized, maximized, etc.). Examples include standard application windows, dialog boxes, and message boxes.
# # Windows with the attribute "rect": These windows have a rect attribute that can be used to get and set the position and size of the window. Examples include standard application windows, dialog boxes, and message boxes.
# # Windows with the attribute "process_id": These windows are associated with a running process and have a process_id attribute that can be used to get the ID of the process that created the window. Examples include standard application windows and some system windows.
# # Windows with the attribute "shell_instance": These windows are associated with a folder in the file system and have a shell_instance attribute that can be used to get information about the folder, such as its path, icon size, view mode, etc. Examples include File Explorer windows.
# # Windows with the attribute "create_cmd": These windows are associated with a special folder and have a create_cmd attribute that can be used to get the command used to create the folder. Examples include special folders such as the Control Panel, Desktop, and My Computer.
# # It's worth noting that these are not mutually exclusive categories, and a single window may have multiple attributes.


# # Windows can be grouped by their attributes in several ways, depending on the specific attributes you are interested in. Here are a few examples:

# # By window style:

# # Child windows: windows that have a parent window and are contained within it. They have the WS_CHILD style.
# # Popup windows: windows that are created and owned by another window. They have the WS_POPUP style.
# # Overlapped windows: windows that have a title bar and a border. They have the WS_OVERLAPPED style.
# # Tool windows: windows that do not appear in the taskbar. They have the WS_TOOLWINDOW style.
# # Dialog boxes: windows that are used to display dialog boxes. They have the DS_MODALFRAME style.
# # By window class:

# # Buttons: windows that are used to initiate an action when clicked. They have the "Button" class.
# # Edit controls: windows that are used to input text. They have the "Edit" class.
# # List boxes: windows that display a list of items. They have the "ListBox" class.
# # Combo boxes: windows that combine a list box and an edit control. They have the "ComboBox" class.
# # Scroll bars: windows that are used to scroll through content. They have the "ScrollBar" class.
# # By window behavior:

# # Top-level windows: windows that are not contained within another window. They have no parent window.
# # Child windows: windows that are contained within another window. They have a parent window.
# # Modeless dialogs: dialogs that allow interaction with other windows while they are open.
# # Modal dialogs: dialogs that do not allow interaction with other windows while they are open.
# # Keep in mind that Windows windows can have multiple styles, classes and behaviors, so a window can belong to multiple group depending on the attributes used for grouping.

# TOP-WINDOW | CHILD-WINDOW

# BASE
# - TOP_WINDOW
# - - "window_title" # Any   - Get
# - - "window_class" # Any   - Get
# - - "_placement"   # Top   - Get (Intermediate)
# - - "position"     # Top   - Get/Set
# - - "size"         # Top   - Get/Set
# - - "window_type"  # Any   - Get
# - - "process_id"   # Type1 - Get
# - - "process_exe"  # Type1 - Get
# - CHILD_WINDOW

# 0: [BASE-WINDOW]:     # ANY-WINDOW
#  .. 1: "window_title" # Any   - Get
#  .. 1: "window_class" # Any   - Get
#  .. 1: "_placement"   # Any   - Get (Intermediate)
#  .. 1: "position"     # Any   - Get/Set
#  .. 1: "size"         # Any   - Get/Set
#  .. 1: "window_type"  # Any   - Get
#  .. 1: "process_id"   # Type1 - Get
#  .. 1: "process_exe"  # Type1 - Get

# 0: [EXPLORER-WINDOW(BASE)]:
#  .. 1: [FOLDER]
#  .. 1: "process_exe" # Get
#  .. 1: "window_type"
#  ...... 2: [FOLDER_VIEW]
#  ......... 3: "icon_size"   # Get/Set
#  ......... 3: "view_mode"   # Get/Set
#  ......... 3: "sort_column" # Get/Set
#  ......... 3: "group_by"    # Get/Set
#  ...... 2: [FOLDER_SELECTION]
#  ......... 3: "focused_file"   # Get/Set
#  ......... 3: "selected_files" # Get/Set
# #

# HVA OM JEG HELLER DELER OPP CLASSER BASERT PÅ HVA SLAGS TYPE DATA SOM HENTES/SETTES

# # WINDOW:"STRINGS"
# window.get_hwnd()
# window.get_title()
# window.get_class()
# window.get_process()
# window.get_process_id()
# window.get_type()
# window.get_create_cmd()

# # WINDOW:[OBJECTS]
# window.focus.get() :: true / false
# window.focus.set() :: true / false
# window.state.get() :: min / max / (normal/original/unchanged)
# window.state.set() :: min / max / (normal/original/unchanged)
# window.visible.get() :: true / false
# window.visible.set() :: true / false
# window.position.set() :: (x, y)
# window.position.get() :: (x, y)
# window.size.set() :: (width, height)
# window.size.get() :: (width, height)
# window.monitor.get() :: 0/1/2/etc
# window.monitor.set() :: 0/1/2/etc
# window.topmost.set() :: true / false
# window.topmost.get() :: true / false

# # WINDOW.FOLDER:[OBJECTS]
# window.folder.view_options.icon_size.get() :: 16/32/64/etc
# window.folder.view_options.icon_size.set() :: 16/32/64/etc
# window.folder.view_options.view_mode.get() :: PREDEFINED_DICT[?] :: 1/2/3/4/5/6/7/8
# window.folder.view_options.view_mode.set() :: PREDEFINED_DICT[?] :: 1/2/3/4/5/6/7/8
# window.folder.view_options.sort_column.get() :: [- / + | VALUE] :: PREDEFINED_DICT[?] :: (examples "prop:-System.Size;")
# window.folder.view_options.sort_column.set() :: [- / + | VALUE] :: PREDEFINED_DICT[?] :: (examples "prop:-System.Size;")
# window.folder.view_options.group_by.get() :: [- / + | VALUE] :: PREDEFINED_DICT[?] :: (examples "System.DateModified" | "System.Null")
# window.folder.view_options.group_by.set() :: [- / + | VALUE] :: PREDEFINED_DICT[?] :: (examples "System.DateModified" | "System.Null")

# # WINDOW.FILES:[OBJECTS]
# window.folder.files.selected_files.get() :: LIST ["filename1.txt", "*.png"] :: (legge inn mulighet for wildcards)
# window.folder.files.focused_file.get() :: "filename1.txt"

# WINDOW:"STRINGS"
# window.hwnd.get()
# window.title.get()
# window.class.get()
# window.process.get()
# window.process_id.get()
# window.type.get()
# window.create_cmd.get()

# WINDOW:[OBJECTS]
# window.focus.get() :: true / false
# window.focus.set() :: true / false
# window.state.get() :: min / max / (normal/original/unchanged)
# window.state.set() :: min / max / (normal/original/unchanged)
# window.visible.get() :: true / false
# window.visible.set() :: true / false
# window.position.set() :: (x, y)
# window.position.get() :: (x, y)
# window.size.set() :: (width, height)
# window.size.get() :: (width, height)
# window.monitor.get() :: 0/1/2/etc
# window.monitor.set() :: 0/1/2/etc
# window.topmost.set() :: true / false
# window.topmost.get() :: true / false

# WINDOW.FOLDER:[OBJECTS]
# window.folder.view_options.icon_size.get() :: 16/32/64/etc
# window.folder.view_options.icon_size.set() :: 16/32/64/etc
# window.folder.view_options.view_mode.get() :: PREDEFINED_DICT[?] :: 1/2/3/4/5/6/7/8
# window.folder.view_options.view_mode.set() :: PREDEFINED_DICT[?] :: 1/2/3/4/5/6/7/8
# window.folder.view_options.sort_column.get() :: [- / + | VALUE] :: PREDEFINED_DICT[?] :: (examples "prop:-System.Size;")
# window.folder.view_options.sort_column.set() :: [- / + | VALUE] :: PREDEFINED_DICT[?] :: (examples "prop:-System.Size;")
# window.folder.view_options.group_by.get() :: [- / + | VALUE] :: PREDEFINED_DICT[?] :: (examples "System.DateModified" | "System.Null")
# window.folder.view_options.group_by.set() :: [- / + | VALUE] :: PREDEFINED_DICT[?] :: (examples "System.DateModified" | "System.Null")

# WINDOW.FILES:[OBJECTS]
# window.folder.files.selected_files.get() :: LIST ["filename1.txt", "*.png"] :: (legge inn mulighet for wildcards)
# window.folder.files.selected_files.set() :: LIST ["filename1.txt", "*.png"] :: (legge inn mulighet for wildcards)
# window.folder.files.focused_file.get() :: "filename1.txt"
# window.folder.files.focused_file.set() :: "filename1.txt"
# window.folder.files.all_files.get() :: TRENGER IKKE Å EKSPONERE DENNE
# window.folder.files.all_files.set() :: TRENGER IKKE Å EKSPONERE DENNE



# [FOLDER]
# window.hwnd
# window.title

# window.visibility_state (get/set)
# window.placement (get/set)

# window.rect (get/set) DENNE KAN KANSKJE EKSKLUDERES PGA PLACEMENT

# window.title (get)
# window.class (get)
# window.controls_state (get/set) :: min/max/normal/topmost
# window.position :: hentes fra placement :: screen er også en del av position (screenspace?)
# window.size :: hentes fra placement
# window.process (get)
# window.process_id (get)
# window.type (get)
# window.create_cmd (get) :: kan settes fra tekstfil?

# window.folder.path

        # self.hwnd = hwnd
        # self._placement = win32gui.GetWindowPlacement(self.hwnd)
        # self._rect = win32gui.GetWindowRect(self.hwnd)
        # self.visibility_state = win32gui.IsWindowVisible(self.hwnd)
        # self.title = win32gui.GetWindowText(self.hwnd)
        # self.class = win32gui.GetClassName(self.hwnd)
        # self.controls_state = self._placement[1]
        # self.position = (self._rect[0], self._rect[1])
        # self.size = (self._rect[2] - self._rect[0], self._rect[3] - self._rect[1])

        # self.process = None
        # self.process_id = None

        # self.type = None
        # self.create_cmd = None

        # self.folder_icon_size = None
        # self.folder_view_mode = None
        # self.folder_sort_column = None
        # self.folder_group_by = None
        # self.folder_selected_files = None
        # self.folder_all_files = None
        # self.folder_focused_file = None


# BURDE SKILLE MELLOM FOLDER / WINDOW

# [FOLDER] : SHELL WINDOWS (RETRIEVES WINDOW DATA THROUGH COM OBJECT)
# - shell_explorer_window
# - special_folder_window
# [WINDOW] : GENERAL WINDOWS (RETRIEVES WINDOW DATA DIRECTLY FROM WINDOW HANDLE)
# process_window
# unlinked_window
# unknown_window

# STRATEGY PATTERN

    # In Windows, 'special folder' is a folder represented as an interface rather
    # than a path, such as 'Desktop', 'Control Panel', 'Recycle Bin', etc.
    # These are registered on the system as constants identified by 'CSIDL'.
# shell_folder | special_folder |
# PATTERN FOR Å FINNE VINDU OG/ELLER VINDUKONTROLLERE

# MÅ LEGGE INN RIBBON-MINIMIZE/MAXIMIZE

# MÅ LEGGE INN MULIGHET FOR RULLERING I STACKEN

# TODO:
# - Use os.path.expandvars() to expand environment variables like %appdata% or %programfiles%
#   in the path variable. os.path.expandvars("%USERPROFILE%")
# - Lage egen klasse for WindowSerializer ({window}>str / str>{window} | for save/load)
# - Remove non-printable characters from the title
#   self.hwnd_title = re.sub(r'[^\x00-\x7F]+','', self.hwnd_title)

# TROR WINDOW_STATE ER SAMME ATTRIBUTT SOM TOPMOST (ALWAYS ON TOP)
# TRENGER I SÅ FALL IKKE Å HEMTE DETTE SOM TO ATTRIBUTTER
# hwnd = win32gui.GetForegroundWindow()
# placement = win32gui.GetWindowPlacement(hwnd)
# if placement[1] == win32con.SW_SHOWMINIMIZED:
#     print("The window is minimized")
# elif placement[1] == win32con.SW_SHOWMAXIMIZED:
#     print("The window is maximized")
# elif placement[1] == win32con.SW_SHOWNORMAL:
#     print("The window is normal")
# elif placement[1] == win32con.HWND_TOPMOST:
#     print("The window is always on top")

"""
One weakness of the pywin32 library is that it's a wrapper around the --Win32 API--,
so it's only available on Windows and can only be used to interact with
Windows-specific features. Additionally, since it's a wrapper around the Win32 API,
it can be quite low-level and may require a lot of code to accomplish certain tasks.
Additionally, the library is relatively old and some of the functionality may be
deprecated and not recommended to use, or could have some bugs. Another weakness
is that the library requires some knowledge of Windows internals and the Win32 API,
so it might have a steeper learning curve for some developers.
"""




# Creates a Windows Shell COM object instance and retrieve 'special folder' paths.
def initialize_shell_windows_instance():

    # In Windows, 'Shell.Application' refers to a COM class that provides access
    # to the top-level object of the Windows Shell (shell32.dll), which includes
    # functionality related to special folders, file explorer windows, accessing
    # folder view settings, desktop, taskbar, etc.
    #
    # The following steps are taken:
    # - Create and return a ShellObject instance of the Shell.Application ProgID.
    # - Create a ShellWindows object (contains all open shell-window instances).
    # - Dictionary map {HWND:SHELL} to enable shell-instances to be reached by HWND.
    shell_object_instance = win32com.client.Dispatch('Shell.Application')
    shell_window_instances = shell_object_instance.Windows()
    shell_window_mapping = {shell.HWND: shell for shell in shell_window_instances}


    shell_object_instance = win32com.client.Dispatch('Shell.Application')
    shell_window_instances = shell_object_instance.Windows()


    # IShellFolder
    shell_folder_instances = [shell.Document for shell in shell_window_instances]



    # In Windows, a 'special folder' refers to a folder represented by an interface
    # rather than a specific path (e.g. 'Desktop', 'Control Panel', etc). They are
    # identified by unique constants called 'CSIDL' (Constant Special Item ID List).
    #
    # The following steps are taken:
    # - Use 'shellcon' to list the 'CSIDL' identifiers and retrieve their constants.
    # - Use 'shell_object_instance' to create shell object namespaces for each constant.
    # - Filter out any invalid namespaces (without a shell Application property).
    # - Retrieve identifier ('Name') and path ('Path') from each namespace.
    # - Dictionary map {'Name':'Path'} to enable path to be reached from 'title'.
    CSIDL_CONSTANTS = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]
    csidl_namespaces = [shell_object_instance.Namespace(constant) for constant in CSIDL_CONSTANTS]
    valid_namespaces = [namespace for namespace in csidl_namespaces if hasattr(namespace, 'Application')]
    special_folders = [[namespace.Self.Name, namespace.Self.Path] for namespace in valid_namespaces]
    special_folders_mapping = {item[0]: item[1] for item in special_folders}


    # shell_folder_instances

    CSIDL_CONSTANTS = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]


                    # style = (win32gui.GetWindowLong(window.hwnd, win32con.GWL_STYLE))
                    # style_names = {x:x for x in dir(win32con) if (x.startswith("WS_") or x.startswith("DS_"))}
                    # for x in style_names:
                    #     if getattr(win32con,x) & style:
                    #         # if style == getattr(win32con,x):
                    #         print(getattr(win32con,x))
                    #         print(style)
                    #         style_name = style_names[x]
                    #         print(style_name)
                            # print(style_names[x])
    # Return the result
    return shell_window_mapping, special_folders_mapping



# The 'WindowType' class represents classification of specific window types.
class WindowType(Enum):
    """
    Enumeration class to represent the different types of windows.
    - SPECIAL_FOLDER: explorer window with mapped path to CSIDL constant.
    - NORMAL_FOLDER: explorer window with a retrievable path.
    - UNSPECIFIED: window with a process other than explorer.
    - UNLINKED: window with title and class but no process.
    - UNKNOWN: any remaining windows not matched with a type.
    """
    SPECIAL_FOLDER = 1, 'SPECIAL_FOLDER'
    NORMAL_FOLDER = 2, 'NORMAL_FOLDER'
    UNSPECIFIED = 3, 'UNSPECIFIED'
    UNLINKED = 4, 'UNLINKED'
    UNKNOWN = 5, 'UNKNOWN'


# The 'Window' class represents a window object and holds its properties and methods.
class Window:
    def __init__(self, hwnd):
        # Initialize instance variables with general window data.
        self.hwnd = hwnd
        self._placement = win32gui.GetWindowPlacement(self.hwnd)
        self._rect = win32gui.GetWindowRect(self.hwnd)
        self.hwnd_visibility_state = win32gui.IsWindowVisible(self.hwnd)
        self.hwnd_title = win32gui.GetWindowText(self.hwnd)
        self.hwnd_class = win32gui.GetClassName(self.hwnd)
        self.hwnd_controls_state = self._placement[1]
        self.hwnd_position = (self._rect[0], self._rect[1])
        self.hwnd_size = (self._rect[2] - self._rect[0], self._rect[3] - self._rect[1])

        # Prepare instance variables for the window process.
        self.hwnd_process = None
        self.hwnd_process_id = None

        # Prepare instance variables for the window type and create-command.
        self.hwnd_type = None
        self.hwnd_create_cmd = None

        # Prepare instance variables for (explorer) folder view options.
        self.folder_icon_size = None
        self.folder_view_mode = None
        self.folder_sort_column = None
        self.folder_group_by = None
        # Prepare instance variable for file selection in folder.
        self.folder_selected_files = None
        self.folder_all_files = None
        self.folder_focused_file = None


    # Method for retrieving the window processes.
    def get_window_process(self):
        # Retrieve the process handle of the window
        hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(self.hwnd)
        hwnd_process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
        hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(hwnd_process_query, False, hwnd_process_id)

        # If a process handle was optained
        if hwnd_process_handle:
            # Update instance variables with the executable path and id
            self.hwnd_process = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
            self.hwnd_process_id = hwnd_process_id
            # Update 'type' with the identifier 'UNSPECIFIED'.
            self.hwnd_type = WindowType.UNSPECIFIED

        # Else if no process was retrieved, set 'type' to 'UNLINKED'.
        elif not self.hwnd_type:
            self.hwnd_type = WindowType.UNLINKED


    # Method for retrieving additional (type-specific) window data.
    def get_explorer_windows(self, shell_window_mapping, special_folders_mapping):
        """
        Parameters:
        - 'shell_window_mapping': Mapping of 'hwnd' to shell instance, used to
           obtain the path of the folder in case of 'NORMAL_FOLDER'.
        - 'special_folders_mapping': Mapping of 'title' to 'special folder' path, used to
           obtain the path of folder in case of 'SPECIAL_FOLDER'.

        This function updates the following instance variables:
        - 'process': The process associated with the window.
        - 'process_id': The process id associated with the window.
        - 'type': The type of the window ('SPECIAL_FOLDER', 'NORMAL_FOLDER', 'UNSPECIFIED', 'UNLINKED', 'UNKNOWN').
        - 'create_cmd': The command to open the window, if applicable.
        """
        # If this is a Windows File-Explorer Window (typically a folder/directory)
        if self.hwnd_class == 'CabinetWClass':
            # Retrieve the folder path through its shell instance
            hwnd_shell_instance = shell_window_mapping[self.hwnd]
            hwnd_shell_path = hwnd_shell_instance.LocationURL

            # If it's a 'SPECIAL_FOLDER' (explorer window without retrievable path):
            # - Update the instance variable 'type' with the identifier 'SPECIAL_FOLDER'.
            # - Check if the path refers to a GUID (global unique identification number).
            # - Transform the path into a cmd-command (using 'Shell:{GUID}' or 'File:/URI'),
            #   this is to make the path executable (in that it creates the actual window).
            # - Update the instance variable 'create_cmd' with the modified path-command.
            if self.hwnd_title in special_folders_mapping:
                self.hwnd_type = WindowType.SPECIAL_FOLDER
                folder_path = special_folders_mapping[self.hwnd_title]
                folder_path_guid_match = bool(re.search(r'::{[\w-]*}', folder_path))
                folder_path_is_guid = folder_path_guid_match if not os.path.exists(folder_path) else False
                command_prefix = 'Shell:' if folder_path_is_guid else 'File:/'
                create_command = os.path.normpath(urllib.parse.unquote(f'{command_prefix}{folder_path}'))
                self.hwnd_create_cmd = create_command

            # Else it's a 'NORMAL_FOLDER' (explorer window with a retrievable path):
            elif (hwnd_shell_path != ''):
                # Update the instance variable 'type' with the identifier 'NORMAL_FOLDER'.
                self.hwnd_type = WindowType.NORMAL_FOLDER
                # Update the instance variable 'create_cmd' with the path (URI).
                self.hwnd_create_cmd = os.path.normpath(urllib.parse.unquote(hwnd_shell_path))
                # Update the instance variables for folder view options.
                folder_obj = hwnd_shell_instance.Document
                self.folder_icon_size = folder_obj.IconSize
                self.folder_view_mode = folder_obj.CurrentViewMode
                self.folder_sort_column = folder_obj.SortColumns
                self.folder_group_by = folder_obj.GroupBy
                print(self.folder_group_by)
                # Update the instance variables for files in folder.
                self.folder_selected_files = [file.Name for file in folder_obj.SelectedItems()]
                self.folder_all_files = [file.Name for file in folder_obj.Folder.Items()]
                self.folder_focused_file = folder_obj.FocusedItem.Name if folder_obj.FocusedItem else None


        # If a title and class was found but no type has been retrieved (no associated window process):
        # - Set instance variable 'type' to 'UNLINKED'.
        if self.hwnd_title and self.hwnd_class and not self.hwnd_type:
            self.hwnd_type = WindowType.UNLINKED





class WindowManager:
    def __init__(self):
        # Initialize an empty list to store Window objects.
        self.all_windows = []

        # Initialize condition: Visible (dette blir en input).
        self.visible_windows_only = True

        # Initialize a Windows Shell COM object and retrieve 'special folder' paths.
        self.shell_instance_mapped, self.special_folders_mapped = initialize_shell_windows_instance()


        self.counter = 0

        # Instead of using win32gui.EnumWindows for retrieving all windows, we are using a
        # continous while loop
        # RETRIEVE AND RETURN LAYOUT DATA FOR CURRENTLY OPEN FILE-EXPLORER WINDOWS
        # def GET_WINS_ZORDER():
        #     # Initialize a list to store data for all (currently open) window handles in
        #     window_data_retrieved = []
        #     # Get the handle of the window that is at the top of the z-order (the window on top)
        #     current_window_handle = win32gui.GetTopWindow(None)
        #     # Use a while loop to ensure traversing through all windows (in the z-order)
        #     while current_window_handle:
        #         window_data_retrieved.append(current_window_handle)
        #         # Get the next window in the z-order
        #         current_window_handle = win32gui.GetWindow(current_window_handle, win32con.GW_HWNDNEXT)

        #     # Return the current layout window data (as list with dictionaries for each currently open file-explorer windows)
        #     return window_data_retrieved
        # a = GET_WINS_ZORDER()




# the win32gui.EnumWindows function simply enumerates all open windows and calls a callback function for each window
# it does not create any COM objects or interact with the windows in any way
# enumerate all top-level windows,
# win32gui.EnumWindows is a function that enumerates all open windows and calls a callback function for each window, passing the handle to the window (hwnd) as an argument.

# The win32gui.EnumWindows function is a function that iterates over a list of all open windows on the system and calls a specified callback function for each window.

# The win32gui.EnumWindows function takes a callback function as an argument and calls the callback function for each open window,
# passing the handle to the window (hwnd) as the first argument. The callback function can then use the hwnd value to retrieve additional
# information about the window, such as the window title, the window class, the window style, etc.

# win32gui.EnumWindows is a function in the win32gui module of the pywin32 library that allows you to enumerate all top-level windows on the screen.
# It takes a callback function and a data argument as arguments, and it calls the callback function for each window, passing the window handle (HWND)
# and the data argument as arguments. The callback function should return True to continue enumerating windows, or False to stop enumerating.
# EnumWindows(callback[, data])
# The callback argument is a function that will be called for each window. It should have the following signature:

# The hwnd argument is the window handle (HWND) of the current window. The data argument is the data argument that was passed to EnumWindows.
# The callback function should return True to continue enumerating windows, or False to stop enumerating.

    # def get_all_open_windows_enumerate(self):
    #     def window_enum_handler(hwnd, ctx):
    #         if win32gui.IsWindowVisible(hwnd):
    #             print ( hex( hwnd ), win32gui.GetWindowText( hwnd ) )

    #     win32gui.EnumWindows( window_enum_handler, None )
    def get_open_windows(self):
        # Reset the list of Window objects
        self.all_windows = []

        #
        # Instead of using
    #     # Retrieve all currently open windows in the Z order
    #     # Instead of using enumerating all windows through win32gui.EnumWindows,
    #     # a continous while loopto enumerate all windows, for retrieving all windows, we are using a
    #     # continous while loop
    #     # RETRIEVE AND RETURN LAYOUT DATA FOR CURRENTLY OPEN FILE-EXPLORER WINDOWS
    #     # Initialize a list to store data for all (currently open) window handles in
    #     window_data_retrieved = []

    #     # Get the handle of the window that is at the top of the z-order (the window on top)
    #     current_hwnd = win32gui.GetTopWindow(None)
    #     # Use a while loop to ensure traversing through all windows (in the z-order)
    #     while current_hwnd:
    #         window_data_retrieved.append(current_hwnd)
    #         # Get the next window in the z-order
    #         current_hwnd = win32gui.GetWindow(current_hwnd, win32con.GW_HWNDNEXT)

    #     # Return the current layout window data (as list with dictionaries for each currently open file-explorer windows)
    #     return window_data_retrieved
    #     a = GET_WINS_ZORDER()

    def wm_get_open_windows(self):
        # Reset the list of Window objects
        self.all_windows = []
        # Enumerate all windows and pass them to the wm_enumerate_windows_callback method
        win32gui.EnumWindows(self.wm_enumerate_windows_callback, None)

    def wm_enumerate_windows_callback(self, hwnd, _):
        # visibleOnly or AllWindows
        if (self.visible_windows_only and win32gui.IsWindowVisible(hwnd)) or (not self.visible_windows_only):
            # Create a Window object for the current window
            window = Window(hwnd)
            #
            window.get_window_process()
            # Get additional type-specific window data for the current window
            window.get_explorer_windows(self.shell_instance_mapped, self.special_folders_mapped)

            # Add the current window to the list of windows
            self.all_windows.append(window)





    def return_windows(self):
        # Reset the list of Window objects
        return self.all_windows






    def wm_save(self, filename):
        # Open the file at the specified filepath in write mode
        # with open(filename, "w", encoding="utf-8") as file:
        with open(filename, "w") as file:
            # Iterate through the list of retrieved windows
            for window in self.all_windows:
                print("[WindowType.SPECIAL_FOLDER, WindowType.NORMAL_FOLDER]")
                if window.hwnd_type in [WindowType.SPECIAL_FOLDER, WindowType.NORMAL_FOLDER]:
                    # print(win32gui.GetWindow(window.hwnd, GWL_EXSTYLE))
                    # print('%s: %s' % (window.hwnd, window.title))
                    # print(win32gui.GetWindowPlacement(window.hwnd))

                    style = (win32gui.GetWindowLong(window.hwnd, win32con.GWL_STYLE))
                    style_names = {x:x for x in dir(win32con) if (x.startswith("WS_") or x.startswith("DS_"))}
                    for x in style_names:
                        if getattr(win32con,x) & style:
                            # if style == getattr(win32con,x):
                            print(getattr(win32con,x))
                            print(style)
                            style_name = style_names[x]
                            print(style_name)
                            # print(style_names[x])

                    print('hwnd: %s' % str(window.hwnd))
                    print('hwnd_visibility_state: %s' % str(window.hwnd_visibility_state))
                    print('hwnd_title: %s' % str(window.hwnd_title))
                    print('hwnd_class: %s' % str(window.hwnd_class))
                    print('hwnd_controls_state: %s' % str(window.hwnd_controls_state))
                    print('hwnd_position: %s' % str(window.hwnd_position))
                    print('hwnd_size: %s' % str(window.hwnd_size))
                    print('hwnd_process: %s' % str(window.hwnd_process))
                    print('hwnd_process_id: %s' % str(window.hwnd_process_id))
                    print('hwnd_create_cmd: %s' % str(window.hwnd_create_cmd))
                    print('hwnd_type: %s' % str(window.hwnd_type))
                    # print('-')
                    print('hwnd_placement: %s' % str(window._placement))
                    print('hwnd_rect: %s' % str(window._rect))
                    #
                    print('folder_icon_size: %s' % str(window.folder_icon_size))
                    print('folder_view_mode: %s' % str(window.folder_view_mode))
                    print('folder_sort_column: %s' % str(window.folder_sort_column))
                    print('folder_group_by: %s' % str(window.folder_group_by))
                    print('folder_selected_files: %s' % str(window.folder_selected_files))
                    print('folder_all_files: %s' % str(window.folder_all_files))
                    print('folder_focused_file: %s' % str(window.folder_focused_file))
                    print('--------------------------------------\n')
                    # Save the current window to the file
                    # window.save(file)
                    # file.write(f"\n\n")
                    # file.write(f"{window.type}\n")
                    # file.write(f"{window.title}\n")
                    # file.write(f"{window.class_name}\n")
                    # file.write(f"{window.position[0]}, {window.position[1]}\n")
                    # file.write(f"{window.size[0]}, {window.size[1]}\n")
                    # if window.process:
                    #     file.write(f"{window.process}\n")
                    # if window.path:
                    #     file.write(f"{window.path}\n")

                # print('\n\n')
                # print("[WindowType.UNSPECIFIED, WindowType.UNLINKED, WindowType.UNKNOWN]:")
                # if window.hwnd_type in [WindowType.UNSPECIFIED, WindowType.UNLINKED, WindowType.UNKNOWN]:
                #     # print(win32gui.GetWindow(window.hwnd, GWL_EXSTYLE))
                #     # print('%s: %s' % (window.hwnd, window.title))
                #     # print(win32gui.GetWindowPlacement(window.hwnd))
                #     print('hwnd: %s' % str(window.hwnd))
                #     print('hwnd_visibility_state: %s' % str(window.hwnd_visibility_state))
                #     print('hwnd_title: %s' % str(window.hwnd_title))
                #     print('hwnd_class: %s' % str(window.hwnd_class))
                #     print('hwnd_controls_state: %s' % str(window.hwnd_controls_state))
                #     print('hwnd_position: %s' % str(window.hwnd_position))
                #     print('hwnd_size: %s' % str(window.hwnd_size))
                #     print('hwnd_process: %s' % str(window.hwnd_process))
                #     print('hwnd_process_id: %s' % str(window.hwnd_process_id))
                #     print('hwnd_create_cmd: %s' % str(window.hwnd_create_cmd))
                #     print('hwnd_type: %s' % str(window.hwnd_type))
                #     # print('-')
                #     print('hwnd_placement: %s' % str(window._placement))
                #     print('hwnd_rect: %s' % str(window._rect))
            print("---------------------")
            print(len(self.all_windows))

def main(args):
    # If the first command-line argument is 'save'
    if args[0] == "save":
        # Create a new WindowManager object
        manager = WindowManager()
        # Update the list of windows in the WindowManager object
        manager.wm_get_open_windows()
        # Save the windows to the file at the specified filepath
        manager.wm_save(args[1])
    # If the first command-line argument is 'load'
    elif args[0] == "load":
        pass


manager = WindowManager()
manager.wm_get_open_windows()
manager.wm_save("windows.txt")

# manager = WindowManager()
# manager.wm_get_open_windows()
# a = manager.return_windows()
# print(dir(a))
# print(len(a))
# print(a)
# for x in a:
#     print(x.hwnd_title)
#     print(dir(x))
