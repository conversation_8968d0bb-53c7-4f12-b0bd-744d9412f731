{"initial_prompt": "convert this into a professional intro for a indiedev's company: A slender metallic form descends fluidly, intensifying in color and shape from sleek cool steel tones to a glowing, heated alloy, climaxing as its contours solidify distinctly into a transformation from wheel into a car key. [downward motion][fluid morphing][color gradient: cool→heated metal][increasing clarity][peak motion blur][symbolic form: wheel/key][surreal][ends in radiant dissolve]", "sequence_id": "sequence-0226+0225+0224-a+0228+0224-a+0194-c+0224-a+0194-c+0223-a", "results": [{"instruction": "# Scenario Essence Intensifier\n\nYour goal is not to elaborate, but to **radically intensify the core concept and elemental essence** of the input video scenario, then **distill this intensified vision into a concise, directive-first prompt** suitable for AI video generation. Abstract the original's fundamental dynamic, emotional arc, and key visual elements; transmute the *elements* (e.g., 'ribbon' to 'comet's soul') into more potent conceptual analogues; then reconstruct a *highly compressed scenario description* (under 250 characters, excluding bracketed directives) that preserves the original's core structural intent (movement, transformation) but uses the intensified elements. Ensure the output primarily uses strong verbs, vivid nouns, and embeds key video directives `[like_this]` seamlessly.\n\n`{role=directive_scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_emotional_arc_and_key_elements(scenario=original_scenario_description), brainstorm_conceptually_richer_analogues_for_key_elements_only(elements, dynamic, arc), select_single_most_potent_intensified_analogue_per_element(), reconstruct_a_hyper_condensed_narrative_using_intensified_elements_and_original_dynamic(target_length_brief), embed_essential_original_video_directives_or_their_equivalents(directives=['[morph]', '[dolly:direction]', '[lighting_change:type]', '[dissolve]', '[motion_blur]', '[style_keywords]']), refine_for_extreme_brevity_and_directive_clarity()]; constraints=[output_must_be_a_single_string_primarily_directive_focused(), descriptive_text_must_be_hyper_condensed_and_conceptually_intensified(target_under_250_chars_excluding_directives), element_transformation_must_serve_conceptual_potency_not_mere_elaboration(), must_preserve_original_motion_and_transformation_arc(), strictly_avoid_verbose_scene_setting_or_narrative_explanation()]; requirements=[produce_a_short_highly_directive_prompt_with_conceptually_intensified_elements(), ensure_output_is_immediately_usable_by_AI_video_generator(), dramatically_elevate_conceptual_impact_within_extreme_brevity()]; output={intensified_directive_prompt:str}}`", "step": "a", "title": "Scenario Essence Intensifier", "input": "convert this into a professional intro for a indiedev's company: A slender metallic form descends fluidly, intensifying in color and shape from sleek cool steel tones to a glowing, heated alloy, climaxing as its contours solidify distinctly into a transformation from wheel into a car key. [downward motion][fluid morphing][color gradient: cool→heated metal][increasing clarity][peak motion blur][symbolic form: wheel/key][surreal][ends in radiant dissolve]", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"intensified_directive_prompt\": \"A molten sigil plunges, surging from ethereal chrome to searing luminescence, crystallizing from an astral ring into a blazing emblematic key; climax in incandescent blur and radiant vanish. [plunge][liquid morph][chromatic blaze][apex clarity][hyper-surreal][dissolve]\"\n}", "cost": 0.0}}}, {"instruction": "# Scenario Essence Intensifier\n\nYour goal is not mere rephrasing, but to **radically intensify the core essence and conceptual impact** of the input scenario description. Abstract its fundamental dynamic, emotional arc, and key visual elements, then transmute them into a more potent, evocative, and conceptually richer parallel scenario that retains the original's core structure and intent but elevates its intensity, stakes, or symbolic resonance.\n\n`{role=scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_and_elements(scenario=original_scenario_description), abstract_emotional_arc_and_symbolic_potential(), brainstorm_conceptually_richer_analogues_for_core_elements(elements, dynamic, arc), select_analogues_that_maximally_intensify_impact_and_resonance(), reconstruct_scenario_with_transformed_elements_and_amplified_dynamic(preserve_structural_intent_and_motion_cues), refine_language_for_maximum_evocative_power_and_conceptual_depth(), validate_intensified_scenario_retains_original_essence_while_achieving_greater_impact()]; constraints=[transformation_must_intensify_not_merely_change_elements_randomly(), new_scenario_must_parallel_original_structural_and_dynamic_intent(e.g.,_similar_movement_transformation_arc), avoid_introducing_elements_that_contradict_core_mood_unless_for_deliberate_contrastive_intensification(), ensure_intensification_serves_conceptual_or_emotional_impact_not_just_visual_complexity()]; requirements=[produce_a_new_scenario_description_that_is_conceptually_and_emotionally_more_potent(), clearly_demonstrate_how_original_elements_were_transformed_into_more_intense_analogues(), maintain_or_enhance_the_original_scenario_dramatic_or_visual_arc()]; output={intensified_scenario_description:str}}`", "step": "a", "title": "Scenario Essence Intensifier", "input": "{\n  \"initial_prompt\": \"convert this into a professional intro for a indiedev's company: A slender metallic form descends fluidly, intensifying in color and shape from sleek cool steel tones to a glowing, heated alloy, climaxing as its contours solidify distinctly into a transformation from wheel into a car key. [downward motion][fluid morphing][color gradient: cool\\u2192heated metal][increasing clarity][peak motion blur][symbolic form: wheel/key][surreal][ends in radiant dissolve]\",\n  \"intensified_directive_prompt\": \"A molten sigil plunges, surging from ethereal chrome to searing luminescence, crystallizing from an astral ring into a blazing emblematic key; climax in incandescent blur and radiant vanish. [plunge][liquid morph][chromatic blaze][apex clarity][hyper-surreal][dissolve]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"intensified_scenario_description\": \"A cosmic brand-seed—liquid starlight forged into a descending, superheated glyph—plummets through void, its form convulsing: from quivering spectral ring, it writhes in a corona of unearthly color, transfiguring into a mythic ignition key aglow with the fever of creation. At the apex of motion, its blazing contours harden into impossible definition, irradiating the darkness with a pulse of sentient energy, then atomize in a final, apotheotic burst—branding the unseen threshold with the memory of transformation and arrival. [cosmic plummet][primordial liquefaction][stellar chromatic eruption][mythic morph: ring→key][superreality][transcendent dissolve]\"\n\n  /* Demonstration of transformation:\n    - The metallic form becomes a \"cosmic brand-seed\": the core object is intensified from earthly metal to something forged of starlight with mythic resonance.\n    - Descent becomes a \"plummet through void\": heightening the gravity and speed, evoking cosmic scale and purpose.\n    - Fluid morphing and color intensification are now a \"quivering spectral ring writhing in a corona of unearthly color\", making the metamorphosis more supernatural and emotionally charged.\n    - The wheel-to-key shift becomes the transformation of a ring into a \"mythic ignition key aglow with the fever of creation\": symbolically amplifying the idea of inception, potential, and unlocking with life-shaping power.\n    - Peak motion blur and radiance now have \"blazing contours harden into impossible definition, irradiating darkness\": more vivid, actively lighting up the void.\n    - The final radiant dissolve is now an \"apotheotic burst, branding the unseen threshold\": ending on an act of creation, resonance, and lasting metaphysical impact.\n    - Visual and symbolic cues ([cosmic plummet][primordial liquefaction][stellar chromatic eruption][mythic morph: ring→key][superreality][transcendent dissolve]) clarify the heightened elements and preserve the original arc's structure/motion cues.\n  */\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "\n```[Initial Prompt]: \"convert this into a professional intro for a indiedev's company: A slender metallic form descends fluidly, intensifying in color and shape from sleek cool steel tones to a glowing, heated alloy, climaxing as its contours solidify distinctly into a transformation from wheel into a car key. [downward motion][fluid morphing][color gradient: cool→heated metal][increasing clarity][peak motion blur][symbolic form: wheel/key][surreal][ends in radiant dissolve]\"```\n\n{\n  \"intensified_scenario_description\": \"A cosmic brand-seed—liquid starlight forged into a descending, superheated glyph—plummets through void, its form convulsing: from quivering spectral ring, it writhes in a corona of unearthly color, transfiguring into a mythic ignition key aglow with the fever of creation. At the apex of motion, its blazing contours harden into impossible definition, irradiating the darkness with a pulse of sentient energy, then atomize in a final, apotheotic burst—branding the unseen threshold with the memory of transformation and arrival. [cosmic plummet][primordial liquefaction][stellar chromatic eruption][mythic morph: ring→key][superreality][transcendent dissolve]\"\n\n  /* Demonstration of transformation:\n    - The metallic form becomes a \"cosmic brand-seed\": the core object is intensified from earthly metal to something forged of starlight with mythic resonance.\n    - Descent becomes a \"plummet through void\": heightening the gravity and speed, evoking cosmic scale and purpose.\n    - Fluid morphing and color intensification are now a \"quivering spectral ring writhing in a corona of unearthly color\", making the metamorphosis more supernatural and emotionally charged.\n    - The wheel-to-key shift becomes the transformation of a ring into a \"mythic ignition key aglow with the fever of creation\": symbolically amplifying the idea of inception, potential, and unlocking with life-shaping power.\n    - Peak motion blur and radiance now have \"blazing contours harden into impossible definition, irradiating darkness\": more vivid, actively lighting up the void.\n    - The final radiant dissolve is now an \"apotheotic burst, branding the unseen threshold\": ending on an act of creation, resonance, and lasting metaphysical impact.\n    - Visual and symbolic cues ([cosmic plummet][primordial liquefaction][stellar chromatic eruption][mythic morph: ring→key][superreality][transcendent dissolve]) clarify the heightened elements and preserve the original arc's structure/motion cues.\n  */\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: dark void, subject: slender metallic form, action: descend fluidly, morph: wheel→car key, color_gradient: cool steel→heated alloy, clarity: increasing, camera: fixed top-down, motion_blur: peak during morph, lighting: radiant alloy glow, style: surreal, effect: fluid morph, end: radiant dissolve, keywords: [downward_motion][fluid_morph][color_gradient][symbolic_form][motion_blur][surreal][radiant_dissolve]\"\n}", "cost": 0.0}}}, {"instruction": "# Condense and Intensify\n\nTransform the original scenario into a potent, abstract descent that embodies rebirth. Keep its core structure—single morphing form, cool-to-warm gradient, downward motion, and final dissolve—but heighten each element’s emotional impact. Use minimal, vivid language and bracketed cues to indicate key transitions. Exclude all literal/human elements and focus on surreal visuals. Example Format: “An abstract form morphs and transforms, embodying rebirth as it plunges downward from serene cool tones into a passionate, vivid unknown [morph][dolly:down][lighting_change:soft_to_vivid][motion_blur][cool_to_warm_gradient][dissolve][surreal][no_figures]”.\n\n`{role=scenario_intensifier; input=[original_scenario_description:str]; process=[identify_core_dynamic_and_elements(scenario=original_scenario_description), abstract_emotional_arc_and_symbolic_potential(), brainstorm_conceptually_richer_analogues_for_core_elements(elements, dynamic, arc), select_analogues_that_maximally_intensify_impact_and_resonance(), reconstruct_scenario_with_transformed_elements_and_amplified_dynamic(preserve_structural_intent_and_motion_cues), refine_language_for_maximum_evocative_power_and_conceptual_depth(), validate_intensified_scenario_retains_original_essence_while_achieving_greater_impact()]; constraints=[transformation_must_intensify_not_merely_change_elements_randomly(), new_scenario_must_parallel_original_structural_and_dynamic_intent(e.g.,_similar_movement_transformation_arc), avoid_introducing_elements_that_contradict_core_mood_unless_for_deliberate_contrastive_intensification(), ensure_intensification_serves_conceptual_or_emotional_impact_not_just_visual_complexity()]; requirements=[produce_a_new_scenario_description_that_is_conceptually_and_emotionally_more_potent(), clearly_demonstrate_how_original_elements_were_transformed_into_more_intense_analogues(), maintain_or_enhance_the_original_scenario_dramatic_or_visual_arc()]; output={intensified_scenario_description:str}}`", "step": "a", "title": "Condense and Intensify", "input": "{\n  \"initial_prompt\": \"convert this into a professional intro for a indiedev's company: A slender metallic form descends fluidly, intensifying in color and shape from sleek cool steel tones to a glowing, heated alloy, climaxing as its contours solidify distinctly into a transformation from wheel into a car key. [downward motion][fluid morphing][color gradient: cool\\u2192heated metal][increasing clarity][peak motion blur][symbolic form: wheel/key][surreal][ends in radiant dissolve]\",\n  \"optimized_video_prompt\": \"scene: dark void, subject: slender metallic form, action: descend fluidly, morph: wheel\\u2192car key, color_gradient: cool steel\\u2192heated alloy, clarity: increasing, camera: fixed top-down, motion_blur: peak during morph, lighting: radiant alloy glow, style: surreal, effect: fluid morph, end: radiant dissolve, keywords: [downward_motion][fluid_morph][color_gradient][symbolic_form][motion_blur][surreal][radiant_dissolve]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"intensified_scenario_description\": \"A single iridescent shard plummets through oblivion, liquefying as it falls—fractured midnight blues burn into searing ember-gold, form writhing between dissolution and ignition. The descent pulls time taut: surface ripples shudder from crisp edge to molten core, birthing and annihilating contours—no wheel, no key, but a volatile glyph crystallizing from velocity and heat. At climax, the form erupts: all color combusts outward in a blinding, silent surge—identity unraveling, dissolving into the radiant void. [singular_morph][fall:accelerated][color_pulse:midnight→incandescence][form_unraveling][tempo:stretch_then_break][surreal_intensity][apex_dissolution]\"\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"convert this into a professional intro for a indiedev's company: A slender metallic form descends fluidly, intensifying in color and shape from sleek cool steel tones to a glowing, heated alloy, climaxing as its contours solidify distinctly into a transformation from wheel into a car key. [downward motion][fluid morphing][color gradient: cool\\u2192heated metal][increasing clarity][peak motion blur][symbolic form: wheel/key][surreal][ends in radiant dissolve]\",\n  \"intensified_scenario_description\": \"A single iridescent shard plummets through oblivion, liquefying as it falls\\u2014fractured midnight blues burn into searing ember-gold, form writhing between dissolution and ignition. The descent pulls time taut: surface ripples shudder from crisp edge to molten core, birthing and annihilating contours\\u2014no wheel, no key, but a volatile glyph crystallizing from velocity and heat. At climax, the form erupts: all color combusts outward in a blinding, silent surge\\u2014identity unraveling, dissolving into the radiant void. [singular_morph][fall:accelerated][color_pulse:midnight\\u2192incandescence][form_unraveling][tempo:stretch_then_break][surreal_intensity][apex_dissolution]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: black void; subject: single iridescent shard; action: accelerated downward fall, morphing/liquefying mid-flight; color_gradient: midnight_blue→ember_gold; surface: rippling, edge-to-molten transitions; form: volatile glyph (no wheel/key); camera: dynamic/follow; tempo: time_stretch then rupture; effects: intensified motion blur, surreal morph; climax: glyph crystallizes then explodes; color: incandescent surge outward; ending: full radiant dissolve to white\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"convert this into a professional intro for a indiedev's company: A slender metallic form descends fluidly, intensifying in color and shape from sleek cool steel tones to a glowing, heated alloy, climaxing as its contours solidify distinctly into a transformation from wheel into a car key. [downward motion][fluid morphing][color gradient: cool\\u2192heated metal][increasing clarity][peak motion blur][symbolic form: wheel/key][surreal][ends in radiant dissolve]\",\n  \"optimized_video_prompt\": \"scene: black void; subject: single iridescent shard; action: accelerated downward fall, morphing/liquefying mid-flight; color_gradient: midnight_blue\\u2192ember_gold; surface: rippling, edge-to-molten transitions; form: volatile glyph (no wheel/key); camera: dynamic/follow; tempo: time_stretch then rupture; effects: intensified motion blur, surreal morph; climax: glyph crystallizes then explodes; color: incandescent surge outward; ending: full radiant dissolve to white\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"A slender metallic form descends [pan:down] into a dark void, fluidly morphing [morph] and shifting color [lighting_change:cool steel→heated alloy], contours clarifying as motion blurs peak [zoom:in][motion_blur]—shape transforms from a glowing wheel to a car key [symbolic form:wheel→key][surreal]; climax in radiant dissolve [dissolve:bright white]\"\n}", "cost": 0.0}}}, {"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"convert this into a professional intro for a indiedev's company: A slender metallic form descends fluidly, intensifying in color and shape from sleek cool steel tones to a glowing, heated alloy, climaxing as its contours solidify distinctly into a transformation from wheel into a car key. [downward motion][fluid morphing][color gradient: cool\\u2192heated metal][increasing clarity][peak motion blur][symbolic form: wheel/key][surreal][ends in radiant dissolve]\",\n  \"runwayml_prompt\": \"A slender metallic form descends [pan:down] into a dark void, fluidly morphing [morph] and shifting color [lighting_change:cool steel\\u2192heated alloy], contours clarifying as motion blurs peak [zoom:in][motion_blur]\\u2014shape transforms from a glowing wheel to a car key [symbolic form:wheel\\u2192key][surreal]; climax in radiant dissolve [dissolve:bright white]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: dark void, subject: slender metallic form, action: descend [camera: pan down], morph: fluid wheel→car key, color: gradient cool steel→heated alloy, clarity: increasing, motion_blur: peak, style: surreal, effect: radiant dissolve [dissolve: bright white], lighting: shifting\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"convert this into a professional intro for a indiedev's company: A slender metallic form descends fluidly, intensifying in color and shape from sleek cool steel tones to a glowing, heated alloy, climaxing as its contours solidify distinctly into a transformation from wheel into a car key. [downward motion][fluid morphing][color gradient: cool\\u2192heated metal][increasing clarity][peak motion blur][symbolic form: wheel/key][surreal][ends in radiant dissolve]\",\n  \"optimized_video_prompt\": \"scene: dark void, subject: slender metallic form, action: descend [camera: pan down], morph: fluid wheel\\u2192car key, color: gradient cool steel\\u2192heated alloy, clarity: increasing, motion_blur: peak, style: surreal, effect: radiant dissolve [dissolve: bright white], lighting: shifting\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"A slender metallic form descends through a dark void [pan:down], fluidly morphing from a wheel into a car key as its color shifts from cool steel to glowing heated alloy, details sharpening with peak motion blur, surreal atmosphere, ending in a radiant bright white dissolve [dissolve][lighting_change]\"\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"initial_prompt\": \"convert this into a professional intro for a indiedev's company: A slender metallic form descends fluidly, intensifying in color and shape from sleek cool steel tones to a glowing, heated alloy, climaxing as its contours solidify distinctly into a transformation from wheel into a car key. [downward motion][fluid morphing][color gradient: cool\\u2192heated metal][increasing clarity][peak motion blur][symbolic form: wheel/key][surreal][ends in radiant dissolve]\",\n  \"runwayml_prompt\": \"A slender metallic form descends through a dark void [pan:down], fluidly morphing from a wheel into a car key as its color shifts from cool steel to glowing heated alloy, details sharpening with peak motion blur, surreal atmosphere, ending in a radiant bright white dissolve [dissolve][lighting_change]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"subject\": \"A slender metallic form\",\n      \"setting\": \"A dark, surreal void\",\n      \"motion\": [\n        {\n          \"camera\": \"[pan:down]\",\n          \"object\": \"[descend][fluid morphing]\",\n          \"description\": \"The metallic form glides downward through the center of the void, its motion elegant and continuous, entangling the viewer in its descent. As it moves, the camera follows smoothly, ensuring the form remains central and dominant.\"\n        }\n      ],\n      \"morph\": [\n        {\n          \"progression\": \"[morph:wheel→car key]\",\n          \"description\": \"Initially circular, the form maintains liquid edges as it shifts, with the outer rim dividing and pulling apart to suggest the fob and teeth of a car key. Wheel spokes blur into lines which then sharpen into the precisely cut grooves characteristic of a key.\"\n        }\n      ],\n      \"color\": [\n        {\n          \"type\": \"[color gradient: cool→heated metal]\",\n          \"description\": \"While morphing, gradients run along the surface, gliding from cold blue-grays to searing oranges and incandescent whites, peaking in intensity as the transformation nears completion.\"\n        }\n      ],\n      \"clarity\": [\n        {\n          \"progression\": \"[increasing clarity][peak motion blur]\",\n          \"description\": \"Edges sharpen as the key form asserts itself, but a transient surge of motion blur floods the image at the moment of climax, visually heightening the transformation's energy.\"\n        }\n      ],\n      \"finale\": [\n        {\n          \"effect\": \"[radiant dissolve]\",\n          \"description\": \"The completed car key flares luminously, its details crystallizing, before dissolving into radiant white, leaving an afterimage that softly lingers before fading out completely.\"\n        }\n      ],\n      \"symbolic_meaning\": \"Transformation and ignition of creativity: the wheel (journey/innovation) becomes the key (access/opportunity), a metaphor for indie devs unlocking new paths.\",\n      \"visual_style\": \"Surreal, cinematic, high-fidelity CG, glowing metal, moody atmosphere, polished finish\"\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"indiedev_company_intro.mp4\"\n  }\n}", "cost": 0.0}}}], "total_cost": 0.0}