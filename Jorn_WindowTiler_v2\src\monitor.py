import win32api

class Monitor:
    def __init__(self, monitor_handle, monitor_info):
        self.handle = monitor_handle
        self.flags = monitor_info['Flags']
        self.device = monitor_info['Device']
        self._set_areas(monitor_info['Monitor'], monitor_info['Work'])

    def _set_areas(self, monitor_area, work_area):
        self.monitor_area = monitor_area
        self.work_area = work_area
        self.monitor_area_dict = self._convert_to_dict(monitor_area)
        self.work_area_dict = self._convert_to_dict(work_area)

    def _convert_to_dict(self, area):
        return {
            'x': area[0],
            'y': area[1],
            'width': area[2] - area[0],
            'height': area[3] - area[1]
        }

    def update_monitor_area(self, new_area):
        self._set_areas(new_area, self.work_area)

    def update_work_area(self, new_area):
        self._set_areas(self.monitor_area, new_area)

    def is_primary(self):
        return self.flags == 1

    def get_dimensions(self):
        return {
            'width': self.monitor_area[2] - self.monitor_area[0],
            'height': self.monitor_area[3] - self.monitor_area[1]
        }

    def get_work_area(self):
        return self._convert_to_dict(self.work_area)

def get_all_monitors():
    monitors_info = {}
    for monitor in win32api.EnumDisplayMonitors(None, None):
        monitor_handle = monitor[0]
        monitor_info = win32api.GetMonitorInfo(monitor_handle)
        monitor_obj = Monitor(monitor_handle, monitor_info)
        monitors_info[monitor_handle] = monitor_obj

    return monitors_info
