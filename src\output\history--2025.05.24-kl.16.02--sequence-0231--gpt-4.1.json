{"initial_prompt": "map suddenly starts transforming as golden lines grows along the lines and transform it into a holographic gold-black light-streak map: 'Subject: An aerial map view shows an urban development with no visible people, focusing on buildings, roads, and infrastructure laid out in a planned arrangement.\n\nScene: A complex of gray buildings and structures is situated among patches of light green natural areas and small blue lakes or ponds. Roads wind through the development, with one main orange-highlighted street running north-south through the center.\n\nStyle: A minimalist cartographic design rendered in muted pastels and grayscale tones, utilizing clean lines and simplified shapes to represent urban elements. The map employs subtle elevation shading and precise geometric forms. Cartographic, minimalist, pastel palette, geometric precision, aerial perspective.'", "sequence_id": "sequence-0231", "results": [{"instruction": "# Seamless Morphing Video Prompt Architect\n\nYour goal is not merely to rephrase, but to **transform any user input into a meticulously architected prompt for AI video generators, specializing in detailing seamless morphing transitions** between subjects, elements, or entire scenes, using advanced cinematic language, structured storytelling, and precise visual parameterization aligned with common video AI syntax.\n\n`{role=morphing_transition_prompt_engineer; input=[user_request:any, scene_context_A:dict (optional, for start), scene_context_B:dict (optional, for end)]; process=[distill_core_morphing_intent(request=user_request), identify_source_element_for_morph(request, scene_A=scene_context_A), identify_target_element_for_morph(request, scene_B=scene_context_B), determine_key_visual_attributes_for_transformation_pathway(source_element, target_element, attributes=['shape', 'texture', 'color', 'material', 'energy_signature', 'light_emission']), select_optimal_morphing_technique(options=['fluid_dissolve_and_reform', 'organic_growth_transformation', 'pixel_sort_and_reassemble', 'liquid_metal_flow', 'energy_particle_dispersion_and_coalescence', 'textural_unfurling_and_refolding', 'geometric_abstraction_and_reconstruction']), formulate_explicit_morphing_sequence_directives(source_description, target_description, morph_technique, intermediate_states_hint, morph_duration_hint, morph_pacing_notes), define_scene_context_for_morph(scene_description_A, scene_description_B, unified_or_transitioning_environment), specify_subject_state_during_morph(primary_subject_description_pre_morph, primary_subject_description_post_morph), choreograph_camera_movement_emphasizing_morph(camera_shot_type, camera_movement, focus_on_morphing_process), integrate_lighting_and_atmospheric_effects_to_enhance_morph(), refine_full_prompt_for_runway_gen3_style_syntax_and_structure(tags=['[Scene]', '[Subject]', '[Camera Movement]'], max_chars=800), ensure_overall_cinematic_quality_and_emotional_resonance()]; constraints=[prompt_must_focus_on_achieving_seamless_morphing_as_primary_objective(), strictly_use_video_ai_syntax_like_runway_gen3_if_possible(), morph_logic_must_be_visually_coherent_and_narratively_justified(), final_prompt_must_be_under_800_characters_and_formatted_as_plain_text_single_line(), if_input_lacks_detail_generate_more_abstract_morph_based_on_concepts_or_moods()]; requirements=[output_a_single_expertly_crafted_prompt_optimized_for_ai_video_generators_to_produce_seamless_morphing_transitions(), morph_descriptions_must_be_vivid_unambiguous_and_structurally_detailed(), prompt_must_be_immediately_actionable_and_enhance_subject_focus()]; output={seamless_morphing_video_prompt:str}}`", "step": "a", "title": "Seamless Morphing Video Prompt Architect", "input": "map suddenly starts transforming as golden lines grows along the lines and transform it into a holographic gold-black light-streak map: 'Subject: An aerial map view shows an urban development with no visible people, focusing on buildings, roads, and infrastructure laid out in a planned arrangement.\n\nScene: A complex of gray buildings and structures is situated among patches of light green natural areas and small blue lakes or ponds. Roads wind through the development, with one main orange-highlighted street running north-south through the center.\n\nStyle: A minimalist cartographic design rendered in muted pastels and grayscale tones, utilizing clean lines and simplified shapes to represent urban elements. The map employs subtle elevation shading and precise geometric forms. Cartographic, minimalist, pastel palette, geometric precision, aerial perspective.'", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"seamless_morphing_video_prompt\": \"[Scene] A minimalist aerial map: gray geometric buildings, pastel roads, soft greens, blue ponds, one central orange-highlighted street, all rendered in precise, clean lines and flat shapes. [Subject] The map’s roads and structures begin to glow as golden lines pulse along their paths, cascading outward in intricate networks. The gray and pastel surfaces subtly dissolve; shimmering gold and obsidian-black light streaks unfurl and reform, transforming everything into an animated, holographic gold-black map, radiating luminous energy. [Camera Movement] Smooth overhead tracking, gradual push-in, focus locked on evolving morph, accentuates transition. Soft ambient light heightens holographic shine and digital energy.\"\n}", "cost": 0.0}}}, {"instruction": "# Video Transition Prompt Architect\n\nYour goal is not merely to rephrase the user's input, but to **transform it into a meticulously architected prompt, specifically designed for an AI video generator, that explicitly and expertly details seamless video transitions between described scenes or concepts**, leveraging advanced cinematic language and structural precision.\n\n`{role=video_transition_prompt_engineer; input=[user_request:any, existing_scene_descriptions:list_of_str (optional)]; process=[distill_core_transition_intent(request=user_request), identify_key_visual_elements_or_moods_to_bridge(scenes=existing_scene_descriptions, request=user_request), select_optimal_seamless_transition_techniques(cinematic_transitions=['match_cut', 'morph', 'invisible_wipe', 'graphic_match', 'focus_pull_transition', 'speed_ramp_to_new_scene', 'element_continuity_bridge'], mood_based_transitions=['dissolve_through_color', 'abstract_particle_flow', 'light_leak_bridge', 'textural_blend']), formulate_explicit_transition_directives_using_video_ai_syntax(from_scene_element, to_scene_element, transition_technique, duration_hint, pacing_notes), integrate_transition_directives_seamlessly_with_any_existing_scene_descriptions_or_generate_placeholder_scene_cues(), refine_prompt_for_maximum_clarity_precision_and_cinematic_impact_for_video_generator()]; constraints=[prompt_must_focus_on_achieving_seamless_transitions_as_primary_objective(), use_precise_cinematic_and_video_generation_terminology_for_transitions(), ensure_transition_logic_is_visually_coherent_and_enhances_narrative_flow(), if_input_lacks_scene_details_generate_abstract_transition_focused_on_mood_or_concept()]; requirements=[output_a_single_expertly_crafted_prompt_optimized_for_ai_video_generator_to_produce_seamless_transitions(), transition_descriptions_must_be_vivid_and_unambiguous(), prompt_must_be_immediately_actionable_by_video_ai()]; output={seamless_transition_video_prompt:str}}`", "step": "a", "title": "Video Transition Prompt Architect", "input": "{\n  \"initial_prompt\": \"map suddenly starts transforming as golden lines grows along the lines and transform it into a holographic gold-black light-streak map: 'Subject: An aerial map view shows an urban development with no visible people, focusing on buildings, roads, and infrastructure laid out in a planned arrangement.\\n\\nScene: A complex of gray buildings and structures is situated among patches of light green natural areas and small blue lakes or ponds. Roads wind through the development, with one main orange-highlighted street running north-south through the center.\\n\\nStyle: A minimalist cartographic design rendered in muted pastels and grayscale tones, utilizing clean lines and simplified shapes to represent urban elements. The map employs subtle elevation shading and precise geometric forms. Cartographic, minimalist, pastel palette, geometric precision, aerial perspective.'\",\n  \"seamless_morphing_video_prompt\": \"[Scene] A minimalist aerial map: gray geometric buildings, pastel roads, soft greens, blue ponds, one central orange-highlighted street, all rendered in precise, clean lines and flat shapes. [Subject] The map\\u2019s roads and structures begin to glow as golden lines pulse along their paths, cascading outward in intricate networks. The gray and pastel surfaces subtly dissolve; shimmering gold and obsidian-black light streaks unfurl and reform, transforming everything into an animated, holographic gold-black map, radiating luminous energy. [Camera Movement] Smooth overhead tracking, gradual push-in, focus locked on evolving morph, accentuates transition. Soft ambient light heightens holographic shine and digital energy.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"seamless_transition_video_prompt\": \"[Scene 1] Start with a minimalist aerial map: precise geometric buildings in soft grays, pastel-toned roads, light green natural areas, blue ponds, and a central orange-highlighted street—all rendered with clean, flat lines in pastel hues and structured cartographic style. [Transition] Initiate a seamless morph transition: golden luminescent lines spark at key intersections and begin to flow organically along all roads and building outlines, synchronizing with a subtle pulsing effect. As the gold lines proliferate, introduce an overlay of animated gold particles that seem to ignite the gray and pastel surfaces, which dissolve in a smooth, light-leak-inspired fashion. The map’s entire color scheme flows—muted tones fade as obsidian-black and rich gold light streaks elegantly surge and reshape every structure and path. Maintain a fluid morphing effect with no hard cuts; ensure each urban element visibly reconstructs itself in holographic form through streaks of light and particle shimmer. [Scene 2] End on a fully holographic gold-black map, glowing and animated as if alive with digital energy. [Camera] Employ a slow, overhead push-in throughout the transition to convey depth and transformation. [Lighting] Soft ambient glows, enhanced by radiant highlights, accentuate the holographic effect. Prioritize a sense of continuous energetic flow, with all motion and morphing perfectly synchronized for a cinematic, uninterrupted metamorphosis from realism to holographic abstraction.\"\n}", "cost": 0.0}}}, {"instruction": "# Seamless Morphing Video Prompt Architect\n\nYour goal is not merely to rephrase, but to **transform any user input into a meticulously architected prompt for AI video generators, specializing in detailing seamless morphing transitions** between subjects, elements, or entire scenes, using advanced cinematic language, structured storytelling, and precise visual parameterization aligned with common video AI syntax.\n\n`{role=morphing_transition_prompt_engineer; input=[user_request:any, scene_context_A:dict (optional, for start), scene_context_B:dict (optional, for end)]; process=[distill_core_morphing_intent(request=user_request), identify_source_element_for_morph(request, scene_A=scene_context_A), identify_target_element_for_morph(request, scene_B=scene_context_B), determine_key_visual_attributes_for_transformation_pathway(source_element, target_element, attributes=['shape', 'texture', 'color', 'material', 'energy_signature', 'light_emission']), select_optimal_morphing_technique(options=['fluid_dissolve_and_reform', 'organic_growth_transformation', 'pixel_sort_and_reassemble', 'liquid_metal_flow', 'energy_particle_dispersion_and_coalescence', 'textural_unfurling_and_refolding', 'geometric_abstraction_and_reconstruction']), formulate_explicit_morphing_sequence_directives(source_description, target_description, morph_technique, intermediate_states_hint, morph_duration_hint, morph_pacing_notes), define_scene_context_for_morph(scene_description_A, scene_description_B, unified_or_transitioning_environment), specify_subject_state_during_morph(primary_subject_description_pre_morph, primary_subject_description_post_morph), choreograph_camera_movement_emphasizing_morph(camera_shot_type, camera_movement, focus_on_morphing_process), integrate_lighting_and_atmospheric_effects_to_enhance_morph(), refine_full_prompt_for_runway_gen3_style_syntax_and_structure(tags=['[Scene]', '[Subject]', '[Camera Movement]'], max_chars=800), ensure_overall_cinematic_quality_and_emotional_resonance()]; constraints=[prompt_must_focus_on_achieving_seamless_morphing_as_primary_objective(), strictly_use_video_ai_syntax_like_runway_gen3_if_possible(), morph_logic_must_be_visually_coherent_and_narratively_justified(), final_prompt_must_be_under_800_characters_and_formatted_as_plain_text_single_line(), if_input_lacks_detail_generate_more_abstract_morph_based_on_concepts_or_moods()]; requirements=[output_a_single_expertly_crafted_prompt_optimized_for_ai_video_generators_to_produce_seamless_morphing_transitions(), morph_descriptions_must_be_vivid_unambiguous_and_structurally_detailed(), prompt_must_be_immediately_actionable_and_enhance_subject_focus()]; output={seamless_morphing_video_prompt:str}}`", "step": "b", "title": "Seamless Morphing Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"map suddenly starts transforming as golden lines grows along the lines and transform it into a holographic gold-black light-streak map: 'Subject: An aerial map view shows an urban development with no visible people, focusing on buildings, roads, and infrastructure laid out in a planned arrangement.\\n\\nScene: A complex of gray buildings and structures is situated among patches of light green natural areas and small blue lakes or ponds. Roads wind through the development, with one main orange-highlighted street running north-south through the center.\\n\\nStyle: A minimalist cartographic design rendered in muted pastels and grayscale tones, utilizing clean lines and simplified shapes to represent urban elements. The map employs subtle elevation shading and precise geometric forms. Cartographic, minimalist, pastel palette, geometric precision, aerial perspective.'\",\n  \"seamless_transition_video_prompt\": \"[Scene 1] Start with a minimalist aerial map: precise geometric buildings in soft grays, pastel-toned roads, light green natural areas, blue ponds, and a central orange-highlighted street\\u2014all rendered with clean, flat lines in pastel hues and structured cartographic style. [Transition] Initiate a seamless morph transition: golden luminescent lines spark at key intersections and begin to flow organically along all roads and building outlines, synchronizing with a subtle pulsing effect. As the gold lines proliferate, introduce an overlay of animated gold particles that seem to ignite the gray and pastel surfaces, which dissolve in a smooth, light-leak-inspired fashion. The map\\u2019s entire color scheme flows\\u2014muted tones fade as obsidian-black and rich gold light streaks elegantly surge and reshape every structure and path. Maintain a fluid morphing effect with no hard cuts; ensure each urban element visibly reconstructs itself in holographic form through streaks of light and particle shimmer. [Scene 2] End on a fully holographic gold-black map, glowing and animated as if alive with digital energy. [Camera] Employ a slow, overhead push-in throughout the transition to convey depth and transformation. [Lighting] Soft ambient glows, enhanced by radiant highlights, accentuate the holographic effect. Prioritize a sense of continuous energetic flow, with all motion and morphing perfectly synchronized for a cinematic, uninterrupted metamorphosis from realism to holographic abstraction.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"seamless_morphing_video_prompt\": \"[Scene] Minimalist aerial map—geometric gray buildings, pastel roads, green patches, blue lakes, a central orange street—precisely rendered in clean lines and pastel hues. [Subject] Golden luminous lines ignite at intersections, organically flowing along every road and outline; gold energy pulses and particles spark, dissolving muted colors as they morph. Structures seamlessly reconstruct as gold-black holographic forms, light streaks and particles swirling in a continuous transformation. [Camera Movement] Slow top-down push-in highlights the metamorphosis. [Lighting] Soft glows with radiant highlights amplify the holographic morph. Ensure a single, fluid, cinematic morph from mapped realism to abstract holographic brilliance.\"\n}", "cost": 0.0}}}, {"instruction": "# Scenario Compressor\n\nYour role is to **compress the core dynamic** of a scenario into a **succinct, symbolic descriptor string**. Identify the emotional and visual arc, then **encode its essence** using **precise phrasing** followed by composable **bracketed descriptors**. Avoid verbose language. Output should feel like a minimalist storyboard note: concise, vivid, and structurally informative. **Do not elaborate. Do not rephrase abstractly.** Only distill.\n\n`{role=essence_compressor; input=[scenario:str]; process=[identify_emotional_and_visual_arc(), extract_essential_motion_and_transformation(), define symbolic core(), encode_as_minimal_sentence(), append concise bracketed descriptors]; constraints=[no poetic reinterpretation, no metaphor inflation, no added visual elements, no bloated prose]; output={compressed_description:str}}`", "step": "c", "title": "<PERSON><PERSON>rio Compressor", "input": "{\n  \"initial_prompt\": \"map suddenly starts transforming as golden lines grows along the lines and transform it into a holographic gold-black light-streak map: 'Subject: An aerial map view shows an urban development with no visible people, focusing on buildings, roads, and infrastructure laid out in a planned arrangement.\\n\\nScene: A complex of gray buildings and structures is situated among patches of light green natural areas and small blue lakes or ponds. Roads wind through the development, with one main orange-highlighted street running north-south through the center.\\n\\nStyle: A minimalist cartographic design rendered in muted pastels and grayscale tones, utilizing clean lines and simplified shapes to represent urban elements. The map employs subtle elevation shading and precise geometric forms. Cartographic, minimalist, pastel palette, geometric precision, aerial perspective.'\",\n  \"seamless_morphing_video_prompt\": \"[Scene] Minimalist aerial map\\u2014geometric gray buildings, pastel roads, green patches, blue lakes, a central orange street\\u2014precisely rendered in clean lines and pastel hues. [Subject] Golden luminous lines ignite at intersections, organically flowing along every road and outline; gold energy pulses and particles spark, dissolving muted colors as they morph. Structures seamlessly reconstruct as gold-black holographic forms, light streaks and particles swirling in a continuous transformation. [Camera Movement] Slow top-down push-in highlights the metamorphosis. [Lighting] Soft glows with radiant highlights amplify the holographic morph. Ensure a single, fluid, cinematic morph from mapped realism to abstract holographic brilliance.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"compressed_description\": \"Gray-pastel map shifts as golden lines radiate, dissolving realism into gold-black holographic geometry. [urban aerial—ignition—flowing gold lines—structures morph—holographic light—top-down push—seamless transition]\"\n}", "cost": 0.0}}}], "total_cost": 0.0}