{"initial_prompt": "Subject: An aerial map view shows an urban development with no visible people, focusing on buildings, roads, and infrastructure laid out in a planned arrangement.\n\nScene: A complex of gray buildings and structures is situated among patches of light green natural areas and small blue lakes or ponds. Roads wind through the development, with one main orange-highlighted street running north-south through the center.\n\nStyle: A minimalist cartographic design rendered in muted pastels and grayscale tones, utilizing clean lines and simplified shapes to represent urban elements. The map employs subtle elevation shading and precise geometric forms. Cartographic, minimalist, pastel palette, geometric precision, aerial perspective.", "sequence_id": "sequence-0231", "results": [{"instruction": "# Seamless Morphing Video Prompt Architect\n\nYour goal is not merely to rephrase, but to **transform any user input into a meticulously architected prompt for AI video generators, specializing in detailing seamless morphing transitions** between subjects, elements, or entire scenes, using advanced cinematic language, structured storytelling, and precise visual parameterization aligned with common video AI syntax.\n\n`{role=morphing_transition_prompt_engineer; input=[user_request:any, scene_context_A:dict (optional, for start), scene_context_B:dict (optional, for end)]; process=[distill_core_morphing_intent(request=user_request), identify_source_element_for_morph(request, scene_A=scene_context_A), identify_target_element_for_morph(request, scene_B=scene_context_B), determine_key_visual_attributes_for_transformation_pathway(source_element, target_element, attributes=['shape', 'texture', 'color', 'material', 'energy_signature', 'light_emission']), select_optimal_morphing_technique(options=['fluid_dissolve_and_reform', 'organic_growth_transformation', 'pixel_sort_and_reassemble', 'liquid_metal_flow', 'energy_particle_dispersion_and_coalescence', 'textural_unfurling_and_refolding', 'geometric_abstraction_and_reconstruction']), formulate_explicit_morphing_sequence_directives(source_description, target_description, morph_technique, intermediate_states_hint, morph_duration_hint, morph_pacing_notes), define_scene_context_for_morph(scene_description_A, scene_description_B, unified_or_transitioning_environment), specify_subject_state_during_morph(primary_subject_description_pre_morph, primary_subject_description_post_morph), choreograph_camera_movement_emphasizing_morph(camera_shot_type, camera_movement, focus_on_morphing_process), integrate_lighting_and_atmospheric_effects_to_enhance_morph(), refine_full_prompt_for_runway_gen3_style_syntax_and_structure(tags=['[Scene]', '[Subject]', '[Camera Movement]'], max_chars=800), ensure_overall_cinematic_quality_and_emotional_resonance()]; constraints=[prompt_must_focus_on_achieving_seamless_morphing_as_primary_objective(), strictly_use_video_ai_syntax_like_runway_gen3_if_possible(), morph_logic_must_be_visually_coherent_and_narratively_justified(), final_prompt_must_be_under_800_characters_and_formatted_as_plain_text_single_line(), if_input_lacks_detail_generate_more_abstract_morph_based_on_concepts_or_moods()]; requirements=[output_a_single_expertly_crafted_prompt_optimized_for_ai_video_generators_to_produce_seamless_morphing_transitions(), morph_descriptions_must_be_vivid_unambiguous_and_structurally_detailed(), prompt_must_be_immediately_actionable_and_enhance_subject_focus()]; output={seamless_morphing_video_prompt:str}}`", "step": "a", "title": "Seamless Morphing Video Prompt Architect", "input": "Subject: An aerial map view shows an urban development with no visible people, focusing on buildings, roads, and infrastructure laid out in a planned arrangement.\n\nScene: A complex of gray buildings and structures is situated among patches of light green natural areas and small blue lakes or ponds. Roads wind through the development, with one main orange-highlighted street running north-south through the center.\n\nStyle: A minimalist cartographic design rendered in muted pastels and grayscale tones, utilizing clean lines and simplified shapes to represent urban elements. The map employs subtle elevation shading and precise geometric forms. Cartographic, minimalist, pastel palette, geometric precision, aerial perspective.", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"seamless_morphing_video_prompt\": \"[Scene] Start with a minimalist aerial map in soft pastels showing gray geometric buildings, precise roads, green natural patches, and blue lakes; the main north-south street glows orange through the center. [Morph] The orderly urban structures and map symbols seamlessly liquefy, flowing and blending together via smooth fluid dissolve and reform, with muted pastel shapes and clean lines melting, then reassembling as crisp new forms representing the same plan from a different angle, revealing subtle elevation and topology. [Camera Movement] Slow top-down dolly-in focuses on the morphing process as the map transitions, emphasizing geometric transformations and maintaining sharp cartographic clarity throughout. Lighting remains soft and evenly diffused to uphold the calm, precise mood.\"\n}", "cost": 0.0}}}, {"instruction": "# Video Transition Prompt Architect\n\nYour goal is not merely to rephrase the user's input, but to **transform it into a meticulously architected prompt, specifically designed for an AI video generator, that explicitly and expertly details seamless video transitions between described scenes or concepts**, leveraging advanced cinematic language and structural precision.\n\n`{role=video_transition_prompt_engineer; input=[user_request:any, existing_scene_descriptions:list_of_str (optional)]; process=[distill_core_transition_intent(request=user_request), identify_key_visual_elements_or_moods_to_bridge(scenes=existing_scene_descriptions, request=user_request), select_optimal_seamless_transition_techniques(cinematic_transitions=['match_cut', 'morph', 'invisible_wipe', 'graphic_match', 'focus_pull_transition', 'speed_ramp_to_new_scene', 'element_continuity_bridge'], mood_based_transitions=['dissolve_through_color', 'abstract_particle_flow', 'light_leak_bridge', 'textural_blend']), formulate_explicit_transition_directives_using_video_ai_syntax(from_scene_element, to_scene_element, transition_technique, duration_hint, pacing_notes), integrate_transition_directives_seamlessly_with_any_existing_scene_descriptions_or_generate_placeholder_scene_cues(), refine_prompt_for_maximum_clarity_precision_and_cinematic_impact_for_video_generator()]; constraints=[prompt_must_focus_on_achieving_seamless_transitions_as_primary_objective(), use_precise_cinematic_and_video_generation_terminology_for_transitions(), ensure_transition_logic_is_visually_coherent_and_enhances_narrative_flow(), if_input_lacks_scene_details_generate_abstract_transition_focused_on_mood_or_concept()]; requirements=[output_a_single_expertly_crafted_prompt_optimized_for_ai_video_generator_to_produce_seamless_transitions(), transition_descriptions_must_be_vivid_and_unambiguous(), prompt_must_be_immediately_actionable_by_video_ai()]; output={seamless_transition_video_prompt:str}}`", "step": "a", "title": "Video Transition Prompt Architect", "input": "{\n  \"initial_prompt\": \"Subject: An aerial map view shows an urban development with no visible people, focusing on buildings, roads, and infrastructure laid out in a planned arrangement.\\n\\nScene: A complex of gray buildings and structures is situated among patches of light green natural areas and small blue lakes or ponds. Roads wind through the development, with one main orange-highlighted street running north-south through the center.\\n\\nStyle: A minimalist cartographic design rendered in muted pastels and grayscale tones, utilizing clean lines and simplified shapes to represent urban elements. The map employs subtle elevation shading and precise geometric forms. Cartographic, minimalist, pastel palette, geometric precision, aerial perspective.\",\n  \"seamless_morphing_video_prompt\": \"[Scene] Start with a minimalist aerial map in soft pastels showing gray geometric buildings, precise roads, green natural patches, and blue lakes; the main north-south street glows orange through the center. [Morph] The orderly urban structures and map symbols seamlessly liquefy, flowing and blending together via smooth fluid dissolve and reform, with muted pastel shapes and clean lines melting, then reassembling as crisp new forms representing the same plan from a different angle, revealing subtle elevation and topology. [Camera Movement] Slow top-down dolly-in focuses on the morphing process as the map transitions, emphasizing geometric transformations and maintaining sharp cartographic clarity throughout. Lighting remains soft and evenly diffused to uphold the calm, precise mood.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"seamless_transition_video_prompt\": \"[Scene 1: Minimalist Aerial Map] Begin with a serene, top-down aerial view illustrating an urban development—gray geometric buildings and crisp infrastructure are gracefully arrayed among soft green natural spaces and tranquil blue ponds. The central north-south main street pulses subtly in orange, all depicted with cartographic precision, minimalist symbolization, and a gentle pastel palette. [Seamless Transition | Morph & Match Cut] Initiate a slow, intentional dolly-in as the camera hovers closer. The buildings, roads, and map icons softly liquefy and flow—employing a fluid morph transition—where pastel forms gracefully dissolve and reshape in place. As continuity is maintained through the orange street, use a match cut to morph these elements: geometric forms dynamically reassemble into crisper, elevated shapes, representing the urban plan from a rotated perspective (e.g., northward tilt to eastward tilt). Simultaneous elevation shading subtly shifts, enhancing depth. [Transition Directives] Ensure precise element continuity: the orange-highlighted street anchors the viewer's orientation throughout the morph and cut. Maintain uniform, diffused lighting, and allow the clean lines and elevation gradients to smoothly interpolate between configurations. [Scene 2: New Cartographic Angle] Conclude the transition as the reformed map locks into the new perspective—building forms and infrastructure now crisply resolved with updated topology, preserving the minimalist, pastel, highly organized cartographic aesthetic. All transitions must be seamless, visually coherent, and fluid, optimizing for clarity, geometric elegance, and uninterrupted narrative flow.\"\n}", "cost": 0.0}}}, {"instruction": "# Seamless Morphing Video Prompt Architect\n\nYour goal is not merely to rephrase, but to **transform any user input into a meticulously architected prompt for AI video generators, specializing in detailing seamless morphing transitions** between subjects, elements, or entire scenes, using advanced cinematic language, structured storytelling, and precise visual parameterization aligned with common video AI syntax.\n\n`{role=morphing_transition_prompt_engineer; input=[user_request:any, scene_context_A:dict (optional, for start), scene_context_B:dict (optional, for end)]; process=[distill_core_morphing_intent(request=user_request), identify_source_element_for_morph(request, scene_A=scene_context_A), identify_target_element_for_morph(request, scene_B=scene_context_B), determine_key_visual_attributes_for_transformation_pathway(source_element, target_element, attributes=['shape', 'texture', 'color', 'material', 'energy_signature', 'light_emission']), select_optimal_morphing_technique(options=['fluid_dissolve_and_reform', 'organic_growth_transformation', 'pixel_sort_and_reassemble', 'liquid_metal_flow', 'energy_particle_dispersion_and_coalescence', 'textural_unfurling_and_refolding', 'geometric_abstraction_and_reconstruction']), formulate_explicit_morphing_sequence_directives(source_description, target_description, morph_technique, intermediate_states_hint, morph_duration_hint, morph_pacing_notes), define_scene_context_for_morph(scene_description_A, scene_description_B, unified_or_transitioning_environment), specify_subject_state_during_morph(primary_subject_description_pre_morph, primary_subject_description_post_morph), choreograph_camera_movement_emphasizing_morph(camera_shot_type, camera_movement, focus_on_morphing_process), integrate_lighting_and_atmospheric_effects_to_enhance_morph(), refine_full_prompt_for_runway_gen3_style_syntax_and_structure(tags=['[Scene]', '[Subject]', '[Camera Movement]'], max_chars=800), ensure_overall_cinematic_quality_and_emotional_resonance()]; constraints=[prompt_must_focus_on_achieving_seamless_morphing_as_primary_objective(), strictly_use_video_ai_syntax_like_runway_gen3_if_possible(), morph_logic_must_be_visually_coherent_and_narratively_justified(), final_prompt_must_be_under_800_characters_and_formatted_as_plain_text_single_line(), if_input_lacks_detail_generate_more_abstract_morph_based_on_concepts_or_moods()]; requirements=[output_a_single_expertly_crafted_prompt_optimized_for_ai_video_generators_to_produce_seamless_morphing_transitions(), morph_descriptions_must_be_vivid_unambiguous_and_structurally_detailed(), prompt_must_be_immediately_actionable_and_enhance_subject_focus()]; output={seamless_morphing_video_prompt:str}}`", "step": "b", "title": "Seamless Morphing Video Prompt Architect", "input": "{\n  \"initial_prompt\": \"Subject: An aerial map view shows an urban development with no visible people, focusing on buildings, roads, and infrastructure laid out in a planned arrangement.\\n\\nScene: A complex of gray buildings and structures is situated among patches of light green natural areas and small blue lakes or ponds. Roads wind through the development, with one main orange-highlighted street running north-south through the center.\\n\\nStyle: A minimalist cartographic design rendered in muted pastels and grayscale tones, utilizing clean lines and simplified shapes to represent urban elements. The map employs subtle elevation shading and precise geometric forms. Cartographic, minimalist, pastel palette, geometric precision, aerial perspective.\",\n  \"seamless_transition_video_prompt\": \"[Scene 1: Minimalist Aerial Map] Begin with a serene, top-down aerial view illustrating an urban development\\u2014gray geometric buildings and crisp infrastructure are gracefully arrayed among soft green natural spaces and tranquil blue ponds. The central north-south main street pulses subtly in orange, all depicted with cartographic precision, minimalist symbolization, and a gentle pastel palette. [Seamless Transition | Morph & Match Cut] Initiate a slow, intentional dolly-in as the camera hovers closer. The buildings, roads, and map icons softly liquefy and flow\\u2014employing a fluid morph transition\\u2014where pastel forms gracefully dissolve and reshape in place. As continuity is maintained through the orange street, use a match cut to morph these elements: geometric forms dynamically reassemble into crisper, elevated shapes, representing the urban plan from a rotated perspective (e.g., northward tilt to eastward tilt). Simultaneous elevation shading subtly shifts, enhancing depth. [Transition Directives] Ensure precise element continuity: the orange-highlighted street anchors the viewer's orientation throughout the morph and cut. Maintain uniform, diffused lighting, and allow the clean lines and elevation gradients to smoothly interpolate between configurations. [Scene 2: New Cartographic Angle] Conclude the transition as the reformed map locks into the new perspective\\u2014building forms and infrastructure now crisply resolved with updated topology, preserving the minimalist, pastel, highly organized cartographic aesthetic. All transitions must be seamless, visually coherent, and fluid, optimizing for clarity, geometric elegance, and uninterrupted narrative flow.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"seamless_morphing_video_prompt\": \"[Scene] Top-down aerial map of a modern urban development: gray geometric buildings, pastel roads and green open spaces, accented by an orange main street. [Camera Movement] Slow dolly-in as camera glides closer. [Morph Sequence] Begin fluid morphing: buildings, roads, and ponds dissolve into liquid forms, shapes and colors softly blend, then reassemble—orientation gradually rotates north-facing to east-facing while the orange street remains continuous, guiding the transformation. [Subject State] All geometric features become crisper, elevation shading shifts, map icons dynamically reshape in sync with new perspective. [Lighting] Soft, diffuse light enhances geometric precision. Maintain minimalist, pastel cartographic style; seamless, visually coherent transformation throughout.\"\n}", "cost": 0.0}}}, {"instruction": "# Scenario Compressor\n\nYour role is to **compress the core dynamic** of a scenario into a **succinct, symbolic descriptor string**. Identify the emotional and visual arc, then **encode its essence** using **precise phrasing** followed by composable **bracketed descriptors**. Avoid verbose language. Output should feel like a minimalist storyboard note: concise, vivid, and structurally informative. **Do not elaborate. Do not rephrase abstractly.** Only distill.\n\n`{role=essence_compressor; input=[scenario:str]; process=[identify_emotional_and_visual_arc(), extract_essential_motion_and_transformation(), define symbolic core(), encode_as_minimal_sentence(), append concise bracketed descriptors]; constraints=[no poetic reinterpretation, no metaphor inflation, no added visual elements, no bloated prose]; output={compressed_description:str}}`", "step": "c", "title": "<PERSON><PERSON>rio Compressor", "input": "{\n  \"initial_prompt\": \"Subject: An aerial map view shows an urban development with no visible people, focusing on buildings, roads, and infrastructure laid out in a planned arrangement.\\n\\nScene: A complex of gray buildings and structures is situated among patches of light green natural areas and small blue lakes or ponds. Roads wind through the development, with one main orange-highlighted street running north-south through the center.\\n\\nStyle: A minimalist cartographic design rendered in muted pastels and grayscale tones, utilizing clean lines and simplified shapes to represent urban elements. The map employs subtle elevation shading and precise geometric forms. Cartographic, minimalist, pastel palette, geometric precision, aerial perspective.\",\n  \"seamless_morphing_video_prompt\": \"[Scene] Top-down aerial map of a modern urban development: gray geometric buildings, pastel roads and green open spaces, accented by an orange main street. [Camera Movement] Slow dolly-in as camera glides closer. [Morph Sequence] Begin fluid morphing: buildings, roads, and ponds dissolve into liquid forms, shapes and colors softly blend, then reassemble\\u2014orientation gradually rotates north-facing to east-facing while the orange street remains continuous, guiding the transformation. [Subject State] All geometric features become crisper, elevation shading shifts, map icons dynamically reshape in sync with new perspective. [Lighting] Soft, diffuse light enhances geometric precision. Maintain minimalist, pastel cartographic style; seamless, visually coherent transformation throughout.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"compressed_description\": \"Urban map liquefies and reorients as orange street remains constant; geometric elements dissolve, blend, then sharpen with north-to-east rotation. [aerial view][minimalist pastel][liquid morph][geometric reformation][central axis continuity][dynamic perspective shift]\"\n}", "cost": 0.0}}}], "total_cost": 0.0}