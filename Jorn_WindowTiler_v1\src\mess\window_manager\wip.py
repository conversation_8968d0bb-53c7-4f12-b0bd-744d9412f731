# Import modules
import os
import sys
import re
import time
import ctypes
import urllib.parse
from enum import Enum

# Import pywin32 modules
import win32api
import win32com.client
import win32com.shell.shellcon as shellcon
import win32con
import win32gui
import win32process
import ctypes
import pythoncom

# Import constants
"""
Constants are not complete memory addresses, they are constant offset values
that are used in conjunction with functions like win32gui.GetWindowLong to
access specific information stored in memory, by providing the offset at which
the information is stored."
"""
from win32con import CW_USEDEFAULT
from win32con import IDI_APPLICATION
from win32con import IMAGE_ICON
from win32con import LR_DEFAULTSIZE
from win32con import LR_LOADFROMFILE
from win32con import WM_DESTROY
from win32con import WM_USER
from win32con import WS_OVERLAPPED
from win32con import WS_BORDER
from win32con import GWL_STYLE # Window style
from win32con import GWL_EXSTYLE # Extended window style

# Import ppretty (for displaying the structure of objects/classes)
from ppretty import ppretty


class WindowType(Enum):
    """
    Enumeration class to represent the different types of windows.
    - SPECIAL_FOLDER: explorer window with mapped path to CSIDL constant.
    - NORMAL_FOLDER: explorer window with a retrievable path.
    - UNSPECIFIED: window with a process other than explorer.
    - UNLINKED: window with title and class but no process.
    - UNKNOWN: any remaining windows not matched with a type.
    """
    SPECIAL_FOLDER = 1, 'SPECIAL_FOLDER'
    NORMAL_FOLDER = 2, 'NORMAL_FOLDER'
    UNSPECIFIED = 3, 'UNSPECIFIED'
    UNLINKED = 4, 'UNLINKED'
    UNKNOWN = 5, 'UNKNOWN'


def get_shell_windows_instance():
    """
    "Shell.Application" refers to a COM class that provides access
    to the top-level object of the Windows Shell (shell32.dll), which includes
    functionality related to special folders, file explorer windows, accessing
    folder view settings, desktop, taskbar, etc.

    The following steps are taken:
    - Create and return a ShellObject instance of the Shell.Application ProgID.
    - Create a ShellWindows object (contains all open shell-window instances).
    - Dictionary map {HWND:SHELL} to enable shell-instances to be reached by HWND.
    """
    shell_object_instance = win32com.client.Dispatch('Shell.Application')
    shell_window_instances = shell_object_instance.Windows()
    shell_window_mapping = {shell.HWND: shell for shell in shell_window_instances}

    """
    "Special Folder" refers to a folder represented by an interface
    rather than a specific path (e.g. 'Desktop', 'Control Panel', etc). They are
    identified by unique constants called 'CSIDL' (Constant Special Item ID List).

    The following steps are taken:
    - Use 'shellcon' to list the 'CSIDL' identifiers and retrieve their constants.
    - Use 'shell_object_instance' to create shell object namespaces for each constant.
    - Filter out any invalid namespaces (without a shell Application property).
    - Retrieve identifier ('Name') and path ('Path') from each namespace.
    - Dictionary map {'Name':'Path'} to enable path to be reached from 'title'.
    """
    CSIDL_CONSTANTS = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]
    csidl_namespaces = [shell_object_instance.Namespace(constant) for constant in CSIDL_CONSTANTS]
    valid_namespaces = [namespace for namespace in csidl_namespaces if hasattr(namespace, 'Application')]
    special_folders = [[namespace.Self.Name, namespace.Self.Path] for namespace in valid_namespaces]
    special_folders_mapping = {item[0]: item[1] for item in special_folders}

    # Return the result
    return shell_window_mapping, special_folders_mapping


# Method for retrieving additional (type-specific) window data.
def get_explorer_windows(shell_window_mapping, special_folders_mapping):
    """
    Parameters:
    - 'shell_window_mapping': Mapping of 'hwnd' to shell instance, used to
       obtain the path of the folder in case of 'NORMAL_FOLDER'.
    - 'special_folders_mapping': Mapping of 'title' to 'special folder' path, used to
       obtain the path of folder in case of 'SPECIAL_FOLDER'.

    This function updates the following instance variables:
    - 'process': The process associated with the window.
    - 'process_id': The process id associated with the window.
    - 'type': The type of the window ('SPECIAL_FOLDER', 'NORMAL_FOLDER', 'UNSPECIFIED', 'UNLINKED', 'UNKNOWN').
    - 'create_cmd': The command to open the window, if applicable.
    """
    # If this is a Windows File-Explorer Window (typically a folder/directory)
    hwnd_class = win32gui.GetClassName(hwnd)
    if hwnd_class == 'CabinetWClass':
        # Retrieve the folder path through its shell instance
        hwnd_shell_instance = shell_window_mapping[hwnd]
        hwnd_shell_path = hwnd_shell_instance.LocationURL

        # If it's a 'SPECIAL_FOLDER' (explorer window without retrievable path):
        # - Update the instance variable 'type' with the identifier 'SPECIAL_FOLDER'.
        # - Check if the path refers to a GUID (global unique identification number).
        # - Transform the path into a cmd-command (using 'Shell:{GUID}' or 'File:/URI'),
        #   this is to make the path executable (in that it creates the actual window).
        # - Update the instance variable 'create_cmd' with the modified path-command.
        if hwnd_title in special_folders_mapping:
            hwnd_type = WindowType.SPECIAL_FOLDER
            folder_path = special_folders_mapping[hwnd_title]
            folder_path_guid_match = bool(re.search(r'::{[\w-]*}', folder_path))
            folder_path_is_guid = folder_path_guid_match if not os.path.exists(folder_path) else False
            command_prefix = 'Shell:' if folder_path_is_guid else 'File:/'
            create_command = os.path.normpath(urllib.parse.unquote(f'{command_prefix}{folder_path}'))
            hwnd_create_cmd = create_command

        # Else it's a 'NORMAL_FOLDER' (explorer window with a retrievable path):
        elif (hwnd_shell_path != ''):
            # Update the instance variable 'type' with the identifier 'NORMAL_FOLDER'.
            hwnd_type = WindowType.NORMAL_FOLDER
            # Update the instance variable 'create_cmd' with the path (URI).
            hwnd_create_cmd = os.path.normpath(urllib.parse.unquote(hwnd_shell_path))
            # Update the instance variables for folder view options.
            folder_obj = hwnd_shell_instance.Document
            folder_icon_size = folder_obj.IconSize
            folder_view_mode = folder_obj.CurrentViewMode
            folder_sort_column = folder_obj.SortColumns
            folder_group_by = folder_obj.GroupBy
            print(folder_group_by)
            # Update the instance variables for files in folder.
            folder_selected_files = [file.Name for file in folder_obj.SelectedItems()]
            folder_all_files = [file.Name for file in folder_obj.Folder.Items()]
            folder_focused_file = folder_obj.FocusedItem.Name if folder_obj.FocusedItem else None


    # If a title and class was found but no type has been retrieved (no associated window process):
    # - Set instance variable 'type' to 'UNLINKED'.
    if hwnd_title and hwnd_class and not hwnd_type:
        hwnd_type = WindowType.UNLINKED


# The 'Window' class represents a window object and holds its properties and methods.
# Each instance stores data on one single window
class Window:
    # Generate a Window object (from 'HWND')
    def __init__(self, hwnd, shell_window_mapping, special_folders_mapping):
        # Generate common window data.
        self.hwnd = hwnd
        self.hwnd_enabled = win32gui.IsWindowEnabled(hwnd)
        self.hwnd_visible = win32gui.IsWindowVisible(hwnd)
        self.hwnd_title = win32gui.GetWindowText(hwnd)
        self.hwnd_class = win32gui.GetClassName(hwnd)
        self._hwnd_placement = win32gui.GetWindowPlacement(hwnd)
        self._hwnd_rect = win32gui.GetWindowRect(hwnd)
        self.hwnd_controls_state = self._hwnd_placement[1]
        self.hwnd_position = (self._hwnd_rect[0], self._hwnd_rect[1])
        self.hwnd_size = ((self._hwnd_rect[2]-self._hwnd_rect[0]), (self._hwnd_rect[3]-self._hwnd_rect[1]))

        # Prepare instance variables for the window type and create-command.
        self.hwnd_type = None
        self.hwnd_create_cmd = None

        # Prepare instance variables for (explorer) folder view options.
        self.folder_icon_size = None
        self.folder_view_mode = None
        self.folder_sort_column = None
        self.folder_group_by = None
        # Prepare instance variable for file selection in folder.
        self.folder_selected_files = None
        self.folder_all_files = None
        self.folder_focused_file = None

        # Prepare instance variables for the window process.
        self.hwnd_process = None
        self.hwnd_process_id = None


        # Retrieve the process handle of the window
        hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(self.hwnd)
        hwnd_process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
        hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(hwnd_process_query, False, hwnd_process_id)
        # If a process handle was optained
        if hwnd_process_handle:
            # Update instance variables with the executable path and id
            self.hwnd_process = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
            self.hwnd_process_id = hwnd_process_id
            # Update 'type' with the identifier 'UNSPECIFIED'.
            self.hwnd_type = WindowType.UNSPECIFIED
        # Else if no process was retrieved, set 'type' to 'UNLINKED'.
        elif not self.hwnd_type:
            self.hwnd_type = WindowType.UNLINKED


        # If this is a Windows File-Explorer Window (typically a folder/directory)
        if self.hwnd_class == 'CabinetWClass':
            # Retrieve the folder path through its shell instance
            hwnd_shell_instance = shell_window_mapping[self.hwnd]
            hwnd_shell_path = hwnd_shell_instance.LocationURL

            # If it's a 'SPECIAL_FOLDER' (explorer window without retrievable path):
            # - Update the instance variable 'type' with the identifier 'SPECIAL_FOLDER'.
            # - Check if the path refers to a GUID (global unique identification number).
            # - Transform the path into a cmd-command (using 'Shell:{GUID}' or 'File:/URI'),
            #   this is to make the path executable (in that it creates the actual window).
            # - Update the instance variable 'create_cmd' with the modified path-command.
            if self.hwnd_title in special_folders_mapping:
                self.hwnd_type = WindowType.SPECIAL_FOLDER
                folder_path = special_folders_mapping[self.hwnd_title]
                folder_path_guid_match = bool(re.search(r'::{[\w-]*}', folder_path))
                folder_path_is_guid = folder_path_guid_match if not os.path.exists(folder_path) else False
                command_prefix = 'Shell:' if folder_path_is_guid else 'File:/'
                create_command = os.path.normpath(urllib.parse.unquote(f'{command_prefix}{folder_path}'))
                self.hwnd_create_cmd = create_command

            # Else it's a 'NORMAL_FOLDER' (explorer window with a retrievable path):
            # - Update the instance variable 'type' with the identifier 'NORMAL_FOLDER'.
            # - Update the instance variable 'create_cmd' with the path (URI).
            # - Update instance variables for folder view options.
            # - Update instance variables for files in folder.
            elif (hwnd_shell_path != ''):
                self.hwnd_type = WindowType.NORMAL_FOLDER
                self.hwnd_create_cmd = os.path.normpath(urllib.parse.unquote(hwnd_shell_path))
                folder_obj = hwnd_shell_instance.Document
                self.folder_icon_size = folder_obj.IconSize
                self.folder_view_mode = folder_obj.CurrentViewMode
                self.folder_sort_column = folder_obj.SortColumns
                self.folder_group_by = folder_obj.GroupBy
                self.folder_selected_files = [file.Name for file in folder_obj.SelectedItems()]
                self.folder_all_files = [file.Name for file in folder_obj.Folder.Items()]
                self.folder_focused_file = folder_obj.FocusedItem.Name if folder_obj.FocusedItem else None

        # If a title and class was found but no type has been retrieved (no associated window process):
        # - Set instance variable 'type' to 'UNLINKED'.
        if self.hwnd_title and self.hwnd_class and not self.hwnd_type:
            self.hwnd_type = WindowType.UNLINKED



# GLOBAL FN: Creates a Windows Shell COM object instance and retrieve 'special folder' paths.









# '''
# Import modules
import os
import sys
import re
import time
import random
import ctypes
import urllib.parse
import json
from enum import Enum
# Error handling
# import traceback
# import pywintypes

# Import pywin32 modules
import win32com.client
import win32com.shell.shellcon as shellcon
import win32con
import win32gui
import win32api
import win32process


def get_window_styles(hwnd):
    styles = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
    ex_styles = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
    return {
        "styles": styles,
        "extended_styles": ex_styles
    }

def get_process_info(hwnd):

    # CTYPES: Retrieve the process handle of the window
    hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
    hwnd_process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
    hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(hwnd_process_query, False, hwnd_process_id)
    # If a process handle was optained
    if hwnd_process_handle:
        try:
            hwnd_process_path = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
        except pywintypes.error:
            print(traceback.format_exc())


    """
    Retrieves information about the process owning the window.

    Args:
    process_id (int): Process ID.

    Returns:
    dict: A dictionary containing process information.
    """
    try:
        process_handle = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ, False, process_id)
        process_path = win32process.GetModuleFileNameEx(process_handle, 0)
        return {
            "process_id": process_id,
            "process_path": process_path
        }
    except Exception as e:
        return {
            "process_id": process_id,
            "error": str(e)
        }

def get_window_category(hwnd):
    # Categorizes the window based on its class and visibility.
    hwnd_visibility = win32gui.IsWindowVisible(hwnd)
    hwnd_class = win32gui.GetClassName(hwnd)
    if hwnd_class.startswith("tooltips_class32"):
        return "Tooltip"
    elif hwnd_class == "Shell_TrayWnd":
        return "Taskbar"
    elif hwnd_visibility and not hwnd_class.startswith("SysShadow"):
        return "Normal"
    else:
        return "Other"

# ... [rest of your code] ...


# get window data
def get_hwnd_data(hwnd):
    # Get the current monitor
    monitor_handle = win32api.MonitorFromWindow(hwnd, win32con.MONITOR_DEFAULTTONEAREST)
    monitor_info = win32api.GetMonitorInfo(monitor_handle)
    monitor_device = (monitor_info["Device"])

    # Get general window data: Intermediate values used to compute state/position/size
    hwnd_placement = win32gui.GetWindowPlacement(hwnd)
    hwnd_rect = win32gui.GetWindowRect(hwnd)
    hwnd_controls_state = hwnd_placement[1]
    hwnd_position = (hwnd_rect[0], hwnd_rect[1])
    hwnd_size = (hwnd_rect[2] - hwnd_rect[0], hwnd_rect[3] - hwnd_rect[1])

    # Get general hwnd (Error if 'GetWindowPlacement' or 'GetWindowRect')
    hwnd_visibility_state = win32gui.IsWindowVisible(hwnd)
    hwnd_title = win32gui.GetWindowText(hwnd)
    hwnd_class = win32gui.GetClassName(hwnd)

    # Categorize the window
    hwnd_category = get_window_category(hwnd_class, hwnd_visibility_state)

    # Get window styles
    window_styles = get_window_styles(hwnd)

    # Get process information
    hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
    process_info = get_process_info(hwnd_process_id)


    # Prepare data for windows with a process handle
    hwnd_process_path = None
    hwnd_process_id = None

    # win32api: Retrieve the executable filename by accessing the current window's process
    # hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
    # hwnd_process_handle = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION, False, hwnd_process_id)

    # CTYPES: Retrieve the process handle of the window
    hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
    hwnd_process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
    hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(hwnd_process_query, False, hwnd_process_id)
    # If a process handle was optained
    if hwnd_process_handle:
        try:
            hwnd_process_path = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
        except pywintypes.error:
            print(traceback.format_exc())


    # Return a dictionary with keys and values for each piece of information
    return {
        "hwnd":hwnd,
        "title":hwnd_title,
        "class":hwnd_class,
        "monitor":monitor_device,
        "visibility":hwnd_visibility_state,
        "controls_state":hwnd_controls_state,
        "position":hwnd_position,
        "size":hwnd_size,
        "placement":hwnd_placement,
        "rect":hwnd_rect,
        "process_path":hwnd_process_path,
        "process_id":hwnd_process_id,
        "category": hwnd_category,
        "styles": window_styles,
        "process_info": process_info,
    }

def get_all_windows():
    """Return a list of all windows."""
    windows = []
    def enum_windows(hwnd, result):
        windows.append(get_hwnd_data(hwnd))
    win32gui.EnumWindows(enum_windows, [])
    return windows




def write_windows_to_json(file_path):
    try:
        windows_data = get_all_windows()
        with open(file_path, 'w') as file:
            json.dump(windows_data, file, indent=4)
        print(f"Data successfully written to {file_path}")
    except Exception as e:
        print(f"Error occurred while writing to JSON: {e}")

# Example usage
write_windows_to_json("window_data.json")

# windows = get_all_windows()
# for x in windows:
#     # print(x.keys())
#     print(x)
time.sleep(99999)

def filter_windows(windows, only_visible=True, window_text=None, window_class=None, ignore_case=True):
    """Filter a list of windows based on the provided criteria."""
    filtered_windows = []
    for hwnd, title, class_name in windows:
        if only_visible and not win32gui.IsWindowVisible(hwnd):
            continue
        if title == "":
            continue
        if window_class is not None:
            if ignore_case and class_name.lower() != window_class.lower():
                continue
            elif not ignore_case and class_name != window_class:
                continue
        if window_text is not None:
            try:
                if ignore_case and not re.search(window_text, title, re.IGNORECASE):
                    continue
                elif not ignore_case and not re.search(window_text, title):
                    continue
            except re.error:
                raise ValueError(f"Invalid regular expression: {window_text}")
        filtered_windows.append((hwnd, title, class_name))
    return filtered_windows




def get_monitor_info(monitor_index):
    """Return information about a specific monitor."""
    monitors = win32api.EnumDisplayMonitors(None, None)
    primary_monitor_index = None
    for i, monitor in enumerate(monitors):
        monitor_info = win32api.GetMonitorInfo(monitor[0])
        if monitor_info['Flags'] == 1:  # This is the primary monitor
            primary_monitor_index = i
            break
    if primary_monitor_index is None:
        raise ValueError("Could not find primary monitor")
    adjusted_monitor_index = primary_monitor_index + monitor_index
    if adjusted_monitor_index < 0 or adjusted_monitor_index >= len(monitors):
        raise ValueError("Invalid monitor index")
    monitor_info = win32api.GetMonitorInfo(monitors[adjusted_monitor_index][0])
    return monitor_info



def calculate_window_position(i, rows, columns, screen_width, screen_height, column_ratios, row_ratios, monitor_info):
    """Calculate the position and size of a window."""
    row = i // columns
    col = i % columns

    if column_ratios is not None:
        window_width = int(screen_width * column_ratios[col])
        x_position = int(sum(column_ratios[:col]) * screen_width) + monitor_info['Monitor'][0]
    else:
        window_width = screen_width // columns
        x_position = col * window_width + monitor_info['Monitor'][0]

    if row_ratios is not None:
        window_height = int(screen_height * row_ratios[row])
        y_position = int(sum(row_ratios[:row]) * screen_height) + monitor_info['Monitor'][1]
    else:
        window_height = screen_height // rows
        y_position = row * window_height + monitor_info['Monitor'][1]

    return x_position, y_position, window_width, window_height

# -----------------------------------------------------------------------------


def tile_windows(windows, rows, columns, row_ratios=None, column_ratios=None, monitor_index=0, num_windows=None):
    """Tile windows on the screen based on the provided criteria."""
    if rows == 0 or columns == 0:
        raise ValueError("Number of rows and columns must be greater than 0")
    if column_ratios is not None and (len(column_ratios) != columns or sum(column_ratios) != 1):
        raise ValueError("Invalid column ratios")
    if row_ratios is not None and (len(row_ratios) != rows or sum(row_ratios) != 1):
        raise ValueError("Invalid row ratios")

    monitor_info = get_monitor_info(monitor_index)
    screen_width = monitor_info['Monitor'][2] - monitor_info['Monitor'][0]
    screen_height = monitor_info['Monitor'][3] - monitor_info['Monitor'][1]

    num_windows = num_windows if num_windows is not None else (rows * columns)
    windows = windows[:num_windows]

    for i, (hwnd, title, class_name) in enumerate(windows):
        try:
            # Ensure the window is in normal state
            # win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
            x_position, y_position, window_width, window_height = calculate_window_position(i, rows, columns, screen_width, screen_height, column_ratios, row_ratios, monitor_info)
            # Set the window size and position without altering the z-order and ensure it is shown
            win32gui.SetWindowPos(hwnd, 0, x_position, y_position, window_width, window_height, win32con.SWP_NOZORDER | win32con.SWP_SHOWWINDOW)
        except:
            pass


def set_window_properties(windows, foreground=False, attribute='normal', topmost=False):
    """
    Sets various properties for the windows

    :param windows: list of tuples, each containing the handle, title, and class name of a window
    :param foreground: boolean, if True the window is brought to the foreground
    :param attribute: str, can be 'maximized', 'minimized', or 'normal'. This sets the window state
    :param topmost: boolean, if True, the window is set to be always on top
    :return: None
    """

    # Set up the attribute dictionary
    attributes = {
        'normal': win32con.SW_SHOWNORMAL,
        'minimized': win32con.SW_SHOWMINIMIZED,
        'maximized': win32con.SW_SHOWMAXIMIZED,
    }

    # Check if the provided attribute is valid
    if attribute not in attributes:
        raise ValueError("Invalid attribute. Must be one of 'maximized', 'minimized', or 'normal'.")

    # Set the properties for each window
    for hwnd, title, class_name in windows:
        # Get the current window placement
        placement = win32gui.GetWindowPlacement(hwnd)

        # Modify the "show command" in the placement
        placement = list(placement)
        placement[1] = attributes[attribute]
        placement = tuple(placement)

        # Set the new window placement
        win32gui.SetWindowPlacement(hwnd, placement)

        # Set the window to the foreground
        if foreground:
            win32gui.SetForegroundWindow(hwnd)

        # Set the window to be always on top
        if topmost is not None:
            win32gui.SetWindowPos(hwnd, win32con.HWND_TOPMOST if topmost else win32con.HWND_NOTOPMOST,
                                  0, 0, 0, 0, win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)






# -----------------------------------------------------------------------------
# Find and close duplicate file-explorer-windows (pointing to the same path)
# -----------------------------------------------------------------------------
windows = get_all_windows()
explorer_windows = filter_windows(windows, only_visible=False, window_text=None, window_class='CabinetWClass', ignore_case=True)
explorer_windows = sorted(explorer_windows, key=lambda window: window[1])

windows_to_keep_dict = {window[1]: window for window in explorer_windows}
windows_to_keep = list(windows_to_keep_dict.values())
windows_to_close = [window for window in explorer_windows if window not in windows_to_keep]

if len(windows_to_keep):
    [print(f'- windows_to_keep  : {hwnd}') for hwnd in windows_to_keep]
if len(windows_to_close):
    [print(f'- windows_to_close : {hwnd}') for hwnd in windows_to_close]

for window in windows_to_close:
    win32gui.PostMessage(window[0], win32con.WM_CLOSE, 0, 0)
# -----------------------------------------------------------------------------




# -----------------------------------------------------------------------------
# Autotile all windows
# -----------------------------------------------------------------------------
all_windows = filter_windows(windows, only_visible=False, window_text=None, window_class=None, ignore_case=True)
[print(f'- all_windows  : {hwnd}') for hwnd in all_windows]
tile_windows(all_windows, rows=2, columns=3, row_ratios=None, column_ratios=None, monitor_index=0, num_windows=None)

# print(windows_to_keep)
time.sleep(99999)
print(windows_to_keep)
print('\n')
print(windows_to_close)
print('\n')
# time.sleep(99999)

# Get all explorer windows
windows = get_all_windows()
explorer_windows = filter_windows(windows, only_visible=False, window_text=None, window_class='CabinetWClass', ignore_case=True)

# Order the windows by paths




# Bring to foreground
# set_window_properties(ordered_windows, foreground=True, attribute='normal', topmost=False)


# Tile the windows
tile_windows(ordered_windows, rows=2, columns=3, row_ratios=None, column_ratios=None, monitor_index=0, num_windows=None)



'''
