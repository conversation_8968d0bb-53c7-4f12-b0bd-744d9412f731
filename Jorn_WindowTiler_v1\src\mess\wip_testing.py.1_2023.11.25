
# Import modules
import os
import sys
import re
import time
import random
import ctypes
import urllib.parse
import json
import math
from enum import Enum
# Error handling
# import traceback
# import pywintypes

# Import pywin32 modules
import win32com.client
import win32com.shell.shellcon as shellcon

from win32con import (
    MONITOR_DEFAULTTONEAREST,
)

import win32gui
import win32api
import win32process


import logging

from rich.console import Console
from rich.table import Table

from win32api import (
    MonitorFromWindow,
    GetMonitorInfo,
)

from win32gui import (
  IsWindowVisible,
  IsIconic,
  GetWindowText,
  GetClassName,
  GetWindowLong,
  EnumWindows,
  MoveWindow,
)
from win32con import (
    GWL_STYLE,
    GWL_EXSTYLE,
)

# Setup logging
logging.basicConfig(level=logging.INFO)



# hwnd.get


class WindowManager:
    def __init__(self):
        self.windows = {}
        self.console = Console()

    def _enum_windows_proc(self, hwnd, _):

        # monitor_handle = MonitorFromWindow(hwnd, MONITOR_DEFAULTTONEAREST)
        # monitor_info = GetMonitorInfo(monitor_handle)
        # w_monitor_index = int("".join(filter(str.isdigit, monitor_info["Device"])))
        # w_monitor_rect = (monitor_info["Monitor"])

        monitor_handle = MonitorFromWindow(hwnd, MONITOR_DEFAULTTONEAREST)
        monitor_info = GetMonitorInfo(monitor_handle)
        # print((monitor_info.keys()))
        # print(dir(monitor_info))
        # print(monitor_info["Monitor"])
        print(monitor_info["Device"])
        monitor_device = (monitor_info["Device"])
        monitor_index = "".join(filter(str.isdigit, monitor_device))
        # monitor_index = ''.join(re.findall(r'\d+$', monitor_device))


        print(monitor_index)
# extract_digits = lambda text: re.findall(r'\d+$', text)[-1] if re.findall(r'\d+$', text) else None

        # print(monitor_info)
        # print(monitor_device)
        self.windows[hwnd] = {
            'visible': IsWindowVisible(hwnd),
            'minimized': IsIconic(hwnd),
            'title': GetWindowText(hwnd),
            'class': GetClassName(hwnd),
            'style': GetWindowLong(hwnd, GWL_STYLE),
            'ex_style': GetWindowLong(hwnd, GWL_EXSTYLE),
        }

    def get_all_windows(self):
        EnumWindows(self._enum_windows_proc, None)
        return self.windows

    def filter_windows(self, visible_only=True, normal_windows_only=True):
        filtered = {}
        for hwnd, attributes in self.windows.items():
            if visible_only and not attributes['visible']:
                continue
            if normal_windows_only:
                if not attributes['title']:
                    continue
                if attributes['class'].startswith(("tooltips_class32", "Shell_TrayWnd")):
                    continue

            filtered[hwnd] = attributes
        return filtered

    # def sort_windows(self, windows, sort_criteria):
    #     """
    #     Sorts windows based on given criteria.
    #     :param windows: Dictionary of windows to sort.
    #     :param sort_criteria: List of tuples with ('field_name', 'order') where order is 'asc' or 'desc'.
    #     :return: Sorted list of windows.
    #     """
    #     for field, order in reversed(sort_criteria):
    #         reverse = order == 'desc'
    #         windows = sorted(windows.items(), key=lambda x: x[1].get(field, ''), reverse=reverse)
    #     return dict(windows)


    def sort_windows(self, windows, sort_criteria):
        """
        Sorts windows based on given criteria.
        :param windows: Dictionary of windows to sort.
        :param sort_criteria: List of tuples with ('field_name', 'order') where order is 'asc' or 'desc'.
        :return: Sorted list of windows.
        """
        sorted_keys = sorted(windows.keys(), key=lambda x: tuple(windows[x].get(field, '') for field, order in sort_criteria), reverse=any(order == 'desc' for field, order in sort_criteria))

        return {k: windows[k] for k in sorted_keys}

    def print_window_data(self, windows):
        table = Table(title="Windows Information")

        table.add_column("HWND", justify="right")
        table.add_column("Title")
        table.add_column("Visible")
        table.add_column("Minimized")
        table.add_column("Class")
        table.add_column("Style", justify="right")
        table.add_column("Extended Style", justify="right")

        for hwnd, info in windows.items():
            table.add_row(
                str(hwnd),
                info['title'],
                str(info['visible']),
                str(info['minimized']),
                info['class'],
                str(info['style']),
                str(info['ex_style'])
            )

        self.console.print(table)

# Example usage
window_manager = WindowManager()
all_windows = window_manager.get_all_windows()
sorted_windows = window_manager.sort_windows(all_windows, [('visible', 'desc'), ('class', 'desc'), ('title', 'desc')])

# filtered_windows = window_manager.filter_windows()
window_manager.print_window_data(sorted_windows)









#     def log_window_data(self, windows):
#         for hwnd, info in windows.items():
#             logging.info(f"HWND: {hwnd}, Title: {info['title']}, Visible: {info['visible']}, "
#                          f"Minimized: {info['minimized']}, Class: {info['class']}, "
#                          f"Style: {info['style']}, Extended Style: {info['ex_style']}")

# # Example usage
# window_manager = WindowManager()
# all_windows = window_manager.get_all_windows()
# filtered_windows = window_manager.filter_windows()
# window_manager.log_window_data(filtered_windows)

# # Utility function to get window rectangle
# def get_window_rect(hwnd):
#     rect = win32gui.GetWindowRect(hwnd)
#     return {'left': rect[0], 'top': rect[1], 'right': rect[2], 'bottom': rect[3]}

# # Function to get detailed information about all windows
# def get_all_windows():
#     windows = {}
#     def enum_windows(hwnd, result):
#         windows[hwnd] = {
#             'visible': win32gui.IsWindowVisible(hwnd),
#             'minimized': win32gui.IsIconic(hwnd),
#             'title': win32gui.GetWindowText(hwnd),
#             'class': win32gui.GetClassName(hwnd),
#             'style': win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE),
#             'ex_style': win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE),
#             'rect': get_window_rect(hwnd)
#         }
#     win32gui.EnumWindows(enum_windows, [])
#     return windows

# # Function to filter windows based on given criteria
# def filter_windows(windows, normal_windows_only=True):
#     filtered = {}
#     for hwnd, attributes in windows.items():
#         if normal_windows_only:
#             # Filter out non-visible, non-titled, and specific class windows
#             if not attributes['visible'] or not attributes['title']:
#                 continue
#             if attributes['class'].startswith(("tooltips_class32", "Shell_TrayWnd")):
#                 continue

#         # Additional filters can be added here based on title, class, etc.
#         filtered[hwnd] = attributes
#     return filtered

# # Function to resize a window
# def resize_window(hwnd, width, height):
#     rect = win32gui.GetWindowRect(hwnd)
#     win32gui.MoveWindow(hwnd, rect[0], rect[1], width, height, True)

# # Function to move a window
# def move_window(hwnd, x, y):
#     rect = win32gui.GetWindowRect(hwnd)
#     win32gui.MoveWindow(hwnd, x, y, rect[2] - rect[0], rect[3] - rect[1], True)

# # Example usage
# all_windows = get_all_windows()
# filtered_windows = filter_windows(all_windows)

# for hwnd, info in filtered_windows.items():
#     print(f"Window Style: {info['style']}, Extended Style: {info['ex_style']}")
#     print(f"Window Rect: {info['rect']}")
    # Example operations on a specific window
    # resize_window(hwnd, 800, 600)
    # move_window(hwnd, 100, 100)

'''


def get_window_styles(hwnd):
    styles = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
    ex_styles = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
    return {
        "styles": styles,
        "extended_styles": ex_styles
    }

def get_process_info(hwnd):

    # CTYPES: Retrieve the process handle of the window
    hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
    hwnd_process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
    hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(hwnd_process_query, False, hwnd_process_id)
    # If a process handle was optained
    if hwnd_process_handle:
        try:
            hwnd_process_path = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
        except pywintypes.error:
            print(traceback.format_exc())


    """
    Retrieves information about the process owning the window.

    Args:
    process_id (int): Process ID.

    Returns:
    dict: A dictionary containing process information.
    """
    try:
        process_handle = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ, False, process_id)
        process_path = win32process.GetModuleFileNameEx(process_handle, 0)
        return {
            "process_id": process_id,
            "process_path": process_path
        }
    except Exception as e:
        return {
            "process_id": process_id,
            "error": str(e)
        }

def get_window_category(hwnd):
    # Categorizes the window based on its class and visibility.
    hwnd_visibility = win32gui.IsWindowVisible(hwnd)
    hwnd_class = win32gui.GetClassName(hwnd)
    if hwnd_class.startswith("tooltips_class32"):
        return "Tooltip"
    elif hwnd_class == "Shell_TrayWnd":
        return "Taskbar"
    elif hwnd_visibility and not hwnd_class.startswith("SysShadow"):
        return "Normal"
    else:
        return "Other"

# ... [rest of your code] ...


# get window data
def get_hwnd_data(hwnd):
    # Get the current monitor
    monitor_handle = MonitorFromWindow(hwnd, MONITOR_DEFAULTTONEAREST)
    monitor_info = GetMonitorInfo(monitor_handle)
    monitor_device = (monitor_info["Device"])

    # Get general window data: Intermediate values used to compute state/position/size
    hwnd_placement = win32gui.GetWindowPlacement(hwnd)
    hwnd_rect = win32gui.GetWindowRect(hwnd)
    hwnd_controls_state = hwnd_placement[1]
    hwnd_position = (hwnd_rect[0], hwnd_rect[1])
    hwnd_size = (hwnd_rect[2] - hwnd_rect[0], hwnd_rect[3] - hwnd_rect[1])

    # Get general hwnd (Error if 'GetWindowPlacement' or 'GetWindowRect')
    hwnd_visibility_state = win32gui.IsWindowVisible(hwnd)
    hwnd_title = win32gui.GetWindowText(hwnd)
    hwnd_class = win32gui.GetClassName(hwnd)

    # Categorize the window
    hwnd_category = get_window_category(hwnd)

    # Get window styles
    window_styles = get_window_styles(hwnd)

    # Get process information
    hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
    process_info = "get_process_info(hwnd)"


    # Prepare data for windows with a process handle
    hwnd_process_path = None
    hwnd_process_id = None

    # win32api: Retrieve the executable filename by accessing the current window's process
    # hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
    # hwnd_process_handle = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION, False, hwnd_process_id)

    # CTYPES: Retrieve the process handle of the window
    hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
    hwnd_process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
    hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(hwnd_process_query, False, hwnd_process_id)
    # If a process handle was optained
    if hwnd_process_handle:
        try:
            hwnd_process_path = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
        except pywintypes.error:
            print(traceback.format_exc())


    # Return a dictionary with keys and values for each piece of information
    return {
        "hwnd":hwnd,
        "title":hwnd_title,
        "class":hwnd_class,
        "monitor":monitor_device,
        "visibility":hwnd_visibility_state,
        "controls_state":hwnd_controls_state,
        "position":hwnd_position,
        "size":hwnd_size,
        "placement":hwnd_placement,
        "rect":hwnd_rect,
        "process_path":hwnd_process_path,
        "process_id":hwnd_process_id,
        "category": hwnd_category,
        "styles": window_styles,
        "process_info": process_info,
    }




def write_windows_to_json(file_path):
    try:
        windows_data = get_all_windows()
        with open(file_path, 'w') as file:
            json.dump(windows_data, file, indent=4)
        print(f"Data successfully written to {file_path}")
    except Exception as e:
        print(f"Error occurred while writing to JSON: {e}")

# Example usage
write_windows_to_json("window_data.json")

# windows = get_all_windows()
# for x in windows:
#     # print(x.keys())
#     print(x)
time.sleep(99999)

def filter_windows(windows, only_visible=True, window_text=None, window_class=None, ignore_case=True):
    """Filter a list of windows based on the provided criteria."""
    filtered_windows = []
    for hwnd, title, class_name in windows:
        if only_visible and not win32gui.IsWindowVisible(hwnd):
            continue
        if title == "":
            continue
        if window_class is not None:
            if ignore_case and class_name.lower() != window_class.lower():
                continue
            elif not ignore_case and class_name != window_class:
                continue
        if window_text is not None:
            try:
                if ignore_case and not re.search(window_text, title, re.IGNORECASE):
                    continue
                elif not ignore_case and not re.search(window_text, title):
                    continue
            except re.error:
                raise ValueError(f"Invalid regular expression: {window_text}")
        filtered_windows.append((hwnd, title, class_name))
    return filtered_windows




def get_monitor_info(monitor_index):
    """Return information about a specific monitor."""
    monitors = win32api.EnumDisplayMonitors(None, None)
    primary_monitor_index = None
    for i, monitor in enumerate(monitors):
        monitor_info = GetMonitorInfo(monitor[0])
        if monitor_info['Flags'] == 1:  # This is the primary monitor
            primary_monitor_index = i
            break
    if primary_monitor_index is None:
        raise ValueError("Could not find primary monitor")
    adjusted_monitor_index = primary_monitor_index + monitor_index
    if adjusted_monitor_index < 0 or adjusted_monitor_index >= len(monitors):
        raise ValueError("Invalid monitor index")
    monitor_info = GetMonitorInfo(monitors[adjusted_monitor_index][0])
    return monitor_info



def calculate_window_position(i, rows, columns, screen_width, screen_height, column_ratios, row_ratios, monitor_info):
    """Calculate the position and size of a window."""
    row = i // columns
    col = i % columns

    if column_ratios is not None:
        window_width = int(screen_width * column_ratios[col])
        x_position = int(sum(column_ratios[:col]) * screen_width) + monitor_info['Monitor'][0]
    else:
        window_width = screen_width // columns
        x_position = col * window_width + monitor_info['Monitor'][0]

    if row_ratios is not None:
        window_height = int(screen_height * row_ratios[row])
        y_position = int(sum(row_ratios[:row]) * screen_height) + monitor_info['Monitor'][1]
    else:
        window_height = screen_height // rows
        y_position = row * window_height + monitor_info['Monitor'][1]

    return x_position, y_position, window_width, window_height

# -----------------------------------------------------------------------------


def tile_windows(windows, rows, columns, row_ratios=None, column_ratios=None, monitor_index=0, num_windows=None):
    """Tile windows on the screen based on the provided criteria."""
    if rows == 0 or columns == 0:
        raise ValueError("Number of rows and columns must be greater than 0")
    if column_ratios is not None and (len(column_ratios) != columns or sum(column_ratios) != 1):
        raise ValueError("Invalid column ratios")
    if row_ratios is not None and (len(row_ratios) != rows or sum(row_ratios) != 1):
        raise ValueError("Invalid row ratios")

    monitor_info = get_monitor_info(monitor_index)
    screen_width = monitor_info['Monitor'][2] - monitor_info['Monitor'][0]
    screen_height = monitor_info['Monitor'][3] - monitor_info['Monitor'][1]

    num_windows = num_windows if num_windows is not None else (rows * columns)
    windows = windows[:num_windows]

    for i, (hwnd, title, class_name) in enumerate(windows):
        try:
            # Ensure the window is in normal state
            # win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
            x_position, y_position, window_width, window_height = calculate_window_position(i, rows, columns, screen_width, screen_height, column_ratios, row_ratios, monitor_info)
            # Set the window size and position without altering the z-order and ensure it is shown
            win32gui.SetWindowPos(hwnd, 0, x_position, y_position, window_width, window_height, win32con.SWP_NOZORDER | win32con.SWP_SHOWWINDOW)
        except:
            pass


def set_window_properties(windows, foreground=False, attribute='normal', topmost=False):
    """
    Sets various properties for the windows

    :param windows: list of tuples, each containing the handle, title, and class name of a window
    :param foreground: boolean, if True the window is brought to the foreground
    :param attribute: str, can be 'maximized', 'minimized', or 'normal'. This sets the window state
    :param topmost: boolean, if True, the window is set to be always on top
    :return: None
    """

    # Set up the attribute dictionary
    attributes = {
        'normal': win32con.SW_SHOWNORMAL,
        'minimized': win32con.SW_SHOWMINIMIZED,
        'maximized': win32con.SW_SHOWMAXIMIZED,
    }

    # Check if the provided attribute is valid
    if attribute not in attributes:
        raise ValueError("Invalid attribute. Must be one of 'maximized', 'minimized', or 'normal'.")

    # Set the properties for each window
    for hwnd, title, class_name in windows:
        # Get the current window placement
        placement = win32gui.GetWindowPlacement(hwnd)

        # Modify the "show command" in the placement
        placement = list(placement)
        placement[1] = attributes[attribute]
        placement = tuple(placement)

        # Set the new window placement
        win32gui.SetWindowPlacement(hwnd, placement)

        # Set the window to the foreground
        if foreground:
            win32gui.SetForegroundWindow(hwnd)

        # Set the window to be always on top
        if topmost is not None:
            win32gui.SetWindowPos(hwnd, win32con.HWND_TOPMOST if topmost else win32con.HWND_NOTOPMOST,
                                  0, 0, 0, 0, win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)






# -----------------------------------------------------------------------------
# Find and close duplicate file-explorer-windows (pointing to the same path)
# -----------------------------------------------------------------------------
windows = get_all_windows()
explorer_windows = filter_windows(windows, only_visible=False, window_text=None, window_class='CabinetWClass', ignore_case=True)
explorer_windows = sorted(explorer_windows, key=lambda window: window[1])

windows_to_keep_dict = {window[1]: window for window in explorer_windows}
windows_to_keep = list(windows_to_keep_dict.values())
windows_to_close = [window for window in explorer_windows if window not in windows_to_keep]

if len(windows_to_keep):
    [print(f'- windows_to_keep  : {hwnd}') for hwnd in windows_to_keep]
if len(windows_to_close):
    [print(f'- windows_to_close : {hwnd}') for hwnd in windows_to_close]

for window in windows_to_close:
    win32gui.PostMessage(window[0], win32con.WM_CLOSE, 0, 0)
# -----------------------------------------------------------------------------




# -----------------------------------------------------------------------------
# Autotile all windows
# -----------------------------------------------------------------------------
all_windows = filter_windows(windows, only_visible=False, window_text=None, window_class=None, ignore_case=True)
[print(f'- all_windows  : {hwnd}') for hwnd in all_windows]
tile_windows(all_windows, rows=2, columns=3, row_ratios=None, column_ratios=None, monitor_index=0, num_windows=None)

# print(windows_to_keep)
time.sleep(99999)
print(windows_to_keep)
print('\n')
print(windows_to_close)
print('\n')
# time.sleep(99999)

# Get all explorer windows
windows = get_all_windows()
explorer_windows = filter_windows(windows, only_visible=False, window_text=None, window_class='CabinetWClass', ignore_case=True)

# Order the windows by paths




# Bring to foreground
# set_window_properties(ordered_windows, foreground=True, attribute='normal', topmost=False)


# Tile the windows
tile_windows(ordered_windows, rows=2, columns=3, row_ratios=None, column_ratios=None, monitor_index=0, num_windows=None)
'''
