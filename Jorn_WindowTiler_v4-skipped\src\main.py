#!/usr/bin/env python3
"""
Enhanced Window Tiler with optional minimized-window restoration,
integrating style-based (Alt+Tab) filtering with psutil-based process checks
and dimension checks, all in a modular class-based architecture.

Requirements:
    pip install pywin32 psutil
"""

from window_tiler.window_tiler_app import WindowTilerApp


def main():
    """
    Application entry point.
    """
    app = WindowTilerApp()
    app.run()


if __name__ == "__main__":
    main()
