## Distilled Highlights
- Built with Python, Selenium, BeautifulSoup, JSON, and Markdown
- Uses batch scripts for environment setup and application launch
- Follows a three-tier architecture: web scraping, data processing, and file organization
- Requires minimal setup with automated Python environment creation

# 04_tech.md

## Technology Stack

### Core Technologies
- **Python**: Primary programming language
- **Selenium**: Web automation for navigating RigDoc interface
- **BeautifulSoup**: HTML parsing for data extraction
- **JSON**: Structured data storage
- **Markdown**: Human-readable interface format

### Infrastructure
- **Batch Scripts**: Environment setup and application launch
  - `py_venv_init.bat`: Creates Python environment
  - `RigOfficeDownloader-v4.bat`: Launches application
- **Python Virtual Environment**: Isolated dependency management
- **File System**: Local storage for downloaded files and metadata

## Technical Decisions

### Web Automation Approach
Selected Selenium over API integration due to:
- Lack of public API for RigDoc system
- Need to replicate human navigation patterns
- Authentication requirements

### Data Storage Strategy
- **JSON**: Selected for:
  - Structured representation of document/file metadata
  - Easy serialization/deserialization
  - Human readability for debugging
- **Markdown**: Selected for:
  - User-friendly editing interface
  - Syntax compatible with basic text editors
  - Clear visual structure for document selection

### Application Architecture
Three-tier design:
1. **Data Collection Layer**: Web scraping, authentication, session management
2. **Processing Layer**: Filtering, metadata extraction, transformation
3. **Presentation Layer**: User interface, file organization, reporting

## Dependencies

### Runtime Dependencies
- Python 3.6+
- Selenium WebDriver
- BeautifulSoup4
- JSON (standard library)
- Various Python utility libraries (detailed in requirements)

### Development Dependencies
- Code linters
- Testing frameworks
- Documentation tools

## Setup and Deployment

### Environment Initialization
```
py_venv_init.bat
```
- Creates isolated Python environment
- Installs all required dependencies
- Configures WebDriver

### Application Launch
```
RigOfficeDownloader-v4.bat [--auto|--interactive|--config]
```

### Run Modes
- **Auto**: Full pipeline execution with minimal intervention
- **Interactive**: Menu-driven execution with user checkpoints
- **Config**: Focus on configuration adjustment

## Technical Constraints

### RigDoc System Limitations
- Session timeouts require robust retry mechanisms
- Page structure changes may require maintenance updates
- Download rate limitations may apply

### Local System Requirements
- Windows operating system (batch scripts)
- Sufficient disk space for downloads
- Chrome/Firefox for WebDriver compatibility

## Technical Evolution
- v1: Basic proof of concept
- v2: Added JSON/Markdown conversion
- v3: Improved error handling
- v4: Enhanced organization with subfolder support
