{"initial_prompt": "Create a sequence of LLM-optimized instructions that follow the schema pattern. Each instruction should have a clear role, defined inputs, explicit process steps, and structured outputs. Focus on clarity, precision, and actionable transformations.", "sequence_id": "sequence-0001-a+0001-b", "results": [{"instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "step": "a", "title": "Instruction Converter", "input": "Create a sequence of LLM-optimized instructions that follow the schema pattern. Each instruction should have a clear role, defined inputs, explicit process steps, and structured outputs. Focus on clarity, precision, and actionable transformations.", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"instruction_format\": \"Generate a series of LLM-optimized instructions conforming to a schema pattern. Assign a distinct role to each instruction. Specify required inputs for each case. Outline precise, stepwise processes for execution. Define structured outputs explicitly. Emphasize clarity, unambiguous directives, and actionable transformation steps in each instruction.\"\n}", "cost": 0.0}}}, {"instruction": "# Essence Distillation\n\nDistill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.\n\n`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "step": "b", "title": "Essence Distillation", "input": "{\n  \"initial_prompt\": \"Create a sequence of LLM-optimized instructions that follow the schema pattern. Each instruction should have a clear role, defined inputs, explicit process steps, and structured outputs. Focus on clarity, precision, and actionable transformations.\",\n  \"instruction_format\": \"Generate a series of LLM-optimized instructions conforming to a schema pattern. Assign a distinct role to each instruction. Specify required inputs for each case. Outline precise, stepwise processes for execution. Define structured outputs explicitly. Emphasize clarity, unambiguous directives, and actionable transformation steps in each instruction.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"distilled_essence\": [\n    {\n      \"role\": \"summarizer\",\n      \"inputs\": [\"text\"],\n      \"process\": [\"analyze text\", \"extract main points\", \"formulate concise summary\"],\n      \"outputs\": [\"summary\"]\n    },\n    {\n      \"role\": \"translator\",\n      \"inputs\": [\"text\", \"target_language\"],\n      \"process\": [\"interpret input text\", \"translate to target_language with fidelity\", \"adapt tone as needed\"],\n      \"outputs\": [\"translated_text\"]\n    },\n    {\n      \"role\": \"classifier\",\n      \"inputs\": [\"data_point\", \"label_set\"],\n      \"process\": [\"compare data_point against label_set criteria\", \"assign most appropriate label\"],\n      \"outputs\": [\"label\"]\n    },\n    {\n      \"role\": \"data_extractor\",\n      \"inputs\": [\"text\", \"fields\"],\n      \"process\": [\"identify fields in text\", \"extract corresponding values\", \"present values in structured format\"],\n      \"outputs\": [\"structured_data\"]\n    },\n    {\n      \"role\": \"rewriter\",\n      \"inputs\": [\"text\", \"constraints\"],\n      \"process\": [\"interpret constraints\", \"modify text to comply\", \"review for consistency and clarity\"],\n      \"outputs\": [\"revised_text\"]\n    }\n  ]\n}", "cost": 0.0}}}], "total_cost": 0.0}