-- -------------------------------------------------------------------------
-- SCENE OPTIONS
-- -------------------------------------------------------------------------
-- Use Centimiters rather than Millimeters
Units.Systemtype = #meters
Units.Displaytype = #Metric
Units.Metrictype = #centimeters



-- -------------------------------------------------------------------------
-- SCREEN
-- -------------------------------------------------------------------------

-- Create the screen (1920x1280)
screen_box  = Box pos:[9.60,5.40,0] width:19.20 length:10.80 height:1.0 name:"screen_1920x1080_box"
screen_edge = Box pos:[9.60,5.40,0] width:19.20 length:10.80 height:1.0 name:"screen_1920x1080_edges" isSelected:on

-- Convert the "edges"
convertToPoly(screen_edge)
polyop.deleteFaces screen_edge #(1,2)
polyOps.flipNormal screen_edge
modPanel.addModToSelection (Shell ()) ui:on
screen_edge.modifiers[#Shell].innerAmount = 0
screen_edge.modifiers[#Shell].outerAmount = 0.2

-- Color/Material
screen_edge.wirecolor = color 0 0 0
black_mtl = meditMaterials[1]
black_mtl.Name = "black_mtl"
black_mtl.ambient = color 0 0 0
black_mtl.Diffuse = color 0 0 0
screen_edge.material = black_mtl


-- Create the windows
hwnd1_box = Box pos:[9.60,5.40,0] width:19.20 length:10.80 height:1.0





-- -------------------------------------------------------------------------
-- MATERIALS
-- -------------------------------------------------------------------------

-- red_mtl = meditMaterials[2]
-- red_mtl.Diffuse = color 141 7 58
-- red_mtl.ambient = color 141 7 58