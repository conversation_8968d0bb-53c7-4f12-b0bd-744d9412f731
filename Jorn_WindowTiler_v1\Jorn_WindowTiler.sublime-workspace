{"auto_complete": {"selected_items": [["color_ye", "color_yellow"], ["class", "classdef\tClass Definition"], ["ope", "openfile\tOpen File"], ["dict", "dictsort\tSort Dictionary List"], ["dic", "dictsort\tSort Dictionary List"], ["doc", "doc2\t(Custom) # \"\"\"docstring: example\"\"\""], ["sep", "sep2\t(Custom) # ============================================================================="], ["__", "__\t__magic__"], ["color_red", "color_red_2"], ["col", "color_yellow_2"], ["color_gr", "color_green_2"], ["colo", "color_yellow_2"], ["color", "color_purple_3"], ["comments", "dark_comments_gray_2"]]}, "buffers": [{"file": "src/window.py", "redo_stack": [[1, 1, "revert", null, "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", "AQAAAAAAAAABAAAA6gUAAAAAAADqBQAAAAAAAAAAAAAAAPC/"]], "settings": {"buffer_size": 3279, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": [[3, 1, "black", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAADCBgAAAAAAAMIGAAAAAAAAAAAAAAAA8L8"]]}, {"file": "src/mess/window_manager/window_enums.py", "settings": {"buffer_size": 416723, "line_ending": "Windows"}}, {"file": "src/mess/window_manager/window_class.py", "settings": {"buffer_size": 5615, "line_ending": "Windows"}}, {"file": "src/completely_new_04.py", "settings": {"buffer_size": 7588, "line_ending": "Windows"}}, {"file": "src/monitor.py", "settings": {"buffer_size": 1609, "line_ending": "Windows"}}, {"file": "src/mess/cli.py", "settings": {"buffer_size": 655, "line_ending": "Windows"}}, {"file": "src/mess/log.py", "settings": {"buffer_size": 7546, "line_ending": "Windows"}}, {"file": "src/mess/window_manager/_0_manager.py", "settings": {"buffer_size": 8353, "line_ending": "Windows"}}, {"file": "src/mess/window_manager/wip.py", "settings": {"buffer_size": 27090, "line_ending": "Windows"}}, {"file": "src/mess/window_manager/window_utils.py", "settings": {"buffer_size": 6203, "line_ending": "Windows"}}, {"file": "src/mess/window_manager/window_operations.py", "settings": {"buffer_size": 4789, "line_ending": "Windows"}}, {"file": "src/mess/test.py", "settings": {"buffer_size": 1461, "line_ending": "Windows"}}, {"file": "src/mess/main.py", "settings": {"buffer_size": 547, "line_ending": "Windows"}}, {"file": "src/mess/utils.py", "settings": {"buffer_size": 517, "line_ending": "Windows"}}, {"file": "src/main.py", "settings": {"buffer_size": 1566, "line_ending": "Windows"}}, {"file": "src/window_tiler.py", "settings": {"buffer_size": 1018, "line_ending": "Windows"}}, {"contents": "# --------------------------------------------------\n# Python\n# --------------------------------------------------\n# -\n__pycache__/\n__pypackages__/\n.eggs/\nvenv/\n# -\n*.pyc\n*.egg-info/\n\n# --------------------------------------------------\n# Sublime\n# --------------------------------------------------\n*.sublime_session\n*.sublime-workspace\n\n# --------------------------------------------------\n# OS-Generated\n# --------------------------------------------------\n.DS_Store\n.DS_Store?\n._*\n.Spotlight-V100\n.Trashes\nehthumbs.db\nThumbs.db\n\n# --------------------------------------------------\n# VSCode: Ignore filenames\n# --------------------------------------------------\n/Crashpad/settings.dat\n/Session Storage/CURRENT\n/code.lock\n/Local State\n/machineid\nWebStorage/QuotaManager\n\n# --------------------------------------------------\n# VSCode: Ignore extensions\n# --------------------------------------------------\n*.tmp\n*.vscdb\n*.vscdb.backup\n\n# --------------------------------------------------\n# VSCode: Logs\n# --------------------------------------------------\n/Backups/\n/Crashpad/\n/logs/\n/Session Storage/\n/User/History/\n/User/sync/\n/User/globalStorage/storage.json\n\n# --------------------------------------------------\n# VSCode: Ignore folders\n# --------------------------------------------------\nblob_storage/\nCache/\nCache_Data/\nCachedData/\nCachedExtensionVSIXs/\nCachedProfilesData/\nCachedStorage/\nCacheStorage/\nCode Cache/\nWebStorage/\nDatabase/\ndatabases/\nDawnCache/\nGPUCache/\nleveldb/\nNetwork/\nScriptCache/\n\n\n# FOLDERS\nDIRNAME/\n\n\n.idea/\n.vscode/\n\n# ------------------------------\n# PYTHON\n# - dirs\n__pycache__/\n__pypackages__/\n.eggs/\nvenv/\n# - files\n*.pyc\n*.egg-info/\n\n\n\n# OS generated\n.DS_Store\n.DS_Store?\n._*\n.Spotlight-V100\n.Trashes\nehthumbs.db\nThumbs.db\n", "settings": {"buffer_size": 1761, "line_ending": "Windows"}, "undo_stack": [[1, 1, "paste", null, "AQAAAAAAAAAAAAAAmgAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/"], [6, 1, "paste", null, "AQAAACcAAAAAAAAANwAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAALAAAAAAAAAAsAAAAAAAAAAAAAAAAAPC/"], [9, 1, "swap_line_up", null, "AgAAAD0AAAAAAAAAPQAAAAAAAAAHAAAALmVnZ3MvCjcAAAAAAAAAPgAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAQwAAAAAAAABDAAAAAAAAAAAAAAAAAPC/"], [11, 1, "run_macro_file", {"file": "res://Packages/Default/Add Line Before.sublime-macro"}, "AQAAAD4AAAAAAAAAPwAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAQwAAAAAAAABDAAAAAAAAAAAAAAAAgE1A"], [12, 1, "toggle_comment", {"block": false}, "AQAAAD4AAAAAAAAAQAAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAPgAAAAAAAAA+AAAAAAAAAAAAAAAAAAAA"], [13, 1, "insert", {"characters": "-"}, "AQAAAEAAAAAAAAAAQQAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAQAAAAAAAAABAAAAAAAAAAAAAAAAAAPC/"], [16, 1, "run_macro_file", {"file": "res://Packages/Default/Add Line Before.sublime-macro"}, "AQAAABoAAAAAAAAAGwAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAHQAAAAAAAAAdAAAAAAAAAAAAAAAAAD5A"], [17, 1, "toggle_comment", {"block": false}, "AQAAABoAAAAAAAAAHAAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAGgAAAAAAAAAaAAAAAAAAAAAAAAAAAAAA"], [18, 1, "insert", {"characters": "-"}, "AQAAABwAAAAAAAAAHQAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAHAAAAAAAAAAcAAAAAAAAAAAAAAAAAPC/"], [25, 1, "insert", {"characters": " dirs"}, "BQAAAB0AAAAAAAAAHgAAAAAAAAAAAAAAHgAAAAAAAAAfAAAAAAAAAAAAAAAfAAAAAAAAACAAAAAAAAAAAAAAACAAAAAAAAAAIQAAAAAAAAAAAAAAIQAAAAAAAAAiAAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAHQAAAAAAAAAdAAAAAAAAAAAAAAAAAPC/"], [28, 1, "insert", {"characters": " files"}, "BgAAAEoAAAAAAAAASwAAAAAAAAAAAAAASwAAAAAAAABMAAAAAAAAAAAAAABMAAAAAAAAAE0AAAAAAAAAAAAAAE0AAAAAAAAATgAAAAAAAAAAAAAATgAAAAAAAABPAAAAAAAAAAAAAABPAAAAAAAAAFAAAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAASgAAAAAAAABKAAAAAAAAAAAAAAAAwFNA"], [31, 1, "upper_case", null, "AQAAABMAAAAAAAAAGQAAAAAAAAAGAAAAUHl0aG9u", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAGQAAAAAAAAATAAAAAAAAAAAAAAAAAPC/"], [36, 1, "insert", {"characters": "\nvenv"}, "BQAAAEYAAAAAAAAARwAAAAAAAAAAAAAARwAAAAAAAABIAAAAAAAAAAAAAABIAAAAAAAAAEkAAAAAAAAAAAAAAEkAAAAAAAAASgAAAAAAAAAAAAAASgAAAAAAAABLAAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAARgAAAAAAAABGAAAAAAAAAAAAAAAAAPC/"], [37, 1, "insert", {"characters": "/"}, "AQAAAEsAAAAAAAAATAAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAASwAAAAAAAABLAAAAAAAAAAAAAAAAAPC/"], [42, 1, "insert", {"characters": "\n"}, "AQAAABAAAAAAAAAAEQAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAEAAAAAAAAAAQAAAAAAAAAAAAAAAAAPC/"], [43, 1, "toggle_comment", {"block": false}, "AQAAABEAAAAAAAAAEwAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAEQAAAAAAAAARAAAAAAAAAAAAAAAAAPC/"], [44, 1, "insert", {"characters": "------------------------------"}, "HgAAABMAAAAAAAAAFAAAAAAAAAAAAAAAFAAAAAAAAAAVAAAAAAAAAAAAAAAVAAAAAAAAABYAAAAAAAAAAAAAABYAAAAAAAAAFwAAAAAAAAAAAAAAFwAAAAAAAAAYAAAAAAAAAAAAAAAYAAAAAAAAABkAAAAAAAAAAAAAABkAAAAAAAAAGgAAAAAAAAAAAAAAGgAAAAAAAAAbAAAAAAAAAAAAAAAbAAAAAAAAABwAAAAAAAAAAAAAABwAAAAAAAAAHQAAAAAAAAAAAAAAHQAAAAAAAAAeAAAAAAAAAAAAAAAeAAAAAAAAAB8AAAAAAAAAAAAAAB8AAAAAAAAAIAAAAAAAAAAAAAAAIAAAAAAAAAAhAAAAAAAAAAAAAAAhAAAAAAAAACIAAAAAAAAAAAAAACIAAAAAAAAAIwAAAAAAAAAAAAAAIwAAAAAAAAAkAAAAAAAAAAAAAAAkAAAAAAAAACUAAAAAAAAAAAAAACUAAAAAAAAAJgAAAAAAAAAAAAAAJgAAAAAAAAAnAAAAAAAAAAAAAAAnAAAAAAAAACgAAAAAAAAAAAAAACgAAAAAAAAAKQAAAAAAAAAAAAAAKQAAAAAAAAAqAAAAAAAAAAAAAAAqAAAAAAAAACsAAAAAAAAAAAAAACsAAAAAAAAALAAAAAAAAAAAAAAALAAAAAAAAAAtAAAAAAAAAAAAAAAtAAAAAAAAAC4AAAAAAAAAAAAAAC4AAAAAAAAALwAAAAAAAAAAAAAALwAAAAAAAAAwAAAAAAAAAAAAAAAwAAAAAAAAADEAAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAEwAAAAAAAAATAAAAAAAAAAAAAAAAAPC/"], [49, 4, "run_macro_file", {"file": "res://Packages/Default/Add Line Before.sublime-macro"}, "BAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAABgAAAAAAAAAGAAAAAAAAAAAAAAAAAPC/"], [50, 1, "paste", null, "AQAAAAAAAAAAAAAAGgEAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"], [56, 1, "paste", null, "AgAAAAkAAAAAAAAAMwAAAAAAAAAAAAAAMwAAAAAAAAAzAAAAAAAAABAAAAAKQmxlbmRlci9tYXRsaWIv", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAGQAAAAAAAAAJAAAAAAAAAAAAAAAAAPC/"], [59, 1, "jorn_delete_line_and_align", null, "AQAAADQAAAAAAAAANAAAAAAAAAAKAAAAX19NQUNPU1gvCg", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAPQAAAAAAAAA9AAAAAAAAAAAAAAAAAPC/"], [65, 1, "paste", null, "AQAAAD0AAAAAAAAAQwAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAQgAAAAAAAABCAAAAAAAAAAAAAAAAAPC/"], [66, 2, "run_macro_file", {"file": "res://Packages/Default/Add Line Before.sublime-macro"}, "AgAAAEMAAAAAAAAARAAAAAAAAAAAAAAAQwAAAAAAAABEAAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAASAAAAAAAAABIAAAAAAAAAAAAAAAAAPC/"], [69, 1, "left_delete", null, "AQAAADMAAAAAAAAAMwAAAAAAAAAJAAAACgojIEZJTEVT", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAPAAAAAAAAAAzAAAAAAAAAAAAAAAAAPC/"], [77, 1, "paste", null, "AQAAADsAAAAAAAAAdAAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAOwAAAAAAAAA7AAAAAAAAAAAAAAAAAPC/"], [82, 1, "insert", {"characters": "PYTHON"}, "BwAAAAIAAAAAAAAAAwAAAAAAAAAAAAAAAwAAAAAAAAADAAAAAAAAAAcAAABGT0xERVJTAwAAAAAAAAAEAAAAAAAAAAAAAAAEAAAAAAAAAAUAAAAAAAAAAAAAAAUAAAAAAAAABgAAAAAAAAAAAAAABgAAAAAAAAAHAAAAAAAAAAAAAAAHAAAAAAAAAAgAAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAgAAAAAAAAAJAAAAAAAAAAAAAAAAAPC/"], [91, 1, "insert", {"characters": "DIRNAME"}, "CAAAAEQAAAAAAAAARQAAAAAAAAAAAAAARQAAAAAAAABFAAAAAAAAAAsAAABfX3B5Y2FjaGVfX0UAAAAAAAAARgAAAAAAAAAAAAAARgAAAAAAAABHAAAAAAAAAAAAAABHAAAAAAAAAEgAAAAAAAAAAAAAAEgAAAAAAAAASQAAAAAAAAAAAAAASQAAAAAAAABKAAAAAAAAAAAAAABKAAAAAAAAAEsAAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAARAAAAAAAAABPAAAAAAAAAAAAAAAAAPC/"], [93, 4, "jorn_delete_line_and_align", null, "BAAAAE0AAAAAAAAATQAAAAAAAAAQAAAAX19weXBhY2thZ2VzX18vCk0AAAAAAAAATQAAAAAAAAAHAAAALmVnZ3MvCk0AAAAAAAAATQAAAAAAAAAGAAAAdmVudi8KTQAAAAAAAABNAAAAAAAAAAYAAAAqLnB5Ywo", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAVAAAAAAAAABUAAAAAAAAAAAAAAAAQFFA"], [94, 3, "jorn_delete_line_and_align", null, "AwAAAE0AAAAAAAAATQAAAAAAAAARAAAAcmVjZW50LWZpbGVzLnR4dApNAAAAAAAAAE0AAAAAAAAAFQAAAHBsYXRmb3JtX3N1cHBvcnQudHh0Ck0AAAAAAAAATQAAAAAAAAAUAAAAcmVjZW50LXNlYXJjaGVzLnR4dAo", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAATQAAAAAAAABNAAAAAAAAAAAAAAAAAAAA"], [97, 1, "cut", null, "AQAAAEwAAAAAAAAATAAAAAAAAAAxAAAACgojIFNVQkxJTUUKKi5zdWJsaW1lX3Nlc3Npb24KKi5zdWJsaW1lLXdvcmtzcGFjZQ", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAfQAAAAAAAABMAAAAAAAAAAAAAAAAAPC/"], [100, 1, "paste", null, "AQAAADgAAAAAAAAAaQAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAOAAAAAAAAAA4AAAAAAAAAAAAAAAAAPC/"], [103, 1, "left_delete", null, "AQAAAH4AAAAAAAAAfgAAAAAAAAAoAAAACiMgUFlUSE9OCiovX19weWNhY2hlX18vKgoqLnB5YwoqLmJsZW5kMQ", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAApgAAAAAAAAB+AAAAAAAAAAAAAAAAAPC/"], [104, 2, "jorn_delete_line_and_align", null, "AgAAAH4AAAAAAAAAfgAAAAAAAAABAAAACn4AAAAAAAAAfgAAAAAAAAABAAAACg", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAfgAAAAAAAAB+AAAAAAAAAAAAAAAAAPC/"], [107, 1, "cut", null, "AQAAAGkAAAAAAAAAaQAAAAAAAAAUAAAACgojIEZPTERFUlMKRElSTkFNRS8", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAfQAAAAAAAABpAAAAAAAAAAAAAAAAAPC/"], [110, 1, "paste", null, "AQAAAMMAAAAAAAAA1wAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAwwAAAAAAAADDAAAAAAAAAAAAAAAAAPC/"], [116, 1, "paste", null, "AQAAAMMAAAAAAAAAjgUAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAwwAAAAAAAADDAAAAAAAAAAAAAAAAAPC/"], [123, 1, "left_delete", null, "AQAAALQBAAAAAAAAtAEAAAAAAAAvAQAAIyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0K", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAtAEAAAAAAADjAgAAAAAAAAAAAAAAAPC/"], [126, 1, "duplicate_line", null, "AQAAAAUCAAAAAAAAOgIAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAA8wEAAAAAAADzAQAAAAAAAAAAAAAAAPC/"], [127, 2, "swap_line_up", null, "BAAAAAUCAAAAAAAABQIAAAAAAAA1AAAAIyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLQrQAQAAAAAAAAUCAAAAAAAAAAAAANABAAAAAAAA0AEAAAAAAAA1AAAAIyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLQq0AQAAAAAAAOkBAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAKAIAAAAAAAAoAgAAAAAAAAAAAAAAAPC/"], [133, 1, "insert", {"characters": "\n"}, "AQAAAAgAAAAAAAAACQAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAACAAAAAAAAAAIAAAAAAAAAAAAAAAAAPC/"], [134, 1, "paste", null, "AQAAAAkAAAAAAAAAjwAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAACQAAAAAAAAAJAAAAAAAAAAAAAAAAAPC/"], [139, 2, "jorn_delete_line_and_align", null, "AgAAAAAAAAAAAAAAAAAAAAAAAAAJAAAAIyBQWVRIT04KAAAAAAAAAAAAAAAAAAAAAAEAAAAK", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAgAAAAAAAAAIAAAAAAAAAAAAAAAAAPC/"], [144, 1, "insert", {"characters": "pYTHON"}, "BwAAADcAAAAAAAAAOAAAAAAAAAAAAAAAOAAAAAAAAAA4AAAAAAAAAAYAAABWU0NvZGU4AAAAAAAAADkAAAAAAAAAAAAAADkAAAAAAAAAOgAAAAAAAAAAAAAAOgAAAAAAAAA7AAAAAAAAAAAAAAA7AAAAAAAAADwAAAAAAAAAAAAAADwAAAAAAAAAPQAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAANwAAAAAAAAA9AAAAAAAAAAAAAAAAAPC/"], [146, 1, "title_case", null, "AQAAADcAAAAAAAAAPQAAAAAAAAAGAAAAcFlUSE9O", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAPQAAAAAAAAA3AAAAAAAAAAAAAAAAAPC/"], [149, 3, "left_delete", null, "AwAAAD8AAAAAAAAAPwAAAAAAAAARAAAASWdub3JlIGV4dGVuc2lvbnM+AAAAAAAAAD4AAAAAAAAAAQAAACA9AAAAAAAAAD0AAAAAAAAAAQAAADo", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAPwAAAAAAAABQAAAAAAAAAAAAAAAAAPC/"], [152, 1, "duplicate_line", null, "AQAAAHMAAAAAAAAAqAAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAASwAAAAAAAABLAAAAAAAAAAAAAAAAAPC/"], [153, 6, "swap_line_down", null, "DAAAALUAAAAAAAAA6gAAAAAAAAAAAAAAcwAAAAAAAABzAAAAAAAAADUAAAAjIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tCsUAAAAAAAAA+gAAAAAAAAAAAAAAgAAAAAAAAACAAAAAAAAAADUAAAAjIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tCswAAAAAAAAAAQEAAAAAAAAAAAAAkAAAAAAAAACQAAAAAAAAADUAAAAjIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tCtIAAAAAAAAABwEAAAAAAAAAAAAAlwAAAAAAAACXAAAAAAAAADUAAAAjIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tCtgAAAAAAAAADQEAAAAAAAAAAAAAnQAAAAAAAACdAAAAAAAAADUAAAAjIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tCtkAAAAAAAAADgEAAAAAAAAAAAAAowAAAAAAAACjAAAAAAAAADUAAAAjIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tCg", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAgAAAAAAAAACAAAAAAAAAAAAAAAAAAPC/"], [154, 1, "duplicate_line", null, "AQAAANkAAAAAAAAADgEAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAsQAAAAAAAACxAAAAAAAAAAAAAAAAAPC/"], [155, 1, "swap_line_down", null, "AgAAABgBAAAAAAAATQEAAAAAAAAAAAAA2QAAAAAAAADZAAAAAAAAADUAAAAjIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tCg", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAA5gAAAAAAAADmAAAAAAAAAAAAAAAAAPC/"], [158, 1, "title_case", null, "AQAAANsAAAAAAAAA4gAAAAAAAAAHAAAAU1VCTElNRQ", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAA4gAAAAAAAADbAAAAAAAAAAAAAAAAAPC/"], [165, 1, "duplicate_line", null, "AQAAABgBAAAAAAAATQEAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAFwEAAAAAAAAXAQAAAAAAAAAAAAAAAPC/"], [166, 3, "swap_line_down", null, "BgAAAF8BAAAAAAAAlAEAAAAAAAAAAAAAGAEAAAAAAAAYAQAAAAAAADUAAAAjIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tCnMBAAAAAAAAqAEAAAAAAAAAAAAAKgEAAAAAAAAqAQAAAAAAADUAAAAjIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tCnQBAAAAAAAAqQEAAAAAAAAAAAAAPgEAAAAAAAA+AQAAAAAAADUAAAAjIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tCg", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAATAEAAAAAAABMAQAAAAAAAAAAAAAAAPC/"], [167, 1, "duplicate_line", null, "AQAAAHQBAAAAAAAAqQEAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAcwEAAAAAAABzAQAAAAAAAAAAAAAAAPC/"], [168, 1, "swap_line_down", null, "AgAAALgBAAAAAAAA7QEAAAAAAAAAAAAAdAEAAAAAAAB0AQAAAAAAADUAAAAjIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tCg", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAqAEAAAAAAACoAQAAAAAAAAAAAAAAAPC/"], [171, 1, "title_case", null, "AQAAAHkBAAAAAAAAggEAAAAAAAAJAAAAR0VORVJBVEVE", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAggEAAAAAAAB5AQAAAAAAAAAAAAAAAPC/"], [181, 1, "paste", null, "AQAAAJ0AAAAAAAAAqQAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAogAAAAAAAACiAAAAAAAAAAAAAAAAAPC/"], [184, 1, "sort_lines", {"case_sensitive": false}, "AQAAAHMAAAAAAAAArgAAAAAAAAA7AAAAX19weWNhY2hlX18vCl9fcHlwYWNrYWdlc19fLwouZWdncy8KdmVudi8KKi5lZ2ctaW5mby8KKi5weWM", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAArgAAAAAAAABzAAAAAAAAAAAAAAAAAPC/"], [192, 3, "swap_line_up", null, "BgAAAIwAAAAAAAAAjAAAAAAAAAAjAAAAX19weWNhY2hlX18vCl9fcHlwYWNrYWdlc19fLwp2ZW52LwqFAAAAAAAAAKgAAAAAAAAAAAAAAIUAAAAAAAAAhQAAAAAAAAAjAAAAX19weWNhY2hlX18vCl9fcHlwYWNrYWdlc19fLwp2ZW52Lwp/AAAAAAAAAKIAAAAAAAAAAAAAAH8AAAAAAAAAfwAAAAAAAAAjAAAAX19weWNhY2hlX18vCl9fcHlwYWNrYWdlc19fLwp2ZW52LwpzAAAAAAAAAJYAAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAArgAAAAAAAACYAAAAAAAAAAAAAAAAgF1A"], [194, 2, "swap_line_up", null, "BAAAAKgAAAAAAAAAqAAAAAAAAAAHAAAALmVnZ3MvCqIAAAAAAAAAqQAAAAAAAAAAAAAAogAAAAAAAACiAAAAAAAAAAcAAAAuZWdncy8KlgAAAAAAAACdAAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAArQAAAAAAAACtAAAAAAAAAAAAAAAAgEhA"], [198, 1, "run_macro_file", {"file": "res://Packages/Default/Add Line Before.sublime-macro"}, "AQAAAKkAAAAAAAAAqgAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAArgAAAAAAAACuAAAAAAAAAAAAAAAAgEhA"], [199, 1, "toggle_comment", {"block": false}, "AQAAAKkAAAAAAAAAqwAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAqQAAAAAAAACpAAAAAAAAAAAAAAAAAAAA"], [202, 1, "jorn_delete_line_and_align", null, "AQAAAKkAAAAAAAAAqQAAAAAAAAADAAAAIyAK", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAqwAAAAAAAACrAAAAAAAAAAAAAAAAADRA"], [213, 1, "paste", null, "AQAAAK8AAAAAAAAA/gAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAArwAAAAAAAACvAAAAAAAAAAAAAAAAAPC/"], [216, 1, "left_delete", null, "AQAAAHIAAAAAAAAAcgAAAAAAAAA9AAAACl9fcHljYWNoZV9fLwpfX3B5cGFja2FnZXNfXy8KdmVudi8KLmVnZ3MvCiouZWdnLWluZm8vCioucHljCg", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAArwAAAAAAAAByAAAAAAAAAAAAAAAAAPC/"], [221, 2, "left_delete", null, "AgAAAHcAAAAAAAAAdwAAAAAAAAAEAAAAZGlyc3YAAAAAAAAAdgAAAAAAAAABAAAAIA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAdwAAAAAAAAB7AAAAAAAAAAAAAAAAAPC/"], [226, 2, "left_delete", null, "AgAAAKUAAAAAAAAApQAAAAAAAAAFAAAAZmlsZXOkAAAAAAAAAKQAAAAAAAAAAQAAACA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAApQAAAAAAAACqAAAAAAAAAAAAAAAAAPC/"], [233, 1, "insert", {"characters": "\n"}, "AQAAALYAAAAAAAAAtwAAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAtgAAAAAAAAC2AAAAAAAAAAAAAAAAAPC/"]]}, {"contents": "# Created by https://www.toptal.com/developers/gitignore/api/python\n# Edit at https://www.toptal.com/developers/gitignore?templates=python\n\n### Python ###\n# Byte-compiled / optimized / DLL files\n__pycache__/\n*.py[cod]\n*$py.class\n\n# C extensions\n*.so\n\n# Distribution / packaging\n.Python\nbuild/\ndevelop-eggs/\ndist/\ndownloads/\neggs/\n.eggs/\nlib/\nlib64/\nparts/\nsdist/\nvar/\nwheels/\nshare/python-wheels/\n*.egg-info/\n.installed.cfg\n*.egg\nMANIFEST\n\n# PyInstaller\n#  Usually these files are written by a python script from a template\n#  before PyInstaller builds the exe, so as to inject date/other infos into it.\n*.manifest\n*.spec\n\n# Installer logs\npip-log.txt\npip-delete-this-directory.txt\n\n# Unit test / coverage reports\nhtmlcov/\n.tox/\n.nox/\n.coverage\n.coverage.*\n.cache\nnosetests.xml\ncoverage.xml\n*.cover\n*.py,cover\n.hypothesis/\n.pytest_cache/\ncover/\n\n# Translations\n*.mo\n*.pot\n\n# Django stuff:\n*.log\nlocal_settings.py\ndb.sqlite3\ndb.sqlite3-journal\n\n# Flask stuff:\ninstance/\n.webassets-cache\n\n# Scrapy stuff:\n.scrapy\n\n# Sphinx documentation\ndocs/_build/\n\n# PyBuilder\n.pybuilder/\ntarget/\n\n# Jupyter Notebook\n.ipynb_checkpoints\n\n# IPython\nprofile_default/\nipython_config.py\n\n# pyenv\n#   For a library or package, you might want to ignore these files since the code is\n#   intended to run in multiple environments; otherwise, check them in:\n# .python-version\n\n# pipenv\n#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.\n#   However, in case of collaboration, if having platform-specific dependencies or dependencies\n#   having no cross-platform support, pipenv may install dependencies that don't work, or not\n#   install all needed dependencies.\n#Pipfile.lock\n\n# poetry\n#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.\n#   This is especially recommended for binary packages to ensure reproducibility, and is more\n#   commonly ignored for libraries.\n#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control\n#poetry.lock\n\n# pdm\n#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.\n#pdm.lock\n#   pdm stores project-wide configurations in .pdm.toml, but it is recommended to not include it\n#   in version control.\n#   https://pdm.fming.dev/#use-with-ide\n.pdm.toml\n\n# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm\n__pypackages__/\n\n# Celery stuff\ncelerybeat-schedule\ncelerybeat.pid\n\n# SageMath parsed files\n*.sage.py\n\n# Environments\n.env\n.venv\nenv/\nvenv/\nENV/\nenv.bak/\nvenv.bak/\n\n# Spyder project settings\n.spyderproject\n.spyproject\n\n# Rope project settings\n.ropeproject\n\n# mkdocs documentation\n/site\n\n# mypy\n.mypy_cache/\n.dmypy.json\ndmypy.json\n\n# Pyre type checker\n.pyre/\n\n# pytype static type analyzer\n.pytype/\n\n# Cython debug symbols\ncython_debug/\n\n# PyCharm\n#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can\n#  be found at https://github.com/github/gitignore/blob/main/Global/JetBrains.gitignore\n#  and can be added to the global gitignore or merged into this file.  For a more nuclear\n#  option (not recommended) you can uncomment the following to ignore the entire idea folder.\n#.idea/\n\n### Python Patch ###\n# Poetry local configuration file - https://python-poetry.org/docs/configuration/#local-configuration\npoetry.toml\n\n# ruff\n.ruff_cache/\n\n# LSP config files\npyrightconfig.json\n\n# End of https://www.toptal.com/developers/gitignore/api/python\n", "settings": {"buffer_size": 3494, "line_ending": "Windows"}, "undo_stack": [[1, 1, "paste", null, "AQAAAAAAAAAAAAAApg0AAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/"]]}, {"contents": "# Editors\n.vscode/\n.idea/\n\n# Vagrant\n.vagrant/\n\n# Mac/OSX\n.DS_Store\n\n# Windows\nThumbs.db\n\n# Source for the following rules: https://raw.githubusercontent.com/github/gitignore/master/Python.gitignore\n# Byte-compiled / optimized / DLL files\n__pycache__/\n*.py[cod]\n*$py.class\n\n# C extensions\n*.so\n\n# Distribution / packaging\n.Python\nbuild/\ndevelop-eggs/\ndist/\ndownloads/\neggs/\n.eggs/\nlib/\nlib64/\nparts/\nsdist/\nvar/\nwheels/\n*.egg-info/\n.installed.cfg\n*.egg\nMANIFEST\n\n# PyInstaller\n#  Usually these files are written by a python script from a template\n#  before PyInstaller builds the exe, so as to inject date/other infos into it.\n*.manifest\n*.spec\n\n# Installer logs\npip-log.txt\npip-delete-this-directory.txt\n\n# Unit test / coverage reports\nhtmlcov/\n.tox/\n.nox/\n.coverage\n.coverage.*\n.cache\nnosetests.xml\ncoverage.xml\n*.cover\n.hypothesis/\n.pytest_cache/\n\n# Translations\n*.mo\n*.pot\n\n# Django stuff:\n*.log\nlocal_settings.py\ndb.sqlite3\n\n# Flask stuff:\ninstance/\n.webassets-cache\n\n# Scrapy stuff:\n.scrapy\n\n# Sphinx documentation\ndocs/_build/\n\n# PyBuilder\ntarget/\n\n# Jupyter Notebook\n.ipynb_checkpoints\n\n# IPython\nprofile_default/\nipython_config.py\n\n# pyenv\n.python-version\n\n# celery beat schedule file\ncelerybeat-schedule\n\n# SageMath parsed files\n*.sage.py\n\n# Environments\n.env\n.venv\nenv/\nvenv/\nENV/\nenv.bak/\nvenv.bak/\n\n# Spyder project settings\n.spyderproject\n.spyproject\n\n# Rope project settings\n.ropeproject\n\n# mkdocs documentation\n/site\n\n# mypy\n.mypy_cache/\n.dmypy.json\ndmypy.json", "settings": {"buffer_size": 1476, "line_ending": "Windows"}, "undo_stack": [[1, 1, "paste", null, "AQAAAAAAAAAAAAAAxAUAAAAAAAAAAAAA", "BwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/"]]}], "build_system": "", "build_system_choices": [], "build_varint": "", "command_palette": {"height": 0.0, "last_filter": "", "selected_items": [["del com", "Jorn - Delete Comments"], ["load", "Origami: <PERSON><PERSON> Saved Layout"], ["packa", "Package Control: Install Package"], ["install", "Package Control: Install Package"], ["font", "FontCycler - Next Font"], ["scheme", "UI: Select Color Scheme"], ["sche", "MarkdownEditing: Select Color scheme"], ["color sch", "UI: Select Color Scheme"], ["mark sc", "MarkdownEditing: Select Color scheme"], ["color", "UI: Select Color Scheme"], ["color sc", "UI: Select Color Scheme"], ["reso", "PackageResourceViewer: Open Resource"], ["list", "Package Control: List Packages"], ["inst", "Package Control: Install Package"], ["remove", "Package Control: Remove Package"], ["diff", "<PERSON><PERSON>"], ["sync", "SyncScroll: Toggle Current View Scroll Sync"], ["extra", "PackageResourceViewer: Extract Package"]], "width": 0.0}, "console": {"height": 131.199996948, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/C/Users/<USER>/Desktop/PRJ/NAS_CLOUD/_DEV/TodoProject/Py_WindowTiler/Jorn_WindowTiler/src", "/C/Users/<USER>/Desktop/PRJ/NAS_CLOUD/_DEV/TodoProject/Py_WindowTiler/Jorn_WindowTiler/src/mess", "/C/Users/<USER>/Desktop/PRJ/NAS_CLOUD/_DEV/TodoProject/Py_WindowTiler/Jorn_WindowTiler/src/mess/_refs", "/C/Users/<USER>/Desktop/PRJ/NAS_CLOUD/_DEV/TodoProject/Py_WindowTiler/Jorn_WindowTiler/src/mess/window_manager"], "file_history": ["/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/UTILS/Py_MyWorkflowUtils/.code/.gitignore", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/.gitignore", "/C/Users/<USER>/Desktop/PRJ/NAS_CLOUD/_DEV/TodoProject/Py_WindowTiler/SaveWindowLayout/Revisions/Rev4_WIP_StructureTesting/WinWinLayoutManager_5_5.py", "/C/Users/<USER>/Desktop/PRJ/NAS_CLOUD/_DEV/TodoProject/Py_WindowTiler/SaveWindowLayout/SaveWindowLayout.py", "/C/Users/<USER>/Desktop/PRJ/NAS_CLOUD/_DEV/TodoProject/Py_WindowTiler/SaveWindowLayout_V2/SaveWindowLayout_V2 - Notes.txt", "/C/Users/<USER>/Desktop/PRJ/NAS_CLOUD/_DEV/TodoProject/Py_WindowTiler/SaveWindowLayout_V2/reference_utils/WinWinLayoutManager_4_New.py", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__WindowUtils/Jorn_WindowTiler/src/main.bat", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__WindowUtils/Jo<PERSON>_WindowTiler/Notes_Temp - Roteliste med tilfeldige notater.txt", "/C/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Cli_Utils/py__WindowUtils/Jorn_WindowTiler/Jorn_WindowTiler.md", "/C/Users/<USER>/Desktop/PRJ/NAS_CLOUD/_DEV/TodoProject/Py_WindowTiler/Unsorted/layout/WinWinLayoutManage_261.py", "/C/Users/<USER>/Desktop/PRJ/NAS_CLOUD/_DEV/TodoProject/Py_WindowTiler/Jorn_WindowTiler/src/completely_new_04.py", "/C/Users/<USER>/Desktop/PRJ/NAS_CLOUD/_DEV/TodoProject/Py_WindowTiler/Jorn_WindowTiler/src/mess/window_data.json", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/CODE/Special_Projects/Py_MyGitToolkit/.gitignore", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/UTILS/Py_MyWorkflowUtils/.sublime/.gitignore", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/CODE/Personal/Projects/InProgress/Py_WindowTiler/Py_Win32WindowMonitor/.gitignore", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/BLENDER/Blend_Proj - Workflow/blender__defaults/4.0/scripts/addons/geometry-script-main/.gitignore", "/C/Users/<USER>/AppData/Roaming/Code/.gitignore", "/C/Users/<USER>/AppData/Roaming/Code____/ (1).gitignore", "/C/Users/<USER>/AppData/Roaming/Sublime Text/.gitignore", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/CODE/Personal/Projects/InProgress/Py_WindowTiler/Jorn_WindowTiler/src/completely_new_04.py", "/C/Users/<USER>/.vscode/.gitignore", "/C/Users/<USER>/AppData/Roaming/Blender Foundation/.gitignore", "/C/Users/<USER>/AppData/Roaming/Blender Foundation/Blender/4.1/scripts/addons/leomoon-lightstudio/.gitignore", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/BLENDER/.gitignore", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/UTILS/FileExplorer-omeryanar/src/.gitignore", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/CODE/Special_Projects/Py_CokkieCutterTemplate/py-cookie-cutter-template/python-package-template/python_project/.gitignore", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/SortBy/.gitignore", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/CODE/Personal/Projects/InProgress/Py_GithubSearchUtil/.code/src/gh-search_main/.gitignore", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/UTILS/Py_MyWorkflowUtils/.sublime/Py_MyWorkflowUtils.sublime-project", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/UTILS/Py_MyWorkflowUtils/_venv_init.cmd", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/CODE/Personal/Projects/Todo/Py_Prj - Download Discord Chats/_playground/.gitignore", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/NOTES/PY-DEV/.sublime/.gitignore", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/UTILS/Py_MyWorkflowUtils/.cache/.PROJECT-NAME.DESKTOP-TJ1HMLP", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/UTILS/Py_MyWorkflowUtils/.cache/.PROJECT-PATH.DESKTOP-TJ1HMLP", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/UTILS/Py_MyWorkflowUtils/.cache/.PYTHON-VERSION.DESKTOP-TJ1HMLP", "/C/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/UTILS/Py_MyWorkflowUtils/.cache/.VENV-PATH.DESKTOP-TJ1HMLP", "/C/Users/<USER>/My Drive/Jorn/Workflow/Work in progress/Py_Script - TileOpenWindows/tilewindows_07.py", "/C/Users/<USER>/My Drive/Jorn/Workflow/Work in progress/Py_Script - TileOpenWindows/tilewindows.sublime-project", "/C/Users/<USER>/My Drive/Jorn/Workflow/Work in progress/Py_Script - TileOpenWindows/tilewindows_01.py", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Python/Snippets - Custom/docstring.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Python/Snippets - Custom/separator.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Python/Snippets - Custom/print_normal.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Sublime Text - Merge with this (fromwork) 2/Packages/Batch File/Batch File (Compound).sublime-syntax", "/C/Users/<USER>/My Drive/Jorn/Workflow/Work in progress/Py_Script - TileOpenWindows/tilewindows_02.py", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/User/color_schemes/python/Jo<PERSON>_Python_Dark_Focused_New_4.sublime-color-scheme", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/User/color_schemes/PlainTextEnhanced/Jorn_PlainTextEnhanced_Dark.sublime-color-scheme", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/User/MaxScript.sublime-settings", "/C/Users/<USER>/AppData/Roaming/Python_VENVs/Private/Py_Script - TileOpenWindows__Python310/venv/Scripts/pywin32_testall.py", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Python/Snippets - Custom/if__main__.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Python/Snippets - Custom/print_len_var.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Python/Snippets - Custom/print_type_var.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Python/Snippets - Custom/len_normal.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Python/Snippets - Custom/import_normal.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Python/Snippets - Custom/type_normal.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Python/Snippets - Custom/print_var.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Python/Snippets - Custom/time_sleep.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Python/Snippets - Custom/print_newline.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Python/Snippets - Custom/os_path_current.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MaxScript/Snippets - Custom/print_var.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/User/JSON.sublime-settings", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/User/Preferences.sublime-settings", "/C/Users/<USER>/My Drive/Jorn/ScreenGrabs/Notes_Temp - Roteliste med tilfeldige notater.txt", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Plain Text Enhanced/PTE_Sample_New.txt", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/User/Python.sublime-settings", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MaxScript/Snippets - Custom/print_len_var.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MaxScript/Snippets - Custom/print_newline.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MaxScript/Snippets - Custom/return_normal.sublime-snippet", "/C/Users/<USER>/My Drive/Jorn/Workflow/MaxScripts/Ferdige_Scripts/Macros/SelectionTools.ms", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MarkdownEditing/plugins/color_schemes.py", "/C/Users/<USER>/OneDrive/Jorn/Todo, Notes, Initiativ/Notes_MaxScript.ms", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MaxScript/snippets/if_do.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MaxScript/snippets/function.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MaxScript/snippets/rollout.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MaxScript/snippets/struct_gui.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MaxScript/Snippets - Custom/len_normal.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MaxScript/Snippets - Custom/print_normal.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MaxScript/Snippets - Custom/type_normal.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MaxScript/Snippets - Custom/except_simple.sublime-snippet", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/User/PlainTextEnhanced.sublime-settings", "/C/ProgramData/Logishrd/LogiOptionsPlus/Plugins/CEP/LogiOptionsPlusAdobe/node_modules/websocket/README.md", "/C/ProgramData/Logishrd/LogiOptionsPlus/Plugins/CEP/LogiOptionsPlusAdobe/node_modules/websocket/2023-06-03.md", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Plain Text Enhanced/PlainTextEnhanced.sublime-settings", "/C/Users/<USER>/Downloads/The.Ultimate.Fighter.S31E01.1080p.WEB-DL.H264-SZLS/The.Ultimate.Fighter.S31E01.1080p.WEB-DL.H264-SZLS.H264-SZLS.nfo", "/C/Users/<USER>/Downloads/UFC.288.PPV.Sterling.vs.Cejudo.HDTV.x264-PUNCH/UFC.288.PPV.Sterling.vs.Cejudo.HDTV.x264-PUNCH.nfo", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MaxScript/maxscript_color_schemes/Ciapre.tmTheme", "/C/Users/<USER>/Dropbox/Dev_Root/Python/Python__Projects/Py_Private_Environments/AutoCreateScriptRevisions/run_and_create_revision_wip/run_and_create_revision_23.py", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/User/color_schemes/python/Jo<PERSON>_Python_Dark_Focused_New_3.sublime-color-scheme", "/C/Users/<USER>/AppData/Roaming/Sublime Text_N_3_Works/Packages/Color Schemes/MonokaiDarker.sublime-color-scheme", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Python/Python.sublime-syntax", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/User/color_schemes/python/Python.Dark.CodeInFocus.Medium - (Humble).tmTheme", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/User/Markdown.sublime-settings", "/C/Users/<USER>/OneDrive/Jorn/Workflow/3dsMax (Settings)/2023_MaxScripts_Updated/New_WIP/ControlResolutionBodyObj_Org_08.ms", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Colorsublime - Themes/cache/Colorsublime-Themes-master/themes/x3-alpha.tmTheme", "/C/Users/<USER>/AppData/Roaming/Sublime Text_WIP/Packages/CustomTabUtils/CustomTabUtils.py", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/User/color_schemes/Default/Jorn_Dark_Focused.sublime-color-scheme", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MarkdownEditing/Default.sublime-commands", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MarkdownEditing/README.md", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/ColorHelper/tests/validate_json_format.py", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/typing/st2/typing.py", "/C/Users/<USER>/OneDrive/Jorn/Workflow/Python/Py_Projects/Py_Project - RigOfficeDownload/RigOfficeDownload_18.py", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MarkdownEditing/plugins/color_schemes.py", "/C/Users/<USER>/OneDrive/Jorn/Workflow/Python/Py_Projects/Py_Project - RigOfficeDownload/RigOfficeDownload_17_New_New.py", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/AlignTab/Context.sublime-menu", "/C/Users/<USER>/OneDrive/Jorn/Workflow/3dsMax (Settings)/2023_MaxScripts_Updated/WIP/SelectionTools.ms", "/C/Users/<USER>/AppData/Local/Microsoft/Edge/User Data/Edge Wallet/113.15507.15480.16/json/wallet/README.md", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MarkdownEditing/plugin.py", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/MarkdownEditing/Context.sublime-menu", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Sublime Text - Merge with this (fromwork) 2/Packages/pyyaml/README.md", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/HighlightDuplicates/HighlightDuplicates.py", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/DeleteBlankLines/DeleteBlankLines.py", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Sublime Text - Merge with this (fromwork) 2/Packages/CustomPluginOverrides/CustomBracketHighlighter.py", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Batch File/Batch File (Compound).sublime-syntax", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Sublime Text - Merge with this (fromwork) 2/Packages/AlignTab/AlignTab.sublime-settings", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Sublime Text - Merge with this (fromwork) 2/Packages/AlignTab/Main.sublime-menu", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Sublime Text - Merge with this (fromwork) 2/Packages/AlignTab/Context.sublime-menu", "/C/Users/<USER>/AppData/Roaming/Sublime Text_08_Works_Plugins/Packages/AlignTab/Context.sublime-menu", "/C/Users/<USER>/AppData/Roaming/Sublime Text 3_01_BEFORE/Backup/20230313100207/AlignTab/Context.sublime-menu", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Plain Text Enhanced/PTE_Sample_New.txt", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Plain Text Enhanced/PlainTextEnhanced.sublime-syntax_prompt", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Plain Text Enhanced/PlainTextEnhanced.sublime-syntax_comments", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Plain Text Enhanced/PlainTextEnhanced.sublime-settings", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Plain Text Enhanced/PlainTextEnhanced.sublime-syntax", "/C/Users/<USER>/AppData/Roaming/Sublime Text_N_3_Works/Packages/Python/syntax_test_python_strings.py", "/C/Users/<USER>/OneDrive/Jorn/Workflow/3dsMax (Settings)/2023_MaxScripts_Updated/New_WIP/ControlResolutionBodyObj_Org_08.ms", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Jorn_TabUtils/Jorn_TabUtils.py", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/User/color_schemes/PlainText/Jorn_PlainText_Dark_Focused.sublime-color-scheme", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Plain Text Enhanced/Plain Text Enhanced/PlainTextEnhanced.sublime-syntax", "/C/Users/<USER>/AppData/Roaming/Sublime Text/Packages/Plain Text Enhanced/Plain Text Enhanced/macros/Insert Space, Move Caret after Checkbox and Insert Space.sublime-macro"], "find": {"height": 27.2}, "find_in_files": {"height": 110.0, "where_history": []}, "find_state": {"case_sensitive": false, "find_history": ["egg", "Py_MyHwndUtils", "/src/py_my_hwnd_utils", "Py_MyHwndUtils"], "highlight": true, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": [], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": [{"buffer": 0, "file": "src/window.py", "selected": true, "semi_transient": false, "settings": {"buffer_size": 3279, "regions": {}, "selection": [[1514, 1514]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "color_helper.scan": {"allow_scanning": false, "color_class": [{"class": "css-level-4", "scopes": ""}], "color_trigger": "(?xi)\n(?:\n    \\b(?<![-#&$])(?:\n        color\\((?!\\s*-)|(?:hsla?|(?:ok)?(?:lch|lab)|hwb|rgba?)\\(\n) |\n\\b(?<![-#&$])[\\w]{3,}(?![(-])\\b|(?<![&])\\#)\n", "current_ext": ".py", "current_syntax": "Python/Python", "enabled": true, "last_updated": 1726421412.62, "scanning": "-comment"}, "sorttabs_lastactivated": 1726421417.74, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 8677, "tab_activation_time": 1726421417, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 0, "stack_multiselect": false, "type": "text"}, {"buffer": 1, "file": "src/mess/window_manager/window_enums.py", "semi_transient": false, "settings": {"buffer_size": 416723, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "color_helper.scan": {"allow_scanning": false, "color_class": [{"class": "css-level-4", "scopes": ""}], "color_trigger": "(?xi)\n(?:\n    \\b(?<![-#&$])(?:\n        color\\((?!\\s*-)|(?:hsla?|(?:ok)?(?:lch|lab)|hwb|rgba?)\\(\n) |\n\\b(?<![-#&$])[\\w]{3,}(?![(-])\\b|(?<![&])\\#)\n", "current_ext": ".py", "current_syntax": "Python/Python", "enabled": true, "last_updated": 1726421412.62, "scanning": "-comment"}, "sorttabs_lastactivated": 1726421415.94, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 2756, "tab_activation_time": 1726421415, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 1, "stack_multiselect": false, "type": "text"}, {"buffer": 2, "file": "src/mess/window_manager/window_class.py", "semi_transient": false, "settings": {"buffer_size": 5615, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1726420513.78, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 1857, "tab_activation_time": 1726420513, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 2, "stack_multiselect": false, "type": "text"}, {"buffer": 3, "file": "src/completely_new_04.py", "semi_transient": false, "settings": {"buffer_size": 7588, "regions": {}, "selection": [[1449, 1449]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1726420513.77, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 1788, "tab_activation_time": 1726420513, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 480.0, "zoom_level": 1.0}, "stack_index": 3, "stack_multiselect": false, "type": "text"}, {"buffer": 4, "file": "src/monitor.py", "semi_transient": false, "settings": {"buffer_size": 1609, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1726420392.82, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 7654, "tab_activation_time": 1726420392, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 4, "stack_multiselect": false, "type": "text"}, {"buffer": 5, "file": "src/mess/cli.py", "semi_transient": false, "settings": {"buffer_size": 655, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1726420390.69, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 7537, "tab_activation_time": 1726420390, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 5, "stack_multiselect": false, "type": "text"}, {"buffer": 6, "file": "src/mess/log.py", "semi_transient": false, "settings": {"buffer_size": 7546, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1726420389.45, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 0, "tab_activation_time": 1726420389, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 6, "stack_multiselect": false, "type": "text"}, {"buffer": 7, "file": "src/mess/window_manager/_0_manager.py", "semi_transient": false, "settings": {"buffer_size": 8353, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1726420382.02, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 1735, "tab_activation_time": 1726420382, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 1728.0, "zoom_level": 1.0}, "stack_index": 7, "stack_multiselect": false, "type": "text"}, {"buffer": 8, "file": "src/mess/window_manager/wip.py", "semi_transient": false, "settings": {"buffer_size": 27090, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1726418723.28, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 43, "tab_activation_time": 1726418723, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 6164.8, "zoom_level": 1.0}, "stack_index": 8, "stack_multiselect": false, "type": "text"}, {"buffer": 9, "file": "src/mess/window_manager/window_utils.py", "semi_transient": false, "settings": {"buffer_size": 6203, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1726418677.37, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 0, "tab_activation_time": 1726418677, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 360.0, "zoom_level": 1.0}, "stack_index": 9, "stack_multiselect": false, "type": "text"}, {"buffer": 10, "file": "src/mess/window_manager/window_operations.py", "semi_transient": false, "settings": {"buffer_size": 4789, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1726418663.52, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 0, "tab_activation_time": 1726418663, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 10, "stack_multiselect": false, "type": "text"}, {"buffer": 11, "file": "src/mess/test.py", "semi_transient": false, "settings": {"buffer_size": 1461, "regions": {}, "selection": [[1230, 33]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1726418642.12, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 5784, "tab_activation_time": 1726418642, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 11, "stack_multiselect": false, "type": "text"}, {"buffer": 12, "file": "src/mess/main.py", "semi_transient": false, "settings": {"buffer_size": 547, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1726418641.2, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 0, "tab_activation_time": 1726418641, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 12, "stack_multiselect": false, "type": "text"}, {"buffer": 13, "file": "src/mess/utils.py", "semi_transient": false, "settings": {"buffer_size": 517, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1726418640.06, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 5784, "tab_activation_time": 1726418640, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 13, "stack_multiselect": false, "type": "text"}, {"buffer": 14, "file": "src/main.py", "semi_transient": false, "settings": {"buffer_size": 1566, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1726418639.14, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 5893, "tab_activation_time": 1726418639, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 14, "stack_multiselect": false, "type": "text"}, {"buffer": 15, "file": "src/window_tiler.py", "semi_transient": false, "settings": {"buffer_size": 1018, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "git_gutter_is_enabled": true, "origPos": [0.0, 0.0], "sorttabs_lastactivated": 1726418637.94, "syncScroll": false, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 16733380, "tab_activation_time": 1726418637, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 15, "stack_multiselect": false, "type": "text"}, {"buffer": 16, "semi_transient": false, "settings": {"buffer_size": 1761, "regions": {}, "selection": [[531, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "default_dir": "C:\\Users\\<USER>\\Desktop\\PRJ\\GIT\\JHP\\WORKFLOW\\CODE\\Personal\\Projects\\InProgress\\Py_GithubSearchUtil\\.code\\src\\gh-search_main", "git_gutter_is_enabled": false, "origPos": [0.0, 0.0], "sorttabs_lastactivated": 1709554547.34, "syncScroll": false, "syntax": "Packages/Git Formats/Git Ignore.sublime-syntax", "tab_activation_duration": 772, "tab_activation_time": 1709554547}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 16, "stack_multiselect": false, "type": "text"}, {"buffer": 17, "semi_transient": false, "settings": {"buffer_size": 3494, "regions": {}, "selection": [[67, 67]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "default_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\Code____", "git_gutter_is_enabled": false, "origPos": [0.0, 60.0], "sorttabs_lastactivated": 1709554145.53, "syncScroll": false, "syntax": "Packages/Git Formats/Git Ignore.sublime-syntax", "tab_activation_duration": 770, "tab_activation_time": 1709554145}, "translation.x": 0.0, "translation.y": 60.0, "zoom_level": 1.0}, "stack_index": 17, "stack_multiselect": false, "type": "text"}, {"buffer": 18, "semi_transient": false, "settings": {"buffer_size": 1476, "regions": {}, "selection": [[78, 78]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "default_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\Code____", "git_gutter_is_enabled": false, "origPos": [0.0, 1920.0], "sorttabs_lastactivated": 1709554039.61, "syncScroll": false, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 698, "tab_activation_time": 1709554039}, "translation.x": 0.0, "translation.y": 1861.6, "zoom_level": 1.0}, "stack_index": 18, "stack_multiselect": false, "type": "text"}]}], "incremental_find": {"height": 27.2}, "input": {"height": 40.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.SublimeLinter": {"height": 0.0}, "output.find_results": {"height": 0.0}, "pinned_build_system": "", "project": "Jorn_WindowTiler.sublime-project", "replace": {"height": 51.2}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["python", "C:\\Users\\<USER>\\OneDrive\\Jorn\\Workflow\\Python\\Py_Projects\\Py_Project - RigOfficeDownload\\RigOfficeDownload_17_New_Intermediate.bat"]], "width": 0.0}, "select_project": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 401.0, "status_bar_visible": true, "template_settings": {}}