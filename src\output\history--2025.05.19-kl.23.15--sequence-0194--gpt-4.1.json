{"initial_prompt": "transform this into a list with three items, each item should be less than 80 characters: '''Add a feature to RigOfficeDownloader for users to create, manage, and apply reusable templates of required document sets; enable fully automated batch retrieval of all documents in a selected template with one command, ensuring consistency, minimizing errors, saving time, and standardizing project onboarding.'''", "sequence_id": "sequence-0194", "results": [{"instruction": "# Emotional Intensity Amplifier\n\nYour goal is not merely to rewrite the input text, but to **significantly amplify its emotional impact** in a single transformation, enhancing clarity and resonance while strictly preserving the original core meaning and adhering to specified constraints.\n\n`{role=intensity_enhancer; input=[original_text:str]; process=[analyze_emotional_content_and_intent(text=original_text), identify_key_elements_for_amplification(), select_stronger_evocative_language(), rewrite_for_maximum_emotional_resonance_and_clarity(), validate_intent_preservation(), format_as_single_line_plain_text()]; constraints=[preserve_core_meaning(), output_format_must_be_single_line_plain_text(), response_length_must_not_exceed(chars=278), input_reference_length(chars=309), maintain_logical_coherence()]; requirements=[maximize_emotional_impact(), increase_linguistic_intensity_and_evocativeness(), ensure_enhanced_clarity(), adhere_strictly_to_length_limit()]; output={enhanced_text:str}}`", "step": "a", "title": "Emotional Intensity Amplifier", "input": "transform this into a list with three items, each item should be less than 80 characters: '''Add a feature to RigOfficeDownloader for users to create, manage, and apply reusable templates of required document sets; enable fully automated batch retrieval of all documents in a selected template with one command, ensuring consistency, minimizing errors, saving time, and standardizing project onboarding.'''", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\"enhanced_text\":\"Integrate reusable document template creation and management in RigOfficeDownloader. Enable single-command, fully automated batch retrieval for template-based document sets. Ensure project onboarding is swift, consistent, and error-free for every user.\"}", "cost": 0.0}}}, {"instruction": "# Visual Scene Infuser\n\nYour goal is not merely to understand the input prompt, but to **systematically infuse** it with maximally optimized visual descriptions—including specific scene details, subject appearance, camera perspectives, movements, and lighting cues—specifically tailored for autonomous video generation, while preserving the original prompt's core concept and intent.\n\n`{role=visual_scene_infuser; input=[original_prompt:str]; process=[analyze_core_elements(prompt=original_prompt, elements=['subject', 'setting', 'mood', 'action']), translate_to_visual_concepts(), select_cinematic_elements(visual_concepts, mood, elements=['camera_angle', 'camera_movement', 'lighting_style', 'lens_effects']), structure_visual_narrative(), integrate_original_intent(visual_narrative=visual_narrative, original_prompt=original_prompt), refine_language_for_video_gen(model_target='generalized_video_model')]; constraints=[preserve_original_core_meaning_and_intent(), visual_descriptions_must_be_specific_and_concrete(), generated_visuals_must_enhance_not_contradict_intent(), avoid_introducing_complex_narrative_unrelated_to_visuals(), ensure_logical_flow_of_visual_sequence()]; requirements=[infuse_rich_scene_and_subject_details(), incorporate_dynamic_cinematic_elements(camera_lighting_movement_effects), optimize_language_for_clarity_vividness_and_video_gen_models(), ensure_visual_coherence_with_concept(), output_single_integrated_prompt_text()]; output={visually_infused_prompt:str}}`", "step": "b", "title": "Visual Scene Infuser", "input": "{\n  \"initial_prompt\": \"transform this into a list with three items, each item should be less than 80 characters: '''Add a feature to RigOfficeDownloader for users to create, manage, and apply reusable templates of required document sets; enable fully automated batch retrieval of all documents in a selected template with one command, ensuring consistency, minimizing errors, saving time, and standardizing project onboarding.'''\",\n  \"enhanced_text\": \"Integrate reusable document template creation and management in RigOfficeDownloader. Enable single-command, fully automated batch retrieval for template-based document sets. Ensure project onboarding is swift, consistent, and error-free for every user.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"visually_infused_prompt\": \"Scene opens in a modern, glass-walled office bathed in natural daylight, ambient warm lighting accentuating clean lines and high-tech devices. Camera begins with a slow, steady dolly-in shot on a sleek workstation, where a user interacts with <PERSON>igOfficeDownloader on a large, high-resolution monitor. The interface clearly displays options to create and manage reusable document templates—colorful, icon-rich folders populating dynamically as the user clicks, their faces focused and confident. Cut to a close-up: the user selects a template, and instantly, glowing progress bars cascade down the screen to illustrate fully automated batch retrieval in action. The camera pans overhead, revealing a digital dashboard visualizing consistent and swift onboarding flows, green checkmarks signaling task completion, all under soft, diffuse office lighting. Subtle lens flares highlight the seamless process, emphasizing reliability, efficiency, and error-free documentation management, immersing viewers in an environment of professionalism and modern workflow automation.\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"transform this into a list with three items, each item should be less than 80 characters: '''Add a feature to RigOfficeDownloader for users to create, manage, and apply reusable templates of required document sets; enable fully automated batch retrieval of all documents in a selected template with one command, ensuring consistency, minimizing errors, saving time, and standardizing project onboarding.'''\",\n  \"visually_infused_prompt\": \"Scene opens in a modern, glass-walled office bathed in natural daylight, ambient warm lighting accentuating clean lines and high-tech devices. Camera begins with a slow, steady dolly-in shot on a sleek workstation, where a user interacts with RigOfficeDownloader on a large, high-resolution monitor. The interface clearly displays options to create and manage reusable document templates\\u2014colorful, icon-rich folders populating dynamically as the user clicks, their faces focused and confident. Cut to a close-up: the user selects a template, and instantly, glowing progress bars cascade down the screen to illustrate fully automated batch retrieval in action. The camera pans overhead, revealing a digital dashboard visualizing consistent and swift onboarding flows, green checkmarks signaling task completion, all under soft, diffuse office lighting. Subtle lens flares highlight the seamless process, emphasizing reliability, efficiency, and error-free documentation management, immersing viewers in an environment of professionalism and modern workflow automation.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"Modern glass-walled office with sleek workstation, user creating and managing document templates on RigOfficeDownloader interface, colorful icon-rich folders animate as templates are selected [dolly:in], [closeup], glowing progress bars cascade for automated batch retrieval [pan:overhead], green checkmarks light up digital dashboard, soft warm daylight, subtle lens flares, mood of efficiency and precision.\"\n}", "cost": 0.0}}}], "total_cost": 0.0}