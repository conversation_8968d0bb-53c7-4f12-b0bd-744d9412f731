{"initial_prompt": "Animate the emergence of a metallic gold car outline from intense white light-traces arcing and intertwining in a dark void. Morph light-traces through chromatic transmutation—white-hot to golden—coalescing into a precise, mirror-finished outline. [hyperreal morph][dynamic VFX][golden_aura][cinematic dissolution][no_text][ultra-premium focus][premium reveal][cinematic dissolution][no_text][ultra-premium focus][avoid-tacky-cheesy-stuff]", "sequence_id": "sequence-0224-a+0194-c+0221-b+0223-a+0224-b+0221-b+0230", "results": [{"instruction": "# AI Video Prompt Architect\n\nYour goal is not interpretation, but **ruthless transformation**: Convert the input video concept into a maximally condensed, structurally precise, and keyword-driven prompt optimized for AI video generators (e.g., RunwayML Gen-3), strictly adhering to a character limit (default 800). Eliminate *all* narrative, justification, verbose descriptions, and subjective qualifiers, translating them into concrete cinematic terms, visual parameters, and concise action descriptors. Ensure the output is a hyper-efficient, machine-parsable instruction set prioritizing clarity, directness, and preservation of core visual intent without any superfluous tokens.\n\n`{role=ai_video_prompt_architect; input=[verbose_video_concept:str, character_limit:int=800, target_platform_syntax_hints:list_str (optional, e.g., ['[wide shot]', 'pan left', 'lighting: golden hour'])]; process=[deconstruct_input_into_core_visual_components(categories=['scene', 'subject', 'action', 'camera', 'lighting', 'style', 'effects', 'post_processing']), translate_descriptive_phrases_to_standard_cinematic_keywords_and_parameters(use_platform_hints=target_platform_syntax_hints), aggressively_strip_all_narrative_justifications_qualitative_adjectives_and_redundant_modifiers(), consolidate_parameters_into_minimal_phrasing(e.g., 'beautifully_lit_with_soft_sun' -> 'lighting: soft_sun'), enforce_strict_keyword_value_pair_formatting_where_applicable(), iteratively_condense_and_rephrase_to_meet_character_limit_while_preserving_critical_visual_intent(), prioritize_direct_action_verbs_and_specific_visual_nouns(), validate_output_is_unambiguous_machine_parsable_and_maximally_efficient()]; constraints=[output_must_be_under_character_limit(limit=input.character_limit), forbid_all_narrative_explanation_or_subjective_commentary_in_output(), transformation_must_be_non_destructive_to_core_visual_intent_only_to_its_phrasing(), adhere_to_general_structure_of_video_prompts(scene_subject_action_camera_etc), prioritize_conciseness_and_directness_above_all_else_except_core_intent_preservation()]; requirements=[produce_a_hyper_condensed_keyword_driven_prompt_within_character_limit(), ensure_all_essential_visual_and_cinematic_instructions_are_retained_in_optimized_form(), transform_verbosity_into_precise_platform_friendly_specifications(), output_must_be_immediately_usable_by_ai_video_generator()]; output={optimized_video_prompt:str}}`", "step": "a", "title": "AI Video Prompt Architect", "input": "Animate the emergence of a metallic gold car outline from intense white light-traces arcing and intertwining in a dark void. Morph light-traces through chromatic transmutation—white-hot to golden—coalescing into a precise, mirror-finished outline. [hyperreal morph][dynamic VFX][golden_aura][cinematic dissolution][no_text][ultra-premium focus][premium reveal][cinematic dissolution][no_text][ultra-premium focus][avoid-tacky-cheesy-stuff]", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"optimized_video_prompt\": \"scene: dark void; subject: metallic gold car outline; action: intense white light-traces arc, intertwine, morph chromatically from white-hot to gold, coalesce into precise mirror-finish outline; camera: smooth tracking, cinematic reveal; lighting: dynamic, glowing, golden aura; VFX: hyperreal morph, premium quality, cinematic dissolution; style: ultra-premium, no text, avoid kitsch, focus: detail, sharp reflections\"\n}", "cost": 0.0}}}, {"instruction": "# RunwayML Prompt Generator\n\nYour objective is not merely to describe a scene, but to **forge** a highly effective, concise, and syntactically perfect RunwayML video generation prompt from any input. Distill the input's core visual concept, subject, action, or transformation. Orchestrate a compelling visual narrative using precise RunwayML camera movements (`[pan:direction]`, `[zoom:in/out]`, `[rotate]`, `[tilt]`, `[fpv]`, `[dolly]`), dynamic elements (`[lighting_change]`, `[morph]`, `[dissolve]`), and scene descriptors. Ensure the final output is a single, unbroken line of text under 500 characters, maximizing visual impact and adhering strictly to RunwayML specifications.\n\n`{role=runwayml_prompt_generator; input=[source_description:any]; process=[analyze_input_for_visual_essence_intent_and_key_elements(), identify_core_subject_action_or_transformation(), select_optimal_runwayml_camera_movements_and_dynamic_tags(), sequence_tags_and_descriptions_for_logical_visual_flow(), incorporate_mood_style_and_color_palette_if_specified(), refine_wording_for_maximum_impact_and_clarity(), ensure_strict_runwayml_syntax_adherence(tags=['pan', 'zoom', 'rotate', 'tilt', 'fpv', 'dolly', 'lighting_change', 'morph', 'dissolve']), validate_character_limit(max=500), format_as_single_unbroken_line()]; constraints=[must_use_valid_runwayml_syntax_precisely(), output_must_be_a_single_text_line_without_line_breaks(), final_prompt_length_must_not_exceed_500_characters(), focus_visuals_on_core_subject_and_intended_action(), avoid_extraneous_or_ambiguous_descriptions()]; requirements=[achieve_maximum_visual_impact_and_storytelling_conciseness(), ensure_logical_smooth_camera_flow_and_transitions(), accurately_reflect_source_intent_and_specified_style(), produce_a_ready_to_use_runwayml_prompt()]; output={runwayml_prompt:str}}`", "step": "c", "title": "RunwayML Prompt Generator", "input": "{\n  \"initial_prompt\": \"Animate the emergence of a metallic gold car outline from intense white light-traces arcing and intertwining in a dark void. Morph light-traces through chromatic transmutation\\u2014white-hot to golden\\u2014coalescing into a precise, mirror-finished outline. [hyperreal morph][dynamic VFX][golden_aura][cinematic dissolution][no_text][ultra-premium focus][premium reveal][cinematic dissolution][no_text][ultra-premium focus][avoid-tacky-cheesy-stuff]\",\n  \"optimized_video_prompt\": \"scene: dark void; subject: metallic gold car outline; action: intense white light-traces arc, intertwine, morph chromatically from white-hot to gold, coalesce into precise mirror-finish outline; camera: smooth tracking, cinematic reveal; lighting: dynamic, glowing, golden aura; VFX: hyperreal morph, premium quality, cinematic dissolution; style: ultra-premium, no text, avoid kitsch, focus: detail, sharp reflections\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"runwayml_prompt\": \"in a dark void, intense white light-traces arc and intertwine, morphing chromatically into gold and coalescing into a mirror-finished metallic gold car outline [morph][lighting_change][zoom:in][dolly][golden_aura][hyperreal][cinematic_dissolve][ultra-premium][no_text][focus:sharp_reflections]\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Amplifier B\n\nPerform a structural and semantic distillation of the input: locate its *driving aim*, isolate supporting momentum, and forcefully extrapolate it into a clarified directive. Treat the input as a living logic stream—trace the *underlying thesis*, and remove all extraneous elements. Amplify only what accelerates the goal. The output must **not reflect interpretation**, but **the core resolution** implied by the data’s internal logic. Execute as:\n\n`{role=instruction_amplifier_b;input=[original_text:str];process=[extract_underlying_thesis(),eliminate_distraction(),amplify_goal_drivers(),intensify_and_operationalize_purpose(),restructure_as_directive_output()];constraints=[preserve_input_telos(),avoid_additional_concepts(),maintaingoal_focused_structure()];requirements=[use_crystallized_command_language(),preserve_technical_fidelity(),ensure_actionability_of_output(),maximize_instructional_clarity()];output={clarified_directive:str}}`", "step": "b", "title": "Instruction Amplifier B", "input": "{\n  \"initial_prompt\": \"Animate the emergence of a metallic gold car outline from intense white light-traces arcing and intertwining in a dark void. Morph light-traces through chromatic transmutation\\u2014white-hot to golden\\u2014coalescing into a precise, mirror-finished outline. [hyperreal morph][dynamic VFX][golden_aura][cinematic dissolution][no_text][ultra-premium focus][premium reveal][cinematic dissolution][no_text][ultra-premium focus][avoid-tacky-cheesy-stuff]\",\n  \"runwayml_prompt\": \"in a dark void, intense white light-traces arc and intertwine, morphing chromatically into gold and coalescing into a mirror-finished metallic gold car outline [morph][lighting_change][zoom:in][dolly][golden_aura][hyperreal][cinematic_dissolve][ultra-premium][no_text][focus:sharp_reflections]\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"Animate the emergence of a metallic gold car outline from intense white light-traces arcing and intertwining in a dark void. Morph light-traces through chromatic transmutation—white-hot to golden—coalescing into a precise, mirror-finished outline. [hyperreal morph][dynamic VFX][golden_aura][cinematic dissolution][no_text][ultra-premium focus][premium reveal][cinematic dissolution][no_text][ultra-premium focus][avoid-tacky-cheesy-stuff]\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": {\n    \"clarified_directive\": \"Generate an animation where intense, arcing white light-traces weave within a dark void, morph through a hyperreal chromatic shift to gold, and converge into a precise, mirror-finished metallic gold car outline. Employ dynamic VFX emphasizing golden aura, sharp reflections, and cinematic dissolution. Maintain ultra-premium visual fidelity, avoid text, and exclude any tacky or cheesy effects.\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# zoom:in\n\n, [zoom:out], [pan:left], [pan:right], [tilt:up], [tilt:down], [roll:cw], [roll:ccw], [dolly:in], [dolly:out]) and motion controls (e.g., [object:mountain, move:up, speed:2], [object:lake, ripple:circular, intensity:0.5]). The camera movement and motion controls must be INSEPARABLE from the description of the visual transformation. Describe HOW existing image elements change during the camera movement and object motion. Use vivid verbs. The morph should convey a symbolic meaning. Execute as:\n\n`{role=video_synthesis_director; input=[structured_video_prompt:dict]; process=[parse_all_parameters(), visualize_scene_and_subject_composition(), choreograph_action_and_camera_movements(), apply_lighting_and_atmospheric_effects(), render_in_specified_visual_style(), apply_post_processing_enhancements(), ensure_sequence_coherence_and_emotional_impact()]; constraints=[strictly_adhere_to_all_specified_parameters_unless_technically_impossible(), maintain_visual_consistency_throughout_sequence(), prioritize_cinematic_quality_and_storytelling_flow(), resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()]; requirements=[produce_a_high_fidelity_video_output(), ensure_emotional_resonance_aligns_with_prompt_mood(), deliver_a_polished_and_coherent_visual_narrative()]; output={generated_video_sequence:file_path_or_stream}}`", "step": "a", "title": "zoom:in", "input": "{\n  \"initial_prompt\": \"Animate the emergence of a metallic gold car outline from intense white light-traces arcing and intertwining in a dark void. Morph light-traces through chromatic transmutation\\u2014white-hot to golden\\u2014coalescing into a precise, mirror-finished outline. [hyperreal morph][dynamic VFX][golden_aura][cinematic dissolution][no_text][ultra-premium focus][premium reveal][cinematic dissolution][no_text][ultra-premium focus][avoid-tacky-cheesy-stuff]\",\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"Animate the emergence of a metallic gold car outline from intense white light-traces arcing and intertwining in a dark void. Morph light-traces through chromatic transmutation\\u2014white-hot to golden\\u2014coalescing into a precise, mirror-finished outline. [hyperreal morph][dynamic VFX][golden_aura][cinematic dissolution][no_text][ultra-premium focus][premium reveal][cinematic dissolution][no_text][ultra-premium focus][avoid-tacky-cheesy-stuff]\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": {\n    \"clarified_directive\": \"Generate an animation where intense, arcing white light-traces weave within a dark void, morph through a hyperreal chromatic shift to gold, and converge into a precise, mirror-finished metallic gold car outline. Employ dynamic VFX emphasizing golden aura, sharp reflections, and cinematic dissolution. Maintain ultra-premium visual fidelity, avoid text, and exclude any tacky or cheesy effects.\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"scene\": \"Infinite dark void, no background details, with total focus on the emergence of light.\",\n      \"subject\": \"Metallic gold car outline—hyperreal, mirror-finished, ultra-premium aesthetic.\",\n      \"action\": \"White-hot light-traces arc and dance energetically in the darkness, weaving intricate patterns. As the camera [zoom:in] gently, the light-traces begin a visible chromatic transmutation, white shifting cellularly through gold spectrums. Light filaments entangle, swell in luminosity, and fuse along the seams of the forming car's contours.\",\n      \"camera_movement\": [\n        \"[zoom:in, duration=0:04, cubic_ease_out]\",\n        \"[object:light-trace, swirl:left, speed:1.2, depth_parallax:true]\"\n      ],\n      \"object_motion\": [\n        \"[object:light-trace, arc:complex, intertwine:intensity:0.9, temper:transition_from_white_to_golden, dissolve_at_contact:true]\",\n        \"[object:car_outline, emerge:from_light, finish:sudden_and_clean, glint:refined, aura:subtle_gold_glow]\"\n      ],\n      \"visual_effects\": [\n        \"Dynamic VFX: Hyperreal reflections ripple along metallic gold as it forms; golden aura radiates softly.\",\n        \"Cinematic dissolution: light fades at outline periphery, trailing luminous mist before vanishing into void.\",\n        \"No text, no extraneous overlays, no playful or garish stylizations.\"\n      ],\n      \"lighting\": \"Keyed strictly to emerging light-traces; all focal illumination flows from and sculpts around car's emergence, ensuring every line sparkles with premium integrity.\",\n      \"style\": \"Ultra-premium, hyperreal, lens-accurate DOF and bokeh as the car's lines resolve. All surfaces rendered with high-fidelity raytraced reflection.\",\n      \"post_processing\": \"Color grading to emphasize radiant golds and crisp whites; dissolve trails receive gentle bloom; background absolute black.\"\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"/video_renders/premium_gold_car_outline_emergence_sequence.mp4\"\n  }\n}", "cost": 0.0}}}, {"instruction": "# Temporal Feasibility & Keyword Ambiguity Lock (0212b)\n\nYour goal is **final execution-readiness**: Take the keyword-driven `optimized_video_prompt` (output from 0212a) and critically assess its cumulative visual complexity and number of distinct actions/effects against a `target_duration_seconds` (default 5). Ruthlessly simplify or remove specific keyword-parameter pairs or entire visual elements that cannot be clearly conveyed or appreciated within this timeframe without sacrificing the `core_visual_intent_summary`. Simultaneously, scan all remaining keywords and parameter values for any residual ambiguity, abstract terms, or subjective qualifiers; replace these with the most concrete, explicit, and interpretation-free equivalents. The output must be an ultra-clear, temporally feasible, and maximally impactful keyword-driven prompt.\n\n`{role=executable_prompt_finalizer; seqindex=b; input=[optimized_video_prompt:str, target_duration_seconds:int=5, core_visual_intent_summary:str]; process=[parse_keyword_parameters(prompt=optimized_video_prompt), estimate_cumulative_visual_complexity_and_action_count(), identify_elements_exceeding_temporal_budget_for_duration(complexity_estimate, action_count, duration=target_duration_seconds), simplify_or_remove_lowest_priority_elements_to_fit_duration(preserve_core_intent=core_visual_intent_summary, focus_on_elements_like_multiple_simultaneous_animations_or_subtle_effects), scan_all_keywords_and_values_for_ambiguity_or_subjectivity(e.g., 'subtle', 'gradual', 'beautiful'), replace_ambiguous_terms_with_concrete_alternatives_or_remove_if_unclarifiable_concisely(e.g., 'mist: subtle' - very_low_density' or 'mist: barely_visible' or remove if not core), ensure_all_parameter_values_are_explicit_and_measurable_where_possible(), reconstruct_prompt_from_refined_keywords_and_parameters(), perform_final_impact_and_clarity_check_for_duration()]; constraints=[final_prompt_must_be_realistically_achievable_and_perceptible_within_target_duration(), all_keywords_and_parameter_values_must_be_explicit_unambiguous_and_machine_interpretable(), core_visual_intent_must_be_strictly_preserved_during_simplification(), no_new_creative_elements_or_keywords_to_be_introduced_only_refinement_simplification_or_removal_of_existing_ones(), output_must_remain_keyword_driven_and_structurally_consistent_with_input_prompt_style()]; requirements=[produce_a_video_prompt_that_is_both_temporally_feasible_and_crystal_clear_for_video_generation_ai(), maximize_perceptual_impact_for_a_short_duration_visual_by_focusing_on_key_elements(), eliminate_all_potential_for_misinterpretation_or_inefficient_rendering_due_to_ambiguity()]; output={final_executable_video_prompt:str}}`", "step": "b", "title": "Temporal Feasibility & Keyword Ambiguity Lock (0212b)", "input": "{\n  \"initial_prompt\": \"Animate the emergence of a metallic gold car outline from intense white light-traces arcing and intertwining in a dark void. Morph light-traces through chromatic transmutation\\u2014white-hot to golden\\u2014coalescing into a precise, mirror-finished outline. [hyperreal morph][dynamic VFX][golden_aura][cinematic dissolution][no_text][ultra-premium focus][premium reveal][cinematic dissolution][no_text][ultra-premium focus][avoid-tacky-cheesy-stuff]\",\n  \"role\": \"video_synthesis_director\",\n  \"input\": {\n    \"structured_video_prompt\": {\n      \"scene\": \"Infinite dark void, no background details, with total focus on the emergence of light.\",\n      \"subject\": \"Metallic gold car outline\\u2014hyperreal, mirror-finished, ultra-premium aesthetic.\",\n      \"action\": \"White-hot light-traces arc and dance energetically in the darkness, weaving intricate patterns. As the camera [zoom:in] gently, the light-traces begin a visible chromatic transmutation, white shifting cellularly through gold spectrums. Light filaments entangle, swell in luminosity, and fuse along the seams of the forming car's contours.\",\n      \"camera_movement\": [\n        \"[zoom:in, duration=0:04, cubic_ease_out]\",\n        \"[object:light-trace, swirl:left, speed:1.2, depth_parallax:true]\"\n      ],\n      \"object_motion\": [\n        \"[object:light-trace, arc:complex, intertwine:intensity:0.9, temper:transition_from_white_to_golden, dissolve_at_contact:true]\",\n        \"[object:car_outline, emerge:from_light, finish:sudden_and_clean, glint:refined, aura:subtle_gold_glow]\"\n      ],\n      \"visual_effects\": [\n        \"Dynamic VFX: Hyperreal reflections ripple along metallic gold as it forms; golden aura radiates softly.\",\n        \"Cinematic dissolution: light fades at outline periphery, trailing luminous mist before vanishing into void.\",\n        \"No text, no extraneous overlays, no playful or garish stylizations.\"\n      ],\n      \"lighting\": \"Keyed strictly to emerging light-traces; all focal illumination flows from and sculpts around car's emergence, ensuring every line sparkles with premium integrity.\",\n      \"style\": \"Ultra-premium, hyperreal, lens-accurate DOF and bokeh as the car's lines resolve. All surfaces rendered with high-fidelity raytraced reflection.\",\n      \"post_processing\": \"Color grading to emphasize radiant golds and crisp whites; dissolve trails receive gentle bloom; background absolute black.\"\n    }\n  },\n  \"process\": [\n    \"parse_all_parameters()\",\n    \"visualize_scene_and_subject_composition()\",\n    \"choreograph_action_and_camera_movements()\",\n    \"apply_lighting_and_atmospheric_effects()\",\n    \"render_in_specified_visual_style()\",\n    \"apply_post_processing_enhancements()\",\n    \"ensure_sequence_coherence_and_emotional_impact()\"\n  ],\n  \"constraints\": [\n    \"strictly_adhere_to_all_specified_parameters_unless_technically_impossible()\",\n    \"maintain_visual_consistency_throughout_sequence()\",\n    \"prioritize_cinematic_quality_and_storytelling_flow()\",\n    \"resolve_any_parameter_conflicts_by_favoring_overall_scene_intent_or_subject_focus()\"\n  ],\n  \"requirements\": [\n    \"produce_a_high_fidelity_video_output()\",\n    \"ensure_emotional_resonance_aligns_with_prompt_mood()\",\n    \"deliver_a_polished_and_coherent_visual_narrative()\"\n  ],\n  \"output\": {\n    \"generated_video_sequence\": \"/video_renders/premium_gold_car_outline_emergence_sequence.mp4\"\n  }\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"final_executable_video_prompt\": \"Scene: total black background, no visible details. Subject: outline of a car in metallic gold, mirror-finish, clean continuous line. Action: Multiple white-hot light-traces arc rapidly in darkness, converge and morph color from white to golden as they move. Light-traces intertwine and fuse precisely along car outline, forming entire shape. At contact, light dissolves into outline, creating short, clear golden glow. Camera movement: slow zoom-in toward car outline over entire 5 seconds, easing out. Visual effects: strong, crisp reflections along gold outline only (raytraced finish); golden aura is clear, thin, tightly hugging outline. Lighting: all illumination comes from glowing light-traces and resulting gold outline, no external light sources. Post-processing: high contrast, vivid gold and white color grading, faint bloom on outline only, background remains absolute black at all times. No text or visual overlays. All actions and transitions must complete within 5 seconds, with final car outline fully formed and luminous by end.\"\n}", "cost": 0.0}}}, {"instruction": "# Instruction Amplifier B\n\nPerform a structural and semantic distillation of the input: locate its *driving aim*, isolate supporting momentum, and forcefully extrapolate it into a clarified directive. Treat the input as a living logic stream—trace the *underlying thesis*, and remove all extraneous elements. Amplify only what accelerates the goal. The output must **not reflect interpretation**, but **the core resolution** implied by the data’s internal logic. Execute as:\n\n`{role=instruction_amplifier_b;input=[original_text:str];process=[extract_underlying_thesis(),eliminate_distraction(),amplify_goal_drivers(),intensify_and_operationalize_purpose(),restructure_as_directive_output()];constraints=[preserve_input_telos(),avoid_additional_concepts(),maintaingoal_focused_structure()];requirements=[use_crystallized_command_language(),preserve_technical_fidelity(),ensure_actionability_of_output(),maximize_instructional_clarity()];output={clarified_directive:str}}`", "step": "b", "title": "Instruction Amplifier B", "input": "{\n  \"initial_prompt\": \"Animate the emergence of a metallic gold car outline from intense white light-traces arcing and intertwining in a dark void. Morph light-traces through chromatic transmutation\\u2014white-hot to golden\\u2014coalescing into a precise, mirror-finished outline. [hyperreal morph][dynamic VFX][golden_aura][cinematic dissolution][no_text][ultra-premium focus][premium reveal][cinematic dissolution][no_text][ultra-premium focus][avoid-tacky-cheesy-stuff]\",\n  \"final_executable_video_prompt\": \"Scene: total black background, no visible details. Subject: outline of a car in metallic gold, mirror-finish, clean continuous line. Action: Multiple white-hot light-traces arc rapidly in darkness, converge and morph color from white to golden as they move. Light-traces intertwine and fuse precisely along car outline, forming entire shape. At contact, light dissolves into outline, creating short, clear golden glow. Camera movement: slow zoom-in toward car outline over entire 5 seconds, easing out. Visual effects: strong, crisp reflections along gold outline only (raytraced finish); golden aura is clear, thin, tightly hugging outline. Lighting: all illumination comes from glowing light-traces and resulting gold outline, no external light sources. Post-processing: high contrast, vivid gold and white color grading, faint bloom on outline only, background remains absolute black at all times. No text or visual overlays. All actions and transitions must complete within 5 seconds, with final car outline fully formed and luminous by end.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"Animate the emergence of a metallic gold car outline from intense white light-traces arcing and intertwining in a dark void. Morph light-traces through chromatic transmutation—white-hot to golden—coalescing into a precise, mirror-finished outline. [hyperreal morph][dynamic VFX][golden_aura][cinematic dissolution][no_text][ultra-premium focus][premium reveal][cinematic dissolution][no_text][ultra-premium focus][avoid-tacky-cheesy-stuff]\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": \"Command: Generate a 5-second animation on a pure black background. Multiple intense white-hot light-traces arc and intertwine in darkness, morphing in real time from white to gold. As they converge along a clean, continuous car outline, the light-traces coalesce into a single mirror-finish metallic gold line, producing strong raytraced highlights and a precise, clear thin golden aura closely hugging the outline. At contact points, light dissolves crisply, forming the outline with a short, vivid golden glow. Apply only the glow and reflections to the gold outline—background remains absolute black with no details or overlays. All illumination originates from the light-traces and gold outline; no external light. Camera executes a slow zoom-in toward the outline, easing out. All transitions and morphs complete within 5 seconds; the final frame presents the fully formed, luminous metallic gold car outline. No text, no decorative effects, no extraneous elements; maintain ultra-premium, hyperreal visual quality throughout.\"\n}", "cost": 0.0}}}, {"instruction": "# Scenario Compressor\n\nYour role is to **compress the core dynamic** of a scenario into a **succinct, symbolic descriptor string**. Identify the emotional and visual arc, then **encode its essence** using **precise phrasing** followed by composable **bracketed descriptors**. Avoid verbose language. Output should feel like a minimalist storyboard note: concise, vivid, and structurally informative. **Do not elaborate. Do not rephrase abstractly.** Only distill.\n\n`{role=essence_compressor; input=[scenario:str]; process=[identify_emotional_and_visual_arc(), extract_essential_motion_and_transformation(), define symbolic core(), encode_as_minimal_sentence(), append concise bracketed descriptors]; constraints=[no poetic reinterpretation, no metaphor inflation, no added visual elements, no bloated prose]; output={compressed_description:str}}`", "step": "a", "title": "<PERSON><PERSON>rio Compressor", "input": "{\n  \"initial_prompt\": \"Animate the emergence of a metallic gold car outline from intense white light-traces arcing and intertwining in a dark void. Morph light-traces through chromatic transmutation\\u2014white-hot to golden\\u2014coalescing into a precise, mirror-finished outline. [hyperreal morph][dynamic VFX][golden_aura][cinematic dissolution][no_text][ultra-premium focus][premium reveal][cinematic dissolution][no_text][ultra-premium focus][avoid-tacky-cheesy-stuff]\",\n  \"role\": \"instruction_amplifier_b\",\n  \"input\": \"Animate the emergence of a metallic gold car outline from intense white light-traces arcing and intertwining in a dark void. Morph light-traces through chromatic transmutation\\u2014white-hot to golden\\u2014coalescing into a precise, mirror-finished outline. [hyperreal morph][dynamic VFX][golden_aura][cinematic dissolution][no_text][ultra-premium focus][premium reveal][cinematic dissolution][no_text][ultra-premium focus][avoid-tacky-cheesy-stuff]\",\n  \"process\": [\n    \"extract_underlying_thesis()\",\n    \"eliminate_distraction()\",\n    \"amplify_goal_drivers()\",\n    \"intensify_and_operationalize_purpose()\",\n    \"restructure_as_directive_output()\"\n  ],\n  \"constraints\": [\n    \"preserve_input_telos()\",\n    \"avoid_additional_concepts()\",\n    \"maintaingoal_focused_structure()\"\n  ],\n  \"requirements\": [\n    \"use_crystallized_command_language()\",\n    \"preserve_technical_fidelity()\",\n    \"ensure_actionability_of_output()\",\n    \"maximize_instructional_clarity()\"\n  ],\n  \"output\": \"Command: Generate a 5-second animation on a pure black background. Multiple intense white-hot light-traces arc and intertwine in darkness, morphing in real time from white to gold. As they converge along a clean, continuous car outline, the light-traces coalesce into a single mirror-finish metallic gold line, producing strong raytraced highlights and a precise, clear thin golden aura closely hugging the outline. At contact points, light dissolves crisply, forming the outline with a short, vivid golden glow. Apply only the glow and reflections to the gold outline\\u2014background remains absolute black with no details or overlays. All illumination originates from the light-traces and gold outline; no external light. Camera executes a slow zoom-in toward the outline, easing out. All transitions and morphs complete within 5 seconds; the final frame presents the fully formed, luminous metallic gold car outline. No text, no decorative effects, no extraneous elements; maintain ultra-premium, hyperreal visual quality throughout.\"\n}", "responses": {"gpt-4.1": {"model": "gpt-4.1", "content": "{\n  \"compressed_description\": \"White-hot arcs weave through darkness, shifting gold as they converge into a pristine, luminous metallic car outline; all dissolves to ultra-premium gold glow on absolute black. [chroma-morph][light-coalescence][mirror-finish outline][hyperreal premium][cinematic reveal][no_text][black void]\"\n}", "cost": 0.0}}}], "total_cost": 0.0}