# RigOfficeDownloader

## Purpose
Automate and streamline technical document retrieval from NOV RigDoc, eliminating tedious manual effort and ensuring rapid, organized access to engineering documentation.

---

## Features
- Three-stage sequential pipeline: **Documents → Files → Downloads**
- Automated and interactive (menu-driven) run modes
- Editable Markdown checkpoints for document/file selection
- Configurable pattern-based filter chains (auto-inclusion/exclusion)
- Metadata-driven file naming and hierarchical subfolder organization
- Robust Selenium-based browser automation, smart waiting, deduplication, error handling
- Strict field ordering in JSON/Markdown outputs
- Full traceability/auditability via JSON and Markdown records

---

## Workflow
1. **Configure** rig number and search URLs (set filters/templates)
2. **Fetch** document metadata from RigDoc to `<rig>-a-docs.json`
3. **Export** docs to Markdown (`<rig>-a-docs.md`); review/edit (set `item_include`)
4. **Import** updated selections from Markdown → JSON
5. **Fetch** file metadata for selected docs → `<rig>-b-files.json`
6. **Export** files to Markdown (`<rig>-b-files.md`); set `item_download` as needed
7. **Import** updated file selections from Markdown
8. **Download** all marked files to `outputs/downloads/<rig>`, auto-organized via generated subfolder paths

---

## Directory Structure
```

outputs/
├── data/         # All metadata/selection (JSON + Markdown)
│   ├── <rig>-a-docs.json
│   ├── <rig>-a-docs.md
│   ├── <rig>-b-files.json
│   └── <rig>-b-files.md
└── downloads/    # All downloaded files, by rig/subfolder structure
└── <rig>/
├── <subfolder>/
│   └── \<item\_generated\_name>.<ext>
└── ...

````

---

## Setup Prerequisites
- Windows OS
- Python 3.11+
- Chrome browser (with valid Chromedriver)
- Valid NOV RigDoc credentials
- Install dependencies:
  ```bash
  pip install -r requirements.txt
````

---

## Execution Run Modes

* `RigOfficeDownloader-v4.bat --auto` (full automation)
* `RigOfficeDownloader-v4.bat --interactive` (menu-driven)
* `RigOfficeDownloader-v4.bat --config` (edit filters/templates)

---

## Configuration Filtering

* Edit filter chains (glob patterns: `*G000*`, `*VOID*`, etc.)
* Specify rig number and search URLs as config
* Modify which fields govern item selection

---

## Benefits

* ≥75% reduction in document preparation time
* Consistent, metadata-driven file organization

---

## Comparison Table

|                       | Manual             | Automated                        |
| --------------------- | ------------------ | -------------------------------- |
| **Time per project**  | 4–6 hours          | <1 hour                          |
| **File organization** | Error-prone/ad hoc | Standardized/metadata-driven     |
| **User review**       | Tedious/repetitive | Markdown checkpoints             |
| **Auditability**      | None               | Full JSON/Markdown traceability  |
| **Expandability**     | N/A                | Configurable, reusable templates |

---

## Extensibility

* Built-in support: subfolders, filter chains, field ordering, advanced config
* Easily adaptable for new departments/organizational patterns

---

## Philosophy

> "Complexity is the enemy of execution — Optimized for engineers, by engineers."

