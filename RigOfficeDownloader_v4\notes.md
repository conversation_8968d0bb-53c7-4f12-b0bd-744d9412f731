
# Status

Work in progress

## Konsept/Prosess

<PERSON> for "automatisk" innhenting av dokumentasjon via. https://rigdoc.nov.com (prosjektinfo, tegninger, osv).


### 1. <PERSON><PERSON> utifra "R4511 - Internkontroll (119299318-Rev.0).xlsx"

    `"C:\Vault\Norway Simulation\Rigs\R4511-R5385-StenaIceMAX\docs\R4511-Internkontroll(119299318-Rev.0).xlsx"`
    ├── ---
    └── `C:\Vault\Norway Simulation\`
        └── `Rigs\R4511 - R5385 - Stena IceMAX\Documents`
            └── `IMPORTANT - Internal Audit - Qualification Record for Simulator 3D Development`
                │
                └── `R4511 - Internkontroll (119299318-Rev.0).xlsx`


### 2. Samler inn essensiell info

| Case No           | Equipment                                   | GA drawing             | GA rev.   | Verified by  | Comment        |
| ----------------- | ------------------------------------------- | ---------------------- | --------- | ------------ | -------------- |
| EQ-28209-104A     | Cylinder Hoisting Rig, 1250st, 48m          | 19129153-GAD           | 01        |              | 29273-106      |
| EQ-28209-104A     | Sheave Cluster Cylinder Rig 1250            | 19129152-GAD           | 02        |              | 29273-106      |
| EQ-28209-106A     | Top Drive, 1250t AC                         | 19140396-GAD           | 01        |              | 29273-106      |
| EQ-28209-120A     | Power Slip 1500 ton                         | DD-10141101-605        | 02        |              | 29273-106      |
| V6056             | Iron Roughneck-Hydratong MPT-200            | V6056-D1100-G0001      | 5         |              | 29273-106      |
| V6051             | Tubular Chute, Main                         | V6051-D1195-G0002      | 2         |              | 29273-107      |
| V6045             | Fingerboards                                | V6045-D1202-G0001      | 03A       |              | 29273-107      |
| V6042             | Hydraracker IV, Main                        | V6042-D1213-G0001      | 0         |              | 29273-107      |
| V6054             | Pipe guide, main under drillfloor           | V6054-D1194-G0002      | 3         |              | 29273-107      |
| EQ-28209-103A     | Elevated Backup Tong; EBT-150               | 1906283-GAD            | 03        |              | 29273-107      |


### 3. RigOffice søkestrenger

    `"search_urls"`
    ├── ---
    ├── `https://rigdoc.nov.com/search/advancedsearch?q=`
    │   │
    │   └── "EQ-28209-104A" -> `&CaseNo=EQ-28209-104A&DocTypeId=66`
    │
    └── `https://rigdoc.nov.com/search?q=`
        │
        ├── "19129153-GAD" -> `19129153-GAD&CaseType=Equipment`
        ├── "19129152-GAD" -> `19129152-GAD&CaseType=Equipment`
        ├── "19140396-GAD" -> `19140396-GAD&CaseType=Equipment`
        ├── "DD-10141101-605" -> `DD-10141101-605&CaseType=Equipment`
        ├── "V6056-D1100-G0001" -> `V6056-D1100-G0001&CaseType=Equipment`
        ├── "V6051-D1195-G0002" -> `V6051-D1195-G0002&CaseType=Equipment`
        ├── "V6045-D1202-G0001" -> `V6045-D1202-G0001&CaseType=Equipment`
        ├── "V6042-D1213-G0001" -> `V6042-D1213-G0001&CaseType=Equipment`
        ├── "V6054-D1194-G0002" -> `V6054-D1194-G0002&CaseType=Equipment`
        ├── "19066283-GAD" -> `19066283-GAD&CaseType=Equipment`
        └── "29273-106" -> `29273-106&CaseType=Task / Deliverable`

---


## Intent
To automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.

## Key Project Aspects

### Primary Problem
Engineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck—it's the manual preparation process.

### Solution Approach
A Python-based utility that:
1. Automatically scrapes document metadata from RigOffice
2. Extracts file information from those documents
3. Downloads and organizes selected files based on user criteria

### Current State
Functional working prototype that:
- Uses a 3-step workflow (document metadata → file metadata → download)
- Stores intermediate results in JSON format
- Allows user intervention between steps
- Provides progress feedback

### Critical Next Steps
1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches
2. **Implement file hash checking** to prevent redundant downloads
3. **Improve progress visibility** during lengthy scraping operations

### Core Technical Pattern
A single-file, modular approach using:
- Selenium for browser automation
- JSON for data storage
- Three-stage processing with user control points
- Incremental updates to avoid redundant work

### Key Success Metrics
- Reduce documentation gathering time by 75%+
- Ensure reliable retrieval of required documentation
- Organize files in a way that streamlines workflow
- Support both broad searches (by rig number) and targeted searches (by specific documents)


---


### RigOfficeDownloader Utility Workflow

This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow is clearly defined in the docstring at the beginning of the main script and follows these steps:

1. Fetch Documents
- The utility starts by scraping document metadata from predefined search URLs
- Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory
- Each document entry includes metadata like title, document number, revision, etc.
- All documents are initially marked with item_include=False
- Each document gets an item_generated_name for better identification

2. Export Documents to Markdown
- The JSON data is exported to a Markdown table: <rig>-a-docs.md
- This allows the user to easily review and edit which documents to include
- The user is expected to edit the Markdown file and set item_include=true for desired documents

3. Import Updated Document Data
- After the user edits the Markdown file, the utility imports the changes back to the JSON file
- This updates which documents are marked for file retrieval

4. Fetch Files for Selected Documents
- For each document with item_include=true, the utility scrapes file metadata
- File data is saved to <rig>-b-files.json
- Each file is initially marked with item_download=False
- Files inherit the document's item_generated_name with additional identifiers

5. Export Files to Markdown
- The file data is exported to a Markdown table: <rig>-b-files.md
- The user reviews and edits which files to download by setting item_download=true

6. Import Updated File Data
- After editing, the utility imports the changes back to the JSON file
- This updates which files are marked for download

7. Download Selected Files
- Files with item_download=true are downloaded
- Files are named according to their item_generated_name + extension
- The utility supports creating subfolders based on '/' in the item_generated_name
- Files are saved to the outputs/downloads/<rig> directory

Interactive Menu
- The utility provides an interactive menu where the user can choose which steps to execute
- This allows for flexibility in the workflow, enabling the user to run specific steps as needed
- The user can also update the rig number and search URLs through this menu

Key Features
- Automatic document and file metadata scraping
- User-friendly Markdown editing interface
- Customizable file naming with item_generated_name
- Support for subfolder organization in downloads
- Deduplication of documents and files
- Configurable field ordering for JSON and Markdown exports

Technical Implementation
- Uses Selenium with Chrome WebDriver for web scraping
- Implements smart waiting strategies for page loading
- Handles browser sessions with proper cleanup
- Provides progress feedback during operations
- Sanitizes filenames for valid paths
