"""
Window Manager module for detecting and managing windows.
"""

import os
import psutil
import ctypes
import win32gui
import win32con
import win32process
from ctypes import wintypes

from .models import Window, WindowType

# Retrieve Shell Window via ctypes (for older PyWin32)
user32 = ctypes.WinDLL("user32", use_last_error=True)
GetShellWindow = user32.GetShellWindow
GetShellWindow.restype = wintypes.HWND

SHELL_WINDOW = GetShellWindow()


class WindowManager:
    """
    Manages window detection, filtering, and grouping operations.
    """

    def __init__(self):
        self.windows = []

    def is_alt_tab_style_window(self, hwnd):
        """
        True if this window is a normal user-facing (Alt+Tab) top-level window.
        """
        # Compare to the shell window from ctypes approach
        if hwnd == SHELL_WINDOW:
            return False

        # Must be root ancestor
        if win32gui.GetAncestor(hwnd, win32con.GA_ROOT) != hwnd:
            return False

        ex_style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
        if ex_style & win32con.WS_EX_TOOLWINDOW:
            return False

        if win32gui.GetWindow(hwnd, win32con.GW_OWNER) != 0:
            return False

        title = win32gui.GetWindowText(hwnd)
        if not title.strip():
            return False

        return True

    def detect_windows(self, skip_explorer=True, min_size=2):
        """
        Enumerate "Alt+Tab style" top-level windows, retrieve process exe path via psutil,
        optionally skip explorer.exe, and skip windows smaller than min_size.

        Returns a reference to self for method chaining.
        """
        self.windows = []

        def enum_callback(hwnd, _):
            if not self.is_alt_tab_style_window(hwnd):
                return True

            _, pid = win32process.GetWindowThreadProcessId(hwnd)
            try:
                p = psutil.Process(pid)
                exe_path = p.exe().lower()
            except (psutil.Error, psutil.NoSuchProcess):
                return True

            if skip_explorer and "explorer.exe" in exe_path:
                return True

            placement = win32gui.GetWindowPlacement(hwnd)
            left, top, right, bottom = placement[4]
            width = right - left
            height = bottom - top

            if width < min_size or height < min_size:
                return True

            w = Window(hwnd, exe_path, (left, top, right, bottom))
            self.windows.append(w)
            return True

        win32gui.EnumWindows(enum_callback, None)
        return self

    def get_windows_by_process(self):
        """
        Group windows by their process name.
        Returns a dictionary with process names as keys and lists of Window objects as values.
        """
        groups = {}
        for w in self.windows:
            pname = w.process_name or "unknown"
            groups.setdefault(pname, []).append(w)
        return groups

    def get_windows_by_type(self):
        """
        Group windows by their window type.
        Returns a dictionary with WindowType enums as keys and lists of Window objects as values.
        """
        groups = {}
        for w in self.windows:
            wtype = w.window_type
            groups.setdefault(wtype, []).append(w)
        return groups

    def filter_windows(self, criteria):
        """
        Filter windows based on a criteria function.
        The criteria function should take a Window object and return True to include it.
        Returns a new list of Windows that match the criteria.
        """
        return [w for w in self.windows if criteria(w)]

    def get_window_count(self):
        """Return the number of detected windows."""
        return len(self.windows)
