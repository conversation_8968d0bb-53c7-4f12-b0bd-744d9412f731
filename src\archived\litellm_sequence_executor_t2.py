# =======================================================
# [2025.04.15 20:51]
# 'https://aistudio.google.com/prompts/10yBQqeBzuNtt3u5G1_wkqsBH3SXNJ-0A'

#!/usr/bin/env python3

# =============================================================================
# SECTION 1: Imports & Basic Setup
# =============================================================================
import asyncio
import json
import os
import sys
import argparse
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Union
import logging

from pydantic import BaseModel, Field, ValidationError
import litellm
from litellm import ModelResponse as LiteLLMModelResponse # Alias to avoid name clash
from litellm import acompletion

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our catalog generator (assuming it exists in the specified path)
try:
    from templates.lvl1.templates_lvl1_md_catalog_generator import (
        load_catalog,
        get_sequence,
        get_all_sequences,
        get_system_instruction,
        regenerate_catalog,
        TemplateObject # Assuming TemplateObject is the type returned by get_sequence
    )
    CATALOG_AVAILABLE = True
except ImportError:
    logger.warning("Catalog generator module not found. Catalog features will be disabled.")
    CATALOG_AVAILABLE = False
    # Define dummy functions/types if catalog is not available
    def load_catalog(): return {}
    def get_sequence(catalog, seq_id): return None
    def get_all_sequences(catalog): return []
    def get_system_instruction(template): return template.get("raw", "") if isinstance(template, dict) else ""
    def regenerate_catalog(force=False): return {}
    TemplateObject = Dict[str, Any] # Placeholder type


# =============================================================================
# SECTION 2: Configuration System
# =============================================================================
class Config:
    """
    Centralized configuration system designed for balanced friction.
    Handles provider/model selection, parameters, and LiteLLM setup.
    """
    # --- Basic Setup ---
    @staticmethod
    def setup_encoding():
        """Configure UTF-8 output for proper display."""
        if hasattr(sys.stdout, "reconfigure"):
            try: sys.stdout.reconfigure(encoding="utf-8", errors="replace")
            except Exception as e: logger.warning(f"Could not reconfigure stdout: {e}")
        if hasattr(sys.stderr, "reconfigure"):
            try: sys.stderr.reconfigure(encoding="utf-8", errors="replace")
            except Exception as e: logger.warning(f"Could not reconfigure stderr: {e}")

    # --- Provider Constants ---
    PROVIDER_ANTHROPIC = "anthropic"
    PROVIDER_DEEPSEEK  = "deepseek"
    PROVIDER_GOOGLE    = "google"
    PROVIDER_OPENAI    = "openai"
    # Add more providers as needed, e.g., "openrouter", "groq", etc.

    # --- Default Provider ---
    # Change this line to set your preferred default provider
    DEFAULT_PROVIDER = PROVIDER_ANTHROPIC

    # --- Model Registry & Provider Defaults ---
    # Maps user-friendly names to LiteLLM model identifiers and sets provider defaults.
    # The 'default_model' key indicates the preferred model for that provider if none is specified.
    # Additional keys (e.g., 'temperature', 'max_tokens') are default parameters for the provider.
    PROVIDER_CONFIG = {
        PROVIDER_OPENAI: {
            "default_model": "gpt-4o-mini",
            "models": {
                "gpt-4o": "gpt-4o",
                "gpt-4o-mini": "gpt-4o-mini",
                "gpt-4-turbo": "gpt-4-turbo",
                "gpt-4": "gpt-4",
                "gpt-3.5-turbo": "gpt-3.5-turbo",
            },
            "temperature": 0.1,
            "max_tokens": 4000,
        },
        PROVIDER_ANTHROPIC: {
            # Note: LiteLLM often expects 'anthropic/' prefix, but sometimes just the model name works depending on env vars.
            # Using OpenRouter format as it's explicit. Add direct Anthropic IDs if needed.
            "default_model": "claude-3.5-sonnet",
            "models": {
                "claude-3.5-sonnet": "anthropic/claude-3-5-sonnet-20240620", # Direct Anthropic ID
                "claude-3-opus": "anthropic/claude-3-opus-20240229",
                "claude-3-sonnet": "anthropic/claude-3-sonnet-20240229",
                "claude-3-haiku": "anthropic/claude-3-haiku-20240307",
                # Example using OpenRouter prefix (requires OPENROUTER_API_KEY)
                "openrouter-claude-3.5-sonnet": "openrouter/anthropic/claude-3.5-sonnet",
            },
            "temperature": 0.2,
            "max_tokens": 4000,
        },
        PROVIDER_GOOGLE: {
            # LiteLLM uses 'gemini/' prefix
            "default_model": "gemini-1.5-flash",
            "models": {
                "gemini-1.5-pro": "gemini/gemini-1.5-pro-latest",
                "gemini-1.5-flash": "gemini/gemini-1.5-flash-latest",
                # Add older models if needed
                # "gemini-pro": "gemini/gemini-pro",
            },
            "temperature": 0.3,
            "max_tokens": 8000, # Gemini often supports larger contexts
        },
        PROVIDER_DEEPSEEK: {
            # LiteLLM uses 'deepseek/' prefix
            "default_model": "deepseek-chat",
            "models": {
                "deepseek-reasoner": "deepseek/deepseek-reasoner",
                "deepseek-coder": "deepseek/deepseek-coder",
                "deepseek-chat": "deepseek/deepseek-chat",
            },
            "temperature": 0.2,
            "max_tokens": 4000,
        },
        # --- Example: Adding OpenRouter as a distinct provider ---
        # "openrouter": {
        #     "default_model": "dolphin-mixtral-8x7b",
        #     "models": {
        #         "dolphin-mixtral-8x7b": "openrouter/cognitivecomputations/dolphin-mixtral-8x7b",
        #         "wizardlm2-8x22b": "openrouter/microsoft/wizardlm-2-8x22b",
        #         # Include models from other providers via OpenRouter if desired
        #         "or-claude-3.5-sonnet": "openrouter/anthropic/claude-3.5-sonnet",
        #         "or-gpt-4o": "openrouter/openai/gpt-4o",
        #     },
        #     "temperature": 0.25,
        #     "max_tokens": 3500,
        # }
    }

    @classmethod
    def get_provider_for_model(cls, model_name: str) -> Optional[str]:
        """Find the provider associated with a given user-friendly model name."""
        for provider, config in cls.PROVIDER_CONFIG.items():
            if model_name in config.get("models", {}):
                return provider
        return None

    @classmethod
    def get_litellm_model_id(cls, model_name: str) -> Optional[str]:
        """Get the LiteLLM model identifier from the user-friendly name."""
        for config in cls.PROVIDER_CONFIG.values():
            if model_name in config.get("models", {}):
                return config["models"][model_name]
        # If not found in registry, assume the name itself might be a valid LiteLLM ID
        logger.warning(f"Model '{model_name}' not found in registry. Attempting to use it directly.")
        return model_name

    @classmethod
    def get_default_model_for_provider(cls, provider: str) -> Optional[str]:
        """Get the default user-friendly model name for a given provider."""
        config = cls.PROVIDER_CONFIG.get(provider)
        return config.get("default_model") if config else None

    @classmethod
    def get_default_model(cls) -> str:
        """Get the default model for the default provider."""
        default_model_name = cls.get_default_model_for_provider(cls.DEFAULT_PROVIDER)
        if default_model_name:
            return default_model_name
        else:
            # Fallback if default provider/model isn't configured correctly
            logger.error("Default provider or its default model not configured. Falling back.")
            # Try finding the first available default model
            for config in cls.PROVIDER_CONFIG.values():
                if "default_model" in config:
                    return config["default_model"]
            # Absolute fallback
            return "gpt-3.5-turbo"

    @classmethod
    def get_model_params(cls, model_name: str, provider: Optional[str] = None, **overrides) -> Dict[str, Any]:
        """
        Get the parameters for a specific model, including provider defaults and overrides.

        Args:
            model_name: The user-friendly model name (e.g., "gpt-4o", "claude-3.5-sonnet").
            provider: The provider name (optional, will be inferred if possible).
            **overrides: Specific parameters to override defaults (e.g., temperature=0.5).

        Returns:
            A dictionary of parameters suitable for LiteLLM, including the 'model' key.
        """
        if not provider:
            provider = cls.get_provider_for_model(model_name)

        provider_config = cls.PROVIDER_CONFIG.get(provider, {})

        # Start with provider defaults (excluding 'models' and 'default_model')
        params = {k: v for k, v in provider_config.items() if k not in ["models", "default_model"]}

        # Apply overrides provided in the function call
        params.update(overrides)

        # Resolve the actual LiteLLM model identifier
        litellm_model_id = cls.get_litellm_model_id(model_name)
        if not litellm_model_id:
             raise ValueError(f"Could not resolve LiteLLM model ID for '{model_name}'")

        params["model"] = litellm_model_id
        return params

    @classmethod
    def get_available_models(cls) -> Dict[str, List[Dict[str, Any]]]:
        """Returns all available models grouped by provider."""
        result = {}
        for provider, config in cls.PROVIDER_CONFIG.items():
            default_model = config.get("default_model")
            provider_models = []
            for name, model_id in config.get("models", {}).items():
                provider_models.append({
                    "name": name,
                    "model_id": model_id,
                    "is_default": (name == default_model)
                })
            if provider_models:
                 # Sort models alphabetically by name for consistent display
                result[provider] = sorted(provider_models, key=lambda x: x['name'])
        return result

    # --- LiteLLM Configuration ---
    @classmethod
    def configure_litellm(cls, verbose: bool = False):
        """Set up LiteLLM with appropriate configuration."""
        litellm.drop_params = True # Automatically drop unsupported params
        litellm.num_retries = 3
        litellm.request_timeout = 180 # Increased timeout
        litellm.set_verbose = verbose # Control verbosity

        # Enable cost tracking ONLY if an appropriate API key is set (e.g., OpenAI, Anthropic)
        # LiteLLM's built-in cost tracking might require specific setups.
        # Consider manual cost calculation based on tokens if needed universally.
        # litellm.success_callback = ["track_cost"] # Example: enable cost tracking callback if configured
        # litellm.callbacks = [MyCustomHandler()] # Example for custom logging/handling

        logger.info("LiteLLM configured:")
        logger.info(f"- Default provider: {cls.DEFAULT_PROVIDER}")
        try:
            default_model_name = cls.get_default_model()
            default_litellm_id = cls.get_litellm_model_id(default_model_name)
            logger.info(f"- Default model: {default_model_name} ({default_litellm_id})")
        except Exception as e:
            logger.error(f"Could not determine default model: {e}")
        logger.info(f"- Retries: {litellm.num_retries}, Timeout: {litellm.request_timeout}s")
        logger.info(f"- Verbose: {litellm.set_verbose}")

        # Set up encoding
        cls.setup_encoding()


# =============================================================================
# SECTION 3: Data Models & Tracking
# =============================================================================
class ModelCallResponse(BaseModel):
    """Detailed response from a single model call for a specific step."""
    model_friendly_name: str = Field(description="User-friendly model name used for the request")
    model_litellm_id: str = Field(description="Actual LiteLLM model identifier used")
    content: Union[str, Dict, List, None] = Field(description="The content of the response, potentially parsed if JSON")
    cost: Optional[float] = Field(None, description="Cost of this specific API call in USD (if available)")
    usage: Optional[Dict[str, int]] = Field(None, description="Token usage (prompt, completion, total)")
    error: Optional[str] = Field(None, description="Error message if the call failed")
    raw_response_obj: Optional[Dict[str, Any]] = Field(None, description="Raw response object from LiteLLM for debugging", exclude=True) # Exclude from final JSON


class InstructionStepResult(BaseModel):
    """Results for a specific instruction step across multiple models."""
    step_id: str = Field(description="The step identifier (e.g., 'a', 'b', '0099-a')")
    title: str = Field(description="The title of the instruction step")
    system_prompt: str = Field(description="The system prompt used for this step")
    user_prompt: str = Field(description="The user prompt used for this step (often the output of the previous step or the initial prompt)")
    responses: Dict[str, ModelCallResponse] = Field(default_factory=dict, description="Dictionary mapping model friendly name to its response for this step")


class ExecutionRunResults(BaseModel):
    """Complete results of executing a user prompt against a sequence of instructions."""
    run_id: str = Field(description="Unique identifier for this execution run (e.g., timestamp)")
    initial_user_prompt: str = Field(description="The initial user prompt that started the sequence")
    sequence_id: str = Field(description="Identifier of the instruction sequence used")
    models_used: List[str] = Field(description="List of user-friendly model names requested for the run")
    steps: List[InstructionStepResult] = Field(default_factory=list, description="Results for each instruction step")
    total_cost: float = Field(0.0, description="Total estimated cost of all successful LLM API calls in USD")
    final_output: Optional[Any] = Field(None, description="The final output from the last step of the sequence (from the first model if multiple were used)")
    errors: List[str] = Field(default_factory=list, description="List of errors encountered during the sequence execution")


# Cost tracking is now integrated into ExecutionRunResults
# No separate CostTracker class needed


# =============================================================================
# SECTION 4: Text Sequence Support (Legacy)
# =============================================================================
def load_text_sequence(sequence_name: str) -> List[Tuple[str, Dict[str, Any]]]:
    """
    Load instructions from a text file with '---' separators.
    Returns a list of tuples: (step_id, fake_template_dict).
    """
    templates_dir = os.path.join(os.path.dirname(__file__), "templates")
    sequence_path = os.path.join(templates_dir, f"{sequence_name}.txt")

    if not os.path.exists(sequence_path):
        raise FileNotFoundError(f"Text sequence file not found: {sequence_path}")

    try:
        with open(sequence_path, "r", encoding="utf-8") as f:
            content = f.read()

        instructions = [part.strip() for part in content.split("---") if part.strip()]

        # Create the fake sequence steps format expected by the execution engine
        sequence_steps = []
        for i, instr in enumerate(instructions):
            step_id = chr(97 + i) # a, b, c...
            fake_template = {
                "id": f"text-{sequence_name}-{step_id}",
                "raw": instr,
                "parts": {
                    "title": f"Text Step {i+1}",
                    "interpretation": instr[:100] + "...", # Basic interpretation
                    "transformation": "`{role=text_executor; input=previous_output:any; process=[execute_raw_instruction()]; output={result:any}}`" # Generic transformation
                }
            }
            sequence_steps.append((step_id, fake_template))
        return sequence_steps

    except Exception as e:
        logger.error(f"Error loading text sequence '{sequence_name}': {e}")
        raise


# =============================================================================
# SECTION 5: Sequence Execution Engine
# =============================================================================

async def execute_single_step(
    system_prompt: str,
    current_input: str,
    model_friendly_name: str,
    step_id: str,
    base_model_params: Dict[str, Any],
    require_json: bool = False
) -> ModelCallResponse:
    """Executes a single LLM call for one step and one model."""

    start_time = datetime.now()
    model_call_response = ModelCallResponse(
        model_friendly_name=model_friendly_name,
        model_litellm_id=base_model_params.get("model", "unknown")
    )

    # Add JSON requirement to system prompt if needed
    effective_system_prompt = system_prompt
    if require_json:
         # Simple appending - might need more sophisticated injection depending on model/prompt
        effective_system_prompt += "\n\nRESPONSE MUST BE A VALID JSON OBJECT. Do not include any preamble, explanation, or markdown formatting before or after the JSON."

    messages = [
        {"role": "system", "content": effective_system_prompt},
        {"role": "user", "content": current_input}
    ]

    logger.info(f"--- Step {step_id} | Model {model_friendly_name} ---")
    logger.info(f"System Prompt Snippet: {effective_system_prompt[:150]}...")
    logger.info(f"User Input Snippet: {current_input[:150]}...")

    full_content = ""
    try:
        response: LiteLLMModelResponse = await acompletion(
            messages=messages,
            stream=False, # Get full response at once for easier processing and cost calculation
            **base_model_params
        )

        # Extract content, usage, and cost
        if response.choices and response.choices[0].message:
           full_content = response.choices[0].message.content or ""

        model_call_response.usage = response.usage.dict() if response.usage else None # Usage(prompt_tokens, completion_tokens, total_tokens)
        model_call_response.cost = response.cost.get('completion_cost', 0.0) if response.cost else 0.0 # Get cost if available from LiteLLM

        # Attempt to parse JSON if required
        if require_json:
            try:
                # Basic cleaning attempt (remove potential markdown backticks)
                cleaned_content = full_content.strip().removeprefix("```json").removesuffix("```").strip()
                model_call_response.content = json.loads(cleaned_content)
                logger.info(f"Successfully parsed JSON response.")
            except json.JSONDecodeError as json_err:
                logger.warning(f"Failed to parse JSON response from {model_friendly_name}: {json_err}")
                model_call_response.content = full_content # Store raw content if JSON parsing fails
                model_call_response.error = f"JSONDecodeError: {json_err}"
        else:
            model_call_response.content = full_content

        # Store raw response object if needed for debugging (optional)
        # model_call_response.raw_response_obj = response.dict()

        logger.info(f"Response Snippet: {str(model_call_response.content)[:150]}...")
        logger.info(f"Cost: ${model_call_response.cost:.6f} | Usage: {model_call_response.usage}")

    except Exception as e:
        logger.error(f"Error during LLM call for {model_friendly_name} in step {step_id}: {e}", exc_info=True)
        model_call_response.content = None
        model_call_response.error = str(e)
        # Assign zero cost on error, or handle cost estimation differently if needed
        model_call_response.cost = 0.0

    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    logger.info(f"--- Step {step_id} | Model {model_friendly_name} | Duration: {duration:.2f}s ---")

    return model_call_response


async def execute_sequence(
    sequence_steps: List[Tuple[str, TemplateObject]],
    initial_user_prompt: str,
    sequence_id: str,
    model_names: List[str],
    output_file: str,
    run_id: str,
    **cli_params # CLI overrides like temperature, max_tokens
) -> None:
    """
    Executes a sequence of instruction steps, processes responses, and saves results.

    Args:
        sequence_steps: List of (step_id, template_dict) tuples.
        initial_user_prompt: The starting input from the user.
        sequence_id: Identifier for the sequence being run.
        model_names: List of user-friendly model names to use for each step.
        output_file: Path to save the final JSON results.
        run_id: Unique ID for this run.
        **cli_params: Additional parameters from CLI to override defaults.
    """
    run_results = ExecutionRunResults(
        run_id=run_id,
        initial_user_prompt=initial_user_prompt,
        sequence_id=sequence_id,
        models_used=model_names
    )

    current_step_input = initial_user_prompt
    all_models_step_outputs: Dict[str, Any] = {m: None for m in model_names} # Track output for each model separately if needed for complex branching (not fully used here yet)


    for step_idx, (step_id, template) in enumerate(sequence_steps):
        step_result = InstructionStepResult(
            step_id=step_id,
            title=template.get("parts", {}).get("title", f"Step {step_id}"),
            system_prompt=get_system_instruction(template),
            user_prompt=current_step_input # Input for this step
        )

        # Determine if this step expects JSON output based on its definition
        # Simple check: look for 'dict' or 'list' or 'object' in the output definition
        transformation_str = template.get("parts", {}).get("transformation", "")
        expects_json = any(kw in transformation_str for kw in [":dict", ":list", ":object"])
        logger.info(f"Step {step_id}: Expects JSON output? {expects_json}")


        # --- Execute for all specified models ---
        tasks = []
        for model_name in model_names:
            try:
                # Get base params, applying CLI overrides
                base_params = Config.get_model_params(model_name, **cli_params)

                tasks.append(
                    execute_single_step(
                        system_prompt=step_result.system_prompt,
                        current_input=current_step_input, # Use the same input for all models in parallel for this step
                        model_friendly_name=model_name,
                        step_id=step_id,
                        base_model_params=base_params,
                        require_json=expects_json
                    )
                )
            except Exception as e:
                logger.error(f"Failed to prepare task for model {model_name} in step {step_id}: {e}")
                # Add an error response directly
                error_response = ModelCallResponse(
                    model_friendly_name=model_name,
                    model_litellm_id=Config.get_litellm_model_id(model_name) or "unknown",
                    content=None,
                    error=f"Task preparation failed: {e}",
                    cost=0.0
                )
                step_result.responses[model_name] = error_response
                run_results.errors.append(f"Step {step_id}, Model {model_name}: Task preparation failed: {e}")


        # Run models for this step concurrently
        if tasks:
            model_responses = await asyncio.gather(*tasks)

            # Process results
            for response in model_responses:
                step_result.responses[response.model_friendly_name] = response
                if response.error:
                    run_results.errors.append(f"Step {step_id}, Model {response.model_friendly_name}: {response.error}")
                if response.cost is not None:
                    run_results.total_cost += response.cost
                # Store the output for potential use in complex branching later
                all_models_step_outputs[response.model_friendly_name] = response.content


        # --- Prepare input for the next step ---
        # Default: Use the output from the *first* successful model in the list
        next_input = None
        for model_name in model_names:
             # Check if the model ran and produced non-error content
            if model_name in step_result.responses and step_result.responses[model_name].content is not None:
                # Ensure the output is stringified for the next step's input prompt
                content = step_result.responses[model_name].content
                if isinstance(content, (dict, list)):
                   next_input = json.dumps(content, indent=2) # Pretty print JSON for readability in next prompt
                else:
                   next_input = str(content)
                logger.info(f"Using output from '{model_name}' as input for the next step.")
                break # Stop after finding the first valid output

        if next_input is None:
            error_msg = f"No successful output from any model in step {step_id} to use as input for the next step."
            logger.error(error_msg)
            run_results.errors.append(error_msg)
            # Decide how to proceed: stop execution, use a default value, or use the previous input?
            # Option: Stop execution
            step_result.system_prompt += "\n\n[Execution Halted: No valid input for next step]"
            run_results.steps.append(step_result) # Add the current step's partial results
            break # Exit the loop over steps
            # Option: Use previous input (might lead to loops or incorrect results)
            # current_step_input = current_step_input # Keep the same input (use with caution)
            # Option: Use a default empty string/object
            # current_step_input = "{}" if expects_json else ""

        current_step_input = next_input # Set input for the next iteration

        # Store the result of this step
        run_results.steps.append(step_result)

        # Save intermediate results progressively (optional, good for long runs)
        # try:
        #     with open(output_file.replace('.json', f'.step_{step_id}.json'), "w", encoding="utf-8") as f_intermediate:
        #         f_intermediate.write(run_results.model_dump_json(indent=2))
        # except Exception as e:
        #     logger.warning(f"Could not save intermediate results for step {step_id}: {e}")


    # --- Finalization ---
    # Set the final output of the entire sequence
    if run_results.steps:
        last_step = run_results.steps[-1]
        # Find the first successful response in the last step
        for model_name in model_names:
            if model_name in last_step.responses and last_step.responses[model_name].content is not None:
                run_results.final_output = last_step.responses[model_name].content
                break

    # Save the complete results
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            # Use Pydantic's serialization for robust JSON conversion
            f.write(run_results.model_dump_json(indent=2, exclude_none=True)) # exclude_none keeps JSON cleaner
        logger.info(f"\nFull results saved to {output_file}")
    except TypeError as e:
        logger.error(f"Serialization error writing results to {output_file}: {e}. Attempting fallback dump.")
        # Fallback: Try dumping with default=str if complex objects cause issues
        try:
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(run_results.model_dump(exclude_none=True), f, indent=2, default=str)
            logger.info(f"Fallback results dump successful.")
        except Exception as dump_err:
            logger.error(f"Fallback results dump failed: {dump_err}")
    except Exception as e:
        logger.error(f"Failed to write results to {output_file}: {e}")


    # Print summary
    print("\n=== EXECUTION SUMMARY ===")
    print(f"Run ID: {run_results.run_id}")
    print(f"Initial Prompt: {run_results.initial_user_prompt[:100]}...")
    print(f"Sequence: {run_results.sequence_id}")
    print(f"Models: {', '.join(run_results.models_used)}")
    print(f"Total Estimated Cost: ${run_results.total_cost:.6f}")
    if run_results.errors:
        print(f"Errors Encountered: {len(run_results.errors)}")
        for err in run_results.errors[:3]: # Print first few errors
            print(f"  - {err}")
        if len(run_results.errors) > 3: print("  ...")

    print("\nStep Summary:")
    for step in run_results.steps:
        print(f"- Step {step.step_id}: {step.title}")
        for model_name, resp in step.responses.items():
            status = "Success" if resp.error is None else "Failed"
            cost_str = f"cost=${resp.cost:.4f}" if resp.cost is not None else "cost=N/A"
            usage_str = f"usage={resp.usage}" if resp.usage else ""
            snippet = str(resp.content)[:80].replace('\n', ' ') + ("..." if len(str(resp.content)) > 80 else "")
            print(f"  - [{model_name}]: {status} ({cost_str} {usage_str}) -> '{snippet}'")

    print(f"\nFinal Output Snippet (from first successful model in last step):")
    print(f"{str(run_results.final_output)[:200]}...")


# =============================================================================
# SECTION 6: CLI Interface & Main Entry Point
# =============================================================================
def print_available_models():
    """Print available models from the configuration in a structured format."""
    models_by_provider = Config.get_available_models()

    print("\n=== Available Models (from Config) ===")
    if not models_by_provider:
        print("No models defined in the configuration.")
        return

    for provider, models in models_by_provider.items():
        print(f"\nProvider: {provider.upper()}")
        default_model_name = Config.get_default_model_for_provider(provider)
        for model in models:
            default_marker = " (provider default)" if model["is_default"] else ""
            global_default_marker = " (global default)" if Config.DEFAULT_PROVIDER == provider and model["is_default"] else ""
            print(f"  - Name: {model['name']}{default_marker}{global_default_marker}")
            print(f"    LiteLLM ID: {model['model_id']}")


async def main():
    parser = argparse.ArgumentParser(
        description="Execute instruction sequences across multiple LLM models using LiteLLM.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--sequence", type=str, default="0099",
        help="Sequence ID from catalog (e.g., '0099') or name of text file (if --use-text is set)."
    )
    parser.add_argument(
        "--prompt", type=str,
        help="User prompt to execute through the sequence. If empty, a default prompt is used."
    )
    parser.add_argument(
        "--models", type=str,
        help="Comma-separated list of user-friendly model names (e.g., 'gpt-4o,claude-3.5-sonnet'). Overrides --provider."
    )
    parser.add_argument(
        "--provider", type=str, default=Config.DEFAULT_PROVIDER, choices=Config.PROVIDER_CONFIG.keys(),
        help="Default provider to use if --models is not set. Will use the provider's default model."
    )
    parser.add_argument(
        "--output", type=str,
        help="Path to output JSON file. If not set, defaults to a timestamped filename."
    )
    parser.add_argument(
        "--use-text", action="store_true",
        help="Load sequence from a .txt file in './templates/' instead of the generated catalog."
    )
    parser.add_argument(
        "--list-sequences", action="store_true",
        help="List available sequences from the catalog and exit."
    )
    parser.add_argument(
        "--list-models", action="store_true",
        help="List available models defined in the configuration and exit."
    )
    parser.add_argument(
        "--force-regenerate", action="store_true",
        help="Force regeneration of the template catalog before running."
    )
    parser.add_argument(
        "--temperature", type=float,
        help="Override default temperature for all models (0.0-2.0)."
    )
    parser.add_argument(
        "--max-tokens", type=int,
        help="Override default max_tokens for all models."
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true",
        help="Enable verbose logging from LiteLLM."
    )

    args = parser.parse_args()

    # Set up LiteLLM and Encoding
    Config.configure_litellm(verbose=args.verbose)
    Config.setup_encoding()

    # --- Handle Informational Arguments ---
    if args.list_models:
        print_available_models()
        return

    if args.list_sequences:
        if not CATALOG_AVAILABLE:
             logger.error("Cannot list sequences because the catalog module is not available.")
             return
        print("\nLoading catalog to list sequences...")
        catalog = regenerate_catalog() # Load existing or generate if missing
        if not catalog:
             logger.warning("Catalog is empty or could not be loaded.")
             return
        print("\nAvailable Sequences (from Catalog):")
        sequences = get_all_sequences(catalog)
        if not sequences:
            print("  No sequences found in the catalog.")
            return
        for seq_id in sequences:
            sequence_steps = get_sequence(catalog, seq_id)
            if sequence_steps:
                # Try to get the title from the first step's template
                first_step_template = sequence_steps[0][1]
                title = first_step_template.get("parts", {}).get("title", "Unknown Title")
                print(f"  - ID: {seq_id}")
                print(f"    Title: {title}")
                print(f"    Steps: {len(sequence_steps)}")
            else:
                print(f"  - ID: {seq_id} (Error loading details)")
        return

    # --- Prepare for Execution ---

    # Load or regenerate catalog if needed (and not using text mode)
    catalog = {}
    if not args.use_text and CATALOG_AVAILABLE:
        if args.force_regenerate:
            print("Forcing catalog regeneration...")
            catalog = regenerate_catalog(force=True)
        else:
            catalog = regenerate_catalog() # Ensures catalog exists
        if not catalog and not args.list_sequences: # Avoid error if just listing
             logger.error("Failed to load or regenerate catalog. Cannot proceed without --use-text.")
             return

    # Determine Model(s) to Use
    models_to_run = []
    if args.models:
        models_to_run = [m.strip() for m in args.models.split(",") if m.strip()]
        logger.info(f"Using models specified via --models: {models_to_run}")
    else:
        provider_to_use = args.provider or Config.DEFAULT_PROVIDER
        default_model = Config.get_default_model_for_provider(provider_to_use)
        if default_model:
            models_to_run = [default_model]
            logger.info(f"Using default model '{default_model}' for provider '{provider_to_use}'.")
        else:
            # Fallback if provider default isn't set, use global default
            global_default = Config.get_default_model()
            models_to_run = [global_default]
            logger.warning(f"Could not find default model for provider '{provider_to_use}'. Using global default: '{global_default}'")

    # Validate models are in config (optional, but good practice)
    valid_models = []
    for m in models_to_run:
        if Config.get_litellm_model_id(m):
             valid_models.append(m)
        else:
             logger.warning(f"Model '{m}' is not explicitly defined in the configuration. Attempting to use directly.")
             # Allow using models not in config if user insists
             valid_models.append(m)
             # Alternatively, raise an error:
             # raise ValueError(f"Model '{m}' not found in configuration. Use --list-models to see available options.")
    if not valid_models:
        logger.error("No valid models selected for execution.")
        print_available_models()
        return
    models_to_run = valid_models


    # Determine User Prompt
    default_prompt = "Analyze the provided Python script (`sequence_executor.py`) and suggest three specific, actionable improvements related to robustness, maintainability, or performance."
    user_prompt = args.prompt or default_prompt
    if not args.prompt:
        logger.info(f"No prompt provided, using default: '{default_prompt[:100]}...'")

    # Determine Sequence ID and Load Sequence Steps
    sequence_id = args.sequence
    sequence_steps = []
    display_name = "" # Name used in logs and output file

    try:
        if args.use_text:
            logger.info(f"Loading sequence from text file: {sequence_id}.txt")
            sequence_steps = load_text_sequence(sequence_id)
            display_name = f"text-{sequence_id}"
        elif CATALOG_AVAILABLE:
            logger.info(f"Loading sequence '{sequence_id}' from catalog.")
            sequence_steps = get_sequence(catalog, sequence_id)
            if not sequence_steps:
                raise ValueError(f"Sequence ID '{sequence_id}' not found in the catalog.")
            display_name = sequence_id
        else:
             raise RuntimeError("Catalog is unavailable and --use-text was not specified.")

    except (FileNotFoundError, ValueError, RuntimeError) as e:
        logger.error(f"Error loading sequence: {e}")
        if not args.use_text and CATALOG_AVAILABLE:
             print("\nAvailable catalog sequences:")
             sequences = get_all_sequences(catalog)
             if sequences:
                 for sid in sequences: print(f"  - {sid}")
             else: print("  (No sequences found)")
        elif args.use_text:
             print(f"\nMake sure a file named '{sequence_id}.txt' exists in the 'templates' directory.")
        return

    # Determine Output Path
    run_id = datetime.now().strftime("%Y%m%d_%H%M%S")
    if args.output:
        output_path = args.output
    else:
        # Auto-generate filename
        model_part = "_".join(models_to_run).replace('/','-') # Sanitize model names for filename
        output_path = f"output_{run_id}_{display_name}_{model_part}.json"
        # Truncate filename if it gets too long
        max_len = 150
        if len(output_path) > max_len:
             output_path = output_path[:max_len-10] + output_path[-10:] # Keep timestamp part
        output_path = os.path.join("outputs", output_path) # Save in an 'outputs' subdirectory

    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_path) or ".", exist_ok=True)


    # Prepare CLI Overrides for LLM Parameters
    cli_params = {}
    if args.temperature is not None:
        cli_params["temperature"] = args.temperature
    if args.max_tokens is not None:
        cli_params["max_tokens"] = args.max_tokens


    # Display Execution Plan
    print("\n--- Starting Sequence Execution ---")
    print(f"Run ID: {run_id}")
    print(f"Sequence: {display_name} ({len(sequence_steps)} steps)")
    print(f"Models:")
    for model in models_to_run:
        try:
             params = Config.get_model_params(model, **cli_params)
             print(f"  - {model} (ID: {params['model']}, Temp: {params.get('temperature')}, MaxTokens: {params.get('max_tokens')})")
        except Exception as e:
             print(f"  - {model} (Error retrieving params: {e})")
    print(f"Initial Prompt: {user_prompt[:150]}...")
    print(f"Output will be saved to: {output_path}")
    print("---")


    # Execute the sequence
    try:
        await execute_sequence(
            sequence_steps=sequence_steps,
            initial_user_prompt=user_prompt,
            sequence_id=display_name,
            model_names=models_to_run,
            output_file=output_path,
            run_id=run_id,
            **cli_params
        )
    except Exception as e:
        logger.error(f"An unexpected error occurred during sequence execution: {e}", exc_info=True)
        # Attempt to save partial results if possible (might need more sophisticated state management)
        print("\nExecution failed. Check logs for details.")


if __name__ == "__main__":
    # Ensure the script directory is in the Python path for imports
    script_dir = os.path.dirname(os.path.abspath(__file__))
    if script_dir not in sys.path:
        sys.path.insert(0, script_dir)

    # Create outputs directory if it doesn't exist
    os.makedirs("outputs", exist_ok=True)

    asyncio.run(main())
